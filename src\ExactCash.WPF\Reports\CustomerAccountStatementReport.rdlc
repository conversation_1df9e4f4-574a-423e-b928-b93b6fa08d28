<?xml version="1.0" encoding="utf-8"?>
<Report xmlns="http://schemas.microsoft.com/sqlserver/reporting/2016/01/reportdefinition" xmlns:rd="http://schemas.microsoft.com/SQLServer/reporting/reportdesigner">
  <AutoRefresh>0</AutoRefresh>
  <DataSources>
    <DataSource Name="CustomerAccountStatementDataSource">
      <ConnectionProperties>
        <DataProvider>System.Data.DataSet</DataProvider>
        <ConnectString>/* Local Connection */</ConnectString>
      </ConnectionProperties>
      <rd:DataSourceID>b8c8c8c8-8c8c-8c8c-8c8c-8c8c8c8c8c8c</rd:DataSourceID>
    </DataSource>
  </DataSources>
  <DataSets>
    <DataSet Name="CustomerAccountStatementDataSet">
      <Query>
        <DataSourceName>CustomerAccountStatementDataSource</DataSourceName>
        <CommandText>/* Local Query */</CommandText>
      </Query>
      <Fields>
        <Field Name="CustomerName">
          <DataField>CustomerName</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="Phone">
          <DataField>Phone</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="Email">
          <DataField>Email</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="Address">
          <DataField>Address</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="FormattedStatementDate">
          <DataField>FormattedStatementDate</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="FormattedFromDate">
          <DataField>FormattedFromDate</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="FormattedToDate">
          <DataField>FormattedToDate</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="FormattedOpeningBalance">
          <DataField>FormattedOpeningBalance</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="FormattedTotalSales">
          <DataField>FormattedTotalSales</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="FormattedTotalPayments">
          <DataField>FormattedTotalPayments</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="FormattedClosingBalance">
          <DataField>FormattedClosingBalance</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="FormattedOutstandingBalance">
          <DataField>FormattedOutstandingBalance</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
      </Fields>
    </DataSet>
    <DataSet Name="CustomerTransactionsDataSet">
      <Query>
        <DataSourceName>CustomerAccountStatementDataSource</DataSourceName>
        <CommandText>/* Local Query */</CommandText>
      </Query>
      <Fields>
        <Field Name="FormattedTransactionDate">
          <DataField>FormattedTransactionDate</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="TransactionType">
          <DataField>TransactionType</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="ReferenceNumber">
          <DataField>ReferenceNumber</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="Description">
          <DataField>Description</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="FormattedDebitAmount">
          <DataField>FormattedDebitAmount</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="FormattedCreditAmount">
          <DataField>FormattedCreditAmount</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="FormattedRunningBalance">
          <DataField>FormattedRunningBalance</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
      </Fields>
    </DataSet>
    <DataSet Name="CompanyInfoDataSet">
      <Query>
        <DataSourceName>CustomerAccountStatementDataSource</DataSourceName>
        <CommandText>/* Local Query */</CommandText>
      </Query>
      <Fields>
        <Field Name="CompanyName">
          <DataField>CompanyName</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="CompanyAddress">
          <DataField>CompanyAddress</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="CompanyPhone">
          <DataField>CompanyPhone</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
      </Fields>
    </DataSet>
  </DataSets>
  <ReportSections>
    <ReportSection>
      <Body>
        <ReportItems>

          <!-- Transactions Table -->
          <Tablix Name="TransactionsTable">
            <TablixBody>
              <TablixColumns>
                <TablixColumn>
                  <Width>1in</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>1in</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>1.2in</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>2in</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>1in</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>1in</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>0.8in</Width>
                </TablixColumn>
              </TablixColumns>
              <TablixRows>
                <TablixRow>
                  <Height>0.3in</Height>
                  <TablixCells>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="DateHeader">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>التاريخ</Value>
                                  <Style>
                                    <FontFamily>Arial</FontFamily>
                                    <FontSize>10pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                    <Color>White</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>DateHeader</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>White</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </Border>
                            <BackgroundColor>#34495E</BackgroundColor>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="TypeHeader">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>النوع</Value>
                                  <Style>
                                    <FontFamily>Arial</FontFamily>
                                    <FontSize>10pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                    <Color>White</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>TypeHeader</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>White</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </Border>
                            <BackgroundColor>#34495E</BackgroundColor>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="ReferenceHeader">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>المرجع</Value>
                                  <Style>
                                    <FontFamily>Arial</FontFamily>
                                    <FontSize>10pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                    <Color>White</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>ReferenceHeader</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>White</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </Border>
                            <BackgroundColor>#34495E</BackgroundColor>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="DescriptionHeader">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>الوصف</Value>
                                  <Style>
                                    <FontFamily>Arial</FontFamily>
                                    <FontSize>10pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                    <Color>White</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>DescriptionHeader</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>White</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </Border>
                            <BackgroundColor>#34495E</BackgroundColor>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="DebitHeader">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>مدين</Value>
                                  <Style>
                                    <FontFamily>Arial</FontFamily>
                                    <FontSize>10pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                    <Color>White</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>DebitHeader</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>White</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </Border>
                            <BackgroundColor>#34495E</BackgroundColor>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="CreditHeader">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>دائن</Value>
                                  <Style>
                                    <FontFamily>Arial</FontFamily>
                                    <FontSize>10pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                    <Color>White</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>CreditHeader</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>White</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </Border>
                            <BackgroundColor>#34495E</BackgroundColor>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="BalanceHeader">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>الرصيد</Value>
                                  <Style>
                                    <FontFamily>Arial</FontFamily>
                                    <FontSize>10pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                    <Color>White</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>BalanceHeader</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>White</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </Border>
                            <BackgroundColor>#34495E</BackgroundColor>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                  </TablixCells>
                </TablixRow>
                <TablixRow>
                  <Height>0.25in</Height>
                  <TablixCells>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="TransactionDate">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!FormattedTransactionDate.Value</Value>
                                  <Style>
                                    <FontFamily>Arial</FontFamily>
                                    <FontSize>9pt</FontSize>
                                    <Color>#2C3E50</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>TransactionDate</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>#BDC3C7</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </Border>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="TransactionType">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!TransactionType.Value</Value>
                                  <Style>
                                    <FontFamily>Arial</FontFamily>
                                    <FontSize>9pt</FontSize>
                                    <Color>#2C3E50</Color>
                                    <FontWeight>Bold</FontWeight>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>TransactionType</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>#BDC3C7</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </Border>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="ReferenceNumber">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!ReferenceNumber.Value</Value>
                                  <Style>
                                    <FontFamily>Arial</FontFamily>
                                    <FontSize>9pt</FontSize>
                                    <Color>#2C3E50</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>ReferenceNumber</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>#BDC3C7</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </Border>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Description">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!Description.Value</Value>
                                  <Style>
                                    <FontFamily>Arial</FontFamily>
                                    <FontSize>9pt</FontSize>
                                    <Color>#2C3E50</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Right</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Description</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>#BDC3C7</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </Border>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="DebitAmount">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!FormattedDebitAmount.Value</Value>
                                  <Style>
                                    <FontFamily>Arial</FontFamily>
                                    <FontSize>9pt</FontSize>
                                    <Color>#E74C3C</Color>
                                    <FontWeight>Bold</FontWeight>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>DebitAmount</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>#BDC3C7</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </Border>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="CreditAmount">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!FormattedCreditAmount.Value</Value>
                                  <Style>
                                    <FontFamily>Arial</FontFamily>
                                    <FontSize>9pt</FontSize>
                                    <Color>#27AE60</Color>
                                    <FontWeight>Bold</FontWeight>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>CreditAmount</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>#BDC3C7</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </Border>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="RunningBalance">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!FormattedRunningBalance.Value</Value>
                                  <Style>
                                    <FontFamily>Arial</FontFamily>
                                    <FontSize>9pt</FontSize>
                                    <Color>#8E44AD</Color>
                                    <FontWeight>Bold</FontWeight>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>RunningBalance</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>#BDC3C7</Color>
                              <Style>Solid</Style>
                              <Width>1pt</Width>
                            </Border>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                  </TablixCells>
                </TablixRow>
              </TablixRows>
            </TablixBody>
            <TablixColumnHierarchy>
              <TablixMembers>
                <TablixMember />
                <TablixMember />
                <TablixMember />
                <TablixMember />
                <TablixMember />
                <TablixMember />
                <TablixMember />
              </TablixMembers>
            </TablixColumnHierarchy>
            <TablixRowHierarchy>
              <TablixMembers>
                <TablixMember>
                  <KeepWithGroup>After</KeepWithGroup>
                </TablixMember>
                <TablixMember>
                  <Group Name="Details" />
                </TablixMember>
              </TablixMembers>
            </TablixRowHierarchy>
            <DataSetName>CustomerTransactionsDataSet</DataSetName>
            <Top>0.1in</Top>
            <Left>0in</Left>
            <Height>0.55in</Height>
            <Width>7.5in</Width>
            <Style>
              <Border>
                <Style>Solid</Style>
                <Color>#BDC3C7</Color>
                <Width>1pt</Width>
              </Border>
            </Style>
          </Tablix>

          <!-- Summary Section -->
          <Rectangle Name="SummaryRectangle">
            <KeepTogether>true</KeepTogether>
            <ReportItems>
              <Textbox Name="SummaryTitle">
                <CanGrow>true</CanGrow>
                <KeepTogether>true</KeepTogether>
                <Paragraphs>
                  <Paragraph>
                    <TextRuns>
                      <TextRun>
                        <Value>ملخص الحساب</Value>
                        <Style>
                          <FontFamily>Arial</FontFamily>
                          <FontSize>14pt</FontSize>
                          <FontWeight>Bold</FontWeight>
                          <Color>#2C3E50</Color>
                        </Style>
                      </TextRun>
                    </TextRuns>
                    <Style>
                      <TextAlign>Center</TextAlign>
                    </Style>
                  </Paragraph>
                </Paragraphs>
                <rd:DefaultName>SummaryTitle</rd:DefaultName>
                <Top>0.1in</Top>
                <Left>0in</Left>
                <Height>0.3in</Height>
                <Width>8in</Width>
              </Textbox>

              <Textbox Name="OpeningBalanceLabel">
                <CanGrow>true</CanGrow>
                <KeepTogether>true</KeepTogether>
                <Paragraphs>
                  <Paragraph>
                    <TextRuns>
                      <TextRun>
                        <Value>الرصيد الافتتاحي: </Value>
                        <Style>
                          <FontFamily>Arial</FontFamily>
                          <FontSize>11pt</FontSize>
                          <FontWeight>Bold</FontWeight>
                          <Color>#2C3E50</Color>
                        </Style>
                      </TextRun>
                      <TextRun>
                        <Value>=First(Fields!FormattedOpeningBalance.Value, "CustomerAccountStatementDataSet")</Value>
                        <Style>
                          <FontFamily>Arial</FontFamily>
                          <FontSize>11pt</FontSize>
                          <Color>#34495E</Color>
                        </Style>
                      </TextRun>
                    </TextRuns>
                    <Style>
                      <TextAlign>Right</TextAlign>
                    </Style>
                  </Paragraph>
                </Paragraphs>
                <rd:DefaultName>OpeningBalanceLabel</rd:DefaultName>
                <Top>0.5in</Top>
                <Left>4.1in</Left>
                <Height>0.25in</Height>
                <Width>3.8in</Width>
              </Textbox>

              <Textbox Name="TotalSalesLabel">
                <CanGrow>true</CanGrow>
                <KeepTogether>true</KeepTogether>
                <Paragraphs>
                  <Paragraph>
                    <TextRuns>
                      <TextRun>
                        <Value>إجمالي المبيعات: </Value>
                        <Style>
                          <FontFamily>Arial</FontFamily>
                          <FontSize>11pt</FontSize>
                          <FontWeight>Bold</FontWeight>
                          <Color>#2C3E50</Color>
                        </Style>
                      </TextRun>
                      <TextRun>
                        <Value>=First(Fields!FormattedTotalSales.Value, "CustomerAccountStatementDataSet")</Value>
                        <Style>
                          <FontFamily>Arial</FontFamily>
                          <FontSize>11pt</FontSize>
                          <Color>#E74C3C</Color>
                        </Style>
                      </TextRun>
                    </TextRuns>
                    <Style>
                      <TextAlign>Right</TextAlign>
                    </Style>
                  </Paragraph>
                </Paragraphs>
                <rd:DefaultName>TotalSalesLabel</rd:DefaultName>
                <Top>0.8in</Top>
                <Left>4.1in</Left>
                <Height>0.25in</Height>
                <Width>3.8in</Width>
              </Textbox>

              <Textbox Name="TotalPaymentsLabel">
                <CanGrow>true</CanGrow>
                <KeepTogether>true</KeepTogether>
                <Paragraphs>
                  <Paragraph>
                    <TextRuns>
                      <TextRun>
                        <Value>إجمالي المدفوعات: </Value>
                        <Style>
                          <FontFamily>Arial</FontFamily>
                          <FontSize>11pt</FontSize>
                          <FontWeight>Bold</FontWeight>
                          <Color>#2C3E50</Color>
                        </Style>
                      </TextRun>
                      <TextRun>
                        <Value>=First(Fields!FormattedTotalPayments.Value, "CustomerAccountStatementDataSet")</Value>
                        <Style>
                          <FontFamily>Arial</FontFamily>
                          <FontSize>11pt</FontSize>
                          <Color>#27AE60</Color>
                        </Style>
                      </TextRun>
                    </TextRuns>
                    <Style>
                      <TextAlign>Right</TextAlign>
                    </Style>
                  </Paragraph>
                </Paragraphs>
                <rd:DefaultName>TotalPaymentsLabel</rd:DefaultName>
                <Top>1.1in</Top>
                <Left>4.1in</Left>
                <Height>0.25in</Height>
                <Width>3.8in</Width>
              </Textbox>

              <Textbox Name="ClosingBalanceLabel">
                <CanGrow>true</CanGrow>
                <KeepTogether>true</KeepTogether>
                <Paragraphs>
                  <Paragraph>
                    <TextRuns>
                      <TextRun>
                        <Value>الرصيد الختامي: </Value>
                        <Style>
                          <FontFamily>Arial</FontFamily>
                          <FontSize>12pt</FontSize>
                          <FontWeight>Bold</FontWeight>
                          <Color>#2C3E50</Color>
                        </Style>
                      </TextRun>
                      <TextRun>
                        <Value>=First(Fields!FormattedClosingBalance.Value, "CustomerAccountStatementDataSet")</Value>
                        <Style>
                          <FontFamily>Arial</FontFamily>
                          <FontSize>12pt</FontSize>
                          <FontWeight>Bold</FontWeight>
                          <Color>#8E44AD</Color>
                        </Style>
                      </TextRun>
                    </TextRuns>
                    <Style>
                      <TextAlign>Right</TextAlign>
                    </Style>
                  </Paragraph>
                </Paragraphs>
                <rd:DefaultName>ClosingBalanceLabel</rd:DefaultName>
                <Top>1.5in</Top>
                <Left>4.1in</Left>
                <Height>0.3in</Height>
                <Width>3.8in</Width>
                <Style>
                  <Border>
                    <Style>Solid</Style>
                    <Color>#8E44AD</Color>
                    <Width>2pt</Width>
                  </Border>
                  <BackgroundColor>#F4F1F8</BackgroundColor>
                  <PaddingLeft>5pt</PaddingLeft>
                  <PaddingRight>5pt</PaddingRight>
                  <PaddingTop>5pt</PaddingTop>
                  <PaddingBottom>5pt</PaddingBottom>
                </Style>
              </Textbox>
            </ReportItems>
            <Top>0.8in</Top>
            <Left>0in</Left>
            <Height>1.5in</Height>
            <Width>7.5in</Width>
            <Style>
              <Border>
                <Style>Solid</Style>
                <Color>#BDC3C7</Color>
                <Width>1pt</Width>
              </Border>
              <BackgroundColor>#FAFBFC</BackgroundColor>
            </Style>
          </Rectangle>
        </ReportItems>
        <Height>3.5in</Height>
        <Style />
      </Body>
      <Width>7.5in</Width>
      <Page>
        <PageHeader>
          <Height>1.4in</Height>
          <PrintOnFirstPage>true</PrintOnFirstPage>
          <PrintOnLastPage>true</PrintOnLastPage>
          <ReportItems>
            <!-- Company Name -->
            <Textbox Name="CompanyName">
              <CanGrow>true</CanGrow>
              <KeepTogether>true</KeepTogether>
              <Paragraphs>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value>=First(Fields!CompanyName.Value, "CompanyInfoDataSet")</Value>
                      <Style>
                        <FontFamily>Arial</FontFamily>
                        <FontSize>18pt</FontSize>
                        <FontWeight>Bold</FontWeight>
                        <Color>#2C3E50</Color>
                      </Style>
                    </TextRun>
                  </TextRuns>
                  <Style>
                    <TextAlign>Center</TextAlign>
                  </Style>
                </Paragraph>
              </Paragraphs>
              <rd:DefaultName>CompanyName</rd:DefaultName>
              <Top>0.05in</Top>
              <Left>0in</Left>
              <Height>0.25in</Height>
              <Width>7.5in</Width>
            </Textbox>

            <!-- Company Address and Phone -->
            <Textbox Name="CompanyInfo">
              <CanGrow>true</CanGrow>
              <KeepTogether>true</KeepTogether>
              <Paragraphs>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value>=First(Fields!CompanyAddress.Value, "CompanyInfoDataSet") + " - " + First(Fields!CompanyPhone.Value, "CompanyInfoDataSet")</Value>
                      <Style>
                        <FontFamily>Arial</FontFamily>
                        <FontSize>10pt</FontSize>
                        <Color>#34495E</Color>
                      </Style>
                    </TextRun>
                  </TextRuns>
                  <Style>
                    <TextAlign>Center</TextAlign>
                  </Style>
                </Paragraph>
              </Paragraphs>
              <rd:DefaultName>CompanyInfo</rd:DefaultName>
              <Top>0.3in</Top>
              <Left>0in</Left>
              <Height>0.15in</Height>
              <Width>7.5in</Width>
            </Textbox>

            <!-- Separator Line -->
            <Line Name="SeparatorLine">
              <Top>0.5in</Top>
              <Left>0in</Left>
              <Height>0in</Height>
              <Width>7.5in</Width>
              <Style>
                <Border>
                  <Style>Solid</Style>
                  <Color>#BDC3C7</Color>
                  <Width>2pt</Width>
                </Border>
              </Style>
            </Line>

            <!-- Header Title -->
            <Textbox Name="HeaderTitle">
              <CanGrow>true</CanGrow>
              <KeepTogether>true</KeepTogether>
              <Paragraphs>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value>كشف حساب عميل</Value>
                      <Style>
                        <FontFamily>Arial</FontFamily>
                        <FontSize>16pt</FontSize>
                        <FontWeight>Bold</FontWeight>
                        <Color>#2C3E50</Color>
                      </Style>
                    </TextRun>
                  </TextRuns>
                  <Style>
                    <TextAlign>Center</TextAlign>
                  </Style>
                </Paragraph>
              </Paragraphs>
              <rd:DefaultName>HeaderTitle</rd:DefaultName>
              <Top>0.6in</Top>
              <Left>0in</Left>
              <Height>0.25in</Height>
              <Width>7.5in</Width>
            </Textbox>

            <!-- Customer Name (Left) -->
            <Textbox Name="HeaderCustomerName">
              <CanGrow>true</CanGrow>
              <KeepTogether>true</KeepTogether>
              <Paragraphs>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value>اسم العميل: </Value>
                      <Style>
                        <FontFamily>Arial</FontFamily>
                        <FontSize>10pt</FontSize>
                        <FontWeight>Bold</FontWeight>
                        <Color>#2C3E50</Color>
                      </Style>
                    </TextRun>
                    <TextRun>
                      <Value>=First(Fields!CustomerName.Value, "CustomerAccountStatementDataSet")</Value>
                      <Style>
                        <FontFamily>Arial</FontFamily>
                        <FontSize>10pt</FontSize>
                        <Color>#34495E</Color>
                      </Style>
                    </TextRun>
                  </TextRuns>
                  <Style>
                    <TextAlign>Right</TextAlign>
                  </Style>
                </Paragraph>
              </Paragraphs>
              <rd:DefaultName>HeaderCustomerName</rd:DefaultName>
              <Top>0.9in</Top>
              <Left>0in</Left>
              <Height>0.2in</Height>
              <Width>2.4in</Width>
            </Textbox>

            <!-- Statement Date (Center) -->
            <Textbox Name="HeaderStatementDate">
              <CanGrow>true</CanGrow>
              <KeepTogether>true</KeepTogether>
              <Paragraphs>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value>تاريخ الكشف: </Value>
                      <Style>
                        <FontFamily>Arial</FontFamily>
                        <FontSize>10pt</FontSize>
                        <FontWeight>Bold</FontWeight>
                        <Color>#2C3E50</Color>
                      </Style>
                    </TextRun>
                    <TextRun>
                      <Value>=First(Fields!FormattedStatementDate.Value, "CustomerAccountStatementDataSet")</Value>
                      <Style>
                        <FontFamily>Arial</FontFamily>
                        <FontSize>10pt</FontSize>
                        <Color>#34495E</Color>
                      </Style>
                    </TextRun>
                  </TextRuns>
                  <Style>
                    <TextAlign>Center</TextAlign>
                  </Style>
                </Paragraph>
              </Paragraphs>
              <rd:DefaultName>HeaderStatementDate</rd:DefaultName>
              <Top>0.9in</Top>
              <Left>2.5in</Left>
              <Height>0.2in</Height>
              <Width>2.4in</Width>
            </Textbox>

            <!-- Period (Right) -->
            <Textbox Name="HeaderPeriod">
              <CanGrow>true</CanGrow>
              <KeepTogether>true</KeepTogether>
              <Paragraphs>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value>من </Value>
                      <Style>
                        <FontFamily>Arial</FontFamily>
                        <FontSize>9pt</FontSize>
                        <FontWeight>Bold</FontWeight>
                        <Color>#2C3E50</Color>
                      </Style>
                    </TextRun>
                    <TextRun>
                      <Value>=First(Fields!FormattedFromDate.Value, "CustomerAccountStatementDataSet")</Value>
                      <Style>
                        <FontFamily>Arial</FontFamily>
                        <FontSize>9pt</FontSize>
                        <Color>#34495E</Color>
                      </Style>
                    </TextRun>
                    <TextRun>
                      <Value> إلى </Value>
                      <Style>
                        <FontFamily>Arial</FontFamily>
                        <FontSize>9pt</FontSize>
                        <FontWeight>Bold</FontWeight>
                        <Color>#2C3E50</Color>
                      </Style>
                    </TextRun>
                    <TextRun>
                      <Value>=First(Fields!FormattedToDate.Value, "CustomerAccountStatementDataSet")</Value>
                      <Style>
                        <FontFamily>Arial</FontFamily>
                        <FontSize>9pt</FontSize>
                        <Color>#34495E</Color>
                      </Style>
                    </TextRun>
                  </TextRuns>
                  <Style>
                    <TextAlign>Left</TextAlign>
                  </Style>
                </Paragraph>
              </Paragraphs>
              <rd:DefaultName>HeaderPeriod</rd:DefaultName>
              <Top>0.9in</Top>
              <Left>5in</Left>
              <Height>0.2in</Height>
              <Width>2.4in</Width>
            </Textbox>
          </ReportItems>
        </PageHeader>
        <PageFooter>
          <Height>0.5in</Height>
          <PrintOnFirstPage>true</PrintOnFirstPage>
          <PrintOnLastPage>true</PrintOnLastPage>
          <ReportItems>
            <Textbox Name="PageNumber">
              <CanGrow>true</CanGrow>
              <KeepTogether>true</KeepTogether>
              <Paragraphs>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value>="صفحة " &amp; Globals!PageNumber &amp; " من " &amp; Globals!TotalPages</Value>
                      <Style>
                        <FontFamily>Arial</FontFamily>
                        <FontSize>10pt</FontSize>
                        <Color>#7F8C8D</Color>
                      </Style>
                    </TextRun>
                  </TextRuns>
                  <Style>
                    <TextAlign>Center</TextAlign>
                  </Style>
                </Paragraph>
              </Paragraphs>
              <rd:DefaultName>PageNumber</rd:DefaultName>
              <Top>0.1in</Top>
              <Left>0in</Left>
              <Height>0.25in</Height>
              <Width>7.5in</Width>
            </Textbox>
          </ReportItems>
        </PageFooter>
        <LeftMargin>0.25in</LeftMargin>
        <RightMargin>0.25in</RightMargin>
        <TopMargin>0.25in</TopMargin>
        <BottomMargin>0.25in</BottomMargin>
        <Style />
      </Page>
    </ReportSection>
  </ReportSections>

  <rd:ReportUnitType>Inch</rd:ReportUnitType>
  <rd:ReportID>b8c8c8c8-8c8c-8c8c-8c8c-8c8c8c8c8c8c</rd:ReportID>
</Report>
