using AutoMapper;
using ExactCash.Application.DTOs;
using ExactCash.Application.Interfaces;
using ExactCash.Application.Responses;
using ExactCash.Domain.Entities;
using ExactCash.Infrastructure.Persistence;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace ExactCash.Application.Services
{
    /// <summary>
    /// Service implementation for payment operations
    /// </summary>
    public class PaymentService : IPaymentService
    {
        private readonly AppPostgreSQLDbContext _context;
        private readonly IMapper _mapper;
        private readonly ILogger<PaymentService> _logger;

        public PaymentService(AppPostgreSQLDbContext context, IMapper mapper, ILogger<PaymentService> logger)
        {
            _context = context;
            _mapper = mapper;
            _logger = logger;
        }

        /// <summary>
        /// Collects a payment against outstanding sales
        /// </summary>
        public async Task<BaseResponse<int>> CollectPaymentAsync(PaymentCollectionDto paymentDto, string collectedBy)
        {
            using var transaction = await _context.Database.BeginTransactionAsync();
            try
            {
                // Validate input
                if (paymentDto == null)
                {
                    return ResponseHelper.Failure<int>(400, 0, "Payment data is required");
                }

                if (!paymentDto.IsValidPayment)
                {
                    return ResponseHelper.Failure<int>(400, 0, "Invalid payment amount");
                }

                // Get the sale
                var sale = await _context.Sales
                    .Include(s => s.Customer)
                    .FirstOrDefaultAsync(s => s.Id == paymentDto.SaleId);

                if (sale == null)
                {
                    return ResponseHelper.Failure<int>(404, 0, "Sale not found");
                }

                // Validate payment amount doesn't exceed remaining amount
                if (paymentDto.PaymentAmount > sale.RemainingAmount)
                {
                    return ResponseHelper.Failure<int>(400, 0, "Payment amount exceeds remaining balance");
                }

                // Create payment record
                var payment = new Payment
                {
                    SaleId = paymentDto.SaleId,
                    Amount = paymentDto.PaymentAmount,
                    PaymentMethod = paymentDto.PaymentMethod,
                    PaymentDate = paymentDto.PaymentDate.ToUniversalTime()
                };

                await _context.Payments.AddAsync(payment);

                // Update sale amounts
                sale.PaidAmount += paymentDto.PaymentAmount;
                sale.RemainingAmount -= paymentDto.PaymentAmount;
                sale.LastUpdatedDate = DateTime.UtcNow;
                _context.Entry(sale).State = EntityState.Modified;

                // Update customer's outstanding balance if it's a credit sale
                if (sale.Customer != null && sale.RemainingAmount >= 0)
                {
                    // Reduce customer's outstanding balance
                    var customer = sale.Customer;
                    customer.LastUpdatedDate = DateTime.UtcNow;
                    _context.Entry(customer).State = EntityState.Modified;
                }

                // Check if there's a credit transaction for this sale
                var creditTransaction = await _context.CreditSaleTransactions
                    .FirstOrDefaultAsync(ct => ct.SaleId == paymentDto.SaleId);

                if (creditTransaction != null)
                {
                    // Create credit payment record
                    var creditPayment = new CreditPayment
                    {
                        CreditSaleTransactionId = creditTransaction.Id,
                        Amount = paymentDto.PaymentAmount,
                        PaymentMethod = paymentDto.PaymentMethod,
                        PaymentDate = paymentDto.PaymentDate.ToUniversalTime(),
                        CollectedBy = collectedBy ?? "System",
                        Notes = paymentDto.Notes
                    };

                    await _context.CreditPayments.AddAsync(creditPayment);

                    // Update credit transaction
                    creditTransaction.PaidAmount += paymentDto.PaymentAmount;
                    creditTransaction.RemainingAmount -= paymentDto.PaymentAmount;
                    creditTransaction.LastUpdatedDate = DateTime.UtcNow;
                    _context.Entry(creditTransaction).State = EntityState.Modified;
                }

                await _context.SaveChangesAsync();
                await transaction.CommitAsync();

                _logger.LogInformation("Payment collected successfully. Payment ID: {PaymentId}, Amount: {Amount}",
                    payment.Id, paymentDto.PaymentAmount);

                return ResponseHelper.Success(200, payment.Id, "Payment collected successfully");
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                _logger.LogError(ex, "Error collecting payment for sale {SaleId}", paymentDto.SaleId);
                return ResponseHelper.Failure<int>(500, 0, $"Error collecting payment: {ex.Message}");
            }
        }

        /// <summary>
        /// Gets payment history for a customer
        /// </summary>
        public async Task<List<PaymentHistoryDto>> GetPaymentHistoryAsync(int customerId, DateTime? fromDate = null, DateTime? toDate = null)
        {
            try
            {
                var query = _context.Payments
                    .Include(p => p.Sale)
                    .Where(p => p.Sale.CustomerId == customerId);

                if (fromDate.HasValue)
                {
                    var fromDateUtc = fromDate.Value.Kind == DateTimeKind.Utc
                        ? fromDate.Value
                        : DateTime.SpecifyKind(fromDate.Value, DateTimeKind.Utc);
                    query = query.Where(p => p.PaymentDate >= fromDateUtc);
                }

                if (toDate.HasValue)
                {
                    var toDateUtc = toDate.Value.Kind == DateTimeKind.Utc
                        ? toDate.Value.AddDays(1)
                        : DateTime.SpecifyKind(toDate.Value.AddDays(1), DateTimeKind.Utc);
                    query = query.Where(p => p.PaymentDate <= toDateUtc);
                }

                var payments = await query
                    .OrderByDescending(p => p.PaymentDate)
                    .Select(p => new PaymentHistoryDto
                    {
                        PaymentId = p.Id,
                        SaleId = p.SaleId,
                        InvoiceNumber = p.Sale.InvoiceNumber,
                        PaymentAmount = p.Amount,
                        PaymentMethod = p.PaymentMethod,
                        PaymentDate = p.PaymentDate,
                        CollectedBy = "System", // You might want to add this field to Payment entity
                        Notes = "", // You might want to add this field to Payment entity
                        RemainingAmountAfterPayment = p.Sale.RemainingAmount
                    })
                    .ToListAsync();

                return payments;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting payment history for customer {CustomerId}", customerId);
                return new List<PaymentHistoryDto>();
            }
        }

        /// <summary>
        /// Gets payment history for a specific sale
        /// </summary>
        public async Task<List<PaymentHistoryDto>> GetSalePaymentHistoryAsync(int saleId)
        {
            try
            {
                var payments = await _context.Payments
                    .Include(p => p.Sale)
                    .Where(p => p.SaleId == saleId)
                    .OrderByDescending(p => p.PaymentDate)
                    .Select(p => new PaymentHistoryDto
                    {
                        PaymentId = p.Id,
                        SaleId = p.SaleId,
                        InvoiceNumber = p.Sale.InvoiceNumber,
                        PaymentAmount = p.Amount,
                        PaymentMethod = p.PaymentMethod,
                        PaymentDate = p.PaymentDate,
                        CollectedBy = "System",
                        Notes = "",
                        RemainingAmountAfterPayment = p.Sale.RemainingAmount
                    })
                    .ToListAsync();

                return payments;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting payment history for sale {SaleId}", saleId);
                return new List<PaymentHistoryDto>();
            }
        }

        /// <summary>
        /// Gets outstanding sales for a customer
        /// </summary>
        public async Task<List<OutstandingSaleDto>> GetOutstandingSalesForCustomerAsync(int customerId)
        {
            try
            {
                var outstandingSales = await _context.Sales
                    .Include(s => s.Customer)
                    .Where(s => s.CustomerId == customerId && s.RemainingAmount > 0)
                    .OrderByDescending(s => s.CreationDate)
                    .Select(s => new OutstandingSaleDto
                    {
                        Id = s.Id,
                        InvoiceNumber = s.InvoiceNumber,
                        CustomerId = s.CustomerId,
                        CustomerName = s.Customer.FullName,
                        CreationDate = s.CreationDate,
                        TotalAmount = s.TotalAmount,
                        PaidAmount = s.PaidAmount,
                        RemainingAmount = s.RemainingAmount,
                        DaysOverdue = (int)(DateTime.UtcNow - s.CreationDate).TotalDays
                    })
                    .ToListAsync();

                return outstandingSales;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting outstanding sales for customer {CustomerId}", customerId);
                return new List<OutstandingSaleDto>();
            }
        }

        /// <summary>
        /// Gets all outstanding sales with optional filters
        /// </summary>
        public async Task<List<OutstandingSaleDto>> GetOutstandingSalesAsync(CreditSalesSearchDto searchDto)
        {
            try
            {
                var query = _context.Sales
                    .Include(s => s.Customer)
                    .Where(s => s.RemainingAmount > 0);

                if (!string.IsNullOrEmpty(searchDto.InvoiceNumber))
                    query = query.Where(s => s.InvoiceNumber.Contains(searchDto.InvoiceNumber));

                if (!string.IsNullOrEmpty(searchDto.CustomerName))
                    query = query.Where(s => s.Customer.FullName.Contains(searchDto.CustomerName));

                if (searchDto.FromDate.HasValue)
                {
                    var fromDateUtc = searchDto.FromDate.Value.Kind == DateTimeKind.Utc
                        ? searchDto.FromDate.Value
                        : DateTime.SpecifyKind(searchDto.FromDate.Value, DateTimeKind.Utc);
                    query = query.Where(s => s.CreationDate >= fromDateUtc);
                }

                if (searchDto.ToDate.HasValue)
                {
                    var toDateUtc = searchDto.ToDate.Value.Kind == DateTimeKind.Utc
                        ? searchDto.ToDate.Value.AddDays(1)
                        : DateTime.SpecifyKind(searchDto.ToDate.Value.AddDays(1), DateTimeKind.Utc);
                    query = query.Where(s => s.CreationDate <= toDateUtc);
                }

                var outstandingSales = await query
                    .OrderByDescending(s => s.CreationDate)
                    .Select(s => new OutstandingSaleDto
                    {
                        Id = s.Id,
                        InvoiceNumber = s.InvoiceNumber,
                        CustomerId = s.CustomerId,
                        CustomerName = s.Customer.FullName,
                        CreationDate = s.CreationDate,
                        TotalAmount = s.TotalAmount,
                        PaidAmount = s.PaidAmount,
                        RemainingAmount = s.RemainingAmount,
                        DaysOverdue = (int)(DateTime.UtcNow - s.CreationDate).TotalDays
                    })
                    .ToListAsync();

                return outstandingSales;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting outstanding sales with search criteria");
                return new List<OutstandingSaleDto>();
            }
        }

        /// <summary>
        /// Gets customer credit limits and utilization
        /// </summary>
        public async Task<List<CustomerCreditLimitDto>> GetCustomerCreditLimitsAsync()
        {
            try
            {
                var customers = await _context.Customers
                    .Where(c => c.CreditLimit > 0)
                    .Select(c => new CustomerCreditLimitDto
                    {
                        CustomerId = c.Id,
                        CustomerName = c.FullName,
                        Phone = c.Phone,
                        Email = c.Email,
                        CreditLimit = c.CreditLimit,
                        OutstandingBalance = _context.Sales
                            .Where(s => s.CustomerId == c.Id)
                            .Sum(s => s.RemainingAmount),
                        AvailableCredit = c.CreditLimit - _context.Sales
                            .Where(s => s.CustomerId == c.Id)
                            .Sum(s => s.RemainingAmount),
                        CreditUtilization = c.CreditLimit > 0 ?
                            (double)(_context.Sales.Where(s => s.CustomerId == c.Id).Sum(s => s.RemainingAmount) / c.CreditLimit) : 0,
                        LastUpdated = c.LastUpdatedDate,
                        UpdatedBy = c.LastUpdatedBy ?? "System"
                    })
                    .ToListAsync();

                return customers;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting customer credit limits");
                return new List<CustomerCreditLimitDto>();
            }
        }

        /// <summary>
        /// Updates customer credit limit
        /// </summary>
        public async Task<BaseResponse<bool>> UpdateCreditLimitAsync(CreditLimitUpdateDto updateDto, string updatedBy)
        {
            try
            {
                var customer = await _context.Customers.FindAsync(updateDto.CustomerId);
                if (customer == null)
                {
                    return ResponseHelper.Failure<bool>(404, false, "Customer not found");
                }

                customer.CreditLimit = updateDto.NewCreditLimit;
                customer.LastUpdatedDate = DateTime.UtcNow;
                _context.Entry(customer).State = EntityState.Modified;

                await _context.SaveChangesAsync();

                _logger.LogInformation("Credit limit updated for customer {CustomerId} by {UpdatedBy}",
                    updateDto.CustomerId, updatedBy);

                return ResponseHelper.Success(200, true, "Credit limit updated successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating credit limit for customer {CustomerId}", updateDto.CustomerId);
                return ResponseHelper.Failure<bool>(500, false, $"Error updating credit limit: {ex.Message}");
            }
        }

        /// <summary>
        /// Gets outstanding payments report
        /// </summary>
        public async Task<OutstandingPaymentsReportDto> GetOutstandingPaymentsReportAsync(DateTime? fromDate, DateTime? toDate)
        {
            try
            {
                var query = _context.Sales
                    .Include(s => s.Customer)
                    .Where(s => s.RemainingAmount > 0);

                if (fromDate.HasValue)
                {
                    var fromDateUtc = fromDate.Value.Kind == DateTimeKind.Utc
                        ? fromDate.Value
                        : DateTime.SpecifyKind(fromDate.Value, DateTimeKind.Utc);
                    query = query.Where(s => s.CreationDate >= fromDateUtc);
                }

                if (toDate.HasValue)
                {
                    var toDateUtc = toDate.Value.Kind == DateTimeKind.Utc
                        ? toDate.Value.AddDays(1)
                        : DateTime.SpecifyKind(toDate.Value.AddDays(1), DateTimeKind.Utc);
                    query = query.Where(s => s.CreationDate <= toDateUtc);
                }

                var outstandingSales = await query.ToListAsync();

                var report = new OutstandingPaymentsReportDto
                {
                    ReportDate = DateTime.UtcNow,
                    FromDate = fromDate ?? DateTime.MinValue,
                    ToDate = toDate ?? DateTime.MaxValue,
                    TotalOutstandingAmount = outstandingSales.Sum(s => s.RemainingAmount),
                    TotalOutstandingInvoices = outstandingSales.Count,
                    OverdueAmount = outstandingSales
                        .Where(s => (DateTime.UtcNow - s.CreationDate).TotalDays > 30)
                        .Sum(s => s.RemainingAmount),
                    OverdueInvoices = outstandingSales
                        .Count(s => (DateTime.UtcNow - s.CreationDate).TotalDays > 30),
                    OutstandingSales = outstandingSales.Select(s => new OutstandingSaleDto
                    {
                        Id = s.Id,
                        InvoiceNumber = s.InvoiceNumber,
                        CustomerId = s.CustomerId,
                        CustomerName = s.Customer?.FullName ?? "Unknown",
                        CreationDate = s.CreationDate,
                        TotalAmount = s.TotalAmount,
                        PaidAmount = s.PaidAmount,
                        RemainingAmount = s.RemainingAmount,
                        DaysOverdue = (int)(DateTime.UtcNow - s.CreationDate).TotalDays
                    }).ToList()
                };

                return report;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating outstanding payments report");
                return new OutstandingPaymentsReportDto
                {
                    ReportDate = DateTime.UtcNow,
                    FromDate = fromDate ?? DateTime.MinValue,
                    ToDate = toDate ?? DateTime.MaxValue,
                    OutstandingSales = new List<OutstandingSaleDto>()
                };
            }
        }

        /// <summary>
        /// Validates if a customer can make a credit purchase
        /// </summary>
        public async Task<CreditValidationDto> ValidateCreditPurchaseAsync(int customerId, decimal amount)
        {
            try
            {
                var customer = await _context.Customers.FindAsync(customerId);
                if (customer == null)
                {
                    return new CreditValidationDto
                    {
                        IsValid = false,
                        Message = "Customer not found",
                        CreditLimit = 0,
                        OutstandingBalance = 0,
                        AvailableCredit = 0,
                        RequestedAmount = amount,
                        RemainingCreditAfterPurchase = 0
                    };
                }

                var outstandingBalance = await _context.Sales
                    .Where(s => s.CustomerId == customerId)
                    .SumAsync(s => s.RemainingAmount);

                var availableCredit = customer.CreditLimit - outstandingBalance;
                var remainingAfterPurchase = availableCredit - amount;

                return new CreditValidationDto
                {
                    IsValid = remainingAfterPurchase >= 0,
                    Message = remainingAfterPurchase >= 0 ? "Credit purchase approved" : "Insufficient credit limit",
                    CreditLimit = customer.CreditLimit,
                    OutstandingBalance = outstandingBalance,
                    AvailableCredit = availableCredit,
                    RequestedAmount = amount,
                    RemainingCreditAfterPurchase = remainingAfterPurchase
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error validating credit purchase for customer {CustomerId}", customerId);
                return new CreditValidationDto
                {
                    IsValid = false,
                    Message = "Error validating credit",
                    RequestedAmount = amount
                };
            }
        }

        /// <summary>
        /// Gets payment methods available in the system
        /// </summary>
        public async Task<List<string>> GetPaymentMethodsAsync()
        {
            // This could be configurable from database in the future
            await Task.CompletedTask; // To make it async

            return new List<string>
            {
                "نقدي",
                "بطاقة ائتمان",
                "بطاقة خصم",
                "تحويل بنكي",
                "شيك",
                "محفظة إلكترونية"
            };
        }

        /// <summary>
        /// Generates payment receipt
        /// </summary>
        public async Task<PaymentReceiptDto> GeneratePaymentReceiptAsync(int paymentId)
        {
            try
            {
                var payment = await _context.Payments
                    .Include(p => p.Sale)
                    .ThenInclude(s => s.Customer)
                    .FirstOrDefaultAsync(p => p.Id == paymentId);

                if (payment == null)
                {
                    return new PaymentReceiptDto();
                }

                // Get company information from system configurations
                var companyName = await _context.systemConfigurations
                    .Where(sc => sc.SettingName == "CompanyName")
                    .Select(sc => sc.SettingValue)
                    .FirstOrDefaultAsync() ?? "شركة الكاش الدقيق";

                var companyAddress = await _context.systemConfigurations
                    .Where(sc => sc.SettingName == "CompanyAddress")
                    .Select(sc => sc.SettingValue)
                    .FirstOrDefaultAsync() ?? "";

                var companyPhone = await _context.systemConfigurations
                    .Where(sc => sc.SettingName == "CompanyPhone")
                    .Select(sc => sc.SettingValue)
                    .FirstOrDefaultAsync() ?? "";

                var receipt = new PaymentReceiptDto
                {
                    PaymentId = payment.Id,
                    ReceiptNumber = $"REC-{payment.Id:D6}",
                    PaymentDate = payment.PaymentDate,
                    CustomerName = payment.Sale.Customer?.FullName ?? "Unknown",
                    CustomerPhone = payment.Sale.Customer?.Phone ?? "",
                    PaymentAmount = payment.Amount,
                    PaymentMethod = payment.PaymentMethod,
                    CollectedBy = "System", // You might want to add this field
                    Notes = "", // You might want to add this field
                    CompanyName = companyName,
                    CompanyAddress = companyAddress,
                    CompanyPhone = companyPhone,
                    PaidInvoices = new List<PaymentReceiptItemDto>
                    {
                        new PaymentReceiptItemDto
                        {
                            InvoiceNumber = payment.Sale.InvoiceNumber,
                            InvoiceDate = payment.Sale.CreationDate,
                            InvoiceAmount = payment.Sale.TotalAmount,
                            PreviouslyPaid = payment.Sale.PaidAmount - payment.Amount,
                            PaymentAmount = payment.Amount,
                            RemainingAmount = payment.Sale.RemainingAmount
                        }
                    }
                };

                return receipt;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating payment receipt for payment {PaymentId}", paymentId);
                return new PaymentReceiptDto();
            }
        }

        /// <summary>
        /// Gets credit payment history from CreditPayment table
        /// </summary>
        public async Task<List<CreditPaymentHistoryDto>> GetCreditPaymentHistoryAsync(CreditPaymentSearchDto searchDto)
        {
            try
            {
                var query = _context.CreditPayments
                    .Include(cp => cp.CreditSaleTransaction)
                    .ThenInclude(cst => cst.Customer)
                    .AsQueryable();

                // Apply filters
                if (searchDto.FromDate.HasValue)
                {
                    var fromDateUtc = searchDto.FromDate.Value.Kind == DateTimeKind.Utc
                        ? searchDto.FromDate.Value
                        : DateTime.SpecifyKind(searchDto.FromDate.Value, DateTimeKind.Utc);
                    query = query.Where(cp => cp.PaymentDate >= fromDateUtc);
                }

                if (searchDto.ToDate.HasValue)
                {
                    var toDateUtc = searchDto.ToDate.Value.Kind == DateTimeKind.Utc
                        ? searchDto.ToDate.Value.AddDays(1)
                        : DateTime.SpecifyKind(searchDto.ToDate.Value.AddDays(1), DateTimeKind.Utc);
                    query = query.Where(cp => cp.PaymentDate <= toDateUtc);
                }

                if (!string.IsNullOrEmpty(searchDto.CustomerName))
                {
                    query = query.Where(cp => cp.CreditSaleTransaction.Customer.FullName.Contains(searchDto.CustomerName));
                }

                if (!string.IsNullOrEmpty(searchDto.PaymentMethod))
                {
                    query = query.Where(cp => cp.PaymentMethod.Contains(searchDto.PaymentMethod));
                }

                if (!string.IsNullOrEmpty(searchDto.ReferenceNumber))
                {
                    query = query.Where(cp => cp.ReferenceNumber.Contains(searchDto.ReferenceNumber));
                }

                var creditPayments = await query
                    .OrderByDescending(cp => cp.PaymentDate)
                    .Select(cp => new CreditPaymentHistoryDto
                    {
                        PaymentId = cp.Id,
                        CreditSaleTransactionId = cp.CreditSaleTransactionId,
                        CustomerName = cp.CreditSaleTransaction.Customer.FullName,
                        PaymentAmount = cp.Amount,
                        PaymentMethod = cp.PaymentMethod,
                        PaymentDate = cp.PaymentDate,
                        ReferenceNumber = cp.ReferenceNumber ?? "",
                        CollectedBy = cp.CollectedBy ?? "System",
                        Notes = cp.Notes ?? "",
                        RemainingAmountAfterPayment = cp.CreditSaleTransaction.RemainingAmount
                    })
                    .ToListAsync();

                return creditPayments;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting credit payment history");
                return new List<CreditPaymentHistoryDto>();
            }
        }
    }
}
