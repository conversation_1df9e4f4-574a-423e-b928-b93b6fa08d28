using ExactCash.API.Seeds;
using ExactCash.Application;
using ExactCash.Application.Interfaces;
using ExactCash.Infrastructure;
using ExactCash.Infrastructure.Persistence;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Identity;
using Microsoft.IdentityModel.Tokens;
using System.Text;
using Microsoft.EntityFrameworkCore;

var builder = WebApplication.CreateBuilder(args);
var configuration = builder.Configuration;
// Add services to the container.

builder.Services.AddControllers();
// Learn more about configuring Swagger/OpenAPI at https://aka.ms/aspnetcore/swashbuckle
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen();

InfrastructureServiceRegistration.AddInfrastructureServices(builder.Services, configuration);
ApplicationServiceRegistration.AddApplicationServices(builder.Services);

builder.Services.AddIdentity<IdentityUser, IdentityRole>(options =>
{
    // Configure password and email settings
    options.User.RequireUniqueEmail = true;
    options.Password.RequiredLength = 6;
    options.Password.RequireDigit = false;
    options.Password.RequireLowercase = false;
    options.Password.RequireUppercase = false;
    options.Password.RequireNonAlphanumeric = false;
})
.AddEntityFrameworkStores<AppPostgreSQLDbContext>()  // Use EF Core to store user data
.AddDefaultTokenProviders();  // Default token providers (like for email confirmation, password reset, etc.)


#pragma warning disable
builder.Services.AddAuthentication(options =>
{
    options.DefaultAuthenticateScheme = JwtBearerDefaults.AuthenticationScheme;
    options.DefaultChallengeScheme = JwtBearerDefaults.AuthenticationScheme;
})
.AddJwtBearer(options =>
{
    options.TokenValidationParameters = new TokenValidationParameters
    {
        ValidateIssuerSigningKey = true,
        IssuerSigningKey = new SymmetricSecurityKey(Encoding.ASCII.GetBytes(builder.Configuration["JWT:Key"])),
        ValidateIssuer = true,
        ValidateAudience = true,
        ValidIssuer = builder.Configuration["JWT:Issuer"],
        ValidAudience = builder.Configuration["JWT:Audience"],
        ValidateLifetime = true,
        ClockSkew = TimeSpan.Zero
    };
});

var app = builder.Build();

// Run migrations at startup
using (var scope = app.Services.CreateScope())
{
    var db = scope.ServiceProvider.GetRequiredService<ExactCash.Infrastructure.Persistence.AppPostgreSQLDbContext>();
    db.Database.Migrate();
}

// Seed default data
using (var scope = app.Services.CreateScope())
{
    var services = scope.ServiceProvider;
    var logger = services.GetRequiredService<ILogger<Program>>();

    try
    {
        // Seed brands
        var brandService = services.GetRequiredService<IBrandService>();
        var context = services.GetRequiredService<AppPostgreSQLDbContext>();
        await DefaultBrands.SeedAsync(brandService, context, logger);

        // Seed roles
        await DefaultRoles.SeedAsync(
            services.GetRequiredService<RoleManager<IdentityRole>>(),
            logger);

        // Seed users
        await DefaultUsers.SeedAsync(
            services.GetRequiredService<UserManager<IdentityUser>>(),
            services.GetRequiredService<RoleManager<IdentityRole>>(),
            logger);

        // Seed role claims
        await DefaultRoleClaims.SeedAsync(
            services.GetRequiredService<RoleManager<IdentityRole>>(),
            logger);

        // Seed system configurations
        await DefaultSystemConfigurations.SeedAsync(context, logger);

        // Load currency configuration
        var systemConfigService = services.GetRequiredService<ISystemConfigurationService>();
        await systemConfigService.LoadCurrencyConfigurationAsync();

        logger.LogInformation("Default data seeded successfully");
    }
    catch (Exception ex)
    {
        logger.LogError(ex, "An error occurred while seeding default data");
    }
}

// Configure the HTTP request pipeline.
if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI();
}

app.UseHttpsRedirection();

app.UseAuthentication();
app.UseAuthorization();

app.MapControllers();

app.Run();
