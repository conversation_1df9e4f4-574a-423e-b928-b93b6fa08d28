﻿<Window x:Class="ExactCash.WPF.Views.ReportsWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:sys="clr-namespace:System;assembly=mscorlib"
        mc:Ignorable="d"
        Title="التقارير - ExactCash POS"
        Height="800"
        Width="1200"
        WindowStartupLocation="CenterScreen"
        FlowDirection="RightToLeft"
        FontFamily="Segoe UI"
        FontSize="14">

        <Window.Resources>
                <Style x:Key="ReportButtonStyle"
                       TargetType="Button">
                        <Setter Property="Background"
                                Value="#2196F3"/>
                        <Setter Property="Foreground"
                                Value="White"/>
                        <Setter Property="BorderThickness"
                                Value="0"/>
                        <Setter Property="Padding"
                                Value="15,10"/>
                        <Setter Property="Margin"
                                Value="5"/>
                        <Setter Property="FontSize"
                                Value="14"/>
                        <Setter Property="FontWeight"
                                Value="SemiBold"/>
                        <Setter Property="Cursor"
                                Value="Hand"/>
                        <Setter Property="Template">
                                <Setter.Value>
                                        <ControlTemplate TargetType="Button">
                                                <Border Background="{TemplateBinding Background}"
                                                        CornerRadius="5"
                                                        BorderThickness="{TemplateBinding BorderThickness}"
                                                        BorderBrush="{TemplateBinding BorderBrush}">
                                                        <ContentPresenter HorizontalAlignment="Center"
                                                                          VerticalAlignment="Center"/>
                                                </Border>
                                                <ControlTemplate.Triggers>
                                                        <Trigger Property="IsMouseOver"
                                                                 Value="True">
                                                                <Setter Property="Background"
                                                                        Value="#1976D2"/>
                                                        </Trigger>
                                                        <Trigger Property="IsPressed"
                                                                 Value="True">
                                                                <Setter Property="Background"
                                                                        Value="#0D47A1"/>
                                                        </Trigger>
                                                </ControlTemplate.Triggers>
                                        </ControlTemplate>
                                </Setter.Value>
                        </Setter>
                </Style>

                <Style x:Key="CategoryHeaderStyle"
                       TargetType="TextBlock">
                        <Setter Property="FontSize"
                                Value="18"/>
                        <Setter Property="FontWeight"
                                Value="Bold"/>
                        <Setter Property="Foreground"
                                Value="#333"/>
                        <Setter Property="Margin"
                                Value="0,10,0,10"/>
                </Style>
        </Window.Resources>

        <Grid>
                <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                </Grid.RowDefinitions>

                <!-- Header -->
                <Border Grid.Row="0"
                        Background="#1976D2"
                        Padding="20">
                        <StackPanel Orientation="Horizontal">
                                <TextBlock Text="📊"
                                           FontSize="24"
                                           Margin="0,0,10,0"/>
                                <TextBlock Text="التقارير والإحصائيات"
                                           FontSize="24"
                                           FontWeight="Bold"
                                           Foreground="White"/>
                        </StackPanel>
                </Border>

                <!-- Main Content -->
                <ScrollViewer Grid.Row="1"
                              VerticalScrollBarVisibility="Auto">
                        <StackPanel Margin="20">

                                <!-- Inventory Reports -->
                                <TextBlock Text="📦 تقارير المخزون"
                                           Style="{StaticResource CategoryHeaderStyle}"/>
                                <WrapPanel Orientation="Horizontal"
                                           Margin="0,0,0,20">
                                        <Button Content="تقرير المخزون الحالي"
                                                Style="{StaticResource ReportButtonStyle}"
                                                Click="InventoryReport_Click"/>
                                        <Button Content="تقرير المخزون المنخفض"
                                                Style="{StaticResource ReportButtonStyle}"
                                                Click="LowStockReport_Click"/>
                                        <Button Content="تقرير حركة المخزون"
                                                Style="{StaticResource ReportButtonStyle}"
                                                Click="StockMovementReport_Click"/>
                                        <Button Content="ملخص المخزون"
                                                Style="{StaticResource ReportButtonStyle}"
                                                Click="InventorySummary_Click"/>
                                </WrapPanel>

                                <!-- Financial Reports -->
                                <TextBlock Text="💰 التقارير المالية"
                                           Style="{StaticResource CategoryHeaderStyle}"/>
                                <WrapPanel Orientation="Horizontal"
                                           Margin="0,0,0,20">
                                        <Button Content="تقرير الأرباح والخسائر"
                                                Style="{StaticResource ReportButtonStyle}"
                                                Click="ProfitLossReport_Click"/>
                                        <Button Content="تقرير التدفق النقدي"
                                                Style="{StaticResource ReportButtonStyle}"
                                                Click="CashFlowReport_Click"/>
                                        <Button Content="تقرير طرق الدفع"
                                                Style="{StaticResource ReportButtonStyle}"
                                                Click="PaymentMethodReport_Click"/>
                                        <Button Content="تقرير الضرائب"
                                                Style="{StaticResource ReportButtonStyle}"
                                                Click="TaxReport_Click"/>
                                        <Button Content="تقرير المصروفات"
                                                Style="{StaticResource ReportButtonStyle}"
                                                Click="ExpenseReport_Click"/>
                                </WrapPanel>

                                <!-- Customer Reports -->
                                <TextBlock Text="👥 تقارير العملاء"
                                           Style="{StaticResource CategoryHeaderStyle}"/>
                                <WrapPanel Orientation="Horizontal"
                                           Margin="0,0,0,20">
                                        <Button Content="تحليل العملاء"
                                                Style="{StaticResource ReportButtonStyle}"
                                                Click="CustomerAnalysisReport_Click"/>
                                        <Button Content="أفضل العملاء"
                                                Style="{StaticResource ReportButtonStyle}"
                                                Click="TopCustomersReport_Click"/>
                                        <Button Content="تاريخ مشتريات العميل"
                                                Style="{StaticResource ReportButtonStyle}"
                                                Click="CustomerPurchaseHistory_Click"/>
                                </WrapPanel>

                                <!-- Performance Reports -->
                                <TextBlock Text="📈 تقارير الأداء"
                                           Style="{StaticResource CategoryHeaderStyle}"/>
                                <WrapPanel Orientation="Horizontal"
                                           Margin="0,0,0,20">
                                        <Button Content="مبيعات كل ساعة"
                                                Style="{StaticResource ReportButtonStyle}"
                                                Click="HourlySalesReport_Click"/>
                                        <Button Content="أداء الكاشيرين"
                                                Style="{StaticResource ReportButtonStyle}"
                                                Click="CashierPerformanceReport_Click"/>
                                        <Button Content="أداء المنتجات"
                                                Style="{StaticResource ReportButtonStyle}"
                                                Click="ProductPerformanceReport_Click"/>
                                </WrapPanel>

                                <!-- Supplier Reports -->
                                <TextBlock Text="🏪 تقارير الموردين"
                                           Style="{StaticResource CategoryHeaderStyle}"/>
                                <WrapPanel Orientation="Horizontal"
                                           Margin="0,0,0,20">
                                        <Button Content="أداء الموردين"
                                                Style="{StaticResource ReportButtonStyle}"
                                                Click="SupplierPerformanceReport_Click"/>
                                        <Button Content="حالة مدفوعات الموردين"
                                                Style="{StaticResource ReportButtonStyle}"
                                                Click="SupplierPaymentStatusReport_Click"/>
                                </WrapPanel>

                                <!-- Audit Reports -->
                                <TextBlock Text="🔍 تقارير المراجعة"
                                           Style="{StaticResource CategoryHeaderStyle}"/>
                                <WrapPanel Orientation="Horizontal"
                                           Margin="0,0,0,20">
                                        <Button Content="سجل المعاملات"
                                                Style="{StaticResource ReportButtonStyle}"
                                                Click="TransactionLogReport_Click"/>
                                        <Button Content="نشاط النظام"
                                                Style="{StaticResource ReportButtonStyle}"
                                                Click="SystemActivityReport_Click"/>
                                </WrapPanel>

                                <!-- Date Range Selection -->
                                <Border Background="#F5F5F5"
                                        CornerRadius="5"
                                        Padding="15"
                                        Margin="0,20,0,0">
                                        <StackPanel>
                                                <TextBlock Text="📅 تحديد فترة التقرير"
                                                           FontWeight="Bold"
                                                           FontSize="16"
                                                           Margin="0,0,0,10"/>
                                                <StackPanel Orientation="Horizontal">
                                                        <TextBlock Text="من تاريخ:"
                                                                   VerticalAlignment="Center"
                                                                   Margin="0,0,10,0"/>
                                                        <DatePicker x:Name="StartDatePicker"
                                                                    SelectedDate="{x:Static sys:DateTime.Today}"
                                                                    Margin="0,0,20,0"/>
                                                        <TextBlock Text="إلى تاريخ:"
                                                                   VerticalAlignment="Center"
                                                                   Margin="0,0,10,0"/>
                                                        <DatePicker x:Name="EndDatePicker"
                                                                    SelectedDate="{x:Static sys:DateTime.Today}"
                                                                    Margin="0,0,20,0"/>
                                                        <Button Content="تطبيق الفترة"
                                                                Style="{StaticResource ReportButtonStyle}"
                                                                Background="#4CAF50"
                                                                Click="ApplyDateRange_Click"/>
                                                </StackPanel>
                                        </StackPanel>
                                </Border>

                        </StackPanel>
                </ScrollViewer>
        </Grid>
</Window>
