using AutoMapper;
using ExactCash.Application.DTOs;
using ExactCash.Application.Helpers;
using ExactCash.Application.Interfaces;
using ExactCash.Application.Responses;
using ExactCash.Domain.Entities;
using ExactCash.Infrastructure.Persistence;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace ExactCash.Application.Services
{
    /// <summary>
    /// Service implementation for managing supplier categories.
    /// </summary>
    public class SupplierCategoryService : ISupplierCategoryService
    {
        private readonly AppPostgreSQLDbContext _context;
        private readonly IMapper _mapper;
        private readonly ILogger<SupplierCategoryService> _logger;

        public SupplierCategoryService(
            AppPostgreSQLDbContext context,
            IMapper mapper,
            ILogger<SupplierCategoryService> logger)
        {
            _context = context;
            _mapper = mapper;
            _logger = logger;
        }

        public async Task<SupplierCategoryDto> GetByIdAsync(int id)
        {
            try
            {
                var category = await _context.SupplierCategories
                    .Where(c => c.Id == id && !c.IsDeleted)
                    .FirstOrDefaultAsync();

                return category != null ? _mapper.Map<SupplierCategoryDto>(category) : null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting supplier category by ID {Id}", id);
                throw;
            }
        }

        public async Task<List<SupplierCategoryDto>> GetAllAsync()
        {
            try
            {
                var categories = await _context.SupplierCategories
                    .Where(c => !c.IsDeleted)
                    .OrderBy(c => c.Name)
                    .ToListAsync();

                return _mapper.Map<List<SupplierCategoryDto>>(categories);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting all supplier categories");
                throw;
            }
        }

        public async Task<List<SupplierCategoryDto>> GetActiveAsync()
        {
            try
            {
                var categories = await _context.SupplierCategories
                    .Where(c => c.IsActive && !c.IsDeleted)
                    .OrderBy(c => c.Name)
                    .ToListAsync();

                return _mapper.Map<List<SupplierCategoryDto>>(categories);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting active supplier categories");
                throw;
            }
        }

        public async Task<BaseResponse<SupplierCategoryDto>> CreateAsync(SupplierCategoryDto categoryDto)
        {
            try
            {
                // Check if category with same name already exists
                var existingCategory = await _context.SupplierCategories
                    .Where(c => c.Name.ToLower() == categoryDto.Name.ToLower() && !c.IsDeleted)
                    .FirstOrDefaultAsync();

                if (existingCategory != null)
                {
                    return ResponseHelper.Failure<SupplierCategoryDto>(
                        StatusCodes.Status400BadRequest, categoryDto,
                        "يوجد تصنيف مورد بنفس الاسم مسبقاً");
                }

                var category = new SupplierCategory
                {
                    Name = categoryDto.Name,
                    Description = categoryDto.Description,
                    IsActive = categoryDto.IsActive,
                    CreationDate = DateTime.UtcNow
                };

                await _context.SupplierCategories.AddAsync(category);
                await _context.SaveChangesAsync();

                var result = _mapper.Map<SupplierCategoryDto>(category);
                return ResponseHelper.Success(StatusCodes.Status201Created, result, "تم إنشاء تصنيف المورد بنجاح");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating supplier category");
                return ResponseHelper.Failure<SupplierCategoryDto>(
                    StatusCodes.Status500InternalServerError, categoryDto,
                    "حدث خطأ أثناء إنشاء تصنيف المورد");
            }
        }

        public async Task<BaseResponse<SupplierCategoryDto>> UpdateAsync(SupplierCategoryDto categoryDto)
        {
            try
            {
                var category = await _context.SupplierCategories
                    .Where(c => c.Id == categoryDto.Id && !c.IsDeleted)
                    .FirstOrDefaultAsync();

                if (category == null)
                {
                    return ResponseHelper.Failure<SupplierCategoryDto>(
                        StatusCodes.Status404NotFound, categoryDto,
                        "تصنيف المورد غير موجود");
                }

                // Check if another category with same name exists
                var existingCategory = await _context.SupplierCategories
                    .Where(c => c.Name.ToLower() == categoryDto.Name.ToLower() &&
                               c.Id != categoryDto.Id && !c.IsDeleted)
                    .FirstOrDefaultAsync();

                if (existingCategory != null)
                {
                    return ResponseHelper.Failure<SupplierCategoryDto>(
                        StatusCodes.Status400BadRequest, categoryDto,
                        "يوجد تصنيف مورد بنفس الاسم مسبقاً");
                }

                category.Name = categoryDto.Name;
                category.Description = categoryDto.Description;
                category.IsActive = categoryDto.IsActive;
                category.LastUpdatedDate = DateTime.UtcNow;

                _context.Entry(category).State = EntityState.Modified;
                await _context.SaveChangesAsync();

                var result = _mapper.Map<SupplierCategoryDto>(category);
                return ResponseHelper.Success(StatusCodes.Status200OK, result, "تم تحديث تصنيف المورد بنجاح");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating supplier category with ID {Id}", categoryDto.Id);
                return ResponseHelper.Failure<SupplierCategoryDto>(
                    StatusCodes.Status500InternalServerError, categoryDto,
                    "حدث خطأ أثناء تحديث تصنيف المورد");
            }
        }

        public async Task DeleteAsync(int id)
        {
            try
            {
                var category = await _context.SupplierCategories.FindAsync(id);
                if (category != null)
                {
                    _context.SupplierCategories.Remove(category);
                    await _context.SaveChangesAsync();
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting supplier category with ID {Id}", id);
                throw;
            }
        }

        public async Task SoftDeleteAsync(int id)
        {
            try
            {
                var category = await _context.SupplierCategories.FindAsync(id);
                if (category != null)
                {
                    category.IsDeleted = true;
                    category.LastUpdatedDate = DateTime.UtcNow;
                    _context.Entry(category).State = EntityState.Modified;
                    await _context.SaveChangesAsync();
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error soft deleting supplier category with ID {Id}", id);
                throw;
            }
        }
    }
}
