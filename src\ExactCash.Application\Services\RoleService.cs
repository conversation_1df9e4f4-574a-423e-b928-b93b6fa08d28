using AutoMapper;
using ExactCash.Application.DTOs;
using ExactCash.Application.DTOs.Common;
using ExactCash.Application.Enums;
using ExactCash.Application.Interfaces;
using ExactCash.Application.Responses;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
#nullable disable

namespace ExactCash.Application.Services
{
    public class RoleService : IRoleService
    {
        private readonly RoleManager<IdentityRole> _roleManager;
        private readonly UserManager<IdentityUser> _userManager;
        private readonly IMapper _mapper;
        private readonly ILogger<RoleService> _logger;

        public RoleService(
            RoleManager<IdentityRole> roleManager,
            UserManager<IdentityUser> userManager,
            IMapper mapper,
            ILogger<RoleService> logger)
        {
            _roleManager = roleManager;
            _userManager = userManager;
            _mapper = mapper;
            _logger = logger;
        }

        public async Task<RoleDto> GetRoleByIdAsync(string id)
        {
            var role = await _roleManager.FindByIdAsync(id);
            if (role == null)
                return null;

            return _mapper.Map<RoleDto>(role);
        }

        public async Task<PagedResponse<RoleDto>> GetAllRolesAsync(PaginationFilter pagination)
        {
            var query = _roleManager.Roles.AsQueryable();

            var totalRecords = await query.CountAsync();

            query = pagination.SortField switch
            {
                nameof(IdentityRole.Name) => pagination.SortOrder == SortOrder.Asc ?
                    query.OrderBy(r => r.Name) : query.OrderByDescending(r => r.Name),
                _ => pagination.SortOrder == SortOrder.Asc ?
                    query.OrderBy(r => r.Name) : query.OrderByDescending(r => r.Name)
            };

            var roles = await query
                .Skip((pagination.PageNumber - 1) * pagination.PageSize)
                .Take(pagination.PageSize)
                .ToListAsync();

            return PaginationHelper.CreatePagedResponse(
                _mapper.Map<List<RoleDto>>(roles),
                pagination.PageNumber,
                pagination.PageSize,
                totalRecords);
        }

        public async Task<List<RoleDto>> GetAllRolesAsync()
        {
            var query = _roleManager.Roles.AsQueryable();

            var roles = await query
                .ToListAsync();

            return _mapper.Map<List<RoleDto>>(roles);
        }

        public async Task<RoleDto> CreateRoleAsync(CreateRoleDto roleDto)
        {
            var role = new IdentityRole
            {
                Name = roleDto.Name
            };

            var result = await _roleManager.CreateAsync(role);
            if (!result.Succeeded)
            {
                _logger.LogError("Failed to create role: {Errors}",
                    string.Join(", ", result.Errors.Select(e => e.Description)));
                return null;
            }

            _logger.LogInformation("Role created successfully: {RoleId}", role.Id);
            return _mapper.Map<RoleDto>(role);
        }

        public async Task<RoleDto> UpdateRoleAsync(string id, UpdateRoleDto roleDto)
        {
            var role = await _roleManager.FindByIdAsync(id);
            if (role == null)
                return null;

            role.Name = roleDto.Name;

            var result = await _roleManager.UpdateAsync(role);
            if (!result.Succeeded)
            {
                _logger.LogError("Failed to update role: {Errors}",
                    string.Join(", ", result.Errors.Select(e => e.Description)));
                return null;
            }

            _logger.LogInformation("Role updated successfully: {RoleId}", role.Id);
            return _mapper.Map<RoleDto>(role);
        }

        public async Task<bool> DeleteRoleAsync(string id)
        {
            var role = await _roleManager.FindByIdAsync(id);
            if (role == null)
                return false;

            // Check if role has users
            var usersInRole = await _userManager.GetUsersInRoleAsync(role.Name);
            if (usersInRole.Any())
            {
                _logger.LogWarning("Cannot delete role {RoleId} because it has users assigned", role.Id);
                return false;
            }

            var result = await _roleManager.DeleteAsync(role);
            if (!result.Succeeded)
            {
                _logger.LogError("Failed to delete role: {Errors}",
                    string.Join(", ", result.Errors.Select(e => e.Description)));
                return false;
            }

            _logger.LogInformation("Role deleted successfully: {RoleId}", role.Id);
            return true;
        }

        public async Task<bool> AssignUsersToRoleAsync(RoleUsersDto roleUsersDto)
        {
            var role = await _roleManager.FindByIdAsync(roleUsersDto.RoleId);
            if (role == null)
                return false;

            foreach (var userId in roleUsersDto.UserIds)
            {
                var user = await _userManager.FindByIdAsync(userId);
                if (user == null)
                    continue;

                if (!await _userManager.IsInRoleAsync(user, role.Name))
                {
                    var result = await _userManager.AddToRoleAsync(user, role.Name);
                    if (!result.Succeeded)
                    {
                        _logger.LogError("Failed to assign user {UserId} to role {RoleId}: {Errors}",
                            userId, role.Id, string.Join(", ", result.Errors.Select(e => e.Description)));
                        return false;
                    }
                }
            }

            _logger.LogInformation("Users assigned to role {RoleId} successfully", role.Id);
            return true;
        }

        public async Task<bool> RemoveUsersFromRoleAsync(RoleUsersDto roleUsersDto)
        {
            var role = await _roleManager.FindByIdAsync(roleUsersDto.RoleId);
            if (role == null)
                return false;

            foreach (var userId in roleUsersDto.UserIds)
            {
                var user = await _userManager.FindByIdAsync(userId);
                if (user == null)
                    continue;

                if (await _userManager.IsInRoleAsync(user, role.Name))
                {
                    var result = await _userManager.RemoveFromRoleAsync(user, role.Name);
                    if (!result.Succeeded)
                    {
                        _logger.LogError("Failed to remove user {UserId} from role {RoleId}: {Errors}",
                            userId, role.Id, string.Join(", ", result.Errors.Select(e => e.Description)));
                        return false;
                    }
                }
            }

            _logger.LogInformation("Users removed from role {RoleId} successfully", role.Id);
            return true;
        }

        public async Task<List<string>> GetUsersInRoleAsync(string roleId)
        {
            var role = await _roleManager.FindByIdAsync(roleId);
            if (role == null)
                return new List<string>();

            var users = await _userManager.GetUsersInRoleAsync(role.Name);
            return users.Select(u => u.Id).ToList();
        }

        public async Task<List<string>> GetRolesForUserAsync(string userId)
        {
            var user = await _userManager.FindByIdAsync(userId);
            if (user == null)
                return new List<string>();

            var roles = await _userManager.GetRolesAsync(user);
            return roles.ToList();
        }

        public async Task<bool> IsUserInRoleAsync(string userId, string roleName)
        {
            var user = await _userManager.FindByIdAsync(userId);
            if (user == null)
                return false;

            return await _userManager.IsInRoleAsync(user, roleName);
        }
    }
}