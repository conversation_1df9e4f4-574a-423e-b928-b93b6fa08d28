using System;
using ExactCash.WASM.Application.DTOs.Common;
using ExactCash.Domain.Entities;
#nullable disable
namespace ExactCash.WASM.Application.DTOs
{
    public class ProductDto : BaseEntityDto
    {
        /// <summary>
        /// The name of the product.
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// The barcode value used for scanning the product.
        /// </summary>
        public string SKU { get; set; }

        /// <summary>
        /// The barcode value used for scanning the product.
        /// </summary>
        public string Barcode { get; set; }

        /// <summary>
        /// A description of the product.
        /// </summary>
        public string Description { get; set; }

        /// <summary>
        /// The selling price of the product.
        /// </summary>
        public decimal SellingPrice { get; set; }

        /// <summary>
        /// The cost price of the product.
        /// </summary>
        public decimal CostPrice { get; set; }

        /// <summary>
        /// The current stock quantity of the product.
        /// </summary>
        public decimal StockQuantity { get; set; } = 1;

        /// <summary>
        /// The minimum stock level before reordering.
        /// </summary>
        public int MinimumStockLevel { get; set; }

        /// <summary>
        /// The ID of the unit of measure for this product.
        /// </summary>
        public int? UnitId { get; set; }

        public UnitDto Unit { get; set; } = new UnitDto();

        /// <summary>
        /// DefaultUnitId
        /// </summary>
        public int? DefaultUnitId { get; set; }

        /// <summary>
        /// The name of the unit of measure.
        /// </summary>
        public string UnitName { get; set; }

        /// <summary>
        /// The ID of the brand for this product.
        /// </summary>
        public int? BrandId { get; set; }

        /// <summary>
        /// The name of the brand.
        /// </summary>
        public string BrandName { get; set; }

        public BrandDto Brand { get; set; } = new BrandDto();

        /// <summary>
        /// The Id of the category to which this product belongs.
        /// </summary>
        public int? CategoryId { get; set; }

        /// <summary>
        /// The name of the category to which this product belongs.
        /// </summary>
        public string CategoryName { get; set; }


        public CategoryDto Category { get; set; } = new CategoryDto();

        /// <summary>
        /// ImagePath
        /// </summary>
        public string ImagePath { get; set; }

        public decimal TotalPricePerProduct { get; set; }

        /// <summary>
        /// The date and time when the product was created.
        /// </summary>
        public decimal Discount { get; set; } = 0;

        /// <summary>
        /// IsDeleted
        /// </summary>
        public bool IsDeleted { get; set; } = false;
    }
}