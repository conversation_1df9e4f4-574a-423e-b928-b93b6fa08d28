﻿using ExactCash.Application.DTOs;
using ExactCash.WPF.Services.Interfaces;

namespace ExactCash.WPF.Services
{
    public class ExpenseServiceClient : IExpenseServiceClient
    {
        private readonly HttpService _httpService;
        private const string BaseUrl = "api/Expense";

        public ExpenseServiceClient(string baseUrl)
        {
            _httpService = new HttpService(baseUrl);
        }

        public async Task<ExpenseDto> CreateAsync(ExpenseDto dto)
        {
            return await _httpService.PostAsync<ExpenseDto>($"{BaseUrl}", dto);
        }

        public async Task<bool> DeleteAsync(int id)
        {
            return await _httpService.DeleteAsync($"{BaseUrl}/{id}");
        }

        public async Task<IEnumerable<ExpenseDto>> GetAllAsync(ExpenseSearchDto search)
        {
            return await _httpService.PostAsync<IEnumerable<ExpenseDto>>("api/Expense/search", search);
        }

        public async Task<ExpenseDto> GetByIdAsync(int id)
        {
            return await _httpService.GetAsync<ExpenseDto>($"{BaseUrl}/{id}");
        }

        public async Task<bool> UpdateAsync(ExpenseDto dto)
        {
            return await _httpService.PutAsync<bool>($"{BaseUrl}/{dto.Id}", dto);
        }
    }
}
