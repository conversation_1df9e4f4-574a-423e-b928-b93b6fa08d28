﻿<Window x:Class="ExactCash.WPF.Views.Expense.AddExpenseView"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="{Binding WindowTitle, RelativeSource={RelativeSource Self}}"
        Height="500"
        Width="500"
        WindowStartupLocation="CenterOwner"
        WindowStyle="None"
        ResizeMode="NoResize"
        Background="White"
        FlowDirection="RightToLeft">
        <Window.Resources>
                <Style x:Key="ModernButton"
                       TargetType="Button">
                        <Setter Property="Background"
                                Value="#0078D4"/>
                        <Setter Property="Foreground"
                                Value="White"/>
                        <Setter Property="BorderThickness"
                                Value="0"/>
                        <Setter Property="Padding"
                                Value="15,8"/>
                        <Setter Property="FontSize"
                                Value="14"/>
                        <Setter Property="Template">
                                <Setter.Value>
                                        <ControlTemplate TargetType="Button">
                                                <Border Background="{TemplateBinding Background}"
                                                        BorderThickness="{TemplateBinding BorderThickness}"
                                                        BorderBrush="{TemplateBinding BorderBrush}"
                                                        CornerRadius="4">
                                                        <ContentPresenter HorizontalAlignment="Center"
                                                                          VerticalAlignment="Center"/>
                                                </Border>
                                        </ControlTemplate>
                                </Setter.Value>
                        </Setter>
                        <Style.Triggers>
                                <Trigger Property="IsMouseOver"
                                         Value="True">
                                        <Setter Property="Background"
                                                Value="#106EBE"/>
                                </Trigger>
                        </Style.Triggers>
                </Style>

                <Style x:Key="SuccessButton"
                       TargetType="Button"
                       BasedOn="{StaticResource ModernButton}">
                        <Setter Property="Background"
                                Value="#28a745"/>
                        <Style.Triggers>
                                <Trigger Property="IsMouseOver"
                                         Value="True">
                                        <Setter Property="Background"
                                                Value="#218838"/>
                                </Trigger>
                        </Style.Triggers>
                </Style>

                <Style x:Key="ModernTextBox"
                       TargetType="TextBox">
                        <Setter Property="Height"
                                Value="35"/>
                        <Setter Property="Padding"
                                Value="10,5"/>
                        <Setter Property="BorderThickness"
                                Value="1"/>
                        <Setter Property="BorderBrush"
                                Value="#CCCCCC"/>
                        <Setter Property="Background"
                                Value="White"/>
                        <Setter Property="FontSize"
                                Value="14"/>
                </Style>

                <DropShadowEffect x:Key="DropShadowEffect"
                                  ShadowDepth="2"
                                  Direction="270"
                                  Color="Black"
                                  Opacity="0.3"
                                  BlurRadius="5"/>
        </Window.Resources>
        <Border BorderBrush="#DEE2E6"
                BorderThickness="1"
                CornerRadius="8">
                <Grid>
                        <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="*"/>
                                <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>

                        <!-- Header -->
                        <Border Background="#0078D4"
                                Grid.Row="0"
                                CornerRadius="8,8,0,0">
                                <Grid Margin="20,15">
                                        <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="*"/>
                                                <ColumnDefinition Width="Auto"/>
                                        </Grid.ColumnDefinitions>
                                        <TextBlock x:Name="HeaderTextBlock"
                                                   Text="{Binding HeaderText, RelativeSource={RelativeSource AncestorType=Window}}"
                                                   Foreground="White"
                                                   FontSize="18"
                                                   FontWeight="SemiBold"/>
                                        <Button Grid.Column="1"
                                                Content="✕"
                                                Foreground="White"
                                                Background="Transparent"
                                                BorderThickness="0"
                                                FontSize="16"
                                                Click="CancelButton_Click"/>
                                </Grid>
                        </Border>

                        <!-- Content -->
                        <StackPanel Grid.Row="1"
                                    Margin="20">
                                <TextBlock Text="التاريخ *"
                                           Margin="0,0,0,5"/>
                                <DatePicker x:Name="DatePicker"
                                            Height="35"
                                            Padding="10,0"
                                            VerticalContentAlignment="Center"
                                            Margin="0,0,0,15"/>

                                <TextBlock Text="المبلغ *"
                                           Margin="0,0,0,5"/>
                                <TextBox x:Name="AmountTextBox"
                                         Height="35"
                                         Padding="10,0"
                                         VerticalContentAlignment="Center"
                                         Margin="0,0,0,15"/>

                                <TextBlock Text="الوصف *"
                                           Margin="0,0,0,5"/>
                                <TextBox x:Name="DescriptionTextBox"
                                         Height="35"
                                         Padding="10,0"
                                         VerticalContentAlignment="Center"
                                         Margin="0,0,0,15"/>

                                <TextBlock Text="التصنيف"
                                           Margin="0,0,0,5"/>
                                <ComboBox x:Name="CategoryComboBox"
                                          Height="35"
                                          Padding="10,0"
                                          VerticalContentAlignment="Center"
                                          Margin="0,0,0,15"
                                          DisplayMemberPath="Name"/>

                                <TextBlock Text="طريقة الدفع"
                                           Margin="0,0,0,5"/>
                                <ComboBox x:Name="PaymentMethodComboBox"
                                          Height="35"
                                          Padding="10,0"
                                          VerticalContentAlignment="Center"
                                          Margin="0,0,0,15"/>

                                <TextBlock Text="المرجع"
                                           Margin="0,0,0,5"/>
                                <TextBox x:Name="ReferenceNumberTextBox"
                                         Height="35"
                                         Padding="10,0"
                                         VerticalContentAlignment="Center"/>
                        </StackPanel>

                        <!-- Footer -->
                        <Border Grid.Row="2"
                                Background="#F8F9FA"
                                BorderThickness="0,1,0,0"
                                BorderBrush="#DEE2E6"
                                CornerRadius="0,0,8,8">
                                <StackPanel Orientation="Horizontal"
                                            HorizontalAlignment="Center"
                                            Margin="20,15">
                                        <Button Content="حفظ"
                                                Width="120"
                                                Height="35"
                                                Margin="0,0,10,0"
                                                Click="SaveButton_Click"
                                                Style="{StaticResource ModernButton}"/>

                                        <Button Content="إلغاء"
                                                Width="120"
                                                Height="35"
                                                Click="CancelButton_Click"
                                                Style="{StaticResource ModernButton}"
                                                Background="#6C757D"/>
                                </StackPanel>
                        </Border>
                </Grid>
        </Border>
</Window> 