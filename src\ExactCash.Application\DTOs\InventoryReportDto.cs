﻿#nullable disable

namespace ExactCash.Application.DTOs
{
    public class InventoryReportDto
    {
        public int ProductId { get; set; }
        public string ProductName { get; set; }
        public string ProductSKU { get; set; }
        public string Barcode { get; set; }
        public string CategoryName { get; set; }
        public string BrandName { get; set; }
        public string UnitName { get; set; }
        public int CurrentStock { get; set; }
        public int MinStock { get; set; }
        public decimal CostPrice { get; set; }
        public decimal SellingPrice { get; set; }
        public decimal StockValue { get; set; }
        public string StockStatus { get; set; }
        public DateTime LastStockUpdate { get; set; }
        public int DaysWithoutMovement { get; set; }
        public decimal ProfitMargin { get; set; }
        public bool IsActive { get; set; }

        // Formatted properties for display
        public string FormattedCostPrice => $"{CostPrice:N2} ج.م";
        public string FormattedSellingPrice => $"{SellingPrice:N2} ج.م";
        public string FormattedStockValue => $"{StockValue:N2} ج.م";
        public string FormattedProfitMargin => $"{ProfitMargin:N1}%";
        public string StockStatusColor => StockStatus switch
        {
            "نفد المخزون" => "#DC3545", // Red
            "مخزون منخفض" => "#FFC107", // Yellow
            "مخزون جيد" => "#28A745", // Green
            _ => "#6C757D" // Gray
        };

        public InventoryReportDto()
        {

        }
    }

    public class LowStockReportDto
    {
        public int ProductId { get; set; }
        public string ProductName { get; set; }
        public string ProductSKU { get; set; }
        public string CategoryName { get; set; }
        public int CurrentStock { get; set; }
        public int MinStock { get; set; }
        public int StockDeficit { get; set; }
        public decimal ReorderValue { get; set; }
        public string SupplierName { get; set; }
        public int DaysOutOfStock { get; set; }
        public decimal LostSalesEstimate { get; set; }

        // Formatted properties for display
        public string FormattedReorderValue => $"{ReorderValue:N2} ج.م";
        public string FormattedLostSalesEstimate => $"{LostSalesEstimate:N2} ج.م";

        public LowStockReportDto()
        {

        }
    }

    public class StockMovementReportDto
    {
        public int MovementId { get; set; }
        public DateTime MovementDate { get; set; }
        public string ProductName { get; set; }
        public string ProductSKU { get; set; }
        public int Quantity { get; set; }
        public string MovementType { get; set; }
        public string Reference { get; set; }
        public string CreatedBy { get; set; }
        public int StockBefore { get; set; }
        public int StockAfter { get; set; }
        public string Reason { get; set; }

        // Formatted properties for display
        public string MovementTypeColor => MovementType switch
        {
            "Purchase" => "#28A745", // Green for stock in
            "Sale" => "#DC3545", // Red for stock out
            "Manual Adjustment" => "#FFC107", // Yellow for adjustments
            "Return" => "#17A2B8", // Blue for returns
            _ => "#6C757D" // Gray
        };

        public string FormattedQuantity => Quantity >= 0 ? $"+{Quantity}" : $"{Quantity}";

        public StockMovementReportDto()
        {

        }
    }

    public class InventorySummaryDto
    {
        public int TotalProducts { get; set; }
        public int ActiveProducts { get; set; }
        public int InactiveProducts { get; set; }
        public int LowStockProducts { get; set; }
        public int OutOfStockProducts { get; set; }
        public decimal TotalInventoryValue { get; set; }
        public decimal TotalCostValue { get; set; }
        public decimal TotalProfitPotential { get; set; }
        public int TotalCategories { get; set; }
        public int TotalBrands { get; set; }
        public decimal AverageStockLevel { get; set; }
        public decimal InventoryTurnoverRatio { get; set; }
        public int DaysOfInventoryOnHand { get; set; }

        // Formatted properties for display
        public string FormattedTotalInventoryValue => $"{TotalInventoryValue:N2} ج.م";
        public string FormattedTotalCostValue => $"{TotalCostValue:N2} ج.م";
        public string FormattedTotalProfitPotential => $"{TotalProfitPotential:N2} ج.م";
        public string FormattedInventoryTurnoverRatio => $"{InventoryTurnoverRatio:N2}";

        public InventorySummaryDto()
        {

        }
    }
}
