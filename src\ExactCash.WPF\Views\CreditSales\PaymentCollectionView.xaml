<Window x:Class="ExactCash.WPF.Views.CreditSales.PaymentCollectionView"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml" x:Name="PaymentCollectionViewScreen"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        mc:Ignorable="d"
        Title="تحصيل دفعة"
        Height="700"
        Width="800"
        FlowDirection="RightToLeft"
        FontFamily="Segoe UI"
        WindowStartupLocation="CenterScreen"
        ResizeMode="NoResize">

    <Window.Resources>
        <Style x:Key="ModernBorder"
                TargetType="Border">
            <Setter Property="Background"
                    Value="White"/>
            <Setter Property="BorderBrush"
                    Value="#DEE2E6"/>
            <Setter Property="BorderThickness"
                    Value="1"/>
            <Setter Property="CornerRadius"
                    Value="6"/>
            <Setter Property="Padding"
                    Value="15"/>
        </Style>

        <Style x:Key="ModernButton"
                TargetType="Button">
            <Setter Property="Height"
                    Value="40"/>
            <Setter Property="Padding"
                    Value="20,5"/>
            <Setter Property="Background"
                    Value="#0078D4"/>
            <Setter Property="Foreground"
                    Value="White"/>
            <Setter Property="BorderThickness"
                    Value="0"/>
            <Setter Property="Cursor"
                    Value="Hand"/>
            <Setter Property="FontSize"
                    Value="14"/>
            <Setter Property="FontWeight"
                    Value="SemiBold"/>
        </Style>

        <Style x:Key="ModernTextBox"
                TargetType="TextBox">
            <Setter Property="Height"
                    Value="40"/>
            <Setter Property="Padding"
                    Value="10,5"/>
            <Setter Property="BorderThickness"
                    Value="1"/>
            <Setter Property="BorderBrush"
                    Value="#CCCCCC"/>
            <Setter Property="Background"
                    Value="White"/>
            <Setter Property="FontSize"
                    Value="14"/>
            <Setter Property="TextAlignment"
                    Value="Right"/>
        </Style>

        <Style x:Key="ModernComboBox"
                TargetType="ComboBox">
            <Setter Property="Height"
                    Value="40"/>
            <Setter Property="Padding"
                    Value="10,5"/>
            <Setter Property="BorderThickness"
                    Value="1"/>
            <Setter Property="BorderBrush"
                    Value="#CCCCCC"/>
            <Setter Property="Background"
                    Value="White"/>
            <Setter Property="FontSize"
                    Value="14"/>
            <Setter Property="HorizontalContentAlignment"
                    Value="Right"/>
        </Style>
    </Window.Resources>

    <Grid Background="#F8F9FA">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0"
                Style="{StaticResource ModernBorder}"
                Margin="20,20,20,10">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0"
                        Orientation="Horizontal">
                    <TextBlock Text="💰"
                            FontSize="24"
                            VerticalAlignment="Center"
                            Margin="0,0,10,0"/>
                    <TextBlock Text="تحصيل دفعة من عميل"
                               FontSize="20"
                               FontWeight="Bold"
                               VerticalAlignment="Center"/>
                </StackPanel>
            </Grid>
        </Border>

        <!-- Main Content -->
        <Border Grid.Row="1"
                Style="{StaticResource ModernBorder}"
                Margin="20,10,20,10">
            <ScrollViewer VerticalScrollBarVisibility="Auto">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <!-- Customer Information -->
                    <GroupBox Grid.Row="0"
                            Header="معلومات العميل"
                            Margin="0,0,0,20">
                        <Grid Margin="10">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>

                            <TextBlock Text="اسم العميل"
                                       VerticalAlignment="Center"
                                       Margin="0,0,10,10"/>
                            <TextBox Grid.Column="1"
                                     Text="{Binding CustomerName, Mode=OneWay}"
                                     Style="{StaticResource ModernTextBox}"
                                     IsReadOnly="True"
                                     Background="#F8F9FA"
                                     Margin="0,0,10,10"/>

                            <TextBlock Text="رقم الهاتف"
                                       Grid.Column="2"
                                       VerticalAlignment="Center"
                                       Margin="0,0,10,10"/>
                            <TextBox Grid.Column="3"
                                     Text="{Binding CustomerPhone, Mode=OneWay}"
                                     Style="{StaticResource ModernTextBox}"
                                     IsReadOnly="True"
                                     Background="#F8F9FA"
                                     Margin="0,0,0,10"/>

                            <TextBlock Text="حد الائتمان"
                                       Grid.Row="1"
                                       VerticalAlignment="Center"
                                       Margin="0,0,10,0"/>
                            <TextBox Grid.Row="1"
                                    Grid.Column="1"
                                     Text="{Binding CreditLimit, StringFormat='{}{0:N2} ج.م', Mode=OneWay}"
                                     Style="{StaticResource ModernTextBox}"
                                     IsReadOnly="True"
                                     Background="#F8F9FA"
                                     Margin="0,0,10,0"/>

                            <TextBlock Text="الرصيد المستحق"
                                       Grid.Row="1"
                                    Grid.Column="2"
                                       VerticalAlignment="Center"
                                       Margin="0,0,10,0"/>
                            <TextBox Grid.Row="1"
                                    Grid.Column="3"
                                     Text="{Binding TotalOutstandingAmount, StringFormat='{}{0:N2} ج.م', Mode=OneWay}"
                                     Style="{StaticResource ModernTextBox}"
                                     IsReadOnly="True"
                                     Background="#FFF3CD"
                                     Foreground="#856404"/>
                        </Grid>
                    </GroupBox>

                    <!-- Outstanding Invoices -->
                    <GroupBox Grid.Row="1"
                            Header="الفواتير المستحقة"
                            Margin="0,0,0,20">
                        <DataGrid ItemsSource="{Binding OutstandingInvoices}"
                                  AutoGenerateColumns="False"
                                  CanUserAddRows="False"
                                  CanUserDeleteRows="False"
                                  IsReadOnly="True"
                                  SelectionMode="Single"
                                  SelectedItem="{Binding SelectedInvoice}"
                                  GridLinesVisibility="All"
                                  BorderThickness="1"
                                  BorderBrush="#E5E5E5"
                                  Background="White"
                                  RowHeaderWidth="0"
                                  HeadersVisibility="Column"
                                  HorizontalGridLinesBrush="#E5E5E5"
                                  VerticalGridLinesBrush="#E5E5E5"
                                  AlternatingRowBackground="#F8F9FA"
                                  MaxHeight="200">
                            <DataGrid.Columns>
                                <DataGridTextColumn Header="رقم الفاتورة"
                                                    Binding="{Binding InvoiceNumber}"
                                                    Width="150"/>
                                <DataGridTextColumn Header="التاريخ"
                                                    Binding="{Binding CreationDate, StringFormat=dd/MM/yyyy}"
                                                    Width="100"/>
                                <DataGridTextColumn Header="إجمالي الفاتورة"
                                                    Binding="{Binding TotalAmount, StringFormat='{}{0:N2} ج.م'}"
                                                    Width="120"/>
                                <DataGridTextColumn Header="المبلغ المدفوع"
                                                    Binding="{Binding PaidAmount, StringFormat='{}{0:N2} ج.م'}"
                                                    Width="120"/>
                                <DataGridTextColumn Header="المبلغ المستحق"
                                                    Binding="{Binding RemainingAmount, StringFormat='{}{0:N2} ج.م'}"
                                                    Width="120"/>
                                <DataGridTextColumn Header="أيام التأخير"
                                                    Binding="{Binding DaysOverdue}"
                                                    Width="100"/>
                            </DataGrid.Columns>
                        </DataGrid>
                    </GroupBox>

                    <!-- Payment Details -->
                    <GroupBox Grid.Row="2"
                            Header="تفاصيل الدفعة"
                            Margin="0,0,0,20">
                        <Grid Margin="10">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>

                            <TextBlock Text="مبلغ الدفعة"
                                       VerticalAlignment="Center"
                                       Margin="0,0,10,10"/>
                            <TextBox Grid.Column="1"
                                     Text="{Binding PaymentAmount, UpdateSourceTrigger=PropertyChanged, StringFormat=N2}"
                                     Style="{StaticResource ModernTextBox}"
                                     Margin="0,0,10,10"/>

                            <TextBlock Text="طريقة الدفع"
                                       Grid.Column="2"
                                       VerticalAlignment="Center"
                                       Margin="0,0,10,10"/>
                            <ComboBox Grid.Column="3"
                                      ItemsSource="{Binding PaymentMethods}"
                                      SelectedItem="{Binding SelectedPaymentMethod}"
                                      Style="{StaticResource ModernComboBox}"
                                      Margin="0,0,0,10"/>

                            <TextBlock Text="تاريخ الدفع"
                                       Grid.Row="1"
                                       VerticalAlignment="Center"
                                       Margin="0,0,10,10"/>
                            <DatePicker Grid.Row="1"
                                    Grid.Column="1"
                                        SelectedDate="{Binding PaymentDate}"
                                        Margin="0,0,10,10"
                                        Height="35"
                                        FontSize="14"/>

                            <TextBlock Text="المحصل بواسطة"
                                       Grid.Row="1"
                                    Grid.Column="2"
                                       VerticalAlignment="Center"
                                       Margin="0,0,10,10"/>
                            <TextBox Grid.Row="1"
                                    Grid.Column="3"
                                     Text="{Binding CollectedBy}"
                                     Style="{StaticResource ModernTextBox}"
                                     Margin="0,0,0,10"/>

                            <TextBlock Text="ملاحظات"
                                       Grid.Row="2"
                                       VerticalAlignment="Top"
                                       Margin="0,5,10,0"/>
                            <TextBox Grid.Row="2"
                                    Grid.Column="1"
                                    Grid.ColumnSpan="3"
                                     Text="{Binding Notes}"
                                     Style="{StaticResource ModernTextBox}"
                                     Height="60"
                                     TextWrapping="Wrap"
                                     AcceptsReturn="True"
                                     VerticalScrollBarVisibility="Auto"/>
                        </Grid>
                    </GroupBox>

                    <!-- Payment Summary -->
                    <GroupBox Grid.Row="3"
                            Header="ملخص الدفعة">
                        <Grid Margin="10">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <StackPanel Grid.Column="0"
                                    HorizontalAlignment="Center">
                                <TextBlock Text="مبلغ الدفعة"
                                        FontWeight="Bold"
                                        HorizontalAlignment="Center"/>
                                <TextBlock Text="{Binding PaymentAmount, StringFormat='{}{0:N2} ج.م'}"
                                           FontSize="18"
                                           FontWeight="Bold"
                                           Foreground="#28A745"
                                           HorizontalAlignment="Center"/>
                            </StackPanel>

                            <StackPanel Grid.Column="1"
                                    HorizontalAlignment="Center">
                                <TextBlock Text="الرصيد بعد الدفع"
                                        FontWeight="Bold"
                                        HorizontalAlignment="Center"/>
                                <TextBlock Text="{Binding RemainingAfterPayment, StringFormat='{}{0:N2} ج.م'}"
                                           FontSize="18"
                                           FontWeight="Bold"
                                           Foreground="#DC3545"
                                           HorizontalAlignment="Center"/>
                            </StackPanel>

                            <StackPanel Grid.Column="2"
                                    HorizontalAlignment="Center">
                                <TextBlock Text="حالة الدفع"
                                        FontWeight="Bold"
                                        HorizontalAlignment="Center"/>
                                <TextBlock Text="{Binding PaymentStatus}"
                                           FontSize="18"
                                           FontWeight="Bold"
                                           Foreground="{Binding PaymentStatusColor}"
                                           HorizontalAlignment="Center"/>
                            </StackPanel>
                        </Grid>
                    </GroupBox>
                </Grid>
            </ScrollViewer>
        </Border>

        <!-- Footer Buttons -->
        <Border Grid.Row="2"
                Style="{StaticResource ModernBorder}"
                Margin="20,10,20,20">
            <StackPanel Orientation="Horizontal"
                        HorizontalAlignment="Center">
                <Button Content="تحصيل الدفعة"
                        Command="{Binding CollectPaymentCommand}"
                        Style="{StaticResource ModernButton}"
                        Background="#28A745"
                        Margin="0,0,10,0"
                        Width="120"/>
                <Button Content="طباعة إيصال"
                        Command="{Binding PrintReceiptCommand}"
                        Style="{StaticResource ModernButton}"
                        Background="#17A2B8"
                        Margin="0,0,10,0"
                        Width="120"/>
                <Button Content="إلغاء"
                        Command="{Binding CancelCommand}"
                        Style="{StaticResource ModernButton}"
                        Background="#6C757D"
                        Width="120"/>
            </StackPanel>
        </Border>
    </Grid>
</Window>
