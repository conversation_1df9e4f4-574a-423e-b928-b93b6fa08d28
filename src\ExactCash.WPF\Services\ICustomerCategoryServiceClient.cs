using ExactCash.Application.DTOs;
using ExactCash.Application.Responses;

namespace ExactCash.WPF.Services
{
    public interface ICustomerCategoryServiceClient
    {
        Task<List<CustomerCategoryDto>> GetAllAsync();
        Task<List<CustomerCategoryDto>> GetActiveAsync();
        Task<CustomerCategoryDto> GetByIdAsync(int id);
        Task<BaseResponse<CustomerCategoryDto>> CreateAsync(CustomerCategoryDto categoryDto);
        Task<BaseResponse<bool>> UpdateAsync(CustomerCategoryDto categoryDto);
        Task<BaseResponse<bool>> DeleteAsync(int id);
    }
}
