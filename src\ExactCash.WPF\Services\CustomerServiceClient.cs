using ExactCash.Domain.Entities;

namespace ExactCash.WPF.Services
{
    public class CustomerServiceClient : ICustomerServiceClient
    {
        private readonly HttpService _httpService;

        public CustomerServiceClient(string baseUrl)
        {
            _httpService = new HttpService(baseUrl);
        }

        public async Task<List<Customer>> GetAllCustomersAsync()
        {
            return await _httpService.GetAsync<List<Customer>>("api/customers");
        }
    }
}
