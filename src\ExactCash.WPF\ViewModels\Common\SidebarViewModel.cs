using ExactCash.WPF.Commands;
using System.Windows.Input;
#nullable disable

namespace ExactCash.WPF.ViewModels.Common
{
    public class SidebarViewModel : ViewModelBase
    {
        public SidebarViewModel()
        {
            NavigateToPosCommand = new RelayCommand(NavigateToPos);
            NavigateToDailySalesCommand = new RelayCommand(NavigateToDailySales);
            NavigateToSalesReportsCommand = new RelayCommand(NavigateToSalesReports);
            NavigateToInventoryCommand = new RelayCommand(NavigateToInventory);
            NavigateToProductsCommand = new RelayCommand(NavigateToProducts);
            NavigateToSettingsCommand = new RelayCommand(NavigateToSettings);
            NavigateToUsersCommand = new RelayCommand(NavigateToUsers);
        }

        public ICommand NavigateToPosCommand { get; }
        public ICommand NavigateToDailySalesCommand { get; }
        public ICommand NavigateToSalesReportsCommand { get; }
        public ICommand NavigateToInventoryCommand { get; }
        public ICommand NavigateToProductsCommand { get; }
        public ICommand NavigateToSettingsCommand { get; }
        public ICommand NavigateToUsersCommand { get; }

        private void NavigateToPos()
        {
            // TODO: Implement navigation to POS view
        }

        private void NavigateToDailySales()
        {
            // TODO: Implement navigation to Daily Sales view
        }

        private void NavigateToSalesReports()
        {
            // TODO: Implement navigation to Sales Reports view
        }

        private void NavigateToInventory()
        {
            // TODO: Implement navigation to Inventory view
        }

        private void NavigateToProducts()
        {
            // TODO: Implement navigation to Products view
        }

        private void NavigateToSettings()
        {
            // TODO: Implement navigation to Settings view
        }

        private void NavigateToUsers()
        {
            // TODO: Implement navigation to Users view
        }
    }
}