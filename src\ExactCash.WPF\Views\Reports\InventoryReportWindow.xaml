﻿<Window x:Class="ExactCash.WPF.Views.Reports.InventoryReportWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        mc:Ignorable="d"
        Title="تقرير المخزون الحالي - ExactCash POS"
        Height="700"
        Width="1200"
        WindowStartupLocation="CenterScreen"
        FlowDirection="RightToLeft"
        FontFamily="Segoe UI">

        <Window.Resources>
                <Style x:Key="HeaderTextStyle"
                       TargetType="TextBlock">
                        <Setter Property="FontWeight"
                                Value="Bold"/>
                        <Setter Property="FontSize"
                                Value="14"/>
                        <Setter Property="Foreground"
                                Value="White"/>
                        <Setter Property="HorizontalAlignment"
                                Value="Center"/>
                        <Setter Property="VerticalAlignment"
                                Value="Center"/>
                </Style>

                <Style x:Key="DataGridStyle"
                       TargetType="DataGrid">
                        <Setter Property="AutoGenerateColumns"
                                Value="False"/>
                        <Setter Property="CanUserAddRows"
                                Value="False"/>
                        <Setter Property="CanUserDeleteRows"
                                Value="False"/>
                        <Setter Property="IsReadOnly"
                                Value="True"/>
                        <Setter Property="SelectionMode"
                                Value="Single"/>
                        <Setter Property="GridLinesVisibility"
                                Value="Horizontal"/>
                        <Setter Property="HorizontalGridLinesBrush"
                                Value="#E0E0E0"/>
                        <Setter Property="AlternatingRowBackground"
                                Value="#F9F9F9"/>
                        <Setter Property="RowBackground"
                                Value="White"/>
                        <Setter Property="HeadersVisibility"
                                Value="Column"/>
                </Style>
        </Window.Resources>

        <Grid>
                <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <!-- Header -->
                <Border Grid.Row="0"
                        Background="#1976D2"
                        Padding="20">
                        <StackPanel Orientation="Horizontal">
                                <TextBlock Text="📦"
                                           FontSize="24"
                                           Margin="0,0,10,0"
                                           Foreground="White"/>
                                <TextBlock Text="تقرير المخزون الحالي"
                                           FontSize="24"
                                           FontWeight="Bold"
                                           Foreground="White"/>
                        </StackPanel>
                </Border>

                <!-- Summary Cards -->
                <Grid Grid.Row="1"
                      Margin="20,20,20,10">
                        <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <!-- Total Products -->
                        <Border Grid.Column="0"
                                Background="#4CAF50"
                                CornerRadius="5"
                                Margin="5"
                                Padding="15">
                                <StackPanel HorizontalAlignment="Center">
                                        <TextBlock Text="إجمالي المنتجات"
                                                   Style="{StaticResource HeaderTextStyle}"/>
                                        <TextBlock x:Name="TotalProductsText"
                                                   Text="0"
                                                   FontSize="28"
                                                   FontWeight="Bold"
                                                   Foreground="White"
                                                   HorizontalAlignment="Center"/>
                                </StackPanel>
                        </Border>

                        <!-- Low Stock Products -->
                        <Border Grid.Column="1"
                                Background="#FF9800"
                                CornerRadius="5"
                                Margin="5"
                                Padding="15">
                                <StackPanel HorizontalAlignment="Center">
                                        <TextBlock Text="مخزون منخفض"
                                                   Style="{StaticResource HeaderTextStyle}"/>
                                        <TextBlock x:Name="LowStockProductsText"
                                                   Text="0"
                                                   FontSize="28"
                                                   FontWeight="Bold"
                                                   Foreground="White"
                                                   HorizontalAlignment="Center"/>
                                </StackPanel>
                        </Border>

                        <!-- Out of Stock -->
                        <Border Grid.Column="2"
                                Background="#F44336"
                                CornerRadius="5"
                                Margin="5"
                                Padding="15">
                                <StackPanel HorizontalAlignment="Center">
                                        <TextBlock Text="نفد المخزون"
                                                   Style="{StaticResource HeaderTextStyle}"/>
                                        <TextBlock x:Name="OutOfStockProductsText"
                                                   Text="0"
                                                   FontSize="28"
                                                   FontWeight="Bold"
                                                   Foreground="White"
                                                   HorizontalAlignment="Center"/>
                                </StackPanel>
                        </Border>

                        <!-- Total Value -->
                        <Border Grid.Column="3"
                                Background="#2196F3"
                                CornerRadius="5"
                                Margin="5"
                                Padding="15">
                                <StackPanel HorizontalAlignment="Center">
                                        <TextBlock Text="قيمة المخزون"
                                                   Style="{StaticResource HeaderTextStyle}"/>
                                        <TextBlock x:Name="TotalValueText"
                                                   Text="0.00 ج.م"
                                                   FontSize="20"
                                                   FontWeight="Bold"
                                                   Foreground="White"
                                                   HorizontalAlignment="Center"/>
                                </StackPanel>
                        </Border>
                </Grid>

                <!-- Data Grid -->
                <DataGrid Grid.Row="2"
                          x:Name="InventoryDataGrid"
                          Style="{StaticResource DataGridStyle}"
                          Margin="20,10,20,10">
                        <DataGrid.Columns>
                                <DataGridTextColumn Header="اسم المنتج"
                                                    Binding="{Binding ProductName}"
                                                    Width="200"/>
                                <DataGridTextColumn Header="رمز المنتج"
                                                    Binding="{Binding ProductSKU}"
                                                    Width="120"/>
                                <DataGridTextColumn Header="الباركود"
                                                    Binding="{Binding Barcode}"
                                                    Width="120"/>
                                <DataGridTextColumn Header="الفئة"
                                                    Binding="{Binding CategoryName}"
                                                    Width="120"/>
                                <DataGridTextColumn Header="العلامة التجارية"
                                                    Binding="{Binding BrandName}"
                                                    Width="120"/>
                                <DataGridTextColumn Header="الوحدة"
                                                    Binding="{Binding UnitName}"
                                                    Width="80"/>
                                <DataGridTextColumn Header="المخزون الحالي"
                                                    Binding="{Binding CurrentStock}"
                                                    Width="100"/>
                                <DataGridTextColumn Header="الحد الأدنى"
                                                    Binding="{Binding MinStock}"
                                                    Width="100"/>
                                <DataGridTextColumn Header="سعر التكلفة"
                                                    Binding="{Binding FormattedCostPrice}"
                                                    Width="120"/>
                                <DataGridTextColumn Header="سعر البيع"
                                                    Binding="{Binding FormattedSellingPrice}"
                                                    Width="120"/>
                                <DataGridTextColumn Header="قيمة المخزون"
                                                    Binding="{Binding FormattedStockValue}"
                                                    Width="120"/>
                                <DataGridTextColumn Header="هامش الربح"
                                                    Binding="{Binding FormattedProfitMargin}"
                                                    Width="100"/>
                                <DataGridTemplateColumn Header="حالة المخزون"
                                                        Width="120">
                                        <DataGridTemplateColumn.CellTemplate>
                                                <DataTemplate>
                                                        <Border CornerRadius="3"
                                                                Padding="5,2">
                                                                <Border.Style>
                                                                        <Style TargetType="Border">
                                                                                <Style.Triggers>
                                                                                        <DataTrigger Binding="{Binding StockStatus}"
                                                                                                     Value="نفد المخزون">
                                                                                                <Setter Property="Background"
                                                                                                        Value="#FFEBEE"/>
                                                                                        </DataTrigger>
                                                                                        <DataTrigger Binding="{Binding StockStatus}"
                                                                                                     Value="مخزون منخفض">
                                                                                                <Setter Property="Background"
                                                                                                        Value="#FFF3E0"/>
                                                                                        </DataTrigger>
                                                                                        <DataTrigger Binding="{Binding StockStatus}"
                                                                                                     Value="مخزون جيد">
                                                                                                <Setter Property="Background"
                                                                                                        Value="#E8F5E8"/>
                                                                                        </DataTrigger>
                                                                                </Style.Triggers>
                                                                        </Style>
                                                                </Border.Style>
                                                                <TextBlock Text="{Binding StockStatus}"
                                                                           FontWeight="SemiBold"
                                                                           HorizontalAlignment="Center">
                                                                        <TextBlock.Style>
                                                                                <Style TargetType="TextBlock">
                                                                                        <Style.Triggers>
                                                                                                <DataTrigger Binding="{Binding StockStatus}"
                                                                                                             Value="نفد المخزون">
                                                                                                        <Setter Property="Foreground"
                                                                                                                Value="#D32F2F"/>
                                                                                                </DataTrigger>
                                                                                                <DataTrigger Binding="{Binding StockStatus}"
                                                                                                             Value="مخزون منخفض">
                                                                                                        <Setter Property="Foreground"
                                                                                                                Value="#F57C00"/>
                                                                                                </DataTrigger>
                                                                                                <DataTrigger Binding="{Binding StockStatus}"
                                                                                                             Value="مخزون جيد">
                                                                                                        <Setter Property="Foreground"
                                                                                                                Value="#388E3C"/>
                                                                                                </DataTrigger>
                                                                                        </Style.Triggers>
                                                                                </Style>
                                                                        </TextBlock.Style>
                                                                </TextBlock>
                                                        </Border>
                                                </DataTemplate>
                                        </DataGridTemplateColumn.CellTemplate>
                                </DataGridTemplateColumn>
                        </DataGrid.Columns>
                </DataGrid>

                <!-- Footer -->
                <Border Grid.Row="3"
                        Background="#F5F5F5"
                        Padding="20">
                        <StackPanel Orientation="Horizontal"
                                    HorizontalAlignment="Center">
                                <Button Content="تحديث البيانات"
                                        Background="#2196F3"
                                        Foreground="White"
                                        Padding="15,8"
                                        Margin="5"
                                        BorderThickness="0"
                                        Click="RefreshData_Click"/>
                                <Button Content="طباعة التقرير"
                                        Background="#4CAF50"
                                        Foreground="White"
                                        Padding="15,8"
                                        Margin="5"
                                        BorderThickness="0"
                                        Click="PrintReport_Click"/>
                                <Button Content="تصدير إلى Excel"
                                        Background="#FF9800"
                                        Foreground="White"
                                        Padding="15,8"
                                        Margin="5"
                                        BorderThickness="0"
                                        Click="ExportToExcel_Click"/>
                                <Button Content="إغلاق"
                                        Background="#757575"
                                        Foreground="White"
                                        Padding="15,8"
                                        Margin="5"
                                        BorderThickness="0"
                                        Click="Close_Click"/>
                        </StackPanel>
                </Border>
        </Grid>
</Window>
