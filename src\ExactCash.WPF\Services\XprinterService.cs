using System.Drawing;
using System.Drawing.Printing;
using ExactCash.Application.DTOs;
#nullable disable

namespace ExactCash.WPF.Services
{
    public class XprinterService : IPrinterService
    {
        private readonly string _printerName;
        private readonly Font _titleFont;
        private readonly Font _normalFont;
        private readonly Font _smallFont;

        public XprinterService(string printerName = "XP-80C")
        {
            _printerName = printerName;
            _titleFont = new Font("Arial", 12, FontStyle.Bold);
            _normalFont = new Font("Arial", 10);
            _smallFont = new Font("Arial", 8);
        }

        public async Task<bool> PrintInvoiceAsync(SaleDto sale)
        {
            try
            {
                var printDocument = new PrintDocument();
                printDocument.PrinterSettings.PrinterName = _printerName;

                // Set paper size for thermal printer (80mm width)
                var paperSize = new PaperSize("Custom", 315, 0); // 80mm = ~315 dots at 96 DPI
                printDocument.DefaultPageSettings.PaperSize = paperSize;

                // Handle the print page event
                printDocument.PrintPage += (sender, e) => PrintPage(sender, e, sale);

                // Print the document
                printDocument.Print();
                return true;
            }
            catch (Exception ex)
            {
                // Log the error
                Console.WriteLine($"Printing error: {ex.Message}");
                return false;
            }
        }

        private void PrintPage(object sender, PrintPageEventArgs e, SaleDto sale)
        {
            var graphics = e.Graphics;
            var yPos = 10;
            var leftMargin = 10;
            var width = e.PageBounds.Width - 20;

            // Store name and header
            graphics.DrawString("ExactCash", _titleFont, Brushes.Black, new RectangleF(leftMargin, yPos, width, 30), new StringFormat { Alignment = StringAlignment.Center });
            yPos += 30;

            // Invoice details
            graphics.DrawString($"رقم الفاتورة: {sale.InvoiceNumber}", _normalFont, Brushes.Black, leftMargin, yPos);
            yPos += 20;
            graphics.DrawString($"التاريخ: {sale.SaleDate:dd/MM/yyyy HH:mm}", _normalFont, Brushes.Black, leftMargin, yPos);
            yPos += 20;
            graphics.DrawString($"نوع الفاتورة: {sale.InvoiceType}", _normalFont, Brushes.Black, leftMargin, yPos);
            yPos += 20;

            // Customer details
            if (sale.Customer != null)
            {
                graphics.DrawString($"العميل: {sale.Customer.FullName}", _normalFont, Brushes.Black, leftMargin, yPos);
                yPos += 20;
                graphics.DrawString($"رقم الهاتف: {sale.Customer.Phone}", _normalFont, Brushes.Black, leftMargin, yPos);
                yPos += 20;
            }

            // Separator line
            graphics.DrawLine(Pens.Black, leftMargin, yPos, width + leftMargin, yPos);
            yPos += 10;

            // Items header
            var col1 = leftMargin; // Quantity column
            var col2 = col1 + 30; // Product name column
            var col3 = width - 100; // Price column
            var col4 = width - 20; // Total column

            graphics.DrawString("الكمية", _smallFont, Brushes.Black, col1, yPos);
            graphics.DrawString("المنتج", _smallFont, Brushes.Black, col2, yPos);
            graphics.DrawString("السعر", _smallFont, Brushes.Black, col3, yPos);
            graphics.DrawString("الإجمالي", _smallFont, Brushes.Black, col4, yPos);
            yPos += 20;

            // Items
            foreach (var item in sale.SaleItems)
            {
                // Draw quantity
                graphics.DrawString(item.Quantity.ToString(), _smallFont, Brushes.Black, col1, yPos);

                // Draw product name with wrapping
                var productNameRect = new RectangleF(col2, yPos, col3 - col2 - 10, 40);
                graphics.DrawString(item.ProductName, _smallFont, Brushes.Black, productNameRect, new StringFormat { Alignment = StringAlignment.Near });

                // Draw price and total
                graphics.DrawString(item.Price.ToString("N2"), _smallFont, Brushes.Black, col3, yPos);
                graphics.DrawString(item.TotalPrice.ToString("N2"), _smallFont, Brushes.Black, col4, yPos);

                // Calculate the height needed for the product name
                var nameHeight = graphics.MeasureString(item.ProductName, _smallFont, (int)(col3 - col2 - 10)).Height;
                yPos += (int)Math.Max(15, nameHeight);
            }

            // Separator line
            yPos += 5;
            graphics.DrawLine(Pens.Black, leftMargin, yPos, width + leftMargin, yPos);
            yPos += 15;

            // Totals
            graphics.DrawString($"إجمالي المبلغ: {sale.TotalAmount:N2}", _normalFont, Brushes.Black, leftMargin, yPos);
            yPos += 20;
            graphics.DrawString($"قيمة الخصم: {sale.DiscountAmount:N2}", _normalFont, Brushes.Black, leftMargin, yPos);
            yPos += 20;
            graphics.DrawString($"قيمة الضريبة: {sale.TaxAmount:N2}", _normalFont, Brushes.Black, leftMargin, yPos);
            yPos += 20;
            graphics.DrawString($"الصافي: {sale.NetAmount:N2}", _normalFont, Brushes.Black, leftMargin, yPos);
            yPos += 20;
            graphics.DrawString($"المبلغ المدفوع: {sale.PaidAmount:N2}", _normalFont, Brushes.Black, leftMargin, yPos);
            yPos += 20;
            graphics.DrawString($"المبلغ المتبقي: {sale.RemainingAmount:N2}", _normalFont, Brushes.Black, leftMargin, yPos);
            yPos += 30;

            // Footer
            graphics.DrawString("شكراً لتعاملكم معنا", _normalFont, Brushes.Black, new RectangleF(leftMargin, yPos, width, 30), new StringFormat { Alignment = StringAlignment.Center });

            // No more pages
            e.HasMorePages = false;
        }
    }
}