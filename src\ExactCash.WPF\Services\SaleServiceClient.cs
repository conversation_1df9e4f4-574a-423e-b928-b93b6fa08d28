﻿using ExactCash.Application.DTOs;
using ExactCash.Application.DTOs.Common;
using ExactCash.Application.Responses;

namespace ExactCash.WPF.Services
{
    public class SaleServiceClient : ISaleServiceClient
    {
        private readonly HttpService _httpService;

        public SaleServiceClient(string baseUrl)
        {
            _httpService = new HttpService(baseUrl);
        }

        public async Task<BaseResponse<string>> CreateAsync(SaleDto sale)
        {
            return await _httpService.PostAsync<BaseResponse<string>>("api/Sales", sale);
        }

        public async Task DeleteAsync(int saleId)
        {
            await _httpService.DeleteAsync($"api/Sales/{saleId}");
        }

        public async Task<PagedResponse<SaleDto>> GetAllAsync(int? customerId, string invoiceNumber, DateTime? startDate, DateTime? endDate, PaginationFilter pagination)
        {
            var queryParams = new List<string>();


            if (customerId.HasValue) queryParams.Add($"customerId={customerId.Value}");
            if (startDate.HasValue)
            {
                var utcStartDate = startDate.Value.Kind == DateTimeKind.Unspecified
                    ? DateTime.SpecifyKind(startDate.Value, DateTimeKind.Utc)
                    : startDate.Value.ToUniversalTime();
                queryParams.Add($"startDate={Uri.EscapeDataString(utcStartDate.ToString("yyyy-MM-ddTHH:mm:ss.fffZ"))}");
            }
            if (endDate.HasValue)
            {
                var utcEndDate = endDate.Value.Kind == DateTimeKind.Unspecified
                    ? DateTime.SpecifyKind(endDate.Value, DateTimeKind.Utc)
                    : endDate.Value.ToUniversalTime();
                queryParams.Add($"endDate={Uri.EscapeDataString(utcEndDate.ToString("yyyy-MM-ddTHH:mm:ss.fffZ"))}");
            }

            if (pagination != null)
            {
                queryParams.Add($"pageNumber={pagination.PageNumber}");
                queryParams.Add($"pageSize={pagination.PageSize}");
            }
            queryParams.Add($"invoiceNumber={invoiceNumber}");
            var queryString = string.Join("&", queryParams);
            var url = $"api/Sales?{queryString}";

            return await _httpService.GetAsync<PagedResponse<SaleDto>>(url);
        }

        public async Task<SaleDto> GetByIdAsync(int saleId)
        {
            return await _httpService.GetAsync<SaleDto>($"api/Sales/{saleId}");
        }

        public async Task UpdateAsync(SaleDto sale)
        {
            await _httpService.PutAsync($"api/Sales/{sale.Id}", sale);
        }

        public async Task<int> GetLastSaleIdAsync()
        {
            try
            {
                return await _httpService.GetAsync<int>("api/Sales/last-id");
            }
            catch
            {
                return 0; // Return 0 if there's an error or no sales exist
            }
        }

        public async Task<List<DailySalesReportDto>> GetDailySalesReport(DateTime? startDate, DateTime? endDate)
        {
            var queryParams = new List<string>();

            if (startDate.HasValue)
            {
                var utcStartDate = startDate.Value.Kind == DateTimeKind.Unspecified
                    ? DateTime.SpecifyKind(startDate.Value, DateTimeKind.Utc)
                    : startDate.Value.ToUniversalTime();
                queryParams.Add($"startDate={Uri.EscapeDataString(utcStartDate.ToString("yyyy-MM-ddTHH:mm:ss.fffZ"))}");
            }
            if (endDate.HasValue)
            {
                var utcEndDate = endDate.Value.Kind == DateTimeKind.Unspecified
                    ? DateTime.SpecifyKind(endDate.Value, DateTimeKind.Utc)
                    : endDate.Value.ToUniversalTime();
                queryParams.Add($"endDate={Uri.EscapeDataString(utcEndDate.ToString("yyyy-MM-ddTHH:mm:ss.fffZ"))}");
            }

            var queryString = string.Join("&", queryParams);
            var url = $"api/Sales/get-daily-sales-report?{queryString}";

            return await _httpService.GetAsync<List<DailySalesReportDto>>(url);
        }

        public async Task<DailySalesSummaryDto> GetDailySalesSummary(DateTime? startDate, DateTime? endDate)
        {
            var queryParams = new List<string>();

            if (startDate.HasValue)
            {
                var utcStartDate = startDate.Value.Kind == DateTimeKind.Unspecified
                    ? DateTime.SpecifyKind(startDate.Value, DateTimeKind.Utc)
                    : startDate.Value.ToUniversalTime();
                queryParams.Add($"startDate={Uri.EscapeDataString(utcStartDate.ToString("yyyy-MM-ddTHH:mm:ss.fffZ"))}");
            }
            if (endDate.HasValue)
            {
                var utcEndDate = endDate.Value.Kind == DateTimeKind.Unspecified
                    ? DateTime.SpecifyKind(endDate.Value, DateTimeKind.Utc)
                    : endDate.Value.ToUniversalTime();
                queryParams.Add($"endDate={Uri.EscapeDataString(utcEndDate.ToString("yyyy-MM-ddTHH:mm:ss.fffZ"))}");
            }

            var queryString = string.Join("&", queryParams);
            var url = $"api/Sales/get-daily-sales-summary?{queryString}";

            return await _httpService.GetAsync<DailySalesSummaryDto>(url);
        }

        public async Task<List<SalesByProductReportDto>> GetSalesByProductReport(DateTime? startDate, DateTime? endDate)
        {
            var queryParams = new List<string>();

            if (startDate.HasValue)
            {
                var utcStartDate = startDate.Value.Kind == DateTimeKind.Unspecified
                    ? DateTime.SpecifyKind(startDate.Value, DateTimeKind.Utc)
                    : startDate.Value.ToUniversalTime();
                queryParams.Add($"startDate={Uri.EscapeDataString(utcStartDate.ToString("yyyy-MM-ddTHH:mm:ss.fffZ"))}");
            }
            if (endDate.HasValue)
            {
                var utcEndDate = endDate.Value.Kind == DateTimeKind.Unspecified
                    ? DateTime.SpecifyKind(endDate.Value, DateTimeKind.Utc)
                    : endDate.Value.ToUniversalTime();
                queryParams.Add($"endDate={Uri.EscapeDataString(utcEndDate.ToString("yyyy-MM-ddTHH:mm:ss.fffZ"))}");
            }

            var queryString = string.Join("&", queryParams);
            var url = $"api/Sales/get-sales-by-product-report?{queryString}";

            return await _httpService.GetAsync<List<SalesByProductReportDto>>(url);
        }

        public async Task<List<SalesByCategoryReportDto>> GetSalesByCategoryReport(DateTime? startDate, DateTime? endDate)
        {
            var queryParams = new List<string>();

            if (startDate.HasValue)
            {
                var utcStartDate = startDate.Value.Kind == DateTimeKind.Unspecified
                    ? DateTime.SpecifyKind(startDate.Value, DateTimeKind.Utc)
                    : startDate.Value.ToUniversalTime();
                queryParams.Add($"startDate={Uri.EscapeDataString(utcStartDate.ToString("yyyy-MM-ddTHH:mm:ss.fffZ"))}");
            }
            if (endDate.HasValue)
            {
                var utcEndDate = endDate.Value.Kind == DateTimeKind.Unspecified
                    ? DateTime.SpecifyKind(endDate.Value, DateTimeKind.Utc)
                    : endDate.Value.ToUniversalTime();
                queryParams.Add($"endDate={Uri.EscapeDataString(utcEndDate.ToString("yyyy-MM-ddTHH:mm:ss.fffZ"))}");
            }

            var queryString = string.Join("&", queryParams);
            var url = $"api/Sales/get-sales-by-category-report?{queryString}";

            return await _httpService.GetAsync<List<SalesByCategoryReportDto>>(url);
        }
    }
}
