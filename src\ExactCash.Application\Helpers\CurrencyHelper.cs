using System.Globalization;

namespace ExactCash.Application.Helpers
{
    public static class CurrencyHelper
    {
        private static string _currencySymbol = "ج.م";
        private static string _currencyPosition = "after";
        private static string _cultureCode = "ar-EG";

        /// <summary>
        /// Sets the currency configuration
        /// </summary>
        public static void SetCurrencyConfiguration(string symbol, string position, string cultureCode)
        {
            _currencySymbol = symbol ?? "ج.م";
            _currencyPosition = position ?? "after";
            _cultureCode = cultureCode ?? "ar-EG";
        }

        /// <summary>
        /// Formats a decimal value as currency with the configured symbol
        /// </summary>
        public static string FormatCurrency(decimal amount)
        {
            try
            {
                var culture = new CultureInfo(_cultureCode);
                var formattedAmount = amount.ToString("N2", culture);
                
                return _currencyPosition.ToLower() == "before" 
                    ? $"{_currencySymbol} {formattedAmount}"
                    : $"{formattedAmount} {_currencySymbol}";
            }
            catch
            {
                // Fallback formatting
                return _currencyPosition.ToLower() == "before" 
                    ? $"{_currencySymbol} {amount:N2}"
                    : $"{amount:N2} {_currencySymbol}";
            }
        }

        /// <summary>
        /// Formats a decimal value as currency for RDLC reports
        /// </summary>
        public static string FormatCurrencyForReport(decimal amount)
        {
            return FormatCurrency(amount);
        }

        /// <summary>
        /// Gets the currency symbol
        /// </summary>
        public static string GetCurrencySymbol()
        {
            return _currencySymbol;
        }

        /// <summary>
        /// Gets the currency format string for string formatting
        /// </summary>
        public static string GetCurrencyFormatString()
        {
            return _currencyPosition.ToLower() == "before" 
                ? $"{_currencySymbol} {{0:N2}}"
                : $"{{0:N2}} {_currencySymbol}";
        }

        /// <summary>
        /// Gets the culture code
        /// </summary>
        public static string GetCultureCode()
        {
            return _cultureCode;
        }

        /// <summary>
        /// Creates a custom NumberFormatInfo with Egyptian Pound settings
        /// </summary>
        public static NumberFormatInfo GetEgyptianCurrencyFormat()
        {
            var format = new NumberFormatInfo
            {
                CurrencySymbol = _currencySymbol,
                CurrencyDecimalDigits = 2,
                CurrencyDecimalSeparator = ".",
                CurrencyGroupSeparator = ",",
                CurrencyGroupSizes = new int[] { 3 },
                CurrencyPositivePattern = _currencyPosition.ToLower() == "before" ? 0 : 1, // 0: $n, 1: n$
                CurrencyNegativePattern = _currencyPosition.ToLower() == "before" ? 1 : 5   // 1: -$n, 5: -n$
            };
            return format;
        }

        /// <summary>
        /// Formats currency for DataGrid display
        /// </summary>
        public static string FormatForDataGrid(decimal amount)
        {
            return FormatCurrency(amount);
        }

        /// <summary>
        /// Gets the format string for WPF binding
        /// </summary>
        public static string GetWpfFormatString()
        {
            return _currencyPosition.ToLower() == "before" 
                ? $"{_currencySymbol} {{0:N2}}"
                : $"{{0:N2}} {_currencySymbol}";
        }
    }
}
