using ExactCash.WASM.Application.DTOs.Common;
#nullable disable

namespace ExactCash.WASM.Application.DTOs
{
    public class CustomerCategoryDto : BaseEntityDto
    {
        /// <summary>
        /// The name of the customer category.
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// The description of the customer category.
        /// </summary>
        public string Description { get; set; }

        /// <summary>
        /// IsActive
        /// </summary>
        public bool IsActive { get; set; }

        public CustomerCategoryDto()
        {
        }

        public CustomerCategoryDto(string name, string description)
        {
            Name = name;
            Description = description;
            CreationDate = DateTime.UtcNow;
        }
    }
}
