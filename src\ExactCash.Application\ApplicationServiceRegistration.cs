﻿using ExactCash.Application.Contracts;
using ExactCash.Application.Interfaces;
using ExactCash.Application.Services;
using Microsoft.Extensions.DependencyInjection;

namespace ExactCash.Application
{
    public static class ApplicationServiceRegistration
    {
        public static IServiceCollection AddApplicationServices(this IServiceCollection services)
        {
            #region automapper
            services.AddAutoMapper(AppDomain.CurrentDomain.GetAssemblies());
            #endregion automapper

            #region Services
            services.AddScoped<ISystemConfigurationService, SystemConfigurationService>();
            services.AddScoped<ICategoryService, CategoryService>();
            services.AddScoped<IProductService, ProductService>();
            services.AddScoped<IUnitService, UnitService>();
            services.AddScoped<ICustomerService, CustomerService>();
            services.AddScoped<ICustomerCategoryService, CustomerCategoryService>();
            services.AddScoped<IDiscountService, DiscountService>();
            services.AddScoped<ISupplierService, SupplierService>();
            services.AddScoped<ISaleService, SaleService>();
            services.AddScoped<IBrandService, BrandService>();
            services.AddScoped<IUserService, UserService>();
            services.AddScoped<IAuthService, AuthService>();
            services.AddScoped<IRoleService, RoleService>();
            services.AddScoped<IRoleClaimService, RoleClaimService>();
            services.AddScoped<IPurchaseService, PurchaseService>();
            services.AddScoped<IExpenseCategoryService, ExpenseCategoryService>();
            services.AddScoped<IExpenseService, ExpenseService>();
            services.AddScoped<IReportService, ReportService>();
            services.AddScoped<ISupplierCategoryService, SupplierCategoryService>();
            services.AddScoped<IPaymentService, PaymentService>();
            services.AddScoped<IInventoryService, InventoryService>();
            #endregion

            return services;
        }
    }
}
