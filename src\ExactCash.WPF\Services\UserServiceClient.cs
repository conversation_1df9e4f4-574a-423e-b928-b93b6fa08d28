﻿using ExactCash.Application.DTOs;
using ExactCash.Application.DTOs.Common;
using ExactCash.Application.Responses;
using Microsoft.AspNetCore.Identity;

namespace ExactCash.WPF.Services
{
    public class UserServiceClient : IUserServiceClient
    {
        private readonly HttpService _httpService;

        public UserServiceClient(string baseUrl)
        {
            _httpService = new HttpService(baseUrl);
        }

        public async Task<PagedResponse<UserDto>> GetAllUsersAsync(string username, string email, string phoneNumber, PaginationFilter pagination)
        {
            var queryParams = new List<string>();
            if (!string.IsNullOrEmpty(username)) queryParams.Add($"username={Uri.EscapeDataString(username)}");
            if (!string.IsNullOrEmpty(email)) queryParams.Add($"email={Uri.EscapeDataString(email)}");
            if (!string.IsNullOrEmpty(phoneNumber)) queryParams.Add($"phoneNumber={Uri.EscapeDataString(phoneNumber)}");
            if (pagination != null)
            {
                queryParams.Add($"pageNumber={pagination.PageNumber}");
                queryParams.Add($"pageSize={pagination.PageSize}");
            }
            var queryString = string.Join("&", queryParams);
            var url = $"api/Users?{queryString}";
            return await _httpService.GetAsync<PagedResponse<UserDto>>(url);
        }

        public async Task<UserDto> GetUserByIdAsync(string id)
        {
            return await _httpService.GetAsync<UserDto>($"api/Users/<USER>");
        }

        public async Task<IdentityResult> CreateUserAsync(CreateUserDto userDto)
        {
            return await _httpService.PostAsync<IdentityResult>("api/Users", userDto);
        }

        public async Task<IdentityResult> UpdateUserAsync(string id, UpdateUserDto userDto)
        {
            return await _httpService.PutAsync<IdentityResult>($"api/Users/<USER>", userDto);
        }

        public async Task<IdentityResult> DeleteUserAsync(string id)
        {
             return await _httpService.DeleteAsync<IdentityResult>($"api/Users/<USER>");
        }

        public async Task<IdentityResult> LockUserAsync(string id)
        {
           return await _httpService.PostAsync<IdentityResult>($"api/Users/<USER>/lock", null);
        }

        public async Task<IdentityResult> UnlockUserAsync(string id)
        {
            return await _httpService.PostAsync<IdentityResult>($"api/Users/<USER>/unlock", null);
        }

        public async Task<IdentityResult> ChangePasswordAsync(string id, ChangePasswordDto passwordDto)
        {
            return await _httpService.PostAsync<IdentityResult>($"api/Users/<USER>/change-password", passwordDto);
        }

        public async Task<IdentityResult> ResetPasswordAsync(ResetPasswordDto resetPasswordDto)
        {
           return await _httpService.PostAsync<IdentityResult>($"api/Users/<USER>", resetPasswordDto);
        }

        public Task<IdentityResult> ConfirmEmailAsync(string id, string token)
        {
            throw new NotImplementedException();
        }

        public Task<string> GenerateEmailConfirmationTokenAsync(string id)
        {
            throw new NotImplementedException();
        }

        public Task<string> GeneratePasswordResetTokenAsync(string email)
        {
            throw new NotImplementedException();
        }
    }
}
