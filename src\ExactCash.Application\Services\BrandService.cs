using AutoMapper;
using ExactCash.Application.DTOs;
using ExactCash.Application.Interfaces;
using ExactCash.Domain.Entities;
using ExactCash.Infrastructure.Persistence;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace ExactCash.Application.Services
{
    public class BrandService : IBrandService
    {
        private readonly AppPostgreSQLDbContext _context;
        private readonly IMapper _mapper;
        private readonly ILogger<BrandService> _logger;

        public BrandService(AppPostgreSQLDbContext context, IMapper mapper, ILogger<BrandService> logger)
        {
            _context = context;
            _mapper = mapper;
            _logger = logger;
        }

        public async Task<BrandDto> GetBrandByIdAsync(int id)
        {
            var brand = await _context.Brands
                .FirstOrDefaultAsync(b => b.Id == id);

            if (brand == null)
                return null;

            return _mapper.Map<BrandDto>(brand);
        }

        public async Task<IEnumerable<BrandDto>> GetAllBrandsAsync()
        {
            var rtesult =  await _context.Brands.ToListAsync();
            return _mapper.Map<IEnumerable<BrandDto>>(rtesult);
        }

        public async Task<BrandDto> CreateBrandAsync(BrandDto brandDto)
        {
            var brand = new Brand
            {
                Name = brandDto.Name,
                Description = brandDto.Description
            };

            await _context.Brands.AddAsync(brand);
            await _context.SaveChangesAsync();

            return _mapper.Map<BrandDto>(brand);
        }

        public async Task UpdateBrandAsync(BrandDto brandDto)
        {
            var brand = await _context.Brands.FindAsync(brandDto.Id);

            if (brand == null)
                throw new KeyNotFoundException($"Brand with ID {brandDto.Id} not found.");

            brand.Name = brandDto.Name;
            brand.Description = brandDto.Description;
            brand.LastUpdatedDate = System.DateTime.UtcNow;

            _context.Entry(brand).State = EntityState.Modified;
            await _context.SaveChangesAsync();
        }

        public async Task DeleteBrandAsync(int id)
        {
            var brand = await _context.Brands
                .Include(b => b.Products)
                .FirstOrDefaultAsync(b => b.Id == id);

            if (brand == null)
                throw new KeyNotFoundException($"Brand with ID {id} not found.");

            if (brand.Products.Any())
                throw new InvalidOperationException("Cannot delete brand with associated products.");

            _context.Brands.Remove(brand);
            await _context.SaveChangesAsync();
        }
    }
}