using ExactCash.WPF.Commands;
using System.Windows.Input;
using System.Windows.Media;
#nullable disable

namespace ExactCash.WPF.ViewModels.Common
{
    public class NotificationViewModel : ViewModelBase
    {
        private string _message;
        private string _icon;
        private System.Windows.Media.Brush _backgroundColor;
        private bool _isVisible;

        public NotificationViewModel()
        {
            CloseCommand = new RelayCommand(Close);
            IsVisible = true;
        }

        public string Message
        {
            get => _message;
            set => SetProperty(ref _message, value);
        }

        public string Icon
        {
            get => _icon;
            set => SetProperty(ref _icon, value);
        }

        public System.Windows.Media.Brush BackgroundColor
        {
            get => _backgroundColor;
            set => SetProperty(ref _backgroundColor, value);
        }

        public bool IsVisible
        {
            get => _isVisible;
            set => SetProperty(ref _isVisible, value);
        }

        public ICommand CloseCommand { get; }

        public void ShowSuccess(string message)
        {
            Message = message;
            Icon = "✓";
            BackgroundColor = new SolidColorBrush(System.Windows.Media.Color.FromRgb(46, 204, 113));
            IsVisible = true;
        }

        public void ShowError(string message)
        {
            Message = message;
            Icon = "⚠";
            BackgroundColor = new SolidColorBrush(System.Windows.Media.Color.FromRgb(231, 76, 60));
            IsVisible = true;
        }

        public void ShowWarning(string message)
        {
            Message = message;
            Icon = "⚠";
            BackgroundColor = new SolidColorBrush(System.Windows.Media.Color.FromRgb(241, 196, 15));
            IsVisible = true;
        }

        public void ShowInfo(string message)
        {
            Message = message;
            Icon = "ℹ";
            BackgroundColor = new SolidColorBrush(System.Windows.Media.Color.FromRgb(52, 152, 219));
            IsVisible = true;
        }

        private void Close()
        {
            IsVisible = false;
        }
    }
}