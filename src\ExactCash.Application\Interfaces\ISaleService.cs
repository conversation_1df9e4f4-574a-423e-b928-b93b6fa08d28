﻿using ExactCash.Application.DTOs;
using ExactCash.Application.DTOs.Common;
using ExactCash.Application.Responses;

namespace ExactCash.Application.Interfaces
{
    public interface ISaleService
    {
        /// <summary>
        /// Creates a new sale record.
        /// </summary>
        /// <param name="sale">The sale object to create.</param>
        /// <returns>The created sale with its generated ID.</returns>
        Task<BaseResponse<string>> CreateAsync(SaleDto sale, string createdBy);

        /// <summary>
        /// Retrieves a sale by its unique identifier.
        /// </summary>
        /// <param name="saleId">The ID of the sale to retrieve.</param>
        /// <returns>The matching sale if found, otherwise null.</returns>
        Task<SaleDto> GetByIdAsync(int saleId);

        /// <summary>
        /// Retrieves all sales.
        /// </summary>
        /// <returns>A list of all sale records.</returns>
        Task<PagedResponse<SaleDto>> GetAllAsync(int? customerId, string invoiceNumber, DateTime? startDate, DateTime? endDate, PaginationFilter pagination);

        /// <summary>
        /// Updates an existing sale record.
        /// </summary>
        /// <param name="sale">The updated sale object.</param>
        /// <returns>The updated sale, or null if not found.</returns>
        Task UpdateAsync(SaleDto sale);

        /// <summary>
        /// Deletes a sale record by ID.
        /// </summary>
        /// <param name="saleId">The ID of the sale to delete.</param>
        /// <returns>True if deletion was successful, false otherwise.</returns>
        Task DeleteAsync(int saleId);

        /// <summary>
        /// GetLastSaleIdAsync
        /// </summary>
        /// <returns></returns>
        Task<int> GetLastSaleIdAsync();

        Task<List<DailySalesReportDto>> GetDailySalesReport(DateTime? startDate, DateTime? endDate);

        /// <summary>
        /// Gets daily sales summary with statistics
        /// </summary>
        Task<DailySalesSummaryDto> GetDailySalesSummary(DateTime? startDate, DateTime? endDate);

        /// <summary>
        /// Gets sales report grouped by product
        /// </summary>
        Task<List<SalesByProductReportDto>> GetSalesByProductReport(DateTime? startDate, DateTime? endDate);

        /// <summary>
        /// Gets sales report grouped by category
        /// </summary>
        Task<List<SalesByCategoryReportDto>> GetSalesByCategoryReport(DateTime? startDate, DateTime? endDate);
    }
}
