@using Microsoft.AspNetCore.Identity
@inject SignInManager<IdentityUser> SignInManager
@inject UserManager<IdentityUser> UserManager

@if (SignInManager.IsSignedIn(User))
{
        <div class="user-menu">
            <button class="btn btn-link dropdown-toggle" type="button" id="userMenuButton" data-bs-toggle="dropdown" aria-expanded="false">
                <i class="fas fa-user-circle"></i>
                <span>@User.Identity?.Name</span>
            </button>
            <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="userMenuButton">
                <li>
                    <a class="dropdown-item" asp-area="Identity" asp-page="/Account/Manage/Index">
                        <i class="fas fa-user"></i> Profile
                    </a>
                </li>
                <li>
                    <a class="dropdown-item" asp-area="Identity" asp-page="/Account/Manage/Email">
                        <i class="fas fa-envelope"></i> Email
                    </a>
                </li>
                <li>
                    <a class="dropdown-item" asp-area="Identity" asp-page="/Account/Manage/ChangePassword">
                        <i class="fas fa-key"></i> Change Password
                    </a>
                </li>
                <li><hr class="dropdown-divider"></li>
                <li>
                    <form class="form-inline" asp-area="Identity" asp-page="/Account/Logout" asp-route-returnUrl="@Url.Action("Index", "Home", new { area = "" })">
                        <button type="submit" class="dropdown-item text-danger">
                            <i class="fas fa-sign-out-alt"></i> Logout
                        </button>
                    </form>
                </li>
            </ul>
        </div>
}
else
{
        <div class="auth-buttons">
            <a class="btn btn-outline-primary me-2" asp-area="Identity" asp-page="/Account/Login">
                <i class="fas fa-sign-in-alt"></i> Login
            </a>
            <a class="btn btn-primary" asp-area="Identity" asp-page="/Account/Register">
                <i class="fas fa-user-plus"></i> Register
            </a>
        </div>
}

<style>
    .user-menu {
        position: relative;
    }

    .user-menu button {
        color: #2c3e50;
        text-decoration: none;
        display: flex;
        align-items: center;
        gap: 0.5rem;
        padding: 0.5rem 1rem;
    }

    .user-menu button:hover {
        color: #3498db;
    }

    .dropdown-menu {
        border: none;
        box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        border-radius: 8px;
        padding: 0.5rem;
    }

    .dropdown-item {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        padding: 0.5rem 1rem;
        color: #2c3e50;
        border-radius: 4px;
    }

    .dropdown-item:hover {
        background-color: #f8f9fa;
    }

    .dropdown-item.text-danger:hover {
        background-color: #fee2e2;
    }

    .auth-buttons {
        display: flex;
        gap: 0.5rem;
    }

    .btn {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        padding: 0.5rem 1rem;
        border-radius: 6px;
        font-weight: 500;
        transition: all 0.2s ease;
    }

    .btn-outline-primary {
        border-color: #3498db;
        color: #3498db;
    }

    .btn-outline-primary:hover {
        background-color: #3498db;
        color: white;
    }

    .btn-primary {
        background-color: #3498db;
        border-color: #3498db;
    }

    .btn-primary:hover {
        background-color: #2980b9;
        border-color: #2980b9;
    }
</style> 