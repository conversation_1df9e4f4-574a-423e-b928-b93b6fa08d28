using System.Windows;
using AutoMapper;
using ExactCash.WPF.Services;
using ExactCash.WPF.Services.Interfaces;
using ExactCash.WPF.ViewModels.Common;
using ExactCash.WPF.ViewModels.Sale;
using ExactCash.WPF.ViewModels.User;

namespace ExactCash.WPF.Views.User
{
    public partial class UserListView : Window
    {

        public UserListView(
        IUserServiceClient userServiceClient,
            IRoleServiceClient roleServiceClient
         )
        {
            DataContext = new UserListViewModel(userServiceClient, roleServiceClient);
            InitializeComponent();
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            this.Close();
        }

        private void MinimizeButton_Click(object sender, RoutedEventArgs e)
        {
            this.WindowState = WindowState.Minimized;
        }
    }
}