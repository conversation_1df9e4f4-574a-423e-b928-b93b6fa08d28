using ExactCash.Application.DTOs;
using ExactCash.Application.Interfaces;
using ExactCash.Application.Responses;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace ExactCash.API.Controllers
{
    /// <summary>
    /// API Controller for managing supplier categories.
    /// </summary>
    [ApiController]
    [Authorize]
    [Route("api/[controller]")]
    public class SupplierCategoryController : ControllerBase
    {
        private readonly ISupplierCategoryService _service;

        public SupplierCategoryController(ISupplierCategoryService service)
        {
            _service = service;
        }

        /// <summary>
        /// Gets a supplier category by its ID.
        /// </summary>
        /// <param name="id">The ID of the supplier category.</param>
        /// <returns>The supplier category.</returns>
        [HttpGet("{id}")]
        public async Task<ActionResult<SupplierCategoryDto>> GetById(int id)
        {
            try
            {
                var category = await _service.GetByIdAsync(id);
                if (category == null)
                {
                    return NotFound($"Supplier category with ID {id} not found.");
                }
                return Ok(category);
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"Internal server error: {ex.Message}");
            }
        }

        /// <summary>
        /// Gets all supplier categories.
        /// </summary>
        /// <returns>A list of all supplier categories.</returns>
        [HttpGet]
        public async Task<ActionResult<List<SupplierCategoryDto>>> GetAll()
        {
            try
            {
                var categories = await _service.GetAllAsync();
                return Ok(categories);
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"Internal server error: {ex.Message}");
            }
        }

        /// <summary>
        /// Gets all active supplier categories.
        /// </summary>
        /// <returns>A list of active supplier categories.</returns>
        [HttpGet("active")]
        public async Task<ActionResult<List<SupplierCategoryDto>>> GetActive()
        {
            try
            {
                var categories = await _service.GetActiveAsync();
                return Ok(categories);
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"Internal server error: {ex.Message}");
            }
        }

        /// <summary>
        /// Creates a new supplier category.
        /// </summary>
        /// <param name="categoryDto">The supplier category to create.</param>
        /// <returns>The created supplier category.</returns>
        [HttpPost]
        public async Task<ActionResult<BaseResponse<SupplierCategoryDto>>> Create([FromBody] SupplierCategoryDto categoryDto)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var result = await _service.CreateAsync(categoryDto);
                if (result.Success)
                {
                    return CreatedAtAction(nameof(GetById), new { id = result.Data.Id }, result);
                }
                return BadRequest(result);
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"Internal server error: {ex.Message}");
            }
        }

        /// <summary>
        /// Updates an existing supplier category.
        /// </summary>
        /// <param name="id">The ID of the supplier category to update.</param>
        /// <param name="categoryDto">The updated supplier category data.</param>
        /// <returns>The updated supplier category.</returns>
        [HttpPut("{id}")]
        public async Task<ActionResult<BaseResponse<SupplierCategoryDto>>> Update(int id, [FromBody] SupplierCategoryDto categoryDto)
        {
            try
            {
                if (id != categoryDto.Id)
                {
                    return BadRequest("ID mismatch between route and body.");
                }

                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var result = await _service.UpdateAsync(categoryDto);
                if (result.Success)
                {
                    return Ok(result);
                }
                return BadRequest(result);
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"Internal server error: {ex.Message}");
            }
        }

        /// <summary>
        /// Deletes a supplier category by its ID.
        /// </summary>
        /// <param name="id">The ID of the supplier category to delete.</param>
        /// <returns>No content if successful.</returns>
        [HttpDelete("{id}")]
        public async Task<IActionResult> Delete(int id)
        {
            try
            {
                await _service.DeleteAsync(id);
                return NoContent();
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"Internal server error: {ex.Message}");
            }
        }

        /// <summary>
        /// Soft deletes a supplier category by its ID.
        /// </summary>
        /// <param name="id">The ID of the supplier category to soft delete.</param>
        /// <returns>No content if successful.</returns>
        [HttpDelete("soft/{id}")]
        public async Task<IActionResult> SoftDelete(int id)
        {
            try
            {
                await _service.SoftDeleteAsync(id);
                return NoContent();
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"Internal server error: {ex.Message}");
            }
        }
    }
}
