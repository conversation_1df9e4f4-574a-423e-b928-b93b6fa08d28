using ExactCash.Application.DTOs;
using ExactCash.Application.Interfaces;
using ExactCash.Application.Responses;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Security.Claims;

namespace ExactCash.API.Controllers
{
    /// <summary>
    /// API Controller for payment operations
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class PaymentsController : ControllerBase
    {
        private readonly IPaymentService _paymentService;
        private readonly ILogger<PaymentsController> _logger;

        public PaymentsController(IPaymentService paymentService, ILogger<PaymentsController> logger)
        {
            _paymentService = paymentService;
            _logger = logger;
        }

        /// <summary>
        /// Collects a payment against outstanding sales
        /// </summary>
        /// <param name="paymentDto">Payment collection details</param>
        /// <returns>Success response with payment ID</returns>
        [HttpPost("collect")]
        public async Task<IActionResult> CollectPayment([FromBody] PaymentCollectionDto paymentDto)
        {
            try
            {
                var userEmail = User.FindFirst(ClaimTypes.Email)?.Value ?? "System";
                var result = await _paymentService.CollectPaymentAsync(paymentDto, userEmail);

                return StatusCode(result.StatusCode, result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in CollectPayment endpoint");
                return StatusCode(500, ResponseHelper.Failure<int>(500, 0, "Internal server error"));
            }
        }

        /// <summary>
        /// Gets payment history for a customer
        /// </summary>
        /// <param name="customerId">Customer ID</param>
        /// <param name="fromDate">Start date filter</param>
        /// <param name="toDate">End date filter</param>
        /// <returns>List of payment history records</returns>
        [HttpGet("history/{customerId}")]
        public async Task<IActionResult> GetPaymentHistory(
            int customerId,
            [FromQuery] DateTime? fromDate = null,
            [FromQuery] DateTime? toDate = null)
        {
            try
            {
                var result = await _paymentService.GetPaymentHistoryAsync(customerId, fromDate, toDate);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting payment history for customer {CustomerId}", customerId);
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// Gets payment history for a specific sale
        /// </summary>
        /// <param name="saleId">Sale ID</param>
        /// <returns>List of payments for the sale</returns>
        [HttpGet("sale-history/{saleId}")]
        public async Task<IActionResult> GetSalePaymentHistory(int saleId)
        {
            try
            {
                var result = await _paymentService.GetSalePaymentHistoryAsync(saleId);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting payment history for sale {SaleId}", saleId);
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// Gets outstanding sales for a customer
        /// </summary>
        /// <param name="customerId">Customer ID</param>
        /// <returns>List of outstanding sales</returns>
        [HttpGet("outstanding/{customerId}")]
        public async Task<IActionResult> GetOutstandingSalesForCustomer(int customerId)
        {
            try
            {
                var result = await _paymentService.GetOutstandingSalesForCustomerAsync(customerId);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting outstanding sales for customer {CustomerId}", customerId);
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// Gets all outstanding sales with optional filters
        /// </summary>
        /// <param name="searchDto">Search filters</param>
        /// <returns>List of outstanding sales</returns>
        [HttpPost("outstanding-search")]
        public async Task<IActionResult> GetOutstandingSales([FromBody] CreditSalesSearchDto searchDto)
        {
            try
            {
                var result = await _paymentService.GetOutstandingSalesAsync(searchDto);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting outstanding sales with search criteria");
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// Gets customer credit limits and utilization
        /// </summary>
        /// <returns>List of customer credit limit information</returns>
        [HttpGet("credit-limits")]
        public async Task<IActionResult> GetCustomerCreditLimits()
        {
            try
            {
                var result = await _paymentService.GetCustomerCreditLimitsAsync();
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting customer credit limits");
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// Updates customer credit limit
        /// </summary>
        /// <param name="updateDto">Credit limit update details</param>
        /// <returns>Success response</returns>
        [HttpPut("credit-limit")]
        public async Task<IActionResult> UpdateCreditLimit([FromBody] CreditLimitUpdateDto updateDto)
        {
            try
            {
                var userEmail = User.FindFirst(ClaimTypes.Email)?.Value ?? "System";
                var result = await _paymentService.UpdateCreditLimitAsync(updateDto, userEmail);

                return StatusCode(result.StatusCode, result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating credit limit for customer {CustomerId}", updateDto.CustomerId);
                return StatusCode(500, ResponseHelper.Failure<bool>(500, false, "Internal server error"));
            }
        }

        /// <summary>
        /// Gets outstanding payments report
        /// </summary>
        /// <param name="fromDate">Start date</param>
        /// <param name="toDate">End date</param>
        /// <returns>Outstanding payments report data</returns>
        [HttpGet("outstanding-report")]
        public async Task<IActionResult> GetOutstandingPaymentsReport(
            [FromQuery] DateTime? fromDate,
            [FromQuery] DateTime? toDate)
        {
            try
            {
                var result = await _paymentService.GetOutstandingPaymentsReportAsync(fromDate, toDate);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating outstanding payments report");
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// Validates if a customer can make a credit purchase
        /// </summary>
        /// <param name="customerId">Customer ID</param>
        /// <param name="amount">Purchase amount</param>
        /// <returns>Validation result with available credit info</returns>
        [HttpGet("validate-credit/{customerId}")]
        public async Task<IActionResult> ValidateCreditPurchase(int customerId, [FromQuery] decimal amount)
        {
            try
            {
                var result = await _paymentService.ValidateCreditPurchaseAsync(customerId, amount);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error validating credit purchase for customer {CustomerId}", customerId);
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// Gets payment methods available in the system
        /// </summary>
        /// <returns>List of payment methods</returns>
        [HttpGet("payment-methods")]
        public async Task<IActionResult> GetPaymentMethods()
        {
            try
            {
                var result = await _paymentService.GetPaymentMethodsAsync();
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting payment methods");
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// Generates payment receipt
        /// </summary>
        /// <param name="paymentId">Payment ID</param>
        /// <returns>Receipt data for printing</returns>
        [HttpGet("receipt/{paymentId}")]
        public async Task<IActionResult> GeneratePaymentReceipt(int paymentId)
        {
            try
            {
                var result = await _paymentService.GeneratePaymentReceiptAsync(paymentId);

                if (result.PaymentId == 0)
                {
                    return NotFound("Payment not found");
                }

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating payment receipt for payment {PaymentId}", paymentId);
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// Gets credit payment history from CreditPayment table
        /// </summary>
        /// <param name="searchDto">Search criteria for filtering payment history</param>
        /// <returns>List of credit payment history records</returns>
        [HttpPost("credit-payment-history")]
        public async Task<IActionResult> GetCreditPaymentHistory([FromBody] CreditPaymentSearchDto searchDto)
        {
            try
            {
                if (searchDto == null)
                {
                    return BadRequest("Search criteria is required");
                }

                var result = await _paymentService.GetCreditPaymentHistoryAsync(searchDto);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting credit payment history with search criteria: {@SearchDto}", searchDto);
                return StatusCode(500, "Internal server error");
            }
        }
    }
}
