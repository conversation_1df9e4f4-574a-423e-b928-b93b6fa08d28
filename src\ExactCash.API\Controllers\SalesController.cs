using ExactCash.Application.DTOs;
using ExactCash.Application.DTOs.Common;
using ExactCash.Application.Interfaces;
using ExactCash.Application.Responses;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Security.Claims;
using ZXing;
#nullable disable

namespace ExactCash.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class SalesController : ControllerBase
    {
        private readonly ISaleService _saleService;

        public SalesController(ISaleService saleService)
        {
            _saleService = saleService;
        }

        [HttpGet]
        public async Task<IActionResult> GetAllSales(
            [FromQuery] int? customerId,
            [FromQuery] string invoiceNumber,
            [FromQuery] DateTime? startDate,
            [FromQuery] DateTime? endDate,
            [FromQuery] PaginationFilter pagination)
        {
            var result = await _saleService.GetAllAsync(customerId, invoiceNumber, startDate, endDate, pagination);
            return Ok(result);
        }


        [HttpGet("get-daily-sales-report")]
        public async Task<IActionResult> GetDailySalesReport(
        [FromQuery] DateTime? startDate,
        [FromQuery] DateTime? endDate)
        {
            var result = await _saleService.GetDailySalesReport(startDate, endDate);
            return Ok(result);
        }

        [HttpGet("get-daily-sales-summary")]
        public async Task<IActionResult> GetDailySalesSummary(
        [FromQuery] DateTime? startDate,
        [FromQuery] DateTime? endDate)
        {
            var result = await _saleService.GetDailySalesSummary(startDate, endDate);
            return Ok(result);
        }

        [HttpGet("get-sales-by-product-report")]
        public async Task<IActionResult> GetSalesByProductReport(
        [FromQuery] DateTime? startDate,
        [FromQuery] DateTime? endDate)
        {
            var result = await _saleService.GetSalesByProductReport(startDate, endDate);
            return Ok(result);
        }

        [HttpGet("get-sales-by-category-report")]
        public async Task<IActionResult> GetSalesByCategoryReport(
        [FromQuery] DateTime? startDate,
        [FromQuery] DateTime? endDate)
        {
            var result = await _saleService.GetSalesByCategoryReport(startDate, endDate);
            return Ok(result);
        }


        [HttpGet("{id}")]
        public async Task<IActionResult> GetSale(int id)
        {
            var sale = await _saleService.GetByIdAsync(id);
            if (sale == null)
                return NotFound();

            return Ok(sale);
        }


        [HttpGet("last-id")]
        public async Task<IActionResult> GetLastId()
        {
            return Ok(await _saleService.GetLastSaleIdAsync());
        }

        [HttpPost]
        public async Task<IActionResult> CreateSale([FromBody] SaleDto saleDto)
        {
            var userEmail = User.FindFirst(ClaimTypes.Email)?.Value;
            var createdSale = await _saleService.CreateAsync(saleDto, userEmail);
            return StatusCode(createdSale.StatusCode, createdSale);
        }

        [HttpPut("{id}")]
        public async Task<IActionResult> UpdateSale(int id, [FromBody] SaleDto saleDto)
        {
            if (id != saleDto.Id)
                return BadRequest("ID mismatch");

            try
            {
                await _saleService.UpdateAsync(saleDto);
                return Ok();
            }
            catch (KeyNotFoundException)
            {
                return NotFound();
            }
        }

        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteSale(int id)
        {
            try
            {
                await _saleService.DeleteAsync(id);
                return NoContent();
            }
            catch (KeyNotFoundException)
            {
                return NotFound();
            }
        }
    }
}