﻿#nullable disable
namespace ExactCash.Application.Responses
{
    /// <summary>
    /// PagedResponse
    /// </summary>
    /// <typeparam name="T"></typeparam>
    public class PagedResponse<T>
    {
        /// <summary>
        /// The actual data for the current page
        /// </summary>
        public List<T> Data { get; set; }
        /// <summary>
        /// page number
        /// </summary>
        public int PageNumber { get; set; }
        /// <summary>
        /// page size
        /// </summary>
        public int PageSize { get; set; }
        /// <summary>
        /// Total number of items
        /// </summary>
        public int TotalItems { get; set; }
        /// <summary>
        /// Total number of pages
        /// </summary>
        public int TotalPages { get; set; }
        /// <summary>
        /// HasNextPage
        /// </summary>
        public bool HasNextPage => PageNumber < TotalPages;
        /// <summary>
        /// HasPreviousPage
        /// </summary>
        public bool HasPreviousPage => PageNumber > 1;
        /// <summary>
        /// ctor
        /// </summary>
        /// <param name="data"></param>
        /// <param name="pageNumber"></param>
        /// <param name="pageSize"></param>
        /// <param name="totalItems"></param>
        public PagedResponse(List<T> data, int pageNumber, int pageSize, int totalItems)
        {
            Data = data;
            PageNumber = pageNumber;
            PageSize = pageSize;
            TotalItems = totalItems;
            TotalPages = (int)Math.Ceiling(totalItems / (double)pageSize);
        }
    }
}
