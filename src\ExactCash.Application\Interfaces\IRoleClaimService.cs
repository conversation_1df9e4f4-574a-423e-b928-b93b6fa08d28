using ExactCash.Application.DTOs;
#nullable disable

namespace ExactCash.Application.Interfaces
{
    public interface IRoleClaimService
    {
        /// <summary>
        /// Retrieves a role claim by its unique identifier.
        /// </summary>
        /// <param name="id">The unique identifier of the role claim.</param>
        /// <returns>The role claim if found, otherwise null.</returns>
        Task<RoleClaimDto> GetRoleClaimByIdAsync(int id);

        /// <summary>
        /// Retrieves all claims associated with a specific role.
        /// </summary>
        /// <param name="roleId">The unique identifier of the role.</param>
        /// <returns>A list of role claims for the specified role.</returns>
        Task<List<RoleClaimDto>> GetRoleClaimsByRoleIdAsync(string roleId);

        /// <summary>
        /// Creates a new role claim.
        /// </summary>
        /// <param name="roleClaimDto">The role claim data to create.</param>
        /// <returns>The created role claim if successful, otherwise null.</returns>
        Task<RoleClaimDto> CreateRoleClaimAsync(CreateRoleClaimDto roleClaimDto);

        /// <summary>
        /// Updates an existing role claim.
        /// </summary>
        /// <param name="id">The unique identifier of the role claim to update.</param>
        /// <param name="roleClaimDto">The updated role claim data.</param>
        /// <returns>The updated role claim if successful, otherwise null.</returns>
        Task<RoleClaimDto> UpdateRoleClaimAsync(int id, UpdateRoleClaimDto roleClaimDto);

        /// <summary>
        /// Deletes a role claim by its unique identifier.
        /// </summary>
        /// <param name="id">The unique identifier of the role claim to delete.</param>
        /// <returns>True if the deletion was successful, otherwise false.</returns>
        Task<bool> DeleteRoleClaimAsync(int id);

        /// <summary>
        /// Adds multiple claims to a role in a single operation.
        /// </summary>
        /// <param name="roleClaimsDto">The role claims data containing the role ID and list of claims to add.</param>
        /// <returns>True if all claims were added successfully, otherwise false.</returns>
        Task<bool> AddClaimsToRoleAsync(RoleClaimsDto roleClaimsDto);

        /// <summary>
        /// Removes multiple claims from a role in a single operation.
        /// </summary>
        /// <param name="roleClaimsDto">The role claims data containing the role ID and list of claims to remove.</param>
        /// <returns>True if all claims were removed successfully, otherwise false.</returns>
        Task<bool> RemoveClaimsFromRoleAsync(RoleClaimsDto roleClaimsDto);

        /// <summary>
        /// Retrieves a list of all unique claim types used in the system.
        /// </summary>
        /// <returns>A list of unique claim types.</returns>
        Task<List<string>> GetRoleClaimTypesAsync();

        /// <summary>
        /// Retrieves a list of all unique claim values for a specific claim type.
        /// </summary>
        /// <param name="claimType">The claim type to get values for.</param>
        /// <returns>A list of unique claim values for the specified claim type.</returns>
        Task<List<string>> GetRoleClaimValuesAsync(string claimType);

        /// <summary>
        /// Checks if a specific role has a particular claim.
        /// </summary>
        /// <param name="roleId">The unique identifier of the role.</param>
        /// <param name="claimType">The type of claim to check.</param>
        /// <param name="claimValue">The value of the claim to check.</param>
        /// <returns>True if the role has the specified claim, otherwise false.</returns>
        Task<bool> HasClaimAsync(string roleId, string claimType, string claimValue);
    }
}