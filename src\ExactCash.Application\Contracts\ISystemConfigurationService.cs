﻿using ExactCash.Domain.Entities;

namespace ExactCash.Application.Contracts
{
    /// <summary>
    /// Interface for handling CRUD operations on system configurations.
    /// </summary>
    public interface ISystemConfigurationService
    {
        /// <summary>
        /// Creates a new system configuration.
        /// </summary>
        Task CreateAsync(SystemConfiguration config);

        /// <summary>
        /// Gets all system configurations.
        /// </summary>
        Task<IEnumerable<SystemConfiguration>> GetAllAsync();

        /// <summary>
        /// Gets a single system configuration by its key.
        /// </summary>
        Task<SystemConfiguration?> GetByKeyAsync(string key);

        /// <summary>
        /// Updates an existing system configuration.
        /// </summary>
        Task UpdateAsync(SystemConfiguration config);

        /// <summary>
        /// Deletes a system configuration by its ID.
        /// </summary>
        Task DeleteAsync(int id);
    }
}
