using ExactCash.Domain.Common;
#nullable disable

namespace ExactCash.Domain.Entities
{
    /// <summary>
    /// Represents a category for suppliers to organize them by type or business area.
    /// </summary>
    public class SupplierCategory : BaseEntity
    {
        /// <summary>
        /// The name of the supplier category.
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// The description of the supplier category.
        /// </summary>
        public string Description { get; set; }

        /// <summary>
        /// Indicates whether the supplier category is active.
        /// </summary>
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// Indicates whether the supplier category is deleted.
        /// </summary>
        public bool IsDeleted { get; set; } = false;

        /// <summary>
        /// Navigation property for all suppliers in this category.
        /// </summary>
        public ICollection<Supplier> Suppliers { get; set; } = new List<Supplier>();
    }
}
