using System.Windows;
using ExactCash.WPF.Controls;

namespace ExactCash.WPF.Helpers
{
    public static class BootstrapMessageBoxHelper
    {
        public static bool? Show(string message, string title = "رسالة", Window owner = null)
        {
            var messageBox = new BootstrapMessageBox
            {
                Title = title,
                Content = message,
                Owner = owner ?? System.Windows.Application.Current.MainWindow,
                WindowStartupLocation = WindowStartupLocation.CenterOwner
            };

            return messageBox.ShowDialog();
        }

        public static void ShowError(string message, string title = "خطأ", Window owner = null)
        {
            var messageBox = new BootstrapMessageBox
            {
                Title = title,
                Content = message,
                Owner = owner ?? System.Windows.Application.Current.MainWindow,
                WindowStartupLocation = WindowStartupLocation.CenterOwner
            };

            messageBox.ShowDialog();
        }

        public static void ShowSuccess(string message, string title = "نجاح", Window owner = null)
        {
            var messageBox = new BootstrapMessageBox
            {
                Title = title,
                Content = message,
                Owner = owner ?? System.Windows.Application.Current.MainWindow,
                WindowStartupLocation = WindowStartupLocation.CenterOwner
            };

            messageBox.ShowDialog();
        }
    }
}