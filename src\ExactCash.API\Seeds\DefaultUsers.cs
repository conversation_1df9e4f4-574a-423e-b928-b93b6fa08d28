using Microsoft.AspNetCore.Identity;

namespace ExactCash.API.Seeds
{
    public static class DefaultUsers
    {
        public static async Task SeedAsync(
            UserManager<IdentityUser> userManager,
            RoleManager<IdentityRole> roleManager,
            ILogger logger)
        {
            try
            {
                var defaultUsers = new List<(string Email, string UserName, string Password, string Role)>
                {
                    ("<EMAIL>", "admin", "Admin@123", "Admin"),
                    ("<EMAIL>", "manager", "Manager@123", "Manager"),
                    ("<EMAIL>", "cashier", "Cashier@123", "Cashier"),
                    ("<EMAIL>", "accountant", "Accountant@123", "Accountant")
                };

                foreach (var (email, userName, password, role) in defaultUsers)
                {
                    if (await userManager.FindByEmailAsync(email) == null)
                    {
                        var user = new IdentityUser
                        {
                            UserName = userName,
                            Email = email,
                            EmailConfirmed = true
                        };

                        var result = await userManager.CreateAsync(user, password);
                        if (result.Succeeded)
                        {
                            logger.LogInformation("User {UserName} created successfully", userName);

                            // Assign role to user
                            if (await roleManager.RoleExistsAsync(role))
                            {
                                await userManager.AddToRoleAsync(user, role);
                                logger.LogInformation("Role {Role} assigned to user {UserName}", role, userName);
                            }
                            else
                            {
                                logger.LogWarning("Role {Role} does not exist for user {UserName}", role, userName);
                            }
                        }
                        else
                        {
                            logger.LogError("Failed to create user {UserName}: {Errors}",
                                userName, string.Join(", ", result.Errors.Select(e => e.Description)));
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "An error occurred while seeding users");
            }
        }
    }
}