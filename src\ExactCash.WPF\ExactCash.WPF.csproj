﻿<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<OutputType>WinExe</OutputType>
		<TargetFramework>net8.0-windows</TargetFramework>
		<Nullable>enable</Nullable>
		<UseWPF>true</UseWPF>
         <UseWindowsForms>true</UseWindowsForms>
		<ImplicitUsings>enable</ImplicitUsings>
		<PlatformTarget>x64</PlatformTarget>
		<ApplicationIcon>Assets\Images\bill_icon.ico</ApplicationIcon>
		<Platforms>AnyCPU;x64</Platforms>
		<!-- Self-contained deployment settings -->
		<SelfContained>false</SelfContained>
		<PublishSingleFile>false</PublishSingleFile>
		<IncludeNativeLibrariesForSelfExtract>true</IncludeNativeLibrariesForSelfExtract>
	</PropertyGroup>

	<ItemGroup>
		<Compile Remove="ViewModels\Sale\SaleItem.cs" />
	</ItemGroup>

	<ItemGroup>
	  <Content Include="Assets\Images\bill_icon.ico" />
	</ItemGroup>

	<ItemGroup>
		<PackageReference Include="Microsoft.Extensions.Configuration" Version="8.0.2" />
		<PackageReference Include="Microsoft.Extensions.Configuration.Json" Version="8.0.2" />
		<PackageReference Include="Microsoft.Extensions.Configuration.Binder" Version="8.0.2" />
		<PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="8.0.1" />
		<PackageReference Include="Microsoft.Extensions.Logging" Version="8.0.1" />
		<PackageReference Include="Microsoft.Extensions.Logging.Console" Version="8.0.1" />
		<PackageReference Include="Microsoft.Extensions.Logging.Debug" Version="8.0.1" />
		<PackageReference Include="ReportViewerCore.WinForms" Version="15.1.26" />
		<PackageReference Include="System.Drawing.Primitives" Version="4.3.0" />
		<PackageReference Include="Microsoft.CodeAnalysis.Common" Version="4.8.0" />
	</ItemGroup>

	<ItemGroup>
		<ProjectReference Include="..\ExactCash.Application\ExactCash.Application.csproj">
			<Private>true</Private>
			<CopyLocal>true</CopyLocal>
		</ProjectReference>
	</ItemGroup>

	<ItemGroup>
		<None Update="appsettings.json">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</None>
		<None Update="DailySalesDataSet.xsd">
		  <Generator>MSDataSetGenerator</Generator>
		  <LastGenOutput>DailySalesDataSet.Designer.cs</LastGenOutput>
		</None>
		<None Update="Reports\CustomerAccountStatementReport.rdlc">
		  <CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="Reports\DailySalesReport.rdlc">
		  <CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="Reports\PurchaseOrderReport.rdlc">
		  <CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="Reports\SalesByCategoryReport.rdlc">
		  <CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="Reports\SalesByProductReport.rdlc">
		  <CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="Reports\SupplierAccountStatementReport.rdlc">
		  <CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
	</ItemGroup>

	<ItemGroup>
		<Folder Include="ViewModels\Configuration\" />
		<Folder Include="ViewModels\Sale\" />
		<Folder Include="Views\Sale\" />
	</ItemGroup>

	<ItemGroup>
	  <Compile Update="DailySalesDataSet.Designer.cs">
	    <DesignTime>True</DesignTime>
	    <AutoGen>True</AutoGen>
	    <DependentUpon>DailySalesDataSet.xsd</DependentUpon>
	  </Compile>
	</ItemGroup>

</Project>
