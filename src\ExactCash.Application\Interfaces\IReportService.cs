﻿using ExactCash.Application.DTOs;

namespace ExactCash.Application.Interfaces
{
    public interface IReportService
    {
        // Inventory Reports
        Task<List<InventoryReportDto>> GetInventoryReport();
        Task<List<LowStockReportDto>> GetLowStockReport();
        Task<List<StockMovementReportDto>> GetStockMovementReport(DateTime? startDate, DateTime? endDate);
        Task<InventorySummaryDto> GetInventorySummary();

        // Financial Reports
        Task<ProfitLossReportDto> GetProfitLossReport(DateTime? startDate, DateTime? endDate);
        Task<CashFlowReportDto> GetCashFlowReport(DateTime? startDate, DateTime? endDate);
        Task<List<PaymentMethodReportDto>> GetPaymentMethodReport(DateTime? startDate, DateTime? endDate);
        Task<List<TaxReportDto>> GetTaxReport(DateTime? startDate, DateTime? endDate);
        Task<List<ExpenseReportDto>> GetExpenseReport(DateTime? startDate, DateTime? endDate);
        Task<List<ExpenseSummaryDto>> GetExpenseSummaryReport(DateTime? startDate, DateTime? endDate);

        // Customer Reports
        Task<List<CustomerAnalysisReportDto>> GetCustomerAnalysisReport(DateTime? startDate, DateTime? endDate);
        Task<List<TopCustomersReportDto>> GetTopCustomersReport(DateTime? startDate, DateTime? endDate, int topCount = 10);
        Task<List<CustomerPurchaseHistoryDto>> GetCustomerPurchaseHistory(int customerId, DateTime? startDate, DateTime? endDate);
        Task<CustomerAccountStatementDto> GetCustomerAccountStatement(int customerId, DateTime? startDate, DateTime? endDate);

        // Performance Reports
        Task<List<HourlySalesReportDto>> GetHourlySalesReport(DateTime? date);
        Task<List<CashierPerformanceReportDto>> GetCashierPerformanceReport(DateTime? startDate, DateTime? endDate);
        Task<List<ProductPerformanceReportDto>> GetProductPerformanceReport(DateTime? startDate, DateTime? endDate);

        // Supplier Reports
        Task<List<SupplierPerformanceReportDto>> GetSupplierPerformanceReport(DateTime? startDate, DateTime? endDate);
        Task<List<SupplierPaymentStatusDto>> GetSupplierPaymentStatusReport();
        Task<SupplierAccountStatementDto> GetSupplierAccountStatement(int supplierId, DateTime? startDate, DateTime? endDate);

        // Audit Reports
        Task<List<TransactionLogDto>> GetTransactionLogReport(DateTime? startDate, DateTime? endDate);
        Task<List<SystemActivityDto>> GetSystemActivityReport(DateTime? startDate, DateTime? endDate);

        // Company Information
        Task<CompanyInfoDto> GetCompanyInfoAsync();
    }

    // Additional DTOs for Supplier and Audit Reports
    public class SupplierPerformanceReportDto
    {
        public int SupplierId { get; set; }
        public string SupplierName { get; set; }
        public decimal TotalPurchases { get; set; }
        public int OrderCount { get; set; }
        public decimal AverageOrderValue { get; set; }
        public decimal TotalPaid { get; set; }
        public decimal OutstandingAmount { get; set; }
        public DateTime LastOrderDate { get; set; }
        public int DaysSinceLastOrder { get; set; }
        public string PaymentReliability { get; set; }

        // Formatted properties for display
        public string FormattedTotalPurchases => $"{TotalPurchases:N2} ج.م";
        public string FormattedAverageOrderValue => $"{AverageOrderValue:N2} ج.م";
        public string FormattedTotalPaid => $"{TotalPaid:N2} ج.م";
        public string FormattedOutstandingAmount => $"{OutstandingAmount:N2} ج.م";

        public string ReliabilityColor => PaymentReliability switch
        {
            "ممتاز" => "#28A745", // Green
            "جيد" => "#17A2B8", // Blue
            "متوسط" => "#FFC107", // Yellow
            "ضعيف" => "#DC3545", // Red
            _ => "#6C757D"
        };

        public SupplierPerformanceReportDto()
        {

        }
    }

    public class SupplierPaymentStatusDto
    {
        public int SupplierId { get; set; }
        public string SupplierName { get; set; }
        public decimal TotalDue { get; set; }
        public decimal OverdueAmount { get; set; }
        public int OverdueDays { get; set; }
        public string PaymentStatus { get; set; }
        public DateTime LastPaymentDate { get; set; }
        public int PendingOrders { get; set; }

        // Formatted properties for display
        public string FormattedTotalDue => $"{TotalDue:N2} ج.م";
        public string FormattedOverdueAmount => $"{OverdueAmount:N2} ج.م";

        public string StatusColor => PaymentStatus switch
        {
            "مدفوع" => "#28A745", // Green
            "مستحق" => "#FFC107", // Yellow
            "متأخر" => "#DC3545", // Red
            _ => "#6C757D"
        };

        public SupplierPaymentStatusDto()
        {

        }
    }

    public class TransactionLogDto
    {
        public int LogId { get; set; }
        public DateTime TransactionDate { get; set; }
        public string TransactionType { get; set; }
        public string Reference { get; set; }
        public decimal Amount { get; set; }
        public string UserName { get; set; }
        public string Description { get; set; }
        public string Status { get; set; }

        // Formatted properties for display
        public string FormattedAmount => $"{Amount:N2} ج.م";

        public string StatusColor => Status switch
        {
            "مكتمل" => "#28A745", // Green
            "معلق" => "#FFC107", // Yellow
            "ملغي" => "#DC3545", // Red
            _ => "#6C757D"
        };

        public TransactionLogDto()
        {

        }
    }

    public class SystemActivityDto
    {
        public int ActivityId { get; set; }
        public DateTime ActivityDate { get; set; }
        public string UserName { get; set; }
        public string ActivityType { get; set; }
        public string Description { get; set; }
        public string IPAddress { get; set; }
        public string Module { get; set; }

        public string ActivityTypeColor => ActivityType switch
        {
            "تسجيل دخول" => "#28A745", // Green
            "تسجيل خروج" => "#17A2B8", // Blue
            "إنشاء" => "#28A745", // Green
            "تعديل" => "#FFC107", // Yellow
            "حذف" => "#DC3545", // Red
            _ => "#6C757D"
        };

        public SystemActivityDto()
        {

        }
    }
}
