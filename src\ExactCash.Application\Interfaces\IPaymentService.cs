using ExactCash.Application.DTOs;
using ExactCash.Application.Responses;

namespace ExactCash.Application.Interfaces
{
    /// <summary>
    /// Service interface for payment operations
    /// </summary>
    public interface IPaymentService
    {
        /// <summary>
        /// Collects a payment against outstanding sales
        /// </summary>
        /// <param name="paymentDto">Payment collection details</param>
        /// <param name="collectedBy">User who collected the payment</param>
        /// <returns>Success response with payment ID</returns>
        Task<BaseResponse<int>> CollectPaymentAsync(PaymentCollectionDto paymentDto, string collectedBy);

        /// <summary>
        /// Gets payment history for a customer
        /// </summary>
        /// <param name="customerId">Customer ID</param>
        /// <param name="fromDate">Start date filter</param>
        /// <param name="toDate">End date filter</param>
        /// <returns>List of payment history records</returns>
        Task<List<PaymentHistoryDto>> GetPaymentHistoryAsync(int customerId, DateTime? fromDate = null, DateTime? toDate = null);

        /// <summary>
        /// Gets payment history for a specific sale
        /// </summary>
        /// <param name="saleId">Sale ID</param>
        /// <returns>List of payments for the sale</returns>
        Task<List<PaymentHistoryDto>> GetSalePaymentHistoryAsync(int saleId);

        /// <summary>
        /// Gets outstanding sales for a customer
        /// </summary>
        /// <param name="customerId">Customer ID</param>
        /// <returns>List of outstanding sales</returns>
        Task<List<OutstandingSaleDto>> GetOutstandingSalesForCustomerAsync(int customerId);

        /// <summary>
        /// Gets all outstanding sales with optional filters
        /// </summary>
        /// <param name="searchDto">Search filters</param>
        /// <returns>List of outstanding sales</returns>
        Task<List<OutstandingSaleDto>> GetOutstandingSalesAsync(CreditSalesSearchDto searchDto);

        /// <summary>
        /// Gets customer credit limits and utilization
        /// </summary>
        /// <returns>List of customer credit limit information</returns>
        Task<List<CustomerCreditLimitDto>> GetCustomerCreditLimitsAsync();

        /// <summary>
        /// Updates customer credit limit
        /// </summary>
        /// <param name="updateDto">Credit limit update details</param>
        /// <param name="updatedBy">User who updated the limit</param>
        /// <returns>Success response</returns>
        Task<BaseResponse<bool>> UpdateCreditLimitAsync(CreditLimitUpdateDto updateDto, string updatedBy);

        /// <summary>
        /// Gets outstanding payments report
        /// </summary>
        /// <param name="fromDate">Start date</param>
        /// <param name="toDate">End date</param>
        /// <returns>Outstanding payments report data</returns>
        Task<OutstandingPaymentsReportDto> GetOutstandingPaymentsReportAsync(DateTime? fromDate, DateTime? toDate);

        /// <summary>
        /// Validates if a customer can make a credit purchase
        /// </summary>
        /// <param name="customerId">Customer ID</param>
        /// <param name="amount">Purchase amount</param>
        /// <returns>Validation result with available credit info</returns>
        Task<CreditValidationDto> ValidateCreditPurchaseAsync(int customerId, decimal amount);

        /// <summary>
        /// Gets payment methods available in the system
        /// </summary>
        /// <returns>List of payment methods</returns>
        Task<List<string>> GetPaymentMethodsAsync();

        /// <summary>
        /// Generates payment receipt
        /// </summary>
        /// <param name="paymentId">Payment ID</param>
        /// <returns>Receipt data for printing</returns>
        Task<PaymentReceiptDto> GeneratePaymentReceiptAsync(int paymentId);

        /// <summary>
        /// Gets credit payment history from CreditPayment table
        /// </summary>
        /// <param name="searchDto">Search criteria</param>
        /// <returns>List of credit payment history records</returns>
        Task<List<CreditPaymentHistoryDto>> GetCreditPaymentHistoryAsync(CreditPaymentSearchDto searchDto);
    }
}
