using ExactCash.Application.DTOs;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace ExactCash.Application.Interfaces
{
    public interface IUnitService
    {
        Task<UnitDto> GetUnitByIdAsync(int id);
        Task<IEnumerable<UnitDto>> GetAllUnitsAsync();
        Task<UnitDto> CreateUnitAsync(UnitDto unitDto);
        Task UpdateUnitAsync(UnitDto unitDto);
        Task DeleteUnitAsync(int id);
    }
} 