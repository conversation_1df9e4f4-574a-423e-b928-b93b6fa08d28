namespace ExactCash.WASM.Services.Interfaces;

public interface IProductService
{
    // Product service interface - to be implemented
}

public interface ISaleService
{
    // Sale service interface - to be implemented
}

public interface ISupplierService
{
    // Supplier service interface - to be implemented
}

public interface IPurchaseOrderService
{
    // Purchase order service interface - to be implemented
}

public interface IExpenseService
{
    // Expense service interface - to be implemented
}

public interface IReportService
{
    // Report service interface - to be implemented
}

public interface IInventoryService
{
    // Inventory service interface - to be implemented
}

public interface IUserService
{
    // User service interface - to be implemented
}

public interface ISystemConfigurationService
{
    // System configuration service interface - to be implemented
}

public interface IModalService
{
    // Modal service interface - to be implemented
}
