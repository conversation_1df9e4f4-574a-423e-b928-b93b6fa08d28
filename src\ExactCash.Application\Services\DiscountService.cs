using AutoMapper;
using ExactCash.Application.DTOs;
using ExactCash.Application.DTOs.Common;
using ExactCash.Application.Enums;
using ExactCash.Application.Interfaces;
using ExactCash.Application.Responses;
using ExactCash.Domain.Entities;
using ExactCash.Infrastructure.Persistence;
using Microsoft.EntityFrameworkCore;

namespace ExactCash.Application.Services
{
    public class DiscountService : IDiscountService
    {
        private readonly AppPostgreSQLDbContext _context;
        private readonly IMapper _mapper;

        public DiscountService(AppPostgreSQLDbContext context, IMapper mapper)
        {
            _context = context;
            _mapper = mapper;
        }

        public async Task<DiscountDto> GetDiscountByIdAsync(int id)
        {
            var discount = await _context.Discounts.FindAsync(id);
            if (discount == null)
                return null;

            return _mapper.Map<DiscountDto>(discount);
        }

        public async Task<PagedResponse<DiscountDto>> GetAllDiscountsAsync(string name, PaginationFilter pagination)
        {
            var query = _context.Discounts.AsQueryable();
            
            if (!string.IsNullOrEmpty(name))
                query = query.Where(d => d.Name.Contains(name));

            var totalRecords = await query.CountAsync();

            query = pagination.SortField switch
            {
                nameof(Discount.CreationDate) => pagination.SortOrder == SortOrder.Asc ? 
                    query.OrderBy(d => d.CreationDate) : query.OrderByDescending(d => d.CreationDate),
                nameof(Discount.Name) => pagination.SortOrder == SortOrder.Asc ? 
                    query.OrderBy(d => d.Name) : query.OrderByDescending(d => d.Name),
                _ => pagination.SortOrder == SortOrder.Desc ? 
                    query.OrderBy(d => d.CreationDate) : query.OrderByDescending(d => d.CreationDate)
            };

            var result = await query
                .Skip((pagination.PageNumber - 1) * pagination.PageSize)
                .Take(pagination.PageSize)
                .ToListAsync();

            return PaginationHelper.CreatePagedResponse(
                _mapper.Map<List<DiscountDto>>(result), 
                pagination.PageNumber, 
                pagination.PageSize, 
                totalRecords);
        }

        public async Task<DiscountDto> CreateDiscountAsync(DiscountDto discountDto)
        {
            var discount = new Discount
            {
                Name = discountDto.Name,
                Amount = discountDto.Amount,
                IsPercentage = discountDto.IsPercentage,
                ProductId = discountDto.ProductId,
                CategoryId = discountDto.CategoryId,
                ApplyToTotal = discountDto.ApplyToTotal,
                EndDate = discountDto.EndDate,
                IsActive = discountDto.IsActive,
                CreationDate = DateTime.UtcNow
            };

            await _context.Discounts.AddAsync(discount);
            await _context.SaveChangesAsync();
            return _mapper.Map<DiscountDto>(discount);
        }

        public async Task UpdateDiscountAsync(DiscountDto discountDto)
        {
            var discount = await _context.Discounts.FindAsync(discountDto.Id);
            if (discount == null)
                throw new KeyNotFoundException($"Discount with ID {discountDto.Id} not found.");

            discount.Name = discountDto.Name;
            discount.Amount = discountDto.Amount;
            discount.IsPercentage = discountDto.IsPercentage;
            discount.ProductId = discountDto.ProductId;
            discount.CategoryId = discountDto.CategoryId;
            discount.ApplyToTotal = discountDto.ApplyToTotal;
            discount.IsActive = discountDto.IsActive;
            discount.LastUpdatedDate = DateTime.UtcNow;
            discount.StartDate = discountDto.StartDate;
            discount.EndDate = discountDto.EndDate;

            _context.Entry(discount).State = EntityState.Modified;
            await _context.SaveChangesAsync();
        }

        public async Task DeleteDiscountAsync(int id)
        {
            var discount = await _context.Discounts.FindAsync(id);
            if (discount == null)
                throw new KeyNotFoundException($"Discount with ID {id} not found.");

            _context.Discounts.Remove(discount);
            await _context.SaveChangesAsync();
        }
    }
} 