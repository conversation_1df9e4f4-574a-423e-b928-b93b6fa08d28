using ExactCash.Application.DTOs;
using ExactCash.Application.DTOs.Common;
using ExactCash.Application.Responses;
#nullable disable

namespace ExactCash.Application.Interfaces
{
    public interface IRoleService
    {
        Task<RoleDto> GetRoleByIdAsync(string id);
        Task<PagedResponse<RoleDto>> GetAllRolesAsync(PaginationFilter pagination);

        Task<List<RoleDto>> GetAllRolesAsync();

        Task<RoleDto> CreateRoleAsync(CreateRoleDto roleDto);
        Task<RoleDto> UpdateRoleAsync(string id, UpdateRoleDto roleDto);
        Task<bool> DeleteRoleAsync(string id);
        Task<bool> AssignUsersToRoleAsync(RoleUsersDto roleUsersDto);
        Task<bool> RemoveUsersFromRoleAsync(RoleUsersDto roleUsersDto);
        Task<List<string>> GetUsersInRoleAsync(string roleId);
        Task<List<string>> GetRolesForUserAsync(string userId);
        Task<bool> IsUserInRoleAsync(string userId, string roleName);
    }
}