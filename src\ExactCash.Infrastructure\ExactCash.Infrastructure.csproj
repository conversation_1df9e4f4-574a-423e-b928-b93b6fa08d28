﻿<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<TargetFramework>net8.0</TargetFramework>
		<ImplicitUsings>enable</ImplicitUsings>
		<Nullable>enable</Nullable>
	</PropertyGroup>

	<ItemGroup>
		<PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="8.0.10" />
		<PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="8.0.10">
			<PrivateAssets>all</PrivateAssets>
			<IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
		</PackageReference>
		<PackageReference Include="Npgsql.EntityFrameworkCore.PostgreSQL" Version="8.0.8" />
	</ItemGroup>
	<ItemGroup>
		<Folder Include="Configurations\" />
		<Folder Include="Migrations\" />
	</ItemGroup>
	<ItemGroup>
	  <ProjectReference Include="..\ExactCash.Domain\ExactCash.Domain.csproj" />
	</ItemGroup>

</Project>
