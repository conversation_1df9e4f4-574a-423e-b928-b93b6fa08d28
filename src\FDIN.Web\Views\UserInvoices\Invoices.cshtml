﻿@model List<FDIN.Web.Data.Sale>
@{
    ViewData["Title"] = "Invoices";
}

<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" />
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" />

<style>
    .invoice-card {
        border-radius: 1.5rem;
        box-shadow: 0 2px 12px rgba(0, 0, 0, 0.07);
        border: none;
        margin-bottom: 1.2rem;
        transition: box-shadow 0.2s;
    }

    .invoice-card:hover {
        box-shadow: 0 4px 24px rgba(0, 0, 0, 0.13);
    }

    .invoice-header {
        font-size: 1.1rem;
        font-weight: 600;
    }

    .invoice-meta {
        font-size: 0.95rem;
        color: #6c757d;
    }

    .invoice-amount {
        font-size: 1.2rem;
        font-weight: 700;
        color: #1a73e8;
    }

    .badge-status {
        font-size: 0.9rem;
        border-radius: 0.7rem;
        padding: 0.4em 1em;
    }

    .invoice-actions a {
        margin-left: 0.5rem;
    }
</style>

<div class="container py-3" style="max-width: 600px;">
    <h3 class="mb-2 text-center">Invoices for @ViewBag.StoreName</h3>
    @if (!string.IsNullOrEmpty(ViewBag.UserName as string))
    {
        <div class="mb-4 text-center text-muted">
            @ViewBag.UserName
        </div>
    }
    @if (Model != null && Model.Any())
    {
        @foreach (var invoice in Model)
        {
            <a href="@Url.Action("Details", "UserInvoices", new { id = invoice.Id })"
                style="text-decoration: none; color: inherit;">
                <div class="card invoice-card">
                    <div class="card-body d-flex align-items-center justify-content-between">
                        <div>
                            <div class="invoice-header">Invoice #: @invoice.InvoiceNumber</div>
                            <div class="invoice-meta">
                                <i class="fa-regular fa-calendar"></i>
                                @invoice.SaleDate.ToString("dd MMM yyyy")
                                &nbsp;|&nbsp;
                                <i class="fa-solid fa-store"></i>
                                @invoice.StoreName
                            </div>
                        </div>
                        <div class="text-end">
                            <div class="invoice-amount text-end" style="white-space: nowrap;">
                                <span class="fw-bold" style="color: #1a73e8;">@invoice.TotalAmount.ToString("N2")</span>
                                <span class="fw-bold" style="color: #1a73e8; margin-left: 0.2em;">SAR</span>
                            </div>
                            <div class="invoice-actions mt-2">
                                @*  <a href="@Url.Action("Details", "UserInvoices", new { id = invoice.Id })" class="btn btn-outline-primary btn-sm" title="View">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="@Url.Action("Download", "UserInvoices", new { id = invoice.Id })" class="btn btn-outline-secondary btn-sm" title="Download">
                                        <i class="fas fa-download"></i>
                                    </a>
                                    <a href="@Url.Action("Print", "UserInvoices", new { id = invoice.Id })" class="btn btn-outline-info btn-sm" title="Print">
                                        <i class="fas fa-print"></i>
                                    </a> *@
                                <a href="javascript:void(0);" title="Share" onclick="shareInvoice('@invoice.Id')"
                                    style="color: var(--bs-gray-600); font-size: 1.3em;">
                                    <i class="fas fa-share-alt"></i>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </a>
        }
    }
    else
    {
        <div class="alert alert-info text-center">No invoices found for this store.</div>
    }
</div>
<script>
@section Scripts {
            <script>
                function shareInvoice(invoiceId) {
                        // You can implement sharing logic here, e.g., copy link to clipboard or open share dialog
                        const url = window.location.origin + '@Url.Action("Details", "UserInvoices")/' + invoiceId;
                navigator.clipboard.writeText(url);
                    }
    </script>
}