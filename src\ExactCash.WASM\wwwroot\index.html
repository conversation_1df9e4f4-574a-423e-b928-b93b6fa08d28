<!DOCTYPE html>
<html lang="ar" dir="rtl">

<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>ExactCash - نظام نقاط البيع</title>
    <base href="/" />
    <link href="css/bootstrap/bootstrap.min.css" rel="stylesheet" />
    <link href="_content/MudBlazor/MudBlazor.min.css" rel="stylesheet" />
    <link href="_content/Blazored.Modal/blazored-modal.css" rel="stylesheet" />
    <link href="_content/Blazored.Toast/blazored-toast.min.css" rel="stylesheet" />
    <link href="css/app.css" rel="stylesheet" />
    <link href="css/rtl.css" rel="stylesheet" />
    <link rel="icon" type="image/png" href="favicon.png" />
    <link href="ExactCash.WASM.styles.css" rel="stylesheet" />
    <link href="manifest.json" rel="manifest" />
    <link rel="apple-touch-icon" sizes="512x512" href="icon-512.png" />
    <link rel="apple-touch-icon" sizes="192x192" href="icon-192.png" />
</head>

<body>
    <div id="app">
        <div class="loading-container">
            <div class="loading-spinner"></div>
            <div class="loading-text">جاري تحميل ExactCash...</div>
        </div>
    </div>

    <div id="blazor-error-ui">
        حدث خطأ غير متوقع. يرجى إعادة تحميل الصفحة.
        <a href="" class="reload">إعادة تحميل</a>
        <a class="dismiss">🗙</a>
    </div>

    <script src="_framework/blazor.webassembly.js"></script>
    <script src="_content/MudBlazor/MudBlazor.min.js"></script>
    <script src="_content/Blazored.Modal/blazored.modal.js"></script>
    <script>
        window.blazorCulture = {
            get: () => window.localStorage['BlazorCulture'],
            set: (value) => window.localStorage['BlazorCulture'] = value
        };
    </script>
</body>

</html>
