using ExactCash.Application.DTOs;
using ExactCash.Application.Responses;
using System.Text.Json;

namespace ExactCash.WPF.Services
{
    public class CustomerCategoryServiceClient : ICustomerCategoryServiceClient
    {
        private readonly HttpService _httpService;
        private readonly string _baseEndpoint = "api/CustomerCategories";

        public CustomerCategoryServiceClient(string baseUrl)
        {
            _httpService = new HttpService(baseUrl);
        }
        public async Task<List<CustomerCategoryDto>> GetAllAsync()
        {
            try
            {
                var response = await _httpService.GetAsync<List<CustomerCategoryDto>>($"{_baseEndpoint}/get-all");
                return response.ToList();
            }
            catch
            {
                return new List<CustomerCategoryDto>();
            }
        }

        public async Task<List<CustomerCategoryDto>> GetActiveAsync()
        {
            try
            {
                var response = await _httpService.GetAsync<List<CustomerCategoryDto>>($"{_baseEndpoint}/get-active");
                return response;
            }
            catch
            {
                return new List<CustomerCategoryDto>();
            }
        }

        public async Task<CustomerCategoryDto> GetByIdAsync(int id)
        {
            try
            {
                var response = await _httpService.GetAsync<CustomerCategoryDto>($"{_baseEndpoint}/{id}");
                return response;
            }
            catch
            {
                return null;
            }
        }

        public async Task<BaseResponse<CustomerCategoryDto>> CreateAsync(CustomerCategoryDto categoryDto)
        {
            try
            {
                var response = await _httpService.PostAsync<BaseResponse<CustomerCategoryDto>>($"{_baseEndpoint}/create?createdBy=System", categoryDto);
                return response;
            }
            catch (Exception ex)
            {
                return ResponseHelper.Failure(500, categoryDto, $"حدث خطأ أثناء إنشاء التصنيف: {ex.Message}");
            }
        }

        public async Task<BaseResponse<bool>> UpdateAsync(CustomerCategoryDto categoryDto)
        {
            try
            {
                var response = await _httpService.PutAsync<BaseResponse<bool>>($"{_baseEndpoint}/update?updatedBy=System", categoryDto);
                return response;
            }
            catch (Exception ex)
            {
                return ResponseHelper.Failure(500, false, $"حدث خطأ أثناء تحديث التصنيف: {ex.Message}");
            }
        }

        public async Task<BaseResponse<bool>> DeleteAsync(int id)
        {
            try
            {
                var response = await _httpService.DeleteAsync<BaseResponse<bool>>($"{_baseEndpoint}/delete/{id}");
                return response;
            }
            catch (Exception ex)
            {
                return ResponseHelper.Failure(500, false, $"حدث خطأ أثناء حذف التصنيف: {ex.Message}");
            }
        }
    }
}
