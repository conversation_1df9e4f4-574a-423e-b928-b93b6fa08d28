using ExactCash.Application.DTOs.Common;
#nullable disable

namespace ExactCash.Application.DTOs
{
    public class SystemConfigurationDto : BaseEntityDto
    {
        /// <summary>
        /// The key of the configuration setting.
        /// </summary>
        public string SettingName { get; set; }

        /// <summary>
        /// The value of the configuration setting.
        /// </summary>
        public string SettingValue { get; set; }

        public string SettingType { get; set; }

        /// <summary>
        /// The description of the configuration setting.
        /// </summary>
        public string Description { get; set; }

        /// <summary>
        /// The category of the configuration setting.
        /// </summary>
        public string Category { get; set; }

        /// <summary>
        /// Indicates whether the configuration setting is active.
        /// </summary>
        public bool IsActive { get; set; }
    }
} 