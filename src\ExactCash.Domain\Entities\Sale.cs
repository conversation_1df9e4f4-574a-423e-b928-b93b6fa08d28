﻿using ExactCash.Domain.Common;
#nullable disable

namespace ExactCash.Domain.Entities
{
    /// <summary>
    /// Represents a sale transaction in the POS system.
    /// </summary>
    public class Sale : BaseEntity
    {
        /// <summary>
        /// The ID of the customer making the purchase.
        /// </summary>
        public int CustomerId { get; set; }

        /// <summary>
        /// Navigation property to the customer making the sale.
        /// </summary>
        public Customer Customer { get; set; }

        /// <summary>
        /// The date the sale was made.
        /// </summary>
        public DateTime SaleDate { get; set; }

        /// <summary>
        /// The unique invoice number for this sale.
        /// </summary>
        public string InvoiceNumber { get; set; }

        /// <summary>
        /// Type of invoice (e.g., Sale, Return).
        /// </summary>
        public string InvoiceType { get; set; }

        /// <summary>
        /// Total discount applied to the entire sale.
        /// </summary>
        public decimal DiscountAmount { get; set; }

        /// <summary>
        /// Total tax applied to the entire sale.
        /// </summary>
        public decimal TaxAmount { get; set; }

        /// <summary>
        /// Net amount before tax (i.e., after discount).
        /// </summary>
        public decimal NetAmount { get; set; }

        /// <summary>
        /// Total amount after discount and tax.
        /// </summary>
        public decimal TotalAmount { get; set; }

        /// <summary>
        /// Amount actually paid by the customer.
        /// </summary>
        public decimal PaidAmount { get; set; }

        /// <summary>
        /// Remaining amount to be paid (used for partial payments or credit).
        /// </summary>
        public decimal RemainingAmount { get; set; }

        /// <summary>
        /// Notes
        /// </summary>
        public string Notes { get; set; }

        /// <summary>
        /// Navigation property for items sold in the sale.
        /// </summary>
        public ICollection<SaleItem> SaleItems { get; set; } = new List<SaleItem>();

        /// <summary>
        /// Navigation property for payments made by the customer.
        /// </summary>
        public ICollection<Payment> Payments { get; set; } = new List<Payment>();

        /// <summary>
        /// Navigation property for credit sale transaction (if this sale is on credit).
        /// </summary>
        public CreditSaleTransaction CreditSaleTransaction { get; set; }
    }

}
