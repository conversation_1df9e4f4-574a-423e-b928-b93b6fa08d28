﻿#nullable disable
namespace ExactCash.Application.Responses
{
    /// <summary>
    /// ResponseHelper
    /// </summary>
    public static class ResponseHelper
    {
        /// <summary>
        /// Success
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="data"></param>
        /// <param name="message"></param>
        /// <returns></returns>
        public static BaseResponse<T> Success<T>(int statusCode, T data, string message = "Operation successful.")
        {
            return new BaseResponse<T>(statusCode, true, message, data);
        }

        /// <summary>
        /// Failure
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="message"></param>
        /// <param name="data"></param>
        /// <returns></returns>
        public static BaseResponse<T> Failure<T>(int statusCode, T data = default, string message= "Operation failed.")
        {
            return new BaseResponse<T>(statusCode, false, message, data);
        }
    }
}
