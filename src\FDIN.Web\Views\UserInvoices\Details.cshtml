﻿@model FDIN.Web.Data.Sale
@{
    ViewData["Title"] = "Invoice Details";
}


<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" />
<style>
    .invoice-box {
        max-width: 800px;
        margin: 40px auto;
        padding: 30px;
        border: 1px solid #eee;
        border-radius: 12px;
        background: #fff;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
        font-size: 16px;
        color: #333;
    }

    .invoice-title {
        font-size: 2rem;
        font-weight: 700;
        letter-spacing: 0.1em;
        margin-bottom: 1.5rem;
    }

    .invoice-header {
        margin-bottom: 2rem;
    }

    .invoice-info {
        margin-bottom: 2rem;
    }

    .table-invoice th,
    .table-invoice td {
        vertical-align: middle;
    }

    .table-invoice th {
        background: #f8f9fa;
    }

    .invoice-summary td {
        font-weight: 600;
    }

    .invoice-footer {
        margin-top: 2rem;
        font-size: 0.95rem;
        color: #888;
        text-align: center;
    }

    img {
        width: 40px;
        height: 40px;
        border-radius: 8px;
        object-fit: contain;
        margin-right: 1rem;
    }
</style>

<div class="invoice-box">
    <div class="d-flex justify-content-between align-items-center invoice-header">
        <div>
            <div class="invoice-title">INVOICE</div>
            <div><strong>Invoice #:</strong> @Model.InvoiceNumber</div>
            <div><strong>Date:</strong> @Model.SaleDate.ToString("dd MMM yyyy")</div>
        </div>
        <div class="text-end">
            <img src="~/Stores/@(Model.StoreName).png" alt="@Model.StoreName" />
            <div class="mt-2"><strong>@Model.StoreName</strong></div>
        </div>
    </div>
    <div class="row invoice-info">
        <div class="col-md-6">
            <div class="mb-2"><strong>Billed To:</strong></div>
            <div>@Model.User?.FullName</div>
            <div>@Model.User?.Email</div>
        </div>
        <div class="col-md-6 text-md-end">
            <div class="mb-2"><strong>Payment Status:</strong></div>
            <div>
                <span
                    class="badge bg-@(Model.PaidAmount >= Model.TotalAmount ? "success" : Model.PaidAmount > 0 ? "warning" : "danger")">
                    @(Model.PaidAmount >= Model.TotalAmount ? "Paid" : Model.PaidAmount > 0 ? "Partial" : "Pending")
                </span>
            </div>
        </div>
    </div>
    <div class="table-responsive">
        <table class="table table-invoice mb-0">
            <thead>
                <tr>
                    <th>#</th>
                    <th>Description</th>
                    <th class="text-center">Unit</th>
                    <th class="text-center">Qty</th>
                    <th class="text-end">Unit Price</th>
                    <th class="text-end">Discount</th>
                    <th class="text-end">Total</th>
                </tr>
            </thead>
            <tbody>
                @{
                    int i = 1;
                }
                @foreach (var item in Model.SaleItems)
                {
                    <tr>
                        <td>@i</td>
                        <td>@item.ProductName</td>
                        <td class="text-center">@item.UnitName</td>
                        <td class="text-center">@item.Quantity</td>
                        <td class="text-end">@item.Price.ToString("N2")</td>
                        <td class="text-end">@item.Discount.ToString("N2")</td>
                        <td class="text-end">@((item.Price * item.Quantity - item.Discount).ToString("N2"))</td>
                    </tr>
                    i++;
                }
            </tbody>
        </table>
    </div>
    <table class="table table-borderless invoice-summary mt-4">
        <tbody>
            <tr>
                <td class="text-end">Subtotal:</td>
                <td class="text-end" style="width: 150px;">@Model.NetAmount.ToString("N2") SAR</td>
            </tr>
            <tr>
                <td class="text-end">Discount:</td>
                <td class="text-end">@Model.DiscountAmount.ToString("N2") SAR</td>
            </tr>
            <tr>
                <td class="text-end">Tax:</td>
                <td class="text-end">@Model.TaxAmount.ToString("N2") SAR</td>
            </tr>
            <tr>
                <td class="text-end">Total:</td>
                <td class="text-end">@Model.TotalAmount.ToString("N2") SAR</td>
            </tr>
            <tr>
                <td class="text-end">Paid:</td>
                <td class="text-end">@Model.PaidAmount.ToString("N2") SAR</td>
            </tr>
            <tr>
                <td class="text-end">Remaining:</td>
                <td class="text-end">@((Model.TotalAmount - Model.PaidAmount).ToString("N2")) SAR</td>
            </tr>
        </tbody>
    </table>
    <div class="invoice-footer mt-4">
        Thank you for your business!<br />
        <span class="no-print">If you need a printed copy, please use your browser's print function.</span>
    </div>
</div>
