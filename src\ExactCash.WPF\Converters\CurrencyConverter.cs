using System;
using System.Globalization;
using System.Windows.Data;

namespace ExactCash.WPF.Converters
{
    /// <summary>
    /// Converter to format decimal values as Egyptian Pound currency
    /// </summary>
    public class CurrencyConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value == null) return "0.00 ج.م";

            if (decimal.TryParse(value.ToString(), out decimal amount))
            {
                return $"{amount:N2} ج.م";
            }

            if (double.TryParse(value.ToString(), out double doubleAmount))
            {
                return $"{doubleAmount:N2} ج.م";
            }

            return $"{value} ج.م";
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value == null) return 0m;

            string stringValue = value.ToString();
            
            // Remove currency symbol and spaces
            stringValue = stringValue.Replace("ج.م", "").Replace(" ", "").Trim();

            if (decimal.TryParse(stringValue, out decimal result))
            {
                return result;
            }

            return 0m;
        }
    }

    /// <summary>
    /// Converter to format decimal values as Egyptian Pound currency with custom format
    /// </summary>
    public class CurrencyFormatterConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value == null) return "0.00 ج.م";

            if (decimal.TryParse(value.ToString(), out decimal amount))
            {
                // Check if parameter specifies position
                string position = parameter?.ToString()?.ToLower() ?? "after";
                
                return position == "before" 
                    ? $"ج.م {amount:N2}"
                    : $"{amount:N2} ج.م";
            }

            return $"{value} ج.م";
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value == null) return 0m;

            string stringValue = value.ToString();
            
            // Remove currency symbol and spaces
            stringValue = stringValue.Replace("ج.م", "").Replace(" ", "").Trim();

            if (decimal.TryParse(stringValue, out decimal result))
            {
                return result;
            }

            return 0m;
        }
    }
}
