﻿using ExactCash.Application.DTOs;
using ExactCash.WPF.Services.Interfaces;

namespace ExactCash.WPF.Services
{
    public class RoleServiceClient : IRoleServiceClient
    {
        private readonly HttpService _httpService;

        public RoleServiceClient(string baseUrl)
        {
            _httpService = new HttpService(baseUrl);
        }

        public async Task<List<RoleDto>> GetAllRolesAsync() {
            return await _httpService.GetAsync<List<RoleDto>>("api/roles/get-all-roles");
        }

    }
}
