namespace ExactCash.Application.DTOs.Inventory
{
    public class LowStockItemDto
    {
        public int Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string Code { get; set; } = string.Empty;
        public int CurrentStock { get; set; }
        public int MinimumStock { get; set; }
        public string CategoryName { get; set; } = string.Empty;
        public string BrandName { get; set; } = string.Empty;
        public decimal UnitPrice { get; set; }
        public string UnitName { get; set; } = string.Empty;
    }
}
