using ExactCash.Application.DTOs;
using ExactCash.Application.Responses;

namespace ExactCash.Application.Interfaces
{
    public interface ICustomerCategoryService
    {
        Task<List<CustomerCategoryDto>> GetAllAsync();
        Task<List<CustomerCategoryDto>> GetActiveAsync();
        Task<CustomerCategoryDto> GetByIdAsync(int id);
        Task<BaseResponse<CustomerCategoryDto>> CreateAsync(CustomerCategoryDto categoryDto, string createdBy);
        Task<BaseResponse<bool>> UpdateAsync(CustomerCategoryDto categoryDto, string updatedBy);
        Task<BaseResponse<bool>> DeleteAsync(int id);
    }
}
