using System.Windows;
using ExactCash.WPF.ViewModels.Customer;
using ExactCash.WPF.Services;

namespace ExactCash.WPF.Views.Customer
{
    public partial class CustomerListView : Window
    {
        public CustomerListView(ICustomerService customerService, ICustomerCategoryServiceClient customerCategoryService)
        {
            InitializeComponent();

            // Create a container for both ViewModels
            var container = new CustomerViewModelContainer
            {
                CustomerListViewModel = new CustomerListViewModel(customerService, customerCategoryService),
                CustomerCategoryViewModel = new CustomerCategoryListViewModel(customerCategoryService)
            };

            DataContext = container;
            Loaded += CustomerListView_Loaded;
        }

        private void CustomerListView_Loaded(object sender, RoutedEventArgs e)
        {
            if (DataContext is CustomerViewModelContainer container)
            {
                container.CustomerListViewModel.CloseWindow = () => Close();
            }
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            Close();
        }

        private void MinimizeButton_Click(object sender, RoutedEventArgs e)
        {
            WindowState = WindowState.Minimized;
        }
    }
}