using ExactCash.Application.DTOs;
using ExactCash.WPF.Services;
using System.Windows;

namespace ExactCash.WPF.Views.Supplier
{
    /// <summary>
    /// Interaction logic for AddSupplierCategoryView.xaml
    /// </summary>
    public partial class AddSupplierCategoryView : Window
    {
        private readonly ISupplierCategoryServiceClient _service;
        private readonly SupplierCategoryDto _editingCategory;
        private readonly bool _isEditMode;

        public AddSupplierCategoryView(ISupplierCategoryServiceClient service, SupplierCategoryDto editingCategory = null)
        {
            InitializeComponent();
            _service = service;
            _editingCategory = editingCategory;
            _isEditMode = editingCategory != null;

            if (_isEditMode)
            {
                // Edit mode
                Title = "تعديل تصنيف مورد";
                HeaderText.Text = "تعديل تصنيف مورد";
                SaveButton.Content = "تحديث";
                
                // Populate fields with existing data
                NameTextBox.Text = _editingCategory.Name;
                DescriptionTextBox.Text = _editingCategory.Description;
                IsActiveCheckBox.IsChecked = _editingCategory.IsActive;
            }
            else
            {
                // Add mode
                Title = "إضافة تصنيف مورد";
                HeaderText.Text = "إضافة تصنيف مورد";
                SaveButton.Content = "حفظ";
                IsActiveCheckBox.IsChecked = true;
            }

            // Focus on name textbox
            NameTextBox.Focus();
        }

        private async void SaveButton_Click(object sender, RoutedEventArgs e)
        {
            // Hide previous validation message
            ValidationMessage.Visibility = Visibility.Collapsed;

            // Validate input
            if (string.IsNullOrWhiteSpace(NameTextBox.Text))
            {
                ShowValidationMessage("يرجى إدخال اسم التصنيف");
                NameTextBox.Focus();
                return;
            }

            try
            {
                // Disable save button to prevent double-click
                SaveButton.IsEnabled = false;

                var categoryDto = new SupplierCategoryDto
                {
                    Id = _isEditMode ? _editingCategory.Id : 0,
                    Name = NameTextBox.Text.Trim(),
                    Description = DescriptionTextBox.Text?.Trim(),
                    IsActive = IsActiveCheckBox.IsChecked ?? true
                };

                if (_isEditMode)
                {
                    // Update existing category
                    var result = await _service.UpdateAsync(categoryDto);
                    if (result.Success)
                    {
                        Helpers.BootstrapMessageBoxHelper.Show("تم تحديث تصنيف المورد بنجاح", owner: this);
                        DialogResult = true;
                        Close();
                    }
                    else
                    {
                        ShowValidationMessage(result.Message);
                    }
                }
                else
                {
                    // Create new category
                    var result = await _service.CreateAsync(categoryDto);
                    if (result.Success)
                    {
                        Helpers.BootstrapMessageBoxHelper.Show("تم إضافة تصنيف المورد بنجاح", owner: this);
                        DialogResult = true;
                        Close();
                    }
                    else
                    {
                        ShowValidationMessage(result.Message);
                    }
                }
            }
            catch (Exception ex)
            {
                ShowValidationMessage($"حدث خطأ: {ex.Message}");
            }
            finally
            {
                // Re-enable save button
                SaveButton.IsEnabled = true;
            }
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }

        private void ShowValidationMessage(string message)
        {
            ValidationMessage.Text = message;
            ValidationMessage.Visibility = Visibility.Visible;
        }

        private void NameTextBox_KeyDown(object sender, System.Windows.Input.KeyEventArgs e)
        {
            if (e.Key == System.Windows.Input.Key.Enter)
            {
                DescriptionTextBox.Focus();
            }
        }

        private void DescriptionTextBox_KeyDown(object sender, System.Windows.Input.KeyEventArgs e)
        {
            if (e.Key == System.Windows.Input.Key.Enter && !e.KeyboardDevice.Modifiers.HasFlag(System.Windows.Input.ModifierKeys.Shift))
            {
                SaveButton_Click(sender, e);
            }
        }
    }
}
