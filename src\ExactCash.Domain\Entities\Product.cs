﻿using ExactCash.Domain.Common;
using System.Text.Json.Serialization;
using System;
#nullable disable

namespace ExactCash.Domain.Entities
{
    /// <summary>
    /// Represents a product available for sale in the POS system.
    /// </summary>
    public class Product : BaseEntity
    {
        #region Props.
        /// <summary>
        /// The name of the product.
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// The Stock Keeping Unit – a unique identifier for the product.
        /// </summary>
        public string SKU { get; set; }

        /// <summary>
        /// The barcode value used for scanning the product.
        /// </summary>
        public string Barcode { get; set; }

        /// <summary>
        /// The unit of measurement (e.g., pcs, kg, liter).
        /// </summary>
        public int? UnitId { get; set; }

        /// <summary>
        /// // Navigation property to Unit
        /// </summary>
        public Unit Unit { get; set; }

        public int? DefaultUnitId { get; set; }

        /// <summary>
        /// The cost price of the product (how much it was purchased for).
        /// </summary>
        public decimal CostPrice { get; set; }

        /// <summary>
        /// The selling price of the product (how much it is sold for).
        /// </summary>
        public decimal SellingPrice { get; set; }

        /// <summary>
        /// Current quantity of the product in stock.
        /// </summary>
        public decimal StockQuantity { get; set; }

        /// <summary>
        /// The minimum stock level before triggering a low-stock alert.
        /// </summary>
        public int MinStock { get; set; }

        /// <summary>
        /// Indicates whether the product is active and available for sale.
        /// </summary>
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// Relative or full path to the product's image.
        /// </summary>
        public string ImagePath { get; set; }

        /// <summary>
        /// Foreign key referencing the product's category.
        /// </summary>
        public int? CategoryId { get; set; }

        /// <summary>
        /// Navigation property for the product's category.
        /// </summary>
        [JsonIgnore]
        public Category Category { get; set; }

        /// <summary>
        /// Collection of PurchaseItems related to this Product
        /// </summary>
        public ICollection<PurchaseItem> PurchaseItems { get; set; } = new List<PurchaseItem>();

        /// <summary>
        /// The description of the product.
        /// </summary>
        public string Description { get; private set; }

        /// <summary>
        /// The ID of the brand for this product.
        /// </summary>
        public int? BrandId { get; set; }

        /// <summary>
        /// The brand of this product.
        /// </summary>
        public Brand Brand { get; set; }

        /// <summary>
        /// The date and time when the product was created.
        /// </summary>
        public decimal Discount { get; set; } = 0;

        /// <summary>
        /// IsDeleted
        /// </summary>
        public bool IsDeleted { get; set; } = false;

        /// <summary>
        /// The sale items associated with this product.
        /// </summary>
        [JsonIgnore]
        public ICollection<SaleItem> SaleItems { get; set; } = new List<SaleItem>();

        #endregion

        #region ctor.
        public Product()
        {
            // Parameterless constructor for JSON deserialization
        }

        public Product(string name, string description, decimal price, int stockQuantity)
        {
            Name = name;
            Description = description;
            SellingPrice = price;
            StockQuantity = stockQuantity;
            CreationDate = DateTime.UtcNow;
        }

        public Product(string name, string sku, string barCode, int unitId, decimal costPrice, decimal sellingPrice, int minStock, bool isActive, int categoryId, string description, int stockQuantity)
        {
            Name = name;
            Description = description;
            SellingPrice = sellingPrice;
            StockQuantity = stockQuantity;
            SKU = sku;
            Barcode = barCode;
            UnitId = unitId;
            CostPrice = costPrice;
            MinStock = minStock;
            IsActive = isActive;
            CategoryId = categoryId;
            CreationDate = DateTime.UtcNow;
        }

        public void Update(string name, string sku, string barCode, int unitId, decimal costPrice, decimal sellingPrice, int minStock, bool isActive, int categoryId, string description, int stockQuantity)
        {
            Name = name;
            Description = description;
            SellingPrice = sellingPrice;
            StockQuantity = stockQuantity;
            SKU = sku;
            Barcode = barCode;
            UnitId = unitId;
            CostPrice = costPrice;
            MinStock = minStock;
            IsActive = isActive;
            CategoryId = categoryId;
            LastUpdatedDate = DateTime.UtcNow;
        }
        #endregion

        #region Methods.
        public void UpdateStock(decimal quantity)
        {
            StockQuantity = quantity;
            LastUpdatedDate = DateTime.UtcNow;
        }

        public void SoftDelete()
        {
            IsDeleted = true;
            LastUpdatedDate = DateTime.UtcNow;
        }
        #endregion
    }
}
