using ExactCash.Application.DTOs;
using ExactCash.Application.DTOs.Common;
using ExactCash.Application.Responses;

namespace ExactCash.Application.Interfaces
{
    public interface ISupplierService
    {
        Task<SupplierDto> GetSupplierByIdAsync(int id);
        Task<PagedResponse<SupplierDto>> GetAllSuppliersAsync(string name,string phoneNumber, PaginationFilter pagination);

        Task<IEnumerable<SupplierDto>> GetAllSuppliersAsync(string name, string phoneNumber);
        Task<BaseResponse<SupplierDto>> CreateSupplierAsync(SupplierDto supplierDto);
        Task<BaseResponse<SupplierDto>> UpdateSupplierAsync(SupplierDto supplierDto);
        Task DeleteSupplierAsync(int id);

        Task<List<SupplierDto>> SearchSuppliersAsync(string searchTerm);
    }
} 