﻿using System;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using System.Windows.Input;
using ExactCash.Application.DTOs;
using ExactCash.Application.DTOs.Common;
using ExactCash.WPF.Commands;
using ExactCash.WPF.Services;
using System.Windows;
using ExactCash.WPF.Views.Supplier;
using System.Windows.Media;
using System.Linq;
using System.Threading.Tasks;
using AutoMapper;
using ExactCash.Application.Interfaces;
using Microsoft.Win32;
using System.IO;
using System.Text;

namespace ExactCash.WPF.ViewModels.Supplier
{
    public class SupplierListViewModel : ViewModelBase
    {
        private readonly ISupplierServiceClient _supplierService;
        private readonly ISupplierCategoryServiceClient _supplierCategoryService;
        private readonly IMapper _mapper;
        private string _searchName;
        private string _searchPhone;
        private SupplierCategoryDto _searchCategory;
        private int _currentPage = 1;
        private int _totalPages;
        private const int PageSize = 20;
        private ObservableCollection<SupplierDto> _suppliers;
        private SupplierDto _selectedSupplier;
        private ObservableCollection<SupplierCategoryDto> _searchCategories;

        public SupplierListViewModel(ISupplierServiceClient supplierService, ISupplierCategoryServiceClient supplierCategoryService, IMapper mapper)
        {
            _supplierService = supplierService;
            _supplierCategoryService = supplierCategoryService;
            _mapper = mapper;

            Suppliers = new ObservableCollection<SupplierDto>();
            SearchCategories = new ObservableCollection<SupplierCategoryDto>();
            SearchCommand = new RelayCommand(ExecuteSearch);
            ResetSearchCommand = new RelayCommand(async () => await ExecuteResetSearch());
            AddNewSupplierCommand = new RelayCommand(() => ExecuteAddNewSupplier());
            EditSupplierCommand = new RelayCommand<SupplierDto>(ExecuteEditSupplier);
            DeleteSupplierCommand = new RelayCommand<SupplierDto>(async (supplier) => await ExecuteDeleteSupplier(supplier));
            RefreshCommand = new RelayCommand(LoadData);
            NextPageCommand = new RelayCommand(async () => await ExecuteNextPage());
            PreviousPageCommand = new RelayCommand(async () => await ExecutePreviousPage());
            ExportToExcelCommand = new RelayCommand(ExportToExcel);

            LoadSearchCategories();
            LoadData();
        }

        public ObservableCollection<SupplierDto> Suppliers
        {
            get => _suppliers;
            set
            {
                _suppliers = value;
                OnPropertyChanged();
                OnPropertyChanged(nameof(TotalItems));
                OnPropertyChanged(nameof(ActiveSuppliersCount));
            }
        }

        public SupplierDto SelectedSupplier
        {
            get => _selectedSupplier;
            set
            {
                _selectedSupplier = value;
                OnPropertyChanged();
            }
        }

        public string SearchName
        {
            get => _searchName;
            set
            {
                _searchName = value;
                OnPropertyChanged();
            }
        }

        public string SearchPhone
        {
            get => _searchPhone;
            set
            {
                _searchPhone = value;
                OnPropertyChanged();
            }
        }

        public SupplierCategoryDto SearchCategory
        {
            get => _searchCategory;
            set
            {
                _searchCategory = value;
                OnPropertyChanged();
            }
        }

        public ObservableCollection<SupplierCategoryDto> SearchCategories
        {
            get => _searchCategories;
            set
            {
                _searchCategories = value;
                OnPropertyChanged();
            }
        }

        public int CurrentPage
        {
            get => _currentPage;
            set
            {
                _currentPage = value;
                OnPropertyChanged();
                OnPropertyChanged(nameof(PaginationInfo));
            }
        }

        public int TotalPages
        {
            get => _totalPages;
            set
            {
                _totalPages = value;
                OnPropertyChanged();
                OnPropertyChanged(nameof(PaginationInfo));
            }
        }

        public string PaginationInfo => $"صفحة {CurrentPage} من {TotalPages}";

        public ICommand SearchCommand { get; }
        public RelayCommand ResetSearchCommand { get; }
        public RelayCommand AddNewSupplierCommand { get; }
        public RelayCommand<SupplierDto> EditSupplierCommand { get; }
        public RelayCommand<SupplierDto> DeleteSupplierCommand { get; }
        public ICommand RefreshCommand { get; }
        public RelayCommand NextPageCommand { get; }
        public RelayCommand PreviousPageCommand { get; }
        public ICommand ExportToExcelCommand { get; }

        public int TotalItems => Suppliers?.Count ?? 0;
        public int ActiveSuppliersCount => Suppliers?.Count(s => s.IsActive) ?? 0;

        private async void LoadData()
        {
            try
            {
                var filter = new PaginationFilter
                {
                    PageNumber = CurrentPage,
                    PageSize = PageSize
                };
                var result = await _supplierService.GetAllSuppliersAsync(SearchName, _searchPhone, filter);
                var suppliers = result.Data.ToList();
                TotalPages = (int)Math.Ceiling(suppliers.Count / (double)PageSize);
                Suppliers = new ObservableCollection<SupplierDto>(suppliers.Skip((CurrentPage - 1) * PageSize).Take(PageSize));
            }
            catch (Exception ex)
            {
                System.Windows.MessageBox.Show($"Error loading suppliers: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void ExecuteSearch()
        {
            try
            {
                var filter = new PaginationFilter
                {
                    PageNumber = CurrentPage,
                    PageSize = PageSize
                };
                var result = await _supplierService.GetAllSuppliersAsync(SearchName, _searchPhone, filter);
                var suppliers = result.Data.ToList();

                if (!string.IsNullOrWhiteSpace(SearchName))
                {
                    suppliers = suppliers.Where(s => s.Name.Contains(SearchName, StringComparison.OrdinalIgnoreCase)).ToList();
                }

                if (!string.IsNullOrWhiteSpace(SearchPhone))
                {
                    suppliers = suppliers.Where(s => s.Phone.Contains(SearchPhone)).ToList();
                }

                if (SearchCategory != null && SearchCategory.Id > 0)
                {
                    suppliers = suppliers.Where(s => s.CategoryId == SearchCategory.Id).ToList();
                }

                TotalPages = (int)Math.Ceiling(suppliers.Count / (double)PageSize);
                CurrentPage = 1;
                Suppliers = new ObservableCollection<SupplierDto>(suppliers.Take(PageSize));
            }
            catch (Exception ex)
            {
                System.Windows.MessageBox.Show($"Error searching suppliers: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async Task ExecuteResetSearch()
        {
            SearchName = string.Empty;
            SearchPhone = string.Empty;
            SearchCategory = null;
            CurrentPage = 1;
            LoadData();
        }

        private void ExecuteAddNewSupplier()
        {
            var addSupplierView = new AddSupplierView();
            var viewModel = new AddSupplierViewModel(_supplierService, _supplierCategoryService, _mapper);
            addSupplierView.DataContext = viewModel;
            addSupplierView.Owner = FindParentWindow();
            viewModel.SupplierAdded += async (s, e) =>
            {
                LoadData();
            };
            addSupplierView.ShowDialog();
        }

        private void ExecuteEditSupplier(SupplierDto supplier)
        {
            if (supplier == null) return;

            var addSupplierView = new AddSupplierView();
            var viewModel = new AddSupplierViewModel(_supplierService, _supplierCategoryService, _mapper);
            addSupplierView.DataContext = viewModel;
            addSupplierView.Owner = FindParentWindow();
            viewModel.SetSupplier(supplier);
            viewModel.SupplierUpdated += async (s, e) =>
            {
                LoadData();
            };
            addSupplierView.ShowDialog();
        }

        private async Task ExecuteDeleteSupplier(SupplierDto supplier)
        {
            if (supplier == null) return;

            var result = System.Windows.MessageBox.Show(
                "Are you sure you want to delete this supplier?",
                "Confirm Delete",
                MessageBoxButton.YesNo,
                MessageBoxImage.Question);

            if (result == MessageBoxResult.Yes)
            {
                try
                {
                    await _supplierService.DeleteSupplierAsync(supplier.Id);
                    LoadData();
                }
                catch (Exception ex)
                {
                    System.Windows.MessageBox.Show($"Error deleting supplier: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        private async Task ExecuteNextPage()
        {
            if (CurrentPage < TotalPages)
            {
                CurrentPage++;
                LoadData();
            }
        }

        private async Task ExecutePreviousPage()
        {
            if (CurrentPage > 1)
            {
                CurrentPage--;
                LoadData();
            }
        }

        private void ExportToExcel()
        {
            var saveFileDialog = new Microsoft.Win32.SaveFileDialog
            {
                Filter = "Excel Files (*.xlsx)|*.xlsx|CSV Files (*.csv)|*.csv",
                FileName = $"الموردين_{DateTime.Now:yyyyMMdd_HHmmss}.csv"
            };

            if (saveFileDialog.ShowDialog() == true)
            {
                try
                {
                    using (var writer = new StreamWriter(saveFileDialog.FileName, false, Encoding.UTF8))
                    {
                        // Write header (adjust columns as per your DataGrid)
                        writer.WriteLine("الاسم,الهاتف,العنوان,البريد الإلكتروني,الملاحظات");
                        foreach (var supplier in Suppliers)
                        {
                            writer.WriteLine($"{supplier.Name},{supplier.Phone},{supplier.Address},{supplier.Email},{supplier.Notes}");
                        }
                    }
                    Helpers.BootstrapMessageBoxHelper.Show(owner: FindParentWindow(), message: "تم تصدير البيانات بنجاح!");
                }
                catch (Exception ex)
                {
                    Helpers.BootstrapMessageBoxHelper.Show(owner: FindParentWindow(), message: $"حدث خطأ أثناء التصدير: {ex.Message}");
                }
            }
        }

        private async void LoadSearchCategories()
        {
            try
            {
                var categories = await _supplierCategoryService.GetActiveAsync();

                SearchCategories.Clear();
                SearchCategories.Add(new SupplierCategoryDto { Id = 0, Name = "-- جميع التصنيفات --" });

                foreach (var category in categories)
                {
                    SearchCategories.Add(category);
                }
            }
            catch (Exception ex)
            {
                // If categories fail to load, just show the "All Categories" option
                SearchCategories.Clear();
                SearchCategories.Add(new SupplierCategoryDto { Id = 0, Name = "-- جميع التصنيفات --" });
            }
        }

        private Window FindParentWindow()
        {
            foreach (Window window in System.Windows.Application.Current.Windows)
            {
                if (window.IsActive)
                {
                    return window;
                }
            }
            return System.Windows.Application.Current.MainWindow;
        }
    }
}