using System.Windows;
using ExactCash.WPF.Services.Interfaces;
using System.Collections.ObjectModel;
using System.Threading.Tasks;
using System.Windows.Controls;
using ExactCash.Application.DTOs;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using System.Windows.Media;
using System.Windows.Data;
using System;
using ExactCash.WPF.Services;

namespace ExactCash.WPF.Views.Configuration
{
    public partial class POSConfigurationsWindow : Window, INotifyPropertyChanged
    {
        private readonly ISystemConfigurationServiceClient _systemConfigurationService;
        private readonly IUnitServiceClient _unitServiceClient;
        private readonly IBrandsServiceClient _brandsServiceClient;
        private readonly ICategoryServiceClient _categoryServiceClient;
        private ObservableCollection<SystemConfigurationDto> _systemConfigurations;
        private ObservableCollection<UnitDto> _units;
        private ObservableCollection<BrandDto> _brands;
        private ObservableCollection<CategoryDto> _categories;

        private UnitDto _newUnit;

        public event PropertyChangedEventHandler PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        public ObservableCollection<SystemConfigurationDto> SystemConfigurations
        {
            get => _systemConfigurations;
            set
            {
                _systemConfigurations = value;
                OnPropertyChanged();
            }
        }

        public ObservableCollection<UnitDto> Units
        {
            get => _units;
            set { _units = value; OnPropertyChanged(); }
        }

        public ObservableCollection<BrandDto> Brands
        {
            get => _brands;
            set { _brands = value; OnPropertyChanged(); }
        }

        public ObservableCollection<CategoryDto> Categories
        {
            get => _categories;
            set { _categories = value; OnPropertyChanged(); }
        }

        public POSConfigurationsWindow(
            ISystemConfigurationServiceClient systemConfigurationService,
            IUnitServiceClient unitServiceClient,
            IBrandsServiceClient brandsServiceClient,
            ICategoryServiceClient categoryServiceClient)
        {
            InitializeComponent();
            _systemConfigurationService = systemConfigurationService;
            _unitServiceClient = unitServiceClient;
            _brandsServiceClient = brandsServiceClient;
            _categoryServiceClient = categoryServiceClient;
            SystemConfigurations = new ObservableCollection<SystemConfigurationDto>();
            Units = new ObservableCollection<UnitDto>();
            Brands = new ObservableCollection<BrandDto>();
            Categories = new ObservableCollection<CategoryDto>();
            DataContext = this;
            LoadData();
            LoadUnits();
            LoadBrands();
            LoadCategories();
        }

        private Window FindParentWindow()
        {
            foreach (Window window in System.Windows.Application.Current.Windows)
            {
                if (window.Content != null &&
                    VisualTreeHelper.GetChild(window.Content as DependencyObject, 0) is FrameworkElement content &&
                    window.Name == "POSConfigurationsScreen")
                {
                    return window;
                }
            }
            return System.Windows.Application.Current.MainWindow;
        }

        private async void LoadData()
        {
            try
            {
                var configurations = await _systemConfigurationService.GetAllSystemConfigurationsAsync();
                SystemConfigurations.Clear();
                foreach (var config in configurations)
                {
                    SystemConfigurations.Add(config);
                }
            }
            catch (System.Exception ex)
            {
                Helpers.BootstrapMessageBoxHelper.Show(owner: FindParentWindow(), message: $"حدث خطأ أثناء تحميل البيانات: {ex.Message}");
            }
        }

        private async void LoadUnits()
        {
            try
            {
                var units = await _unitServiceClient.GetAllUnitsAsync();
                Units.Clear();
                foreach (var unit in units)
                {
                    Units.Add(unit);
                }
                // Clear new unit reference after loading
                _newUnit = null;
            }
            catch (Exception ex)
            {
                Helpers.BootstrapMessageBoxHelper.Show(owner: FindParentWindow(), message: $"حدث خطأ أثناء تحميل الوحدات: {ex.Message}");
            }
        }

        private async void LoadBrands()
        {
            try
            {
                var brands = await _brandsServiceClient.GetAllBrandsAsync();
                Brands.Clear();
                foreach (var brand in brands)
                {
                    Brands.Add(brand);
                }
            }
            catch (Exception ex)
            {
                Helpers.BootstrapMessageBoxHelper.Show(owner: FindParentWindow(), message: $"حدث خطأ أثناء تحميل العلامات التجارية: {ex.Message}");
            }
        }

        private async void LoadCategories()
        {
            try
            {
                var categories = await _categoryServiceClient.GetAllAsync();
                Categories.Clear();
                foreach (var category in categories)
                {
                    Categories.Add(category);
                }
            }
            catch (Exception ex)
            {
                Helpers.BootstrapMessageBoxHelper.Show(owner: FindParentWindow(), message: $"حدث خطأ أثناء تحميل الفئات: {ex.Message}");
            }
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            Close();
        }

        private void MinimizeButton_Click(object sender, RoutedEventArgs e)
        {
            WindowState = WindowState.Minimized;
        }

        // System Configurations Tab Event Handlers
        private async void ConfigurationsGrid_RowEditEnding(object sender, DataGridRowEditEndingEventArgs e)
        {
            if (e.EditAction == DataGridEditAction.Commit && e.Row.Item is SystemConfigurationDto configuration)
            {
                try
                {
                    await _systemConfigurationService.UpdateSystemConfigurationAsync(configuration);
                    Helpers.BootstrapMessageBoxHelper.Show(owner: FindParentWindow(), message: "تم حفظ التعديلات بنجاح");
                }
                catch (Exception ex)
                {
                    Helpers.BootstrapMessageBoxHelper.Show(owner: FindParentWindow(), message: $"حدث خطأ أثناء حفظ التعديلات: {ex.Message}");
                    // Cancel the edit to prevent invalid data
                    e.Cancel = true;
                }
            }
        }

        private async void SaveConfigurationButton_Click(object sender, RoutedEventArgs e)
        {
            if (sender is System.Windows.Controls.Button button && button.Tag is SystemConfigurationDto configuration)
            {
                // Validate required fields
                if (string.IsNullOrWhiteSpace(configuration.SettingValue))
                {
                    Helpers.BootstrapMessageBoxHelper.Show(owner: FindParentWindow(), message: "لا يمكن ترك قيمة الإعداد فارغة");
                    return;
                }

                try
                {
                    // Commit any pending edits first
                    ConfigurationsGrid.CommitEdit();

                    await _systemConfigurationService.UpdateSystemConfigurationAsync(configuration);
                    Helpers.BootstrapMessageBoxHelper.Show(owner: FindParentWindow(), message: "تم حفظ الإعداد بنجاح");
                }
                catch (Exception ex)
                {
                    Helpers.BootstrapMessageBoxHelper.Show(owner: FindParentWindow(), message: $"حدث خطأ أثناء حفظ الإعداد: {ex.Message}");
                }
            }
        }





        private void AddUnitButton_Click(object sender, RoutedEventArgs e)
        {
            _newUnit = new UnitDto
            {
                Id = 0, // Explicitly set to 0 to indicate new unit
                Name = string.Empty,
                Symbol = string.Empty,
                ConversionRateToBaseUnit = 1,
                IsActive = true
            };
            Units.Add(_newUnit);
            UnitsGrid.SelectedItem = _newUnit;
            UnitsGrid.ScrollIntoView(_newUnit);
            UnitsGrid.BeginEdit();
        }

        private async void SaveUnitButton_Click(object sender, RoutedEventArgs e)
        {
            if (UnitsGrid.SelectedItem is UnitDto unit && (unit == _newUnit || unit.Id == 0))
            {
                // Validate required fields
                if (string.IsNullOrWhiteSpace(unit.Name))
                {
                    Helpers.BootstrapMessageBoxHelper.Show(owner: FindParentWindow(), message: "يرجى إدخال اسم الوحدة");
                    return;
                }

                if (string.IsNullOrWhiteSpace(unit.Symbol))
                {
                    Helpers.BootstrapMessageBoxHelper.Show(owner: FindParentWindow(), message: "يرجى إدخال رمز الوحدة");
                    return;
                }

                if (unit.ConversionRateToBaseUnit <= 0)
                {
                    Helpers.BootstrapMessageBoxHelper.Show(owner: FindParentWindow(), message: "يجب أن يكون معامل التحويل أكبر من صفر");
                    return;
                }

                try
                {
                    // Commit any pending edits first
                    UnitsGrid.CommitEdit();

                    await _unitServiceClient.CreateUnitAsync(unit);
                    Helpers.BootstrapMessageBoxHelper.Show(owner: FindParentWindow(), message: "تمت إضافة الوحدة بنجاح");
                    LoadUnits();
                    _newUnit = null;
                }
                catch (Exception ex)
                {
                    Helpers.BootstrapMessageBoxHelper.Show(owner: FindParentWindow(), message: $"حدث خطأ أثناء إضافة الوحدة: {ex.Message}");
                }
            }
            else
            {
                Helpers.BootstrapMessageBoxHelper.Show(owner: FindParentWindow(), message: "يرجى تحديد الوحدة الجديدة لحفظها");
            }
        }

        private async void UnitsGrid_RowEditEnding(object sender, DataGridRowEditEndingEventArgs e)
        {
            if (e.EditAction == DataGridEditAction.Commit && e.Row.Item is UnitDto unit)
            {
                // Skip auto-save for new units (Id = 0) - they should be saved via SaveUnitButton
                if (unit.Id == 0 || unit == _newUnit)
                {
                    return;
                }

                try
                {
                    await _unitServiceClient.UpdateUnitAsync(unit.Id, unit);
                    Helpers.BootstrapMessageBoxHelper.Show(owner: FindParentWindow(), message: "تم حفظ التعديلات بنجاح");
                }
                catch (Exception ex)
                {
                    Helpers.BootstrapMessageBoxHelper.Show(owner: FindParentWindow(), message: $"حدث خطأ أثناء حفظ التعديلات: {ex.Message}");
                    // Cancel the edit to prevent invalid data
                    e.Cancel = true;
                }
            }
        }

        private async void DeleteUnitButton_Click(object sender, RoutedEventArgs e)
        {
            if (sender is System.Windows.Controls.Button button && button.Tag is UnitDto unit)
            {
                var result = System.Windows.MessageBox.Show(
                    "هل أنت متأكد من حذف هذه الوحدة؟",
                    "تأكيد الحذف",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Warning);

                if (result == MessageBoxResult.Yes)
                {
                    try
                    {
                        await _unitServiceClient.DeleteUnitAsync(unit.Id);
                        Units.Remove(unit);
                        Helpers.BootstrapMessageBoxHelper.Show(owner: FindParentWindow(), message: "تم حذف الوحدة بنجاح");
                    }
                    catch (Exception ex)
                    {
                        Helpers.BootstrapMessageBoxHelper.Show(owner: FindParentWindow(), message: $"حدث خطأ أثناء حذف الوحدة: {ex.Message}");
                    }
                }
            }
        }

        // Category Tab Event Handlers
        private void AddCategoryButton_Click(object sender, RoutedEventArgs e)
        {
            var newCategory = new CategoryDto
            {
                Id = 0, // Explicitly set to 0 to indicate new category
                Name = string.Empty
            };
            Categories.Add(newCategory);
            CategoriesGrid.SelectedItem = newCategory;
            CategoriesGrid.ScrollIntoView(newCategory);
            CategoriesGrid.BeginEdit();
        }

        private async void SaveCategoryButton_Click(object sender, RoutedEventArgs e)
        {
            if (CategoriesGrid.SelectedItem is CategoryDto category && category.Id == 0)
            {
                // Validate required fields
                if (string.IsNullOrWhiteSpace(category.Name))
                {
                    Helpers.BootstrapMessageBoxHelper.Show(owner: FindParentWindow(), message: "يرجى إدخال اسم الفئة");
                    return;
                }

                try
                {
                    // Commit any pending edits first
                    CategoriesGrid.CommitEdit();

                    await _categoryServiceClient.CreateAsync(new Domain.Entities.Category
                    {
                        Name = category.Name,
                    });
                    Helpers.BootstrapMessageBoxHelper.Show(owner: FindParentWindow(), message: "تمت إضافة الفئة بنجاح");
                    LoadCategories();
                }
                catch (Exception ex)
                {
                    Helpers.BootstrapMessageBoxHelper.Show(owner: FindParentWindow(), message: $"حدث خطأ أثناء إضافة الفئة: {ex.Message}");
                }
            }
            else
            {
                Helpers.BootstrapMessageBoxHelper.Show(owner: FindParentWindow(), message: "يرجى تحديد الفئة الجديدة لحفظها");
            }
        }

        private async void DeleteCategoryButton_Click(object sender, RoutedEventArgs e)
        {
            if (sender is System.Windows.Controls.Button button && button.Tag is CategoryDto category)
            {
                var result = System.Windows.MessageBox.Show("هل أنت متأكد من حذف هذه الفئة؟", "تأكيد الحذف", MessageBoxButton.YesNo, MessageBoxImage.Warning);
                if (result == MessageBoxResult.Yes)
                {
                    try
                    {
                        await _categoryServiceClient.DeleteAsync(category.Id);
                        Categories.Remove(category);
                        Helpers.BootstrapMessageBoxHelper.Show(owner: FindParentWindow(), message: "تم حذف الفئة بنجاح");
                    }
                    catch (Exception ex)
                    {
                        Helpers.BootstrapMessageBoxHelper.Show(owner: FindParentWindow(), message: $"حدث خطأ أثناء حذف الفئة: {ex.Message}");
                    }
                }
            }
        }

        private async void CategoriesGrid_RowEditEnding(object sender, DataGridRowEditEndingEventArgs e)
        {
            if (e.EditAction == DataGridEditAction.Commit && e.Row.Item is CategoryDto category)
            {
                // Skip auto-save for new categories (Id = 0) - they should be saved via SaveCategoryButton
                if (category.Id == 0)
                {
                    return;
                }

                try
                {
                    await _categoryServiceClient.UpdateAsync(new Domain.Entities.Category { Name = category.Name, Id = category.Id });
                    Helpers.BootstrapMessageBoxHelper.Show(owner: FindParentWindow(), message: "تم حفظ التعديلات بنجاح");
                }
                catch (Exception ex)
                {
                    Helpers.BootstrapMessageBoxHelper.Show(owner: FindParentWindow(), message: $"حدث خطأ أثناء حفظ التعديلات: {ex.Message}");
                    // Cancel the edit to prevent invalid data
                    e.Cancel = true;
                }
            }
        }

        // Brand Tab Event Handlers
        private void AddBrandButton_Click(object sender, RoutedEventArgs e)
        {
            var newBrand = new BrandDto
            {
                Name = string.Empty,
                Description = string.Empty
            };
            Brands.Add(newBrand);
            BrandsGrid.SelectedItem = newBrand;
            BrandsGrid.ScrollIntoView(newBrand);
            BrandsGrid.BeginEdit();
        }

        private async void SaveBrandButton_Click(object sender, RoutedEventArgs e)
        {
            if (BrandsGrid.SelectedItem is BrandDto brand && !string.IsNullOrWhiteSpace(brand.Name))
            {
                try
                {
                    if (brand.Id == 0)
                    {
                        await _brandsServiceClient.CreateBrandAsync(brand);
                        Helpers.BootstrapMessageBoxHelper.Show(owner: FindParentWindow(), message: "تمت إضافة العلامة التجارية بنجاح");
                    }
                    else
                    {
                        await _brandsServiceClient.UpdateBrandAsync(brand);
                        Helpers.BootstrapMessageBoxHelper.Show(owner: FindParentWindow(), message: "تم حفظ التعديلات بنجاح");
                    }
                    LoadBrands();
                }
                catch (Exception ex)
                {
                    Helpers.BootstrapMessageBoxHelper.Show(owner: FindParentWindow(), message: $"حدث خطأ أثناء حفظ العلامة التجارية: {ex.Message}");
                }
            }
        }

        private async void DeleteBrandButton_Click(object sender, RoutedEventArgs e)
        {
            if (sender is System.Windows.Controls.Button button && button.Tag is BrandDto brand)
            {
                var result = System.Windows.MessageBox.Show("هل أنت متأكد من حذف هذه العلامة التجارية؟", "تأكيد الحذف", MessageBoxButton.YesNo, MessageBoxImage.Warning);
                if (result == MessageBoxResult.Yes)
                {
                    try
                    {
                        await _brandsServiceClient.DeleteBrandAsync(brand.Id);
                        Brands.Remove(brand);
                        Helpers.BootstrapMessageBoxHelper.Show(owner: FindParentWindow(), message: "تم حذف العلامة التجارية بنجاح");
                    }
                    catch (Exception ex)
                    {
                        Helpers.BootstrapMessageBoxHelper.Show(owner: FindParentWindow(), message: $"حدث خطأ أثناء حذف العلامة التجارية: {ex.Message}");
                    }
                }
            }
        }

        private async void BrandsGrid_RowEditEnding(object sender, DataGridRowEditEndingEventArgs e)
        {
            if (e.EditAction == DataGridEditAction.Commit && e.Row.Item is BrandDto brand && brand.Id != 0)
            {
                try
                {
                    await _brandsServiceClient.UpdateBrandAsync(brand);
                    Helpers.BootstrapMessageBoxHelper.Show(owner: FindParentWindow(), message: "تم حفظ التعديلات بنجاح");
                }
                catch (Exception ex)
                {
                    Helpers.BootstrapMessageBoxHelper.Show(owner: FindParentWindow(), message: $"حدث خطأ أثناء حفظ التعديلات: {ex.Message}");
                }
            }
        }
    }
}