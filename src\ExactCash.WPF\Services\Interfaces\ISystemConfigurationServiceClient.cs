﻿using ExactCash.Application.DTOs.Common;
using ExactCash.Application.DTOs;
using ExactCash.Application.Responses;

namespace ExactCash.WPF.Services.Interfaces
{
    public interface ISystemConfigurationServiceClient
    {
        Task<SystemConfigurationDto> GetSystemConfigurationByIdAsync(int id);
        
        Task<PagedResponse<SystemConfigurationDto>> GetAllSystemConfigurationsAsync(string key, string category, PaginationFilter pagination);

        Task<List<SystemConfigurationDto>> GetAllSystemConfigurationsAsync();

        Task<SystemConfigurationDto> CreateSystemConfigurationAsync(SystemConfigurationDto configurationDto);
        
        Task UpdateSystemConfigurationAsync(SystemConfigurationDto configurationDto);
        
        Task DeleteSystemConfigurationAsync(int id);
    }
}
