﻿using ExactCash.Application.DTOs;
using ExactCash.Application.DTOs.Common;
using ExactCash.Application.Responses;
using ExactCash.Domain.Entities;
using Microsoft.AspNetCore.Http;

namespace ExactCash.Application.Contracts
{
    public interface IProductService
    {
        Task<BaseResponse<Product>> CreateAsync(Product product, IFormFile imageFile,string createdBy);
        Task<Product> GetByIdAsync(int id);
        Task<PagedResponse<ProductDto>> GetAllAsync(string name, string sku, int? categoryId,int?brandId, int? unitId, string barcode, PaginationFilter pagination);
        Task<List<ExactCash.Application.DTOs.ProductDto>> SearchProductsAsync(string searchTerm);
        Task<DTOs.ProductDto> GetProductByBarcodeAsync(string barcode);
        Task<BaseResponse<Product>> UpdateAsync(int id, Product product, IFormFile formFile);
        Task<bool> DeleteAsync(int id);
        Task<bool> UpdateStockAsync(int productId, int quantityChange);
        //Task<bool> PrintBarcodeAsync(string barcode);
        Task<byte[]> GenerateBarcodeImageAsync(int productId);
    }
}
