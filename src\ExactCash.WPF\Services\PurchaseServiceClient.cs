﻿using ExactCash.Application.DTOs;
using ExactCash.Application.DTOs.Common;
using ExactCash.Application.Responses;

namespace ExactCash.WPF.Services
{
    public class PurchaseServiceClient : IPurchaseServiceClient
    {
        private readonly HttpService _httpService;

        public PurchaseServiceClient(string baseUrl)
        {
            _httpService = new HttpService(baseUrl);
        }

        public async Task<BaseResponse<bool>> CreatePurchaseAsync(PurchaseDto purchaseDto)
        {
            return await _httpService.PostAsync<BaseResponse<bool>>("api/Purchases", purchaseDto);
        }

        public async Task DeletePurchaseAsync(int id)
        {
            await _httpService.DeleteAsync($"api/Purchases/{id}");
        }

        public async Task<PagedResponse<PurchaseDto>> GetAllPurchasesAsync(int? supplierId, string poNumber, DateTime? startDate, DateTime? endDate, PaginationFilter pagination)
        {
            var queryParams = new List<string>();

            if (supplierId != null) queryParams.Add($"supplierId={supplierId}");
            if (!string.IsNullOrEmpty(poNumber)) queryParams.Add($"poNumber={poNumber}");
            if (startDate.HasValue)
            {
                var utcStartDate = startDate.Value.Kind == DateTimeKind.Unspecified
                    ? DateTime.SpecifyKind(startDate.Value, DateTimeKind.Utc)
                    : startDate.Value.ToUniversalTime();
                queryParams.Add($"startDate={Uri.EscapeDataString(utcStartDate.ToString("yyyy-MM-ddTHH:mm:ss.fffZ"))}");
            }
            if (endDate.HasValue)
            {
                var utcEndDate = endDate.Value.Kind == DateTimeKind.Unspecified
                    ? DateTime.SpecifyKind(endDate.Value, DateTimeKind.Utc)
                    : endDate.Value.ToUniversalTime();
                queryParams.Add($"endDate={Uri.EscapeDataString(utcEndDate.ToString("yyyy-MM-ddTHH:mm:ss.fffZ"))}");
            }

            if (pagination != null)
            {
                queryParams.Add($"pageNumber={pagination.PageNumber}");
                queryParams.Add($"pageSize={pagination.PageSize}");
            }

            var queryString = string.Join("&", queryParams);
            var url = $"api/Purchases?{queryString}";

            return await _httpService.GetAsync<PagedResponse<PurchaseDto>>(url);
        }

        public async Task<int> GetLastPOIdAsync()
        {
            return await _httpService.GetAsync<int>("api/Purchases/last-id");
        }

        public async Task<PurchaseDto> GetPurchaseByIdAsync(int id)
        {
            return await _httpService.GetAsync<PurchaseDto>($"api/Purchases/{id}");
        }

        public async Task UpdatePurchaseAsync(PurchaseDto purchaseDto)
        {
            await _httpService.PutAsync($"api/Purchases/{purchaseDto.Id}", purchaseDto);
        }

        public async Task<List<PurchaseOrderReportDto>> GetPurchaseOrderReport(DateTime? startDate, DateTime? endDate)
        {
            var queryParams = new List<string>();

            if (startDate.HasValue)
            {
                var utcStartDate = startDate.Value.Kind == DateTimeKind.Unspecified
                    ? DateTime.SpecifyKind(startDate.Value, DateTimeKind.Utc)
                    : startDate.Value.ToUniversalTime();
                queryParams.Add($"startDate={Uri.EscapeDataString(utcStartDate.ToString("yyyy-MM-ddTHH:mm:ss.fffZ"))}");
            }
            if (endDate.HasValue)
            {
                var utcEndDate = endDate.Value.Kind == DateTimeKind.Unspecified
                    ? DateTime.SpecifyKind(endDate.Value, DateTimeKind.Utc)
                    : endDate.Value.ToUniversalTime();
                queryParams.Add($"endDate={Uri.EscapeDataString(utcEndDate.ToString("yyyy-MM-ddTHH:mm:ss.fffZ"))}");
            }

            var queryString = string.Join("&", queryParams);
            var url = $"api/Purchases/get-purchase-order-report?{queryString}";

            return await _httpService.GetAsync<List<PurchaseOrderReportDto>>(url);
        }
    }
}
