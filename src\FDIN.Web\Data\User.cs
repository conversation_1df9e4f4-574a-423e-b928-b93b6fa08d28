﻿#nullable disable
namespace FDIN.Web.Data
{
    public class User : BaseEntity
    {
        /// <summary>
        /// The full name of the customer.
        /// </summary>
        public string FullName { get; set; }

        /// <summary>
        /// The phone number of the customer for contact purposes.
        /// </summary>
        public string Phone { get; set; }

        /// <summary>
        /// The email address of the customer.
        /// </summary>
        public string Email { get; set; }

        /// <summary>
        /// The physical address of the customer.
        /// </summary>
        public string Address { get; set; }

        /// <summary>
        /// Sales
        /// </summary>
        public virtual List<Sale> Sales { get; set; }

    }
}
