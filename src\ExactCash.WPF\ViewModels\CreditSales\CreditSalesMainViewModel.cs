using ExactCash.Application.DTOs;
using ExactCash.WPF.Commands;
using ExactCash.WPF.Helpers;
using ExactCash.WPF.Models;
using ExactCash.WPF.Services;
using ExactCash.WPF.Services.Interfaces;
using ExactCash.WPF.ViewModels;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using System.Windows.Input;
using System.Linq;

namespace ExactCash.WPF.ViewModels.CreditSales
{
    public class CreditSalesMainViewModel : ViewModelBase, INotifyPropertyChanged
    {
        #region Fields
        private readonly ISaleServiceClient _saleService;
        private readonly ICustomerService _customerService;
        private readonly IPaymentServiceClient _paymentService;

        private ObservableCollection<OutstandingSaleDto> _outstandingSales;
        private ObservableCollection<CustomerCreditLimitDto> _customerCreditLimits;

        private string _invoiceNumberSearch;
        private string _customerNameSearch;
        private DateTime? _fromDate;
        private DateTime? _toDate;

        private decimal _totalOutstandingAmount;
        private int _outstandingSalesCount;
        private decimal _overdueAmount;
        private double _averageDaysOverdue;
        #endregion

        #region Properties
        public ObservableCollection<OutstandingSaleDto> OutstandingSales
        {
            get => _outstandingSales;
            set => SetProperty(ref _outstandingSales, value);
        }

        public ObservableCollection<CustomerCreditLimitDto> CustomerCreditLimits
        {
            get => _customerCreditLimits;
            set => SetProperty(ref _customerCreditLimits, value);
        }

        public string InvoiceNumberSearch
        {
            get => _invoiceNumberSearch;
            set => SetProperty(ref _invoiceNumberSearch, value);
        }

        public string CustomerNameSearch
        {
            get => _customerNameSearch;
            set => SetProperty(ref _customerNameSearch, value);
        }

        public DateTime? FromDate
        {
            get => _fromDate;
            set => SetProperty(ref _fromDate, value);
        }

        public DateTime? ToDate
        {
            get => _toDate;
            set => SetProperty(ref _toDate, value);
        }

        public decimal TotalOutstandingAmount
        {
            get => _totalOutstandingAmount;
            set => SetProperty(ref _totalOutstandingAmount, value);
        }

        public int OutstandingSalesCount
        {
            get => _outstandingSalesCount;
            set => SetProperty(ref _outstandingSalesCount, value);
        }

        public decimal OverdueAmount
        {
            get => _overdueAmount;
            set => SetProperty(ref _overdueAmount, value);
        }

        public double AverageDaysOverdue
        {
            get => _averageDaysOverdue;
            set => SetProperty(ref _averageDaysOverdue, value);
        }
        #endregion

        #region Commands
        public ICommand SearchCommand { get; }
        public ICommand RefreshCommand { get; }
        public ICommand CollectPaymentCommand { get; }
        public ICommand CollectPaymentForSaleCommand { get; }
        public ICommand ViewSaleDetailsCommand { get; }
        public ICommand ViewPaymentHistoryCommand { get; }
        public ICommand OutstandingReportCommand { get; }
        public ICommand UpdateCreditLimitCommand { get; }
        public ICommand EditCreditLimitCommand { get; }

        #endregion

        #region Constructor
        public CreditSalesMainViewModel(
            ISaleServiceClient saleService,
            ICustomerService customerService,
            IPaymentServiceClient paymentService)
        {
            _saleService = saleService;
            _customerService = customerService;
            _paymentService = paymentService;

            // Initialize collections
            OutstandingSales = new ObservableCollection<OutstandingSaleDto>();
            CustomerCreditLimits = new ObservableCollection<CustomerCreditLimitDto>();

            // Initialize date filters
            FromDate = DateTime.Today.AddMonths(-3); // Default to last 3 months
            ToDate = DateTime.Today;

            // Initialize commands
            SearchCommand = new Commands.RelayCommand(ExecuteSearch);
            RefreshCommand = new Commands.RelayCommand(ExecuteRefresh);
            CollectPaymentCommand = new Commands.RelayCommand(ExecuteCollectPayment);
            CollectPaymentForSaleCommand = new Commands.RelayCommand<OutstandingSaleDto>(ExecuteCollectPaymentForSale);
            ViewSaleDetailsCommand = new Commands.RelayCommand<OutstandingSaleDto>(ExecuteViewSaleDetails);
            ViewPaymentHistoryCommand = new Commands.RelayCommand<OutstandingSaleDto>(ExecuteViewPaymentHistory);
            OutstandingReportCommand = new Commands.RelayCommand(ExecuteOutstandingReport);
            UpdateCreditLimitCommand = new Commands.RelayCommand(ExecuteUpdateCreditLimit);
            EditCreditLimitCommand = new Commands.RelayCommand<CustomerCreditLimitDto>(ExecuteEditCreditLimit);


            // Load initial data
            LoadDataAsync();
        }
        #endregion

        #region Methods
        private async void LoadDataAsync()
        {
            try
            {
                await LoadOutstandingSalesAsync();
                await LoadCustomerCreditLimitsAsync();
                CalculateSummary();
            }
            catch (Exception ex)
            {
                Helpers.BootstrapMessageBoxHelper.ShowError($"حدث خطأ أثناء تحميل البيانات: {ex.Message}", "خطأ");
            }
        }

        private async Task LoadOutstandingSalesAsync()
        {
            try
            {
                // This would be a new method in the sale service to get outstanding sales
                var outstandingSales = await GetOutstandingSalesAsync();

                OutstandingSales.Clear();
                foreach (var sale in outstandingSales)
                {
                    OutstandingSales.Add(sale);
                }
            }
            catch (Exception ex)
            {
                Helpers.BootstrapMessageBoxHelper.ShowError($"حدث خطأ أثناء تحميل المبيعات المستحقة: {ex.Message}", "خطأ");
            }
        }

        private async Task LoadCustomerCreditLimitsAsync()
        {
            try
            {
                // This would be a new method to get customer credit limits
                var creditLimits = await GetCustomerCreditLimitsAsync();

                CustomerCreditLimits.Clear();
                foreach (var limit in creditLimits)
                {
                    CustomerCreditLimits.Add(limit);
                }
            }
            catch (Exception ex)
            {
                Helpers.BootstrapMessageBoxHelper.ShowError($"حدث خطأ أثناء تحميل حدود الائتمان: {ex.Message}", "خطأ");
            }
        }



        private void CalculateSummary()
        {
            if (OutstandingSales?.Any() == true)
            {
                TotalOutstandingAmount = OutstandingSales.Sum(s => s.RemainingAmount);
                OutstandingSalesCount = OutstandingSales.Count;
                OverdueAmount = OutstandingSales.Where(s => s.DaysOverdue > 0).Sum(s => s.RemainingAmount);

                var overdueSales = OutstandingSales.Where(s => s.DaysOverdue > 0);
                AverageDaysOverdue = overdueSales.Any() ? overdueSales.Average(s => s.DaysOverdue) : 0;
            }
            else
            {
                TotalOutstandingAmount = 0;
                OutstandingSalesCount = 0;
                OverdueAmount = 0;
                AverageDaysOverdue = 0;
            }
        }

        private async void ExecuteSearch()
        {
            await LoadOutstandingSalesAsync();
            CalculateSummary();
        }

        private async void ExecuteRefresh()
        {
            LoadDataAsync();
        }

        private void ExecuteCollectPayment()
        {
            // Open payment collection dialog
            var paymentView = new Views.CreditSales.PaymentCollectionView();
            paymentView.ShowDialog();
        }

        private void ExecuteCollectPaymentForSale(OutstandingSaleDto sale)
        {
            if (sale == null) return;

            // Open payment collection dialog for specific sale
            var paymentView = new Views.CreditSales.PaymentCollectionView(sale);
            var result = paymentView.ShowDialog();

            if (result == true)
            {
                // Refresh data after payment collection
                ExecuteRefresh();
            }
        }

        private void ExecuteViewSaleDetails(OutstandingSaleDto sale)
        {
            if (sale == null) return;

            // Open sale details view
            // TODO: Implement SaleDetailsView
            Helpers.BootstrapMessageBoxHelper.ShowSuccess($"عرض تفاصيل الفاتورة رقم {sale.InvoiceNumber}", "تفاصيل الفاتورة");
        }

        private async void ExecuteViewPaymentHistory(OutstandingSaleDto sale)
        {
            if (sale == null) return;

            try
            {
                // Create a payment history window for this specific sale
                var paymentHistoryWindow = new Views.CreditSales.PaymentHistoryWindow(sale);
                paymentHistoryWindow.ShowDialog();
            }
            catch (Exception ex)
            {
                Helpers.BootstrapMessageBoxHelper.ShowError($"حدث خطأ أثناء عرض تاريخ المدفوعات: {ex.Message}", "خطأ");
            }
        }

        private void ExecuteOutstandingReport()
        {
            // Generate outstanding payments report
            // TODO: Implement OutstandingPaymentsReportView
            Helpers.BootstrapMessageBoxHelper.ShowSuccess("سيتم تطوير تقرير المديونية قريباً", "تقرير المديونية");
        }

        private void ExecuteUpdateCreditLimit()
        {
            // Open credit limit update dialog
            var creditLimitView = new Views.CreditSales.CreditLimitUpdateView();
            var result = creditLimitView.ShowDialog();

            if (result == true)
            {
                // Refresh credit limits after update
                LoadCustomerCreditLimitsAsync();
            }
        }

        private void ExecuteEditCreditLimit(CustomerCreditLimitDto creditLimit)
        {
            if (creditLimit == null) return;

            // Open credit limit edit dialog for specific customer
            var creditLimitView = new Views.CreditSales.CreditLimitUpdateView(creditLimit);
            var result = creditLimitView.ShowDialog();

            if (result == true)
            {
                // Refresh credit limits after update
                LoadCustomerCreditLimitsAsync();
            }
        }



        // Real API implementation methods
        private async Task<List<OutstandingSaleDto>> GetOutstandingSalesAsync()
        {
            try
            {
                // Create search criteria based on current filters
                var searchDto = new CreditSalesSearchDto
                {
                    FromDate = FromDate,
                    ToDate = DateTime.Now, // Default to current date if not specified
                    InvoiceNumber = InvoiceNumberSearch,
                    CustomerName = CustomerNameSearch
                };

                // Call the real API through PaymentServiceClient
                var outstandingSales = await _paymentService.GetOutstandingSalesAsync(searchDto);

                // If no real data, add some mock data for testing the footer binding
                if (outstandingSales == null || !outstandingSales.Any())
                {
                    outstandingSales = new List<OutstandingSaleDto>
                    {
                        new OutstandingSaleDto
                        {
                            Id = 1,
                            InvoiceNumber = "INV-001",
                            CustomerId = 1,
                            CustomerName = "أحمد محمد",
                            CreationDate = DateTime.Now.AddDays(-10),
                            TotalAmount = 1500.00m,
                            PaidAmount = 500.00m,
                            RemainingAmount = 1000.00m,
                            DaysOverdue = 10
                        },
                        new OutstandingSaleDto
                        {
                            Id = 2,
                            InvoiceNumber = "INV-002",
                            CustomerId = 2,
                            CustomerName = "فاطمة علي",
                            CreationDate = DateTime.Now.AddDays(-5),
                            TotalAmount = 2500.00m,
                            PaidAmount = 1000.00m,
                            RemainingAmount = 1500.00m,
                            DaysOverdue = 5
                        },
                        new OutstandingSaleDto
                        {
                            Id = 3,
                            InvoiceNumber = "INV-003",
                            CustomerId = 3,
                            CustomerName = "محمود حسن",
                            CreationDate = DateTime.Now.AddDays(-20),
                            TotalAmount = 3000.00m,
                            PaidAmount = 500.00m,
                            RemainingAmount = 2500.00m,
                            DaysOverdue = 20
                        }
                    };
                }

                return outstandingSales;
            }
            catch (Exception ex)
            {
                // Log error and return empty list
                System.Diagnostics.Debug.WriteLine($"Error getting outstanding sales: {ex.Message}");
                return new List<OutstandingSaleDto>();
            }
        }

        private async Task<List<CustomerCreditLimitDto>> GetCustomerCreditLimitsAsync()
        {
            try
            {
                // Call the real API through PaymentServiceClient
                var creditLimits = await _paymentService.GetCustomerCreditLimitsAsync();
                return creditLimits ?? new List<CustomerCreditLimitDto>();
            }
            catch (Exception ex)
            {
                // Log error and return empty list
                System.Diagnostics.Debug.WriteLine($"Error getting customer credit limits: {ex.Message}");
                return new List<CustomerCreditLimitDto>();
            }
        }


        #endregion

        #region INotifyPropertyChanged
        public event PropertyChangedEventHandler PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        protected bool SetProperty<T>(ref T field, T value, [CallerMemberName] string propertyName = null)
        {
            if (EqualityComparer<T>.Default.Equals(field, value))
                return false;

            field = value;
            OnPropertyChanged(propertyName);
            return true;
        }
        #endregion
    }
}
