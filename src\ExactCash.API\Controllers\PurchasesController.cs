using ExactCash.Application.DTOs;
using ExactCash.Application.DTOs.Common;
using ExactCash.Application.Interfaces;
using Microsoft.AspNetCore.Mvc;
using System.Security.Claims;
#nullable disable

namespace ExactCash.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class PurchasesController : ControllerBase
    {
        private readonly IPurchaseService _purchaseService;

        public PurchasesController(IPurchaseService purchaseService)
        {
            _purchaseService = purchaseService;
        }

        [HttpGet]
        public async Task<IActionResult> GetAllPurchases(
            [FromQuery] int? supplierId,
            [FromQuery] string poNumber,
            [FromQuery] DateTime? startDate,
            [FromQuery] DateTime? endDate,
            [FromQuery] PaginationFilter pagination)
        {
            var result = await _purchaseService.GetAllPurchasesAsync(supplierId, poNumber, startDate, endDate, pagination);
            return Ok(result);
        }

        [HttpGet("{id}")]
        public async Task<IActionResult> GetPurchase(int id)
        {
            var purchase = await _purchaseService.GetPurchaseByIdAsync(id);
            if (purchase == null)
                return NotFound();

            return Ok(purchase);
        }


        [HttpGet("last-id")]
        public async Task<IActionResult> GetLastId()
        {
            return Ok(await _purchaseService.GetLastPOIdAsync());
        }

        [HttpGet("get-purchase-order-report")]
        public async Task<IActionResult> GetPurchaseOrderReport(
        [FromQuery] DateTime? startDate,
        [FromQuery] DateTime? endDate)
        {
            var result = await _purchaseService.GetPurchaseOrderReport(startDate, endDate);
            return Ok(result);
        }



        [HttpPost]
        public async Task<IActionResult> CreatePurchase([FromBody] PurchaseDto purchaseDto)
        {
            var userEmail = User.FindFirst(ClaimTypes.Email)?.Value;
            var createdPurchase = await _purchaseService.CreatePurchaseAsync(purchaseDto, userEmail);
            return StatusCode(createdPurchase.StatusCode, createdPurchase);
        }

        [HttpPut("{id}")]
        public async Task<IActionResult> UpdatePurchase(int id, [FromBody] PurchaseDto purchaseDto)
        {
            if (id != purchaseDto.Id)
                return BadRequest("ID mismatch");

            try
            {
                await _purchaseService.UpdatePurchaseAsync(purchaseDto);
                return NoContent();
            }
            catch (KeyNotFoundException)
            {
                return NotFound();
            }
        }

        [HttpDelete("{id}")]
        public async Task<IActionResult> DeletePurchase(int id)
        {
            try
            {
                await _purchaseService.DeletePurchaseAsync(id);
                return NoContent();
            }
            catch (KeyNotFoundException)
            {
                return NotFound();
            }
        }
    }
}