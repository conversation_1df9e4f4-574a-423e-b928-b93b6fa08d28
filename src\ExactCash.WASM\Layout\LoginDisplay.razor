﻿@using System.Text.Json
@using System.Security.Claims
@using ExactCash.WASM.Identity
@using Microsoft.AspNetCore.Components.Authorization
@inject IHttpClientFactory ClientFactory
@inject AuthenticationStateProvider _authenticationStateProvider
@inject BlazorBootstrap.ToastService BlazorBootstrapToastService
@inject IAccountManagement AccountManagement
@inject NavigationManager Navigation
@inject IJSRuntime JS
@inject IConfiguration Configuration;
<div class="login-display">
    <div class="user-info">
        <div class="details">
            <div class="availability">
                <a href="Identity/Manage">Hello, @userEmail</a>
                @* <button class="btn btn-primary" @onclick="Logout">Logout</button> *@
                <a href="#" onclick="@Logout">Logout</a>
            </div>
        </div>
    </div>
</div>

@code {
    #nullable disable
    int userId;
    string userEmail = "";
    bool isAvailable = false;

    private async Task Logout()
    {
        await AccountManagement.LogoutAsync(userId);
        Navigation.NavigateTo("/login");
    }
}