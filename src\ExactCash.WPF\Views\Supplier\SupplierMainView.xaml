<Window x:Class="ExactCash.WPF.Views.Supplier.SupplierMainView"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="إدارة الموردين"
        Height="800"
        Width="1200"
        WindowStartupLocation="CenterScreen"
        WindowStyle="None"
        AllowsTransparency="True"
        Background="Transparent"
        FlowDirection="RightToLeft"
        x:Name="SupplierMainViewScreen">

    <Window.Resources>
        <!-- Modern Button Style -->
        <Style x:Key="ModernButton"
               TargetType="Button">
            <Setter Property="Background"
                    Value="#0078D4"/>
            <Setter Property="Foreground"
                    Value="White"/>
            <Setter Property="BorderThickness"
                    Value="0"/>
            <Setter Property="Padding"
                    Value="15,8"/>
            <Setter Property="FontSize"
                    Value="14"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                CornerRadius="4">
                            <ContentPresenter HorizontalAlignment="Center"
                                              VerticalAlignment="Center"/>
                        </Border>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
            <Style.Triggers>
                <Trigger Property="IsMouseOver"
                         Value="True">
                    <Setter Property="Background"
                            Value="#106EBE"/>
                </Trigger>
            </Style.Triggers>
        </Style>

        <!-- Success Button Style -->
        <Style x:Key="SuccessButton"
               TargetType="Button">
            <Setter Property="Background"
                    Value="#28A745"/>
            <Setter Property="Foreground"
                    Value="White"/>
            <Setter Property="BorderThickness"
                    Value="0"/>
            <Setter Property="Padding"
                    Value="15,8"/>
            <Setter Property="FontSize"
                    Value="14"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                CornerRadius="4">
                            <ContentPresenter HorizontalAlignment="Center"
                                              VerticalAlignment="Center"/>
                        </Border>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
            <Style.Triggers>
                <Trigger Property="IsMouseOver"
                         Value="True">
                    <Setter Property="Background"
                            Value="#218838"/>
                </Trigger>
            </Style.Triggers>
        </Style>
    </Window.Resources>

    <Border Background="White"
            CornerRadius="10"
            BorderThickness="1"
            BorderBrush="#E0E0E0">
        <Border.Effect>
            <DropShadowEffect Color="Gray"
                              Direction="315"
                              ShadowDepth="5"
                              Opacity="0.3"
                              BlurRadius="10"/>
        </Border.Effect>
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="60"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>

            <!-- Header -->
            <Border Grid.Row="0"
                    Background="#0078D4"
                    CornerRadius="10,10,0,0">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <TextBlock Text="إدارة الموردين"
                               Grid.Column="0"
                               VerticalAlignment="Center"
                               HorizontalAlignment="Center"
                               FontSize="20"
                               FontWeight="Bold"
                               Foreground="White"/>

                    <!-- Close Button -->
                    <Button Grid.Column="1"
                            Content="✕"
                            Click="CloseButton_Click"
                            FontSize="16"
                            Foreground="White"
                            Width="60"
                            Height="60"
                            Background="Transparent"
                            BorderThickness="0"/>
                    <!-- Minimize Button -->
                    <Button Grid.Column="2"
                            Content="−"
                            Click="MinimizeButton_Click"
                            FontSize="20"
                            Foreground="White"
                            Width="60"
                            Height="60"
                            Background="Transparent"
                            BorderThickness="0"/>
                </Grid>
            </Border>

            <!-- Content -->
            <Grid Grid.Row="1"
                  Margin="20">
                <TabControl>
                    <!-- Suppliers Tab -->
                    <TabItem Header="الموردين">
                        <Border BorderThickness="1"
                                BorderBrush="#dee2e6"
                                Background="White"
                                CornerRadius="6"
                                Padding="0">
                            <Grid Margin="20">
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="*"/>
                                    <RowDefinition Height="Auto"/>
                                </Grid.RowDefinitions>

                                <!-- Header -->
                                <TextBlock Text="قائمة الموردين"
                                           FontSize="20"
                                           FontWeight="Bold"
                                           Foreground="#0078D4"
                                           Margin="0,0,0,20"/>

                                <!-- Search Section -->
                                <Grid Grid.Row="1"
                                      Margin="0,0,0,20">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="Auto"/>
                                        <ColumnDefinition Width="Auto"/>
                                    </Grid.ColumnDefinitions>

                                    <Grid Grid.Column="0"
                                          Margin="0,0,10,0">
                                        <TextBox Text="{Binding SupplierListViewModel.SearchName, UpdateSourceTrigger=PropertyChanged}"
                                                 Height="35"
                                                 Padding="10,5"
                                                 FontSize="14"
                                                 BorderBrush="#CCCCCC"
                                                 BorderThickness="1"
                                                 Background="White"
                                                 FlowDirection="RightToLeft"/>
                                        <TextBlock Text="البحث بالاسم..."
                                                   Foreground="Gray"
                                                   FontSize="14"
                                                   Margin="15,0,0,0"
                                                   VerticalAlignment="Center"
                                                   HorizontalAlignment="Right"
                                                   IsHitTestVisible="False"
                                                   FlowDirection="RightToLeft">
                                            <TextBlock.Style>
                                                <Style TargetType="TextBlock">
                                                    <Setter Property="Visibility"
                                                            Value="Collapsed"/>
                                                    <Style.Triggers>
                                                        <DataTrigger Binding="{Binding SupplierListViewModel.SearchName}"
                                                                     Value="">
                                                            <Setter Property="Visibility"
                                                                    Value="Visible"/>
                                                        </DataTrigger>
                                                        <DataTrigger Binding="{Binding SupplierListViewModel.SearchName}"
                                                                     Value="{x:Null}">
                                                            <Setter Property="Visibility"
                                                                    Value="Visible"/>
                                                        </DataTrigger>
                                                    </Style.Triggers>
                                                </Style>
                                            </TextBlock.Style>
                                        </TextBlock>
                                    </Grid>

                                    <Grid Grid.Column="1"
                                          Margin="0,0,10,0">
                                        <TextBox Text="{Binding SupplierListViewModel.SearchPhone, UpdateSourceTrigger=PropertyChanged}"
                                                 Height="35"
                                                 Padding="10,5"
                                                 FontSize="14"
                                                 BorderBrush="#CCCCCC"
                                                 BorderThickness="1"
                                                 Background="White"
                                                 FlowDirection="RightToLeft"/>
                                        <TextBlock Text="البحث برقم الهاتف..."
                                                   Foreground="Gray"
                                                   FontSize="14"
                                                   Margin="15,0,0,0"
                                                   VerticalAlignment="Center"
                                                   HorizontalAlignment="Right"
                                                   IsHitTestVisible="False"
                                                   FlowDirection="RightToLeft">
                                            <TextBlock.Style>
                                                <Style TargetType="TextBlock">
                                                    <Setter Property="Visibility"
                                                            Value="Collapsed"/>
                                                    <Style.Triggers>
                                                        <DataTrigger Binding="{Binding SupplierListViewModel.SearchPhone}"
                                                                     Value="">
                                                            <Setter Property="Visibility"
                                                                    Value="Visible"/>
                                                        </DataTrigger>
                                                        <DataTrigger Binding="{Binding SupplierListViewModel.SearchPhone}"
                                                                     Value="{x:Null}">
                                                            <Setter Property="Visibility"
                                                                    Value="Visible"/>
                                                        </DataTrigger>
                                                    </Style.Triggers>
                                                </Style>
                                            </TextBlock.Style>
                                        </TextBlock>
                                    </Grid>

                                    <ComboBox Grid.Column="2"
                                              ItemsSource="{Binding SupplierListViewModel.SearchCategories}"
                                              SelectedItem="{Binding SupplierListViewModel.SearchCategory}"
                                              DisplayMemberPath="Name"
                                              Height="35"
                                              Margin="0,0,10,0"
                                              Padding="10,5"
                                              FontSize="14"
                                              BorderBrush="#CCCCCC"
                                              BorderThickness="1"
                                              Background="White"
                                              FlowDirection="RightToLeft"/>

                                    <Button Grid.Column="3"
                                            Content="بحث"
                                            Command="{Binding SupplierListViewModel.SearchCommand}"
                                            Width="80"
                                            Height="35"
                                            Margin="0,0,10,0"
                                            Background="#0078D4"
                                            Foreground="White"
                                            BorderThickness="0"
                                            FontWeight="Bold"
                                            Cursor="Hand"/>

                                    <Button Grid.Column="4"
                                            Content="إعادة تعيين"
                                            Command="{Binding SupplierListViewModel.ResetSearchCommand}"
                                            Width="100"
                                            Height="35"
                                            Background="#6C757D"
                                            Foreground="White"
                                            BorderThickness="0"
                                            FontWeight="Bold"
                                            Cursor="Hand"/>
                                </Grid>

                                <!-- Statistics -->
                                <Grid Grid.Row="2"
                                      Margin="0,0,0,20">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>

                                    <Border Grid.Column="0"
                                            Background="#E3F2FD"
                                            CornerRadius="5"
                                            Padding="15"
                                            Margin="0,0,10,0">
                                        <StackPanel>
                                            <TextBlock Text="إجمالي الموردين"
                                                       FontWeight="Bold"
                                                       Foreground="#1976D2"/>
                                            <TextBlock Text="{Binding SupplierListViewModel.TotalItems}"
                                                       FontSize="24"
                                                       FontWeight="Bold"
                                                       Foreground="#1976D2"/>
                                        </StackPanel>
                                    </Border>

                                    <Border Grid.Column="1"
                                            Background="#E8F5E8"
                                            CornerRadius="5"
                                            Padding="15"
                                            Margin="0,0,10,0">
                                        <StackPanel>
                                            <TextBlock Text="الموردين النشطين"
                                                       FontWeight="Bold"
                                                       Foreground="#388E3C"/>
                                            <TextBlock Text="{Binding SupplierListViewModel.ActiveSuppliersCount}"
                                                       FontSize="24"
                                                       FontWeight="Bold"
                                                       Foreground="#388E3C"/>
                                        </StackPanel>
                                    </Border>

                                    <Border Grid.Column="2"
                                            Background="#FFF3E0"
                                            CornerRadius="5"
                                            Padding="15">
                                        <StackPanel>
                                            <TextBlock Text="الصفحة الحالية"
                                                       FontWeight="Bold"
                                                       Foreground="#F57C00"/>
                                            <TextBlock FontSize="24"
                                                       FontWeight="Bold"
                                                       Foreground="#F57C00">
                                                <TextBlock.Text>
                                                    <MultiBinding StringFormat="{}{0} من {1}">
                                                        <Binding Path="SupplierListViewModel.CurrentPage"/>
                                                        <Binding Path="SupplierListViewModel.TotalPages"/>
                                                    </MultiBinding>
                                                </TextBlock.Text>
                                            </TextBlock>
                                        </StackPanel>
                                    </Border>
                                </Grid>

                                <!-- Action Buttons -->
                                <StackPanel Grid.Row="3"
                                            Orientation="Horizontal"
                                            HorizontalAlignment="Right"
                                            Margin="0,0,0,10">
                                    <Button Content="إضافة مورد جديد"
                                            Command="{Binding SupplierListViewModel.AddNewSupplierCommand}"
                                            Style="{StaticResource SuccessButton}"
                                            Height="35"
                                            Width="120"
                                            Margin="0,0,10,0"/>
                                    <Button Content="تحديث"
                                            Command="{Binding SupplierListViewModel.RefreshCommand}"
                                            Style="{StaticResource ModernButton}"
                                            Height="35"
                                            Width="80"
                                            Margin="0,0,10,0"/>
                                    <Button Content="تصدير إلى Excel"
                                            Command="{Binding SupplierListViewModel.ExportToExcelCommand}"
                                            Style="{StaticResource ModernButton}"
                                            Height="35"
                                            Width="120"/>
                                </StackPanel>

                                <!-- DataGrid -->
                                <DataGrid Grid.Row="4"
                                          AutoGenerateColumns="False"
                                          IsReadOnly="True"
                                          SelectionMode="Single"
                                          SelectionUnit="FullRow"
                                          GridLinesVisibility="None"
                                          RowHeight="40"
                                          BorderThickness="1"
                                          BorderBrush="#DDDDDD"
                                          Background="White"
                                          ItemsSource="{Binding SupplierListViewModel.Suppliers}"
                                          AlternatingRowBackground="#F8F9FA">
                                    <DataGrid.Columns>
                                        <DataGridTextColumn Header="الاسم"
                                                            Binding="{Binding Name}"
                                                            Width="*"/>
                                        <DataGridTextColumn Header="الهاتف"
                                                            Binding="{Binding Phone}"
                                                            Width="120"/>
                                        <DataGridTextColumn Header="البريد الإلكتروني"
                                                            Binding="{Binding Email}"
                                                            Width="150"/>
                                        <DataGridTextColumn Header="العنوان"
                                                            Binding="{Binding Address}"
                                                            Width="200"/>
                                        <DataGridTextColumn Header="التصنيف"
                                                            Binding="{Binding CategoryName}"
                                                            Width="120"/>
                                        <DataGridCheckBoxColumn Header="نشط"
                                                                Binding="{Binding IsActive}"
                                                                Width="80"/>
                                        <DataGridTemplateColumn Header="الإجراءات"
                                                                Width="160">
                                            <DataGridTemplateColumn.CellTemplate>
                                                <DataTemplate>
                                                    <StackPanel Orientation="Horizontal"
                                                                HorizontalAlignment="Center">
                                                        <Button Style="{StaticResource ModernButton}"
                                                                Command="{Binding DataContext.SupplierListViewModel.EditSupplierCommand, RelativeSource={RelativeSource AncestorType=DataGrid}}"
                                                                CommandParameter="{Binding}"
                                                                ToolTip="تعديل"
                                                                Width="35"
                                                                Height="30"
                                                                Margin="0,0,5,0">
                                                            <TextBlock Text="✏️"
                                                                       FontSize="16"/>
                                                        </Button>
                                                        <Button Style="{StaticResource ModernButton}"
                                                                Background="#dc3545"
                                                                Command="{Binding DataContext.SupplierListViewModel.DeleteSupplierCommand, RelativeSource={RelativeSource AncestorType=DataGrid}}"
                                                                CommandParameter="{Binding}"
                                                                ToolTip="حذف"
                                                                Width="35"
                                                                Height="30">
                                                            <TextBlock Text="🗑️"
                                                                       FontSize="16"/>
                                                        </Button>
                                                    </StackPanel>
                                                </DataTemplate>
                                            </DataGridTemplateColumn.CellTemplate>
                                        </DataGridTemplateColumn>
                                    </DataGrid.Columns>
                                </DataGrid>

                                <!-- Pagination -->
                                <StackPanel Grid.Row="5"
                                            Orientation="Horizontal"
                                            HorizontalAlignment="Center"
                                            Margin="0,20,0,0">
                                    <Button Content="السابق"
                                            Command="{Binding SupplierListViewModel.PreviousPageCommand}"
                                            Style="{StaticResource ModernButton}"
                                            Height="35"
                                            Width="80"
                                            Margin="0,0,10,0"/>
                                    <TextBlock Text="{Binding SupplierListViewModel.PaginationInfo}"
                                               VerticalAlignment="Center"
                                               Margin="10,0"/>
                                    <Button Content="التالي"
                                            Command="{Binding SupplierListViewModel.NextPageCommand}"
                                            Style="{StaticResource ModernButton}"
                                            Height="35"
                                            Width="80"
                                            Margin="10,0,0,0"/>
                                </StackPanel>
                            </Grid>
                        </Border>
                    </TabItem>

                    <!-- Supplier Categories Tab -->
                    <TabItem Header="تصنيفات الموردين">
                        <Border BorderThickness="1"
                                BorderBrush="#dee2e6"
                                Background="White"
                                CornerRadius="6"
                                Padding="0">
                            <Grid Margin="20">
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="*"/>
                                </Grid.RowDefinitions>

                                <!-- Header -->
                                <TextBlock Text="تصنيفات الموردين"
                                           FontSize="20"
                                           FontWeight="Bold"
                                           Foreground="#0078D4"
                                           Margin="0,0,0,20"/>

                                <!-- Action Buttons -->
                                <StackPanel Grid.Row="1"
                                            Orientation="Horizontal"
                                            HorizontalAlignment="Right"
                                            Margin="0,0,0,10">
                                    <Button Content="إضافة تصنيف"
                                            Command="{Binding SupplierCategoryListViewModel.AddCategoryCommand}"
                                            Style="{StaticResource SuccessButton}"
                                            Height="35"
                                            Width="120"
                                            Margin="0,0,10,0"/>
                                    <Button Content="تحديث"
                                            Command="{Binding SupplierCategoryListViewModel.RefreshCommand}"
                                            Style="{StaticResource ModernButton}"
                                            Height="35"
                                            Width="80"
                                            Margin="0,0,10,0"/>
                                    <Button Content="تصدير إلى Excel"
                                            Command="{Binding SupplierCategoryListViewModel.ExportToExcelCommand}"
                                            Style="{StaticResource ModernButton}"
                                            Height="35"
                                            Width="120"/>
                                </StackPanel>

                                <!-- DataGrid -->
                                <DataGrid Grid.Row="2"
                                          AutoGenerateColumns="False"
                                          IsReadOnly="True"
                                          SelectionMode="Single"
                                          SelectionUnit="FullRow"
                                          GridLinesVisibility="None"
                                          RowHeight="40"
                                          BorderThickness="1"
                                          BorderBrush="#DDDDDD"
                                          Background="White"
                                          ItemsSource="{Binding SupplierCategoryListViewModel.Categories}"
                                          AlternatingRowBackground="#F8F9FA">
                                    <DataGrid.Columns>
                                        <DataGridTextColumn Header="الاسم"
                                                            Binding="{Binding Name}"
                                                            Width="*"/>
                                        <DataGridTextColumn Header="الوصف"
                                                            Binding="{Binding Description}"
                                                            Width="2*"/>
                                        <DataGridCheckBoxColumn Header="نشط"
                                                                Binding="{Binding IsActive}"
                                                                Width="80"/>
                                        <DataGridTemplateColumn Header="الإجراءات"
                                                                Width="160">
                                            <DataGridTemplateColumn.CellTemplate>
                                                <DataTemplate>
                                                    <StackPanel Orientation="Horizontal"
                                                                HorizontalAlignment="Center">
                                                        <Button Style="{StaticResource ModernButton}"
                                                                Command="{Binding DataContext.SupplierCategoryListViewModel.EditCommand, RelativeSource={RelativeSource AncestorType=DataGrid}}"
                                                                CommandParameter="{Binding}"
                                                                ToolTip="تعديل"
                                                                Width="35"
                                                                Height="30"
                                                                Margin="0,0,5,0">
                                                            <TextBlock Text="✏️"
                                                                       FontSize="16"/>
                                                        </Button>
                                                        <Button Style="{StaticResource ModernButton}"
                                                                Background="#dc3545"
                                                                Command="{Binding DataContext.SupplierCategoryListViewModel.DeleteCommand, RelativeSource={RelativeSource AncestorType=DataGrid}}"
                                                                CommandParameter="{Binding}"
                                                                ToolTip="حذف"
                                                                Width="35"
                                                                Height="30">
                                                            <TextBlock Text="🗑️"
                                                                       FontSize="16"/>
                                                        </Button>
                                                    </StackPanel>
                                                </DataTemplate>
                                            </DataGridTemplateColumn.CellTemplate>
                                        </DataGridTemplateColumn>
                                    </DataGrid.Columns>
                                </DataGrid>
                            </Grid>
                        </Border>
                    </TabItem>
                </TabControl>
            </Grid>
        </Grid>
    </Border>
</Window>
