using ExactCash.Application.DTOs;
using ExactCash.Application.DTOs.Common;
using ExactCash.Application.Responses;
using ExactCash.WPF.Services.Interfaces;
using System.Net.Http;

namespace ExactCash.WPF.Services
{
    /// <summary>
    /// Service client for payment operations
    /// </summary>
    public class PaymentServiceClient : IPaymentServiceClient
    {
        private readonly HttpService _httpService;
        private const string BaseUrl = "api/Payments";

        public PaymentServiceClient(string baseUrl)
        {
            _httpService = new HttpService(baseUrl);
        }

        /// <summary>
        /// Collects a payment against outstanding sales
        /// </summary>
        /// <param name="paymentDto">Payment collection details</param>
        /// <returns>Success response with payment ID</returns>
        public async Task<BaseResponse<int>> CollectPaymentAsync(PaymentCollectionDto paymentDto)
        {
            try
            {
                // Validate input
                if (paymentDto == null)
                {
                    return ResponseHelper.Failure<int>(400, 0, "Payment data is required");
                }

                if (!paymentDto.IsValidPayment)
                {
                    return ResponseHelper.Failure<int>(400, 0, "Invalid payment amount");
                }

                // Call the API endpoint for collecting payment
                var response = await _httpService.PostAsync<BaseResponse<int>>($"{BaseUrl}/collect", paymentDto);

                return response ?? ResponseHelper.Failure<int>(500, 0, "No response from server");
            }
            catch (HttpRequestException httpEx)
            {
                return ResponseHelper.Failure<int>(500, 0, $"Network error: {httpEx.Message}");
            }
            catch (Exception ex)
            {
                return ResponseHelper.Failure<int>(500, 0, $"Error collecting payment: {ex.Message}");
            }
        }

        /// <summary>
        /// Gets payment history for a customer
        /// </summary>
        public async Task<List<PaymentHistoryDto>> GetPaymentHistoryAsync(int customerId, DateTime? fromDate = null, DateTime? toDate = null)
        {
            try
            {
                var queryParams = new List<string>();
                if (fromDate.HasValue)
                    queryParams.Add($"fromDate={fromDate.Value:yyyy-MM-dd}");
                if (toDate.HasValue)
                    queryParams.Add($"toDate={toDate.Value:yyyy-MM-dd}");

                var queryString = queryParams.Count > 0 ? "?" + string.Join("&", queryParams) : "";
                var response = await _httpService.GetAsync<List<PaymentHistoryDto>>($"{BaseUrl}/history/{customerId}{queryString}");

                return response ?? new List<PaymentHistoryDto>();
            }
            catch (Exception)
            {
                return new List<PaymentHistoryDto>();
            }
        }

        /// <summary>
        /// Gets payment history for a specific sale
        /// </summary>
        public async Task<List<PaymentHistoryDto>> GetSalePaymentHistoryAsync(int saleId)
        {
            try
            {
                var response = await _httpService.GetAsync<List<PaymentHistoryDto>>($"{BaseUrl}/sale-history/{saleId}");
                return response ?? new List<PaymentHistoryDto>();
            }
            catch (Exception)
            {
                return new List<PaymentHistoryDto>();
            }
        }

        /// <summary>
        /// Gets outstanding sales for a customer
        /// </summary>
        public async Task<List<OutstandingSaleDto>> GetOutstandingSalesForCustomerAsync(int customerId)
        {
            try
            {
                var response = await _httpService.GetAsync<List<OutstandingSaleDto>>($"{BaseUrl}/outstanding/{customerId}");
                return response ?? new List<OutstandingSaleDto>();
            }
            catch (Exception)
            {
                return new List<OutstandingSaleDto>();
            }
        }

        /// <summary>
        /// Gets all outstanding sales with optional filters
        /// </summary>
        public async Task<List<OutstandingSaleDto>> GetOutstandingSalesAsync(CreditSalesSearchDto searchDto)
        {
            try
            {
                var response = await _httpService.PostAsync<List<OutstandingSaleDto>>($"{BaseUrl}/outstanding-search", searchDto);
                return response ?? new List<OutstandingSaleDto>();
            }
            catch (Exception)
            {
                return new List<OutstandingSaleDto>();
            }
        }

        /// <summary>
        /// Gets customer credit limits and utilization
        /// </summary>
        public async Task<List<CustomerCreditLimitDto>> GetCustomerCreditLimitsAsync()
        {
            try
            {
                var response = await _httpService.GetAsync<List<CustomerCreditLimitDto>>($"{BaseUrl}/credit-limits");
                return response ?? new List<CustomerCreditLimitDto>();
            }
            catch (Exception)
            {
                return new List<CustomerCreditLimitDto>();
            }
        }

        /// <summary>
        /// Updates customer credit limit
        /// </summary>
        public async Task<BaseResponse<bool>> UpdateCreditLimitAsync(CreditLimitUpdateDto updateDto)
        {
            try
            {
                if (updateDto == null)
                {
                    return ResponseHelper.Failure<bool>(400, false, "Credit limit update data is required");
                }

                var response = await _httpService.PutAsync<BaseResponse<bool>>($"{BaseUrl}/credit-limit", updateDto);
                return response ?? ResponseHelper.Failure<bool>(500, false, "No response from server");
            }
            catch (HttpRequestException httpEx)
            {
                return ResponseHelper.Failure<bool>(500, false, $"Network error: {httpEx.Message}");
            }
            catch (Exception ex)
            {
                return ResponseHelper.Failure<bool>(500, false, $"Error updating credit limit: {ex.Message}");
            }
        }

        /// <summary>
        /// Gets outstanding payments report
        /// </summary>
        public async Task<OutstandingPaymentsReportDto> GetOutstandingPaymentsReportAsync(DateTime? fromDate, DateTime? toDate)
        {
            try
            {
                var queryParams = new List<string>();
                if (fromDate.HasValue)
                    queryParams.Add($"fromDate={fromDate.Value:yyyy-MM-dd}");
                if (toDate.HasValue)
                    queryParams.Add($"toDate={toDate.Value:yyyy-MM-dd}");

                var queryString = queryParams.Count > 0 ? "?" + string.Join("&", queryParams) : "";
                var response = await _httpService.GetAsync<OutstandingPaymentsReportDto>($"{BaseUrl}/outstanding-report{queryString}");

                return response ?? new OutstandingPaymentsReportDto();
            }
            catch (Exception)
            {
                return new OutstandingPaymentsReportDto();
            }
        }

        /// <summary>
        /// Validates if a customer can make a credit purchase
        /// </summary>
        public async Task<CreditValidationDto> ValidateCreditPurchaseAsync(int customerId, decimal amount)
        {
            try
            {
                var response = await _httpService.GetAsync<CreditValidationDto>($"{BaseUrl}/validate-credit/{customerId}?amount={amount}");
                return response ?? new CreditValidationDto { IsValid = false, Message = "Unable to validate credit" };
            }
            catch (Exception)
            {
                return new CreditValidationDto { IsValid = false, Message = "Error validating credit" };
            }
        }

        /// <summary>
        /// Gets payment methods available in the system
        /// </summary>
        public async Task<List<string>> GetPaymentMethodsAsync()
        {
            try
            {
                var response = await _httpService.GetAsync<List<string>>($"{BaseUrl}/payment-methods");
                return response ?? new List<string> { "نقدي", "بطاقة ائتمان", "بطاقة خصم", "تحويل بنكي", "شيك" };
            }
            catch (Exception)
            {
                return new List<string> { "نقدي", "بطاقة ائتمان", "بطاقة خصم", "تحويل بنكي", "شيك" };
            }
        }

        /// <summary>
        /// Generates payment receipt
        /// </summary>
        public async Task<PaymentReceiptDto> GeneratePaymentReceiptAsync(int paymentId)
        {
            try
            {
                var response = await _httpService.GetAsync<PaymentReceiptDto>($"{BaseUrl}/receipt/{paymentId}");
                return response ?? new PaymentReceiptDto();
            }
            catch (Exception)
            {
                return new PaymentReceiptDto();
            }
        }

        /// <summary>
        /// Gets credit payment history from CreditPayment table
        /// </summary>
        public async Task<List<CreditPaymentHistoryDto>> GetCreditPaymentHistoryAsync(CreditPaymentSearchDto searchDto)
        {
            try
            {
                var response = await _httpService.PostAsync<List<CreditPaymentHistoryDto>>($"{BaseUrl}/credit-payment-history", searchDto);
                return response ?? new List<CreditPaymentHistoryDto>();
            }
            catch (Exception)
            {
                return new List<CreditPaymentHistoryDto>();
            }
        }

    }
}
