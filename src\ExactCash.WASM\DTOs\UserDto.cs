#nullable disable
using ExactCash.WASM.Application.DTOs.Common;
using System.Collections.Generic;

namespace ExactCash.WASM.Application.DTOs
{
    public class UserDto
    {
        /// <summary>
        /// Id
        /// </summary>
        public Guid Id { get; set; }

        /// <summary>
        /// The email address of the user.
        /// </summary>
        public string Email { get; set; }

        /// <summary>
        /// The username of the user.
        /// </summary>
        public string UserName { get; set; }

        /// <summary>
        /// Password
        /// </summary>
        public string Password { get; set; }

        /// <summary>
        /// The phone number of the user.
        /// </summary>
        public string PhoneNumber { get; set; }

        /// <summary>
        /// Whether the user's email is confirmed.
        /// </summary>
        public bool EmailConfirmed { get; set; }

        /// <summary>
        /// Whether the user's phone number is confirmed.
        /// </summary>
        public bool PhoneNumberConfirmed { get; set; }

        /// <summary>
        /// Whether two-factor authentication is enabled for the user.
        /// </summary>
        public bool TwoFactorEnabled { get; set; }

        /// <summary>
        /// Whether the user is locked out.
        /// </summary>
        public bool LockoutEnabled { get; set; }

        /// <summary>
        /// The number of failed access attempts.
        /// </summary>
        public int AccessFailedCount { get; set; }

        /// <summary>
        /// The roles assigned to the user.
        /// </summary>
        public IList<string> Roles { get; set; } = new List<string>();
    }

    public class CreateUserDto
    {
        /// <summary>
        /// The email address of the user.
        /// </summary>
        public string Email { get; set; }

        /// <summary>
        /// The username of the user.
        /// </summary>
        public string UserName { get; set; }

        /// <summary>
        /// The password of the user.
        /// </summary>
        public string Password { get; set; }

        /// <summary>
        /// The phone number of the user.
        /// </summary>
        public string PhoneNumber { get; set; }

        /// <summary>
        /// The roles to assign to the user.
        /// </summary>
        public IList<string> Roles { get; set; } = new List<string>();
    }

    public class UpdateUserDto
    {
        /// <summary>
        /// The email address of the user.
        /// </summary>
        public string Email { get; set; }

        /// <summary>
        /// The username of the user.
        /// </summary>
        public string UserName { get; set; }

        /// <summary>
        /// The phone number of the user.
        /// </summary>
        public string PhoneNumber { get; set; }

        /// <summary>
        /// The roles to assign to the user.
        /// </summary>
        public IList<string> Roles { get; set; } = new List<string>();
    }

    public class ChangePasswordDto
    {
        /// <summary>
        /// The current password of the user.
        /// </summary>
        public string CurrentPassword { get; set; }

        /// <summary>
        /// The new password of the user.
        /// </summary>
        public string NewPassword { get; set; }
    }

    public class ResetPasswordDto
    {
        /// <summary>
        /// The email address of the user.
        /// </summary>
        public string Email { get; set; }

        /// <summary>
        /// The token for password reset.
        /// </summary>
        public string Token { get; set; }

        /// <summary>
        /// The new password of the user.
        /// </summary>
        public string NewPassword { get; set; }
    }
}