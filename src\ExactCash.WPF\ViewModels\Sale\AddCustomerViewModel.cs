using System;
using System.Windows.Input;
using System.Windows;
using ExactCash.Domain.Entities;
using ExactCash.WPF.Commands;
using ExactCash.WPF.Services;
using ExactCash.WPF.ViewModels.Common;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using ExactCash.Application.DTOs;
using ExactCash.WPF.Helpers;
using System.Collections.ObjectModel;
using System.Linq;
using Microsoft.Extensions.DependencyInjection;
using System.Windows.Media;
#nullable disable

namespace ExactCash.WPF.ViewModels.Sale
{
    public class AddCustomerViewModel : INotifyPropertyChanged
    {
        private readonly ICustomerService _customerService;
        private readonly ICustomerCategoryServiceClient _customerCategoryService;
        private readonly SaleViewModel _saleViewModel;
        private string _newCustomerName;
        private string _newCustomerPhone;
        private string _newCustomerEmail;
        private string _newCustomerAddress;
        private RelayCommand _saveCustomerCommand;
        private RelayCommand _cancelCommand;
        private bool _isEditMode;
        private int _customerId;
        private ObservableCollection<CustomerCategoryDto> _categories;
        private CustomerCategoryDto _selectedCategory;

        public Action CloseWindow { get; set; }

        public ICommand SaveCustomerCommand => _saveCustomerCommand;
        public ICommand CancelCommand => _cancelCommand;

        public bool IsEditMode => _isEditMode;

        public string WindowTitle => IsEditMode ? "تعديل العميل" : "إضافة عميل جديد";
        public string SaveButtonText => IsEditMode ? "حفظ" : "إضافة";

        public string NewCustomerName
        {
            get => _newCustomerName;
            set
            {
                if (_newCustomerName != value)
                {
                    _newCustomerName = value;
                    OnPropertyChanged();
                }
            }
        }

        public string NewCustomerPhone
        {
            get => _newCustomerPhone;
            set
            {
                if (_newCustomerPhone != value)
                {
                    _newCustomerPhone = value;
                    OnPropertyChanged();
                }
            }
        }

        public string NewCustomerEmail
        {
            get => _newCustomerEmail;
            set
            {
                if (_newCustomerEmail != value)
                {
                    _newCustomerEmail = value;
                    OnPropertyChanged();
                }
            }
        }

        public string NewCustomerAddress
        {
            get => _newCustomerAddress;
            set
            {
                if (_newCustomerAddress != value)
                {
                    _newCustomerAddress = value;
                    OnPropertyChanged();
                }
            }
        }

        public ObservableCollection<CustomerCategoryDto> Categories
        {
            get => _categories;
            set
            {
                if (_categories != value)
                {
                    _categories = value;
                    OnPropertyChanged();
                }
            }
        }

        public CustomerCategoryDto SelectedCategory
        {
            get => _selectedCategory;
            set
            {
                if (_selectedCategory != value)
                {
                    _selectedCategory = value;
                    OnPropertyChanged();
                }
            }
        }

        public AddCustomerViewModel(ICustomerService customerService)
        {
            _customerService = customerService;
            _customerCategoryService = ((App)System.Windows.Application.Current).ServiceProvider.GetRequiredService<ICustomerCategoryServiceClient>();
            _saveCustomerCommand = new RelayCommand(ExecuteSaveCustomer);
            _cancelCommand = new RelayCommand(ExecuteCancel);
            _isEditMode = false;
            LoadCategories();
        }

        public AddCustomerViewModel(ICustomerService customerService, CustomerDto customer)
        {
            _customerService = customerService;
            _customerCategoryService = ((App)System.Windows.Application.Current).ServiceProvider.GetRequiredService<ICustomerCategoryServiceClient>();
            _saveCustomerCommand = new RelayCommand(ExecuteSaveCustomer);
            _cancelCommand = new RelayCommand(ExecuteCancel);
            _isEditMode = true;
            _customerId = customer.Id;

            // Initialize fields with customer data
            NewCustomerName = customer.FullName;
            NewCustomerPhone = customer.Phone;
            NewCustomerEmail = customer.Email;
            NewCustomerAddress = customer.Address;

            LoadCategories();

            // Set selected category after loading categories
            if (customer.CategoryId.HasValue)
            {
                Task.Run(async () =>
                {
                    await Task.Delay(100); // Small delay to ensure categories are loaded
                    System.Windows.Application.Current.Dispatcher.Invoke(() =>
                    {
                        SelectedCategory = Categories?.FirstOrDefault(c => c.Id == customer.CategoryId.Value);
                    });
                });
            }
        }

        private async void LoadCategories()
        {
            try
            {
                var response = await _customerCategoryService.GetActiveAsync();
                if (response != null)
                {
                    Categories = new ObservableCollection<CustomerCategoryDto>(response);

                    // Add "No Category" option at the beginning
                    Categories.Insert(0, new CustomerCategoryDto { Id = 0, Name = "-- بدون تصنيف --" });
                }
                else
                {
                    Categories = new ObservableCollection<CustomerCategoryDto>
                    {
                        new CustomerCategoryDto { Id = 0, Name = "-- بدون تصنيف --" }
                    };
                }
            }
            catch (Exception ex)
            {
                Categories = new ObservableCollection<CustomerCategoryDto>
                {
                    new CustomerCategoryDto { Id = 0, Name = "-- بدون تصنيف --" }
                };
                // Optionally log the error
            }
        }
        private Window FindParentWindow()
        {
            // Find all windows
            foreach (Window window in System.Windows.Application.Current.Windows)
            {
                // Look for the window that contains a SaleView
                if (window.Content != null &&
                    VisualTreeHelper.GetChild(window.Content as DependencyObject, 0) is FrameworkElement content &&
                    window.Name == "AddCustomerScreen")
                {
                    return window;
                }
            }
            return System.Windows.Application.Current.MainWindow;
        }

        private async void ExecuteSaveCustomer()
        {
            if (string.IsNullOrWhiteSpace(NewCustomerName) || string.IsNullOrWhiteSpace(NewCustomerPhone))
            {
                BootstrapMessageBoxHelper.ShowError("الرجاء إدخال اسم العميل ورقم الهاتف",owner: FindParentWindow());
                return;
            }

            try
            {
                var customer = new CustomerDto
                {
                    Id = _isEditMode ? _customerId : 0,
                    FullName = NewCustomerName,
                    Phone = NewCustomerPhone,
                    Address = NewCustomerAddress,
                    Email = NewCustomerEmail,
                    CategoryId = SelectedCategory?.Id > 0 ? SelectedCategory.Id : null,
                    CategoryName = SelectedCategory?.Id > 0 ? SelectedCategory.Name : null
                };

                if (_isEditMode)
                {
                    var customerToUpdate = new Domain.Entities.Customer
                    {
                        Id = _customerId,
                        FullName = NewCustomerName,
                        Phone = NewCustomerPhone,
                        Address = NewCustomerAddress,
                        Email = NewCustomerEmail,
                        CategoryId = SelectedCategory?.Id > 0 ? SelectedCategory.Id : null
                    };
                    await _customerService.UpdateCustomerAsync(customerToUpdate);
                    BootstrapMessageBoxHelper.ShowSuccess("تم تحديث العميل بنجاح", owner: FindParentWindow());
                }
                else
                {
                    await _customerService.CreateCustomerAsync(customer);
                    BootstrapMessageBoxHelper.ShowSuccess("تم إضافة العميل بنجاح", owner: FindParentWindow());
                }

                CloseWindow?.Invoke();
            }
            catch (Exception ex)
            {
                BootstrapMessageBoxHelper.ShowError($"حدث خطأ أثناء {(_isEditMode ? "تحديث" : "إضافة")} العميل: {ex.Message}", owner: FindParentWindow());
            }
        }

        private void ExecuteCancel()
        {
            CloseWindow?.Invoke();
        }

        public event PropertyChangedEventHandler PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
            if (propertyName == nameof(IsEditMode))
            {
                OnPropertyChanged(nameof(WindowTitle));
                OnPropertyChanged(nameof(SaveButtonText));
            }
        }
    }
}