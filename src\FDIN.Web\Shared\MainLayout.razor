@inherits LayoutComponentBase
@inject NavigationManager NavigationManager

<div class="page">
    <div class="sidebar @(_sidebarExpanded ? "expanded" : "collapsed")">
        <div class="sidebar-header">
            <a href="/" class="logo">
                <img src="~/logo-en.jpeg" alt="FDIN Logo" />
            </a>
            <button class="btn btn-link sidebar-toggle" @onclick="ToggleSidebar">
                <i class="fas @(_sidebarExpanded ? "fa-chevron-left" : "fa-chevron-right")"></i>
            </button>
        </div>
        <nav class="sidebar-nav">
            <NavLink class="nav-item" href="" Match="NavLinkMatch.All">
                <i class="fas fa-home"></i>
                <span class="nav-text">Dashboard</span>
            </NavLink>
            <NavLink class="nav-item" href="invoices">
                <i class="fas fa-file-invoice"></i>
                <span class="nav-text">Invoices</span>
            </NavLink>
            <NavLink class="nav-item" href="customers">
                <i class="fas fa-users"></i>
                <span class="nav-text">Customers</span>
            </NavLink>
            <NavLink class="nav-item" href="products">
                <i class="fas fa-box"></i>
                <span class="nav-text">Products</span>
            </NavLink>
            <NavLink class="nav-item" href="reports">
                <i class="fas fa-chart-bar"></i>
                <span class="nav-text">Reports</span>
            </NavLink>
        </nav>
    </div>

    <div class="main @(_sidebarExpanded ? "sidebar-expanded" : "sidebar-collapsed")">
        <header class="top-nav">
            <div class="top-nav-left">
                <button class="btn btn-link menu-toggle" @onclick="ToggleSidebar">
                    <i class="fas fa-bars"></i>
                </button>
                <div class="page-title">
                    @PageTitle
                </div>
            </div>
            <div class="top-nav-right">
                <div class="user-menu">
                    <button class="btn btn-link" @onclick="ToggleUserMenu">
                        <i class="fas fa-user-circle"></i>
                        <span>@UserName</span>
                    </button>
                    @if (_showUserMenu)
                    {
                            <div class="user-dropdown">
                                <a href="profile" class="dropdown-item">
                                    <i class="fas fa-user"></i> Profile
                                </a>
                                <a href="settings" class="dropdown-item">
                                    <i class="fas fa-cog"></i> Settings
                                </a>
                                <div class="dropdown-divider"></div>
                                <a href="logout" class="dropdown-item text-danger">
                                    <i class="fas fa-sign-out-alt"></i> Logout
                                </a>
                            </div>
                    }
                </div>
            </div>
        </header>

        <main class="content">
            @Body
        </main>
    </div>
</div>

@code {
    private bool _sidebarExpanded = true;
    private bool _showUserMenu = false;
    private string UserName = "John Doe"; // Replace with actual user name
    private string PageTitle = "Dashboard"; // Replace with actual page title

    private void ToggleSidebar()
    {
        _sidebarExpanded = !_sidebarExpanded;
    }

    private void ToggleUserMenu()
    {
        _showUserMenu = !_showUserMenu;
    }
}

<style>
    .page {
        display: flex;
        min-height: 100vh;
        font-family: 'Inter', sans-serif;
    }

    .sidebar {
        background: #2c3e50;
        color: white;
        width: 260px;
        transition: all 0.3s ease;
        display: flex;
        flex-direction: column;
    }

    .sidebar.collapsed {
        width: 70px;
    }

    .sidebar-header {
        padding: 1.5rem;
        display: flex;
        align-items: center;
        justify-content: space-between;
        border-bottom: 1px solid rgba(255,255,255,0.1);
    }

    .sidebar-header .logo img {
        max-width: 120px;
        height: auto;
    }

    .sidebar.collapsed .logo img {
        display: none;
    }

    .sidebar-toggle {
        color: white;
        padding: 0;
    }

    .sidebar-nav {
        padding: 1rem 0;
        flex: 1;
    }

    .nav-item {
        display: flex;
        align-items: center;
        padding: 0.75rem 1.5rem;
        color: rgba(255,255,255,0.8);
        text-decoration: none;
        transition: all 0.2s ease;
    }

    .nav-item:hover {
        background: rgba(255,255,255,0.1);
        color: white;
    }

    .nav-item.active {
        background: rgba(255,255,255,0.1);
        color: white;
        border-left: 4px solid #3498db;
    }

    .nav-item i {
        width: 20px;
        margin-right: 1rem;
    }

    .sidebar.collapsed .nav-text {
        display: none;
    }

    .main {
        flex: 1;
        background: #f8f9fa;
        transition: all 0.3s ease;
    }

    .top-nav {
        background: white;
        padding: 1rem 1.5rem;
        display: flex;
        align-items: center;
        justify-content: space-between;
        box-shadow: 0 2px 4px rgba(0,0,0,0.05);
    }

    .top-nav-left {
        display: flex;
        align-items: center;
        gap: 1rem;
    }

    .menu-toggle {
        color: #2c3e50;
        padding: 0;
    }

    .page-title {
        font-size: 1.25rem;
        font-weight: 600;
        color: #2c3e50;
    }

    .user-menu {
        position: relative;
    }

    .user-menu button {
        color: #2c3e50;
        text-decoration: none;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .user-dropdown {
        position: absolute;
        top: 100%;
        right: 0;
        background: white;
        border-radius: 4px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        min-width: 200px;
        z-index: 1000;
    }

    .dropdown-item {
        padding: 0.75rem 1rem;
        color: #2c3e50;
        text-decoration: none;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .dropdown-item:hover {
        background: #f8f9fa;
    }

    .dropdown-divider {
        border-top: 1px solid #eee;
        margin: 0.5rem 0;
    }

    .content {
        padding: 1.5rem;
    }
</style> 