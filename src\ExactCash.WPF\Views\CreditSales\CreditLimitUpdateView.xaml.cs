using ExactCash.Application.DTOs;
using ExactCash.WPF.ViewModels.CreditSales;
using System.Windows;

namespace ExactCash.WPF.Views.CreditSales
{
    /// <summary>
    /// Interaction logic for CreditLimitUpdateView.xaml
    /// </summary>
    public partial class CreditLimitUpdateView : Window
    {
        public CreditLimitUpdateView()
        {
            InitializeComponent();
            DataContext = new CreditLimitUpdateViewModel();
        }

        public CreditLimitUpdateView(CustomerCreditLimitDto creditLimit)
        {
            InitializeComponent();
            DataContext = new CreditLimitUpdateViewModel(creditLimit);
        }
    }
}
