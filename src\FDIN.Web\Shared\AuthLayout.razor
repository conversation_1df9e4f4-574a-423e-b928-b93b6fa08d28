@inherits LayoutComponentBase

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>@ViewData["Title"] - FDIN</title>
    <link rel="stylesheet" href="~/lib/bootstrap/dist/css/bootstrap.min.css" />
    <link rel="stylesheet" href="~/css/site.css" asp-append-version="true" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" />
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body class="auth-layout">
    <div class="auth-container">
        <div class="auth-content">
            <div class="auth-header">
                <a href="/" class="logo">
                    <img src="~/logo-en.jpeg" alt="FDIN Logo" />
                </a>
            </div>
            <div class="auth-body">
                @Body
            </div>
            <div class="auth-footer">
                <p>&copy; @DateTime.Now.Year - FDIN. All rights reserved.</p>
            </div>
        </div>
    </div>

    <script src="~/lib/jquery/dist/jquery.min.js"></script>
    <script src="~/lib/bootstrap/dist/js/bootstrap.bundle.min.js"></script>
    <script src="~/js/site.js" asp-append-version="true"></script>
    @await RenderSectionAsync("Scripts", required: false)
</body>
</html>

<style>
    .auth-layout {
        min-height: 100vh;
        background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
        font-family: 'Inter', sans-serif;
    }

    .auth-container {
        display: flex;
        align-items: center;
        justify-content: center;
        min-height: 100vh;
        padding: 2rem;
    }

    .auth-content {
        background: white;
        border-radius: 12px;
        box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
        width: 100%;
        max-width: 480px;
        overflow: hidden;
    }

    .auth-header {
        padding: 2rem;
        text-align: center;
        border-bottom: 1px solid #eee;
    }

    .auth-header .logo img {
        max-width: 150px;
        height: auto;
    }

    .auth-body {
        padding: 2rem;
    }

    .auth-footer {
        padding: 1.5rem;
        text-align: center;
        color: #666;
        font-size: 0.875rem;
        border-top: 1px solid #eee;
    }
</style> 