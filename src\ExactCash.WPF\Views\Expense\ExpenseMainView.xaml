<Window x:Class="ExactCash.WPF.Views.Expense.ExpenseMainView"
        x:Name="ExpenseMainViewScreen"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:local="clr-namespace:ExactCash.WPF.Views.Expense"
        Title="المصروفات"
        Height="750"
        Width="1200"
        WindowStartupLocation="CenterOwner"
        FlowDirection="RightToLeft"
        Background="#f8f9fa"
        WindowStyle="None"
        AllowsTransparency="True"
        ResizeMode="CanResize"
        WindowState="Maximized">
    <Window.Resources>
        <Style x:Key="ModernButton"
               TargetType="Button">
            <Setter Property="Background"
                    Value="#0078D4"/>
            <Setter Property="Foreground"
                    Value="White"/>
            <Setter Property="BorderThickness"
                    Value="0"/>
            <Setter Property="Padding"
                    Value="15,8"/>
            <Setter Property="FontSize"
                    Value="14"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                CornerRadius="4">
                            <ContentPresenter HorizontalAlignment="Center"
                                              VerticalAlignment="Center"/>
                        </Border>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
            <Style.Triggers>
                <Trigger Property="IsMouseOver"
                         Value="True">
                    <Setter Property="Background"
                            Value="#106EBE"/>
                </Trigger>
            </Style.Triggers>
        </Style>

        <Style x:Key="SuccessButton"
               TargetType="Button"
               BasedOn="{StaticResource ModernButton}">
            <Setter Property="Background"
                    Value="#28a745"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver"
                         Value="True">
                    <Setter Property="Background"
                            Value="#218838"/>
                </Trigger>
            </Style.Triggers>
        </Style>

        <Style x:Key="ModernTextBox"
               TargetType="TextBox">
            <Setter Property="Height"
                    Value="35"/>
            <Setter Property="Padding"
                    Value="10,5"/>
            <Setter Property="BorderThickness"
                    Value="1"/>
            <Setter Property="BorderBrush"
                    Value="#CCCCCC"/>
            <Setter Property="Background"
                    Value="White"/>
            <Setter Property="FontSize"
                    Value="14"/>
        </Style>

        <DropShadowEffect x:Key="DropShadowEffect"
                          ShadowDepth="2"
                          Direction="270"
                          Color="Black"
                          Opacity="0.3"
                          BlurRadius="5"/>
    </Window.Resources>
    <Border BorderThickness="1"
            BorderBrush="#dee2e6"
            Background="White"
            CornerRadius="6">
        <DockPanel>
            <!-- Header -->
            <Border DockPanel.Dock="Top"
                    Background="#0078D4"
                    Height="60"
                    BorderThickness="0,0,0,1"
                    BorderBrush="White">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>
                    <!-- Title -->
                    <TextBlock Text="المصروفات"
                               Foreground="White"
                               FontSize="20"
                               FontWeight="Bold"
                               VerticalAlignment="Center"
                               Margin="20,0,0,0"/>
                    <!-- Close Button -->
                    <Button Grid.Column="1"
                            Content="✕"
                            Click="CloseButton_Click"
                            FontSize="20"
                            Foreground="White"
                            Width="60"
                            Height="60"
                            Background="Transparent"
                            BorderThickness="0"/>
                    <!-- Minimize Button -->
                    <Button Grid.Column="2"
                            Content="−"
                            Click="MinimizeButton_Click"
                            FontSize="20"
                            Foreground="White"
                            Width="60"
                            Height="60"
                            Background="Transparent"
                            BorderThickness="0"/>
                </Grid>
            </Border>
            <!-- Content -->
            <Grid Margin="20">
                <TabControl>
                    <TabItem Header="المصروفات">
                        <Border BorderThickness="1"
                                BorderBrush="#dee2e6"
                                Background="White"
                                CornerRadius="6"
                                Padding="0">
                            <Grid Margin="20">
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="*"/>
                                </Grid.RowDefinitions>
                                <!-- Header -->
                                <TextBlock Text="قائمة المصروفات"
                                           FontSize="20"
                                           FontWeight="Bold"
                                           Foreground="#0078D4"
                                           Margin="0,0,0,20"/>
                                <!-- Statistics Panel -->
                                <Border Background="#E3F2FD"
                                        CornerRadius="8"
                                        Padding="20,10"
                                        Margin="0,0,0,15">
                                    <Grid>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="2*"/>
                                            <ColumnDefinition Width="2*"/>
                                            <ColumnDefinition Width="2*"/>
                                            <ColumnDefinition Width="2*"/>
                                        </Grid.ColumnDefinitions>
                                        <StackPanel Grid.Column="0"
                                                    HorizontalAlignment="Center">
                                            <TextBlock Text="إجمالي المصروفات"
                                                       FontWeight="Bold"
                                                       Foreground="#1976D2"
                                                       FontSize="14"/>
                                            <TextBlock Text="{Binding ExpenseListViewModel.TotalAmount, StringFormat=N2}"
                                                       FontSize="18"
                                                       FontWeight="Bold"/>
                                        </StackPanel>
                                        <StackPanel Grid.Column="1"
                                                    HorizontalAlignment="Center">
                                            <TextBlock Text="عدد العمليات"
                                                       FontWeight="Bold"
                                                       Foreground="#1976D2"
                                                       FontSize="14"/>
                                            <TextBlock Text="{Binding ExpenseListViewModel.ExpenseCount}"
                                                       FontSize="18"
                                                       FontWeight="Bold"/>
                                        </StackPanel>
                                        <StackPanel Grid.Column="2"
                                                    HorizontalAlignment="Center">
                                            <TextBlock Text="متوسط المصروف"
                                                       FontWeight="Bold"
                                                       Foreground="#1976D2"
                                                       FontSize="14"/>
                                            <TextBlock Text="{Binding ExpenseListViewModel.AverageAmount, StringFormat=N2}"
                                                       FontSize="18"
                                                       FontWeight="Bold"/>
                                        </StackPanel>
                                        <StackPanel Grid.Column="3"
                                                    HorizontalAlignment="Center">
                                            <TextBlock Text="مجموع المصروفات للفترة المحددة"
                                                       FontWeight="Bold"
                                                       Foreground="#1976D2"
                                                       FontSize="14"/>
                                            <TextBlock Text="{Binding ExpenseListViewModel.FilteredTotalAmount, StringFormat=N2}"
                                                       FontSize="18"
                                                       FontWeight="Bold"/>
                                        </StackPanel>
                                    </Grid>
                                </Border>
                                <!-- Search Panel -->
                                <Border Grid.Row="2"
                                        Background="#F4F6FA"
                                        CornerRadius="8"
                                        Padding="20,15"
                                        Margin="0,0,0,20"
                                        BorderBrush="#E0E0E0"
                                        BorderThickness="1">
                                    <Grid>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="180"/>
                                            <ColumnDefinition Width="160"/>
                                            <ColumnDefinition Width="80"/>
                                            <ColumnDefinition Width="160"/>
                                            <ColumnDefinition Width="80"/>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="100"/>
                                            <ColumnDefinition Width="100"/>
                                        </Grid.ColumnDefinitions>
                                        <TextBlock Text="التصنيف:"
                                                   VerticalAlignment="Center"
                                                   Grid.Column="0"
                                                   Margin="0,0,5,0"/>
                                        <ComboBox Width="150"
                                                  ItemsSource="{Binding ExpenseListViewModel.Categories}"
                                                  SelectedItem="{Binding ExpenseListViewModel.SelectedCategory, Mode=TwoWay}"
                                                  DisplayMemberPath="Name"
                                                  Grid.Column="1"/>
                                        <TextBlock Text="من:"
                                                   VerticalAlignment="Center"
                                                   Grid.Column="2"
                                                   Margin="10,0,5,0"/>
                                        <DatePicker SelectedDate="{Binding ExpenseListViewModel.DateFrom, Mode=TwoWay}"
                                                    Width="140"
                                                    Grid.Column="3"/>
                                        <TextBlock Text="إلى:"
                                                   VerticalAlignment="Center"
                                                   Grid.Column="4"
                                                   Margin="10,0,5,0"/>
                                        <DatePicker SelectedDate="{Binding ExpenseListViewModel.DateTo, Mode=TwoWay}"
                                                    Width="140"
                                                    Grid.Column="5"/>
                                        <Button Content="بحث"
                                                Command="{Binding ExpenseListViewModel.SearchCommand}"
                                                Style="{StaticResource ModernButton}"
                                                Width="90"
                                                Height="35"
                                                Grid.Column="6"
                                                Margin="10,0,0,0"/>
                                        <Button Content="مسح"
                                                Command="{Binding ExpenseListViewModel.ClearSearchCommand}"
                                                Style="{StaticResource ModernButton}"
                                                Width="90"
                                                Height="35"
                                                Background="#6C757D"
                                                Grid.Column="7"/>
                                    </Grid>
                                </Border>
                                <!-- Action Buttons -->
                                <StackPanel Grid.Row="3"
                                            Orientation="Horizontal"
                                            HorizontalAlignment="Right"
                                            Margin="0,0,0,10">
                                    <Button Content="تصدير إلى Excel"
                                            Width="120"
                                            Height="35"
                                            Margin="0,0,10,0"
                                            Command="{Binding ExpenseListViewModel.ExportToExcelCommand}"
                                            Style="{StaticResource ModernButton}"/>
                                    <Button Content="إضافة مصروف"
                                            Width="120"
                                            Height="35"
                                            Margin="0,0,10,0"
                                            Command="{Binding ExpenseListViewModel.AddExpenseCommand}"
                                            Style="{StaticResource SuccessButton}"/>
                                </StackPanel>
                                <!-- DataGrid -->
                                <DataGrid Grid.Row="4"
                                          AutoGenerateColumns="False"
                                          IsReadOnly="True"
                                          SelectionMode="Single"
                                          SelectionUnit="FullRow"
                                          GridLinesVisibility="None"
                                          RowHeight="40"
                                          BorderThickness="1"
                                          BorderBrush="#DDDDDD"
                                          Background="White"
                                          ItemsSource="{Binding ExpenseListViewModel.Expenses}"
                                          AlternatingRowBackground="#F8F9FA">
                                    <DataGrid.Resources>
                                        <Style TargetType="DataGridRow">
                                            <Setter Property="BorderThickness"
                                                    Value="0"/>
                                            <Setter Property="BorderBrush"
                                                    Value="Transparent"/>
                                        </Style>
                                        <Style TargetType="DataGridCell">
                                            <Setter Property="BorderThickness"
                                                    Value="0"/>
                                            <Setter Property="BorderBrush"
                                                    Value="Transparent"/>
                                        </Style>
                                    </DataGrid.Resources>
                                    <DataGrid.Columns>
                                        <DataGridTextColumn Header="التاريخ"
                                                            Binding="{Binding ExpenseDate, StringFormat=dd/MM/yyyy}"
                                                            Width="150"/>
                                        <DataGridTextColumn Header="المبلغ"
                                                            Binding="{Binding Amount, StringFormat=N2}"
                                                            Width="100"/>
                                        <DataGridTextColumn Header="الوصف"
                                                            Binding="{Binding Description}"
                                                            Width="200"/>
                                        <DataGridTextColumn Header="التصنيف"
                                                            Binding="{Binding CategoryName}"
                                                            Width="150"/>
                                        <DataGridTextColumn Header="طريقة الدفع"
                                                            Binding="{Binding PaymentMethodName}"
                                                            Width="120"/>
                                        <DataGridTextColumn Header="المرجع"
                                                            Binding="{Binding ReferenceNumber}"
                                                            Width="120"/>
                                        <!-- Actions Column -->
                                        <DataGridTemplateColumn Header="الإجراءات"
                                                                Width="160">
                                            <DataGridTemplateColumn.CellTemplate>
                                                <DataTemplate>
                                                    <StackPanel Orientation="Horizontal"
                                                                HorizontalAlignment="Center">
                                                        <Button Style="{StaticResource ModernButton}"
                                                                Command="{Binding DataContext.ExpenseListViewModel.EditCommand, RelativeSource={RelativeSource AncestorType=DataGrid}}"
                                                                CommandParameter="{Binding}"
                                                                ToolTip="تعديل"
                                                                Width="35"
                                                                Height="30"
                                                                Margin="0,0,5,0">
                                                            <TextBlock Text="✏️"
                                                                       FontSize="16"/>
                                                        </Button>
                                                        <Button Style="{StaticResource ModernButton}"
                                                                Background="#dc3545"
                                                                Command="{Binding DataContext.ExpenseListViewModel.DeleteCommand, RelativeSource={RelativeSource AncestorType=DataGrid}}"
                                                                CommandParameter="{Binding}"
                                                                ToolTip="حذف"
                                                                Width="35"
                                                                Height="30">
                                                            <TextBlock Text="🗑️"
                                                                       FontSize="16"/>
                                                        </Button>
                                                    </StackPanel>
                                                </DataTemplate>
                                            </DataGridTemplateColumn.CellTemplate>
                                        </DataGridTemplateColumn>
                                    </DataGrid.Columns>
                                </DataGrid>
                            </Grid>
                        </Border>
                    </TabItem>
                    <TabItem Header="تصنيفات المصروفات">
                        <Border BorderThickness="1"
                                BorderBrush="#dee2e6"
                                Background="White"
                                CornerRadius="6"
                                Padding="0">

                            <Grid Margin="20">
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="*"/>
                                </Grid.RowDefinitions>
                                <!-- Header -->
                                <TextBlock Text="تصنيفات المصروفات"
                                           FontSize="20"
                                           FontWeight="Bold"
                                           Foreground="#0078D4"
                                           Margin="0,0,0,20"/>
                                <!-- Action Buttons -->
                                <StackPanel Grid.Row="1"
                                            Orientation="Horizontal"
                                            HorizontalAlignment="Right"
                                            Margin="0,0,0,10">
                                    <Button Content="تصدير إلى Excel"
                                            Width="120"
                                            Height="35"
                                            Margin="0,0,10,0"
                                            Command="{Binding ExpenseCategoryListViewModel.ExportToExcelCommand}"
                                            Style="{StaticResource ModernButton}"/>
                                    <Button Content="إضافة تصنيف"
                                            Width="120"
                                            Height="35"
                                            Margin="0,0,10,0"
                                            Command="{Binding ExpenseCategoryListViewModel.AddCategoryCommand}"
                                            Style="{StaticResource SuccessButton}"/>
                                </StackPanel>
                                <!-- DataGrid -->
                                <DataGrid Grid.Row="2"
                                          AutoGenerateColumns="False"
                                          CanUserAddRows="False"
                                          BorderThickness="1"
                                          BorderBrush="#DEE2E6"
                                          Background="White"
                                          RowHeight="40"
                                          ItemsSource="{Binding ExpenseCategoryListViewModel.Categories}"
                                          GridLinesVisibility="None"
                                          AlternatingRowBackground="#F8F9FA"
                                          HorizontalScrollBarVisibility="Auto"
                                          VerticalScrollBarVisibility="Auto">
                                    <DataGrid.Resources>
                                        <Style TargetType="DataGridRow">
                                            <Setter Property="BorderThickness"
                                                    Value="0"/>
                                            <Setter Property="BorderBrush"
                                                    Value="Transparent"/>
                                        </Style>
                                        <Style TargetType="DataGridCell">
                                            <Setter Property="BorderThickness"
                                                    Value="0"/>
                                            <Setter Property="BorderBrush"
                                                    Value="Transparent"/>
                                        </Style>
                                    </DataGrid.Resources>
                                    <DataGrid.Columns>
                                        <DataGridTextColumn Header="اسم التصنيف"
                                                            Binding="{Binding Name}"
                                                            Width="200"/>
                                        <DataGridTextColumn Header="الوصف"
                                                            Binding="{Binding Description}"
                                                            Width="300"/>

                                        <!-- Actions Column -->
                                        <DataGridTemplateColumn Header="الإجراءات"
                                                                Width="160">
                                            <DataGridTemplateColumn.CellTemplate>
                                                <DataTemplate>
                                                    <StackPanel Orientation="Horizontal"
                                                                HorizontalAlignment="Center">

                                                        <Button Style="{StaticResource ModernButton}"
                                                                Command="{Binding DataContext.ExpenseCategoryListViewModel.EditCommand, RelativeSource={RelativeSource AncestorType=DataGrid}}"
                                                                CommandParameter="{Binding}"
                                                                ToolTip="تعديل"
                                                                Width="35"
                                                                Height="30"
                                                                Margin="0,0,5,0">
                                                            <TextBlock Text="✏️"
                                                                       FontSize="16"/>
                                                        </Button>
                                                        <Button Style="{StaticResource ModernButton}"
                                                                Background="#dc3545"
                                                                Command="{Binding DataContext.ExpenseCategoryListViewModel.DeleteCommand, RelativeSource={RelativeSource AncestorType=DataGrid}}"
                                                                CommandParameter="{Binding}"
                                                                ToolTip="حذف"
                                                                Width="35"
                                                                Height="30">
                                                            <TextBlock Text="🗑️"
                                                                       FontSize="16"/>
                                                        </Button>
                                                    </StackPanel>
                                                </DataTemplate>
                                            </DataGridTemplateColumn.CellTemplate>
                                        </DataGridTemplateColumn>



                                    </DataGrid.Columns>
                                </DataGrid>
                            </Grid>
                        </Border>
                    </TabItem>
                </TabControl>
            </Grid>
        </DockPanel>
    </Border>
</Window> 