﻿using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Threading;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Input;
using ExactCash.Application.DTOs;
using ExactCash.Domain.Entities;
using ExactCash.WPF.Commands;
using ExactCash.WPF.Services;
using ExactCash.WPF.Services.Product;
using ExactCash.WPF.ViewModels.Common;
using ExactCash.WPF.Views.Sale;
using Newtonsoft.Json.Linq;
using AutoMapper;
using System.Windows.Media;
using ExactCash.Application.Responses;
using Microsoft.AspNetCore.Http;
using System.Net.Http;
using System.Text;
using Microsoft.Extensions.Configuration;
using Microsoft.ReportingServices.ReportProcessing.OnDemandReportObjectModel;
#nullable disable

namespace ExactCash.WPF.ViewModels.Sale
{
    public class SaleViewModel : INotifyPropertyChanged
    {
        private readonly IApiUserService _apiService;
        private readonly LoadingViewModel _loadingViewModel;
        private readonly ExactCash.WPF.Services.ICustomerService _customerService;
        private readonly IProductService _productService;
        private readonly ISaleServiceClient _saleService;
        private readonly IPrinterService _printerService;
        private readonly IUnitServiceClient _unitServiceClient;
        private readonly IMapper _mapper;
        private readonly ICategoryServiceClient _categoryServiceClient;
        private readonly IBrandsServiceClient _brandsServiceClient;
        private readonly NotificationViewModel _notificationViewModel;
        private string _searchTerm;
        private ExactCash.Domain.Entities.Customer _selectedCustomer;
        private ObservableCollection<ExactCash.Domain.Entities.Customer> _searchResults;
        private RelayCommand<ExactCash.Domain.Entities.Customer> _selectCustomerCommand;
        private RelayCommand _addNewCustomerCommand;
        private RelayCommand _findCustomerCommand;
        private string _productSearchTerm;
        private Application.DTOs.ProductDto _selectedProduct;
        private ObservableCollection<Application.DTOs.ProductDto> _productSearchResults;
        private CancellationTokenSource _searchCancellationTokenSource;
        private const int SearchDelayMilliseconds = 500; // 500ms delay before searching
        private string _barcodeSearchTerm;
        private bool _isSettingSelectedProduct;
        private ObservableCollection<ExactCash.WPF.Models.CartProduct> _selectedProducts;
        private RelayCommand _addProductToCartCommand;
        private RelayCommand<ExactCash.WPF.Models.CartProduct> _removeProductCommand;
        private RelayCommand _searchByBarcodeCommand;
        private string _invoiceNumber;
        private string _invoiceType;
        private decimal _discountAmount;
        private decimal _taxAmount;
        private decimal _netAmount;
        private decimal _totalAmount;
        private decimal _paidAmount;
        private decimal _remainingAmount;
        private string _notes;
        private RelayCommand _saveInvoiceCommand;
        private RelayCommand _printInvoiceCommand;
        private RelayCommand _addNewProductCommand;
        private ObservableCollection<UnitDto> _availableUnits;
        private readonly IConfiguration _configuration;
        private static readonly Dictionary<string, string> PaymentTypeMapping = new Dictionary<string, string>
        {
            { "نقدي", "Cash" },
            { "آجل", "Credit" }
        };
        private int currentSaleId = 0;

        public ICommand SelectCustomerCommand => _selectCustomerCommand;
        public ICommand AddNewCustomerCommand => _addNewCustomerCommand;
        public ICommand FindCustomerCommand => _findCustomerCommand;
        public ICommand AddToCartCommand { get; }
        public ICommand AddProductToCartCommand => _addProductToCartCommand ??= new RelayCommand(ExecuteAddProductToCart, CanAddProductToCart);
        public ICommand RemoveProductCommand => _removeProductCommand ??= new RelayCommand<ExactCash.WPF.Models.CartProduct>(ExecuteRemoveProduct);
        public ICommand SearchByBarcodeCommand => _searchByBarcodeCommand ??= new RelayCommand(ExecuteSearchByBarcode);
        public ICommand SaveInvoiceCommand => _saveInvoiceCommand ??= new RelayCommand(ExecuteSaveInvoice, CanSaveInvoice);
        public ICommand PrintInvoiceCommand => _printInvoiceCommand ??= new RelayCommand(ExecutePrintInvoice, CanPrintInvoice);
        public ICommand AddNewProductCommand => _addNewProductCommand ??= new RelayCommand(ExecuteAddNewProduct);

        private string _customerName;
        public string CustomerName
        {
            get => SelectedCustomer?.FullName ?? _customerName ?? string.Empty;
            set
            {
                if (_customerName != value)
                {
                    _customerName = value;
                    OnPropertyChanged();
                }
            }
        }

        private string _customerPhone;
        public string CustomerPhone
        {
            get => SelectedCustomer?.Phone ?? _customerPhone ?? string.Empty;
            set
            {
                if (_customerPhone != value)
                {
                    _customerPhone = value;
                    OnPropertyChanged();
                }
            }
        }

        private decimal _customerDiscount;
        public decimal CustomerDiscount
        {
            get => _customerDiscount; // Keep this as is since we don't have DefaultDiscount in the Customer entity yet
            set
            {
                if (_customerDiscount != value)
                {
                    _customerDiscount = value;
                    OnPropertyChanged();
                }
            }
        }

        public string ProductSearchTerm
        {
            get => _productSearchTerm;
            set
            {
                if (_productSearchTerm != value)
                {
                    _productSearchTerm = value;
                    OnPropertyChanged();
                    // Only search if we're not setting the selected product name
                    if (!_isSettingSelectedProduct)
                    {
                        DebounceSearch();
                    }
                }
            }
        }

        public string BarcodeSearchTerm
        {
            get => _barcodeSearchTerm;
            set
            {
                if (_barcodeSearchTerm != value)
                {
                    _barcodeSearchTerm = value;
                    OnPropertyChanged();
                }
            }
        }

        public Application.DTOs.ProductDto SelectedProduct
        {
            get => _selectedProduct;
            set
            {
                if (_selectedProduct != value)
                {
                    _selectedProduct = value;
                    OnPropertyChanged();
                    if (value != null)
                    {
                        // Update the search term to show the selected product name
                        _isSettingSelectedProduct = true;
                        ProductSearchTerm = value.Name;
                        _isSettingSelectedProduct = false;
                        // Close the dropdown
                        ProductSearchResults = new ObservableCollection<Application.DTOs.ProductDto>();
                    }
                }
            }
        }

        public ObservableCollection<Application.DTOs.ProductDto> ProductSearchResults
        {
            get => _productSearchResults;
            set
            {
                _productSearchResults = value;
                OnPropertyChanged();
                OnPropertyChanged(nameof(HasSearchResults));
            }
        }

        public bool HasSearchResults => ProductSearchResults?.Any() == true;

        public ObservableCollection<ExactCash.WPF.Models.CartProduct> SelectedProducts
        {
            get => _selectedProducts;
            set
            {
                _selectedProducts = value;
                OnPropertyChanged();
                CalculateTotalAmount();
            }
        }

        public string InvoiceNumber
        {
            get => _invoiceNumber;
            set => SetProperty(ref _invoiceNumber, value);
        }

        public string InvoiceType
        {
            get => _invoiceType;
            set => SetProperty(ref _invoiceType, value);
        }

        public decimal DiscountAmount
        {
            get => _discountAmount;
            set
            {
                if (SetProperty(ref _discountAmount, value))
                {
                    CalculateNetAmount();
                }
            }
        }

        public decimal TaxAmount
        {
            get => _taxAmount;
            set => SetProperty(ref _taxAmount, value);
        }

        public decimal NetAmount
        {
            get => _netAmount;
            set => SetProperty(ref _netAmount, value);
        }

        public decimal TotalAmount
        {
            get => _totalAmount;
            set
            {
                _totalAmount = value;
                OnPropertyChanged();
                CalculateNetAmount();
            }
        }

        public decimal PaidAmount
        {
            get => _paidAmount;
            set
            {
                if (SetProperty(ref _paidAmount, value))
                {
                    CalculateRemainingAmount();
                }
            }
        }

        public decimal RemainingAmount
        {
            get => _remainingAmount;
            set => SetProperty(ref _remainingAmount, value);
        }

        public string Notes
        {
            get => _notes;
            set => SetProperty(ref _notes, value);
        }

        public ObservableCollection<UnitDto> AvailableUnits
        {
            get => _availableUnits;
            set
            {
                _availableUnits = value;
                OnPropertyChanged();
            }
        }

        public SaleViewModel(
            IApiUserService apiService,
            LoadingViewModel loadingViewModel,
            ExactCash.WPF.Services.ICustomerService customerService,
            IProductService productService,
            ISaleServiceClient saleService,
            IUnitServiceClient unitServiceClient,
            IMapper mapper,
            ICategoryServiceClient categoryServiceClient,
            IBrandsServiceClient brandsServiceClient,
            NotificationViewModel notificationViewModel,
            IConfiguration configuration)
        {
            _apiService = apiService;
            _loadingViewModel = loadingViewModel;
            _customerService = customerService;
            _productService = productService;
            _saleService = saleService;
            _printerService = new XprinterService();
            _unitServiceClient = unitServiceClient;
            _mapper = mapper;
            _notificationViewModel = notificationViewModel;
            _selectedProducts = new ObservableCollection<ExactCash.WPF.Models.CartProduct>();
            _productSearchResults = new ObservableCollection<Application.DTOs.ProductDto>();
            _availableUnits = new ObservableCollection<UnitDto>();
            _searchResults = new ObservableCollection<ExactCash.Domain.Entities.Customer>();
            _searchCancellationTokenSource = new CancellationTokenSource();

            // Initialize commands
            _selectCustomerCommand = new RelayCommand<ExactCash.Domain.Entities.Customer>(ExecuteSelectCustomer);
            _addNewCustomerCommand = new RelayCommand(ExecuteAddNewCustomer);
            _findCustomerCommand = new RelayCommand(ExecuteFindCustomer);
            AddToCartCommand = new RelayCommand(AddToCart, CanAddToCart);

            // Initialize properties
            _customerName = string.Empty;
            _customerPhone = string.Empty;
            _customerDiscount = 0;
            _invoiceType = "نقدي";
            _notes = string.Empty;
            _configuration = configuration;
            // Generate initial invoice number
            GenerateInvoiceNumberAsync();

            // Load initial data
            LoadInitialData();

            // Subscribe to collection changes
            SelectedProducts.CollectionChanged += SelectedProducts_CollectionChanged;
            _categoryServiceClient = categoryServiceClient;
            _brandsServiceClient = brandsServiceClient;
        }

        private async void LoadInitialData()
        {
            try
            {
                _loadingViewModel.IsVisible = false;
                await LoadUnits();
            }
            catch (Exception ex)
            {
                System.Windows.MessageBox.Show($"Error loading initial data: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                _loadingViewModel.IsVisible = false;
            }
        }

        private async Task LoadUnits()
        {
            try
            {
                var units = await _unitServiceClient.GetAllUnitsAsync();
                AvailableUnits.Clear();
                foreach (var unit in units)
                {
                    AvailableUnits.Add(unit);
                }
            }
            catch (Exception ex)
            {
                System.Windows.MessageBox.Show($"Error loading units: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void GenerateInvoiceNumberAsync()
        {
            try
            {

                var lastSaleId = await _saleService.GetLastSaleIdAsync();
                var nextNumber = lastSaleId + 1;
                var today = DateTime.Now;
                if (string.IsNullOrEmpty(InvoiceNumber))
                {
                    InvoiceNumber = $"INV-{today:yyyyMMddHHmmss}-{nextNumber:D3}";
                }
            }
            catch (Exception ex)
            {
                // If there's an error getting the last sale ID, fall back to a default number
                var today = DateTime.Now;
                InvoiceNumber = $"INV-{today:yyyyMMddHHmmss}-001";
            }
        }

        private Window FindParentWindow()
        {
            // Find all windows
            foreach (Window window in System.Windows.Application.Current.Windows)
            {
                // Look for the window that contains a SaleView
                if (window.Content != null &&
                    VisualTreeHelper.GetChild(window.Content as DependencyObject, 0) is FrameworkElement content &&
                    window.Name == "SaleViewScreen")
                {
                    return window;
                }
            }
            return System.Windows.Application.Current.MainWindow;
        }

        private void ExecuteAddNewCustomer()
        {
            var addCustomerView = new AddCustomerView();
            addCustomerView.Owner = FindParentWindow();
            ((AddCustomerViewModel)addCustomerView.DataContext).CloseWindow = () =>
            {
                addCustomerView.DialogResult = true;
                addCustomerView.Close();
            };
            if (addCustomerView.ShowDialog() == true)
            {
                SearchCustomers();
            }
        }

        private void ExecuteFindCustomer()
        {
            var customerSelectionView = new Views.Sale.CustomerSelectionView();
            customerSelectionView.Owner = FindParentWindow();

            if (customerSelectionView.ShowDialog() == true && customerSelectionView.SelectedCustomer != null)
            {
                var selectedCustomer = customerSelectionView.SelectedCustomer;

                // Convert CustomerDto to Domain Customer entity
                var customer = new ExactCash.Domain.Entities.Customer
                {
                    Id = selectedCustomer.Id,
                    FullName = selectedCustomer.FullName,
                    Phone = selectedCustomer.Phone,
                    Email = selectedCustomer.Email,
                    Address = selectedCustomer.Address
                };

                // Set the selected customer
                ExecuteSelectCustomer(customer);
            }
        }

        private void ExecuteSelectCustomer(ExactCash.Domain.Entities.Customer customer)
        {
            if (customer == null)
                return;

            // Clear the backing fields first
            _customerName = null;
            _customerPhone = null;

            // Set the selected customer (this will trigger PropertyChanged for CustomerName and CustomerPhone)
            SelectedCustomer = customer;

            // Force UI refresh by explicitly calling OnPropertyChanged
            OnPropertyChanged(nameof(CustomerName));
            OnPropertyChanged(nameof(CustomerPhone));
            OnPropertyChanged(nameof(CustomerDiscount));

            // Clear search
            SearchTerm = string.Empty;
            SearchResults.Clear();
        }

        public string SearchTerm
        {
            get => _searchTerm;
            set
            {
                if (_searchTerm != value)
                {
                    _searchTerm = value;
                    OnPropertyChanged();
                    SearchCustomers();
                }
            }
        }

        public ExactCash.Domain.Entities.Customer SelectedCustomer
        {
            get => _selectedCustomer;
            set
            {
                if (_selectedCustomer != value)
                {
                    _selectedCustomer = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(CustomerName));
                    OnPropertyChanged(nameof(CustomerPhone));
                    OnPropertyChanged(nameof(CustomerDiscount));
                }
            }
        }

        public ObservableCollection<ExactCash.Domain.Entities.Customer> SearchResults
        {
            get => _searchResults;
            set
            {
                _searchResults = value;
                OnPropertyChanged();
            }
        }

        private async void SearchCustomers()
        {
            if (string.IsNullOrWhiteSpace(SearchTerm))
            {
                SearchResults.Clear();
                CustomerName = string.Empty;
                CustomerPhone = string.Empty;
                return;
            }

            try
            {
                var customer = await _customerService.SearchCustomersAsync(SearchTerm);
                SearchResults.Clear();
                if (customer != null && customer.Id != 0)
                {
                    SearchResults.Add(customer);
                    SelectedCustomer = customer;
                    CustomerName = customer.FullName;
                    CustomerPhone = customer.Phone;
                }
                else
                {
                    CustomerName = string.Empty;
                    CustomerPhone = string.Empty;
                }
            }
            catch (Exception ex)
            {
                // Handle error (you might want to show a message to the user)
                Console.WriteLine($"Error searching customers: {ex.Message}");
            }
        }

        private async void DebounceSearch()
        {
            // Cancel any pending search
            _searchCancellationTokenSource.Cancel();
            _searchCancellationTokenSource = new CancellationTokenSource();

            try
            {
                // Wait for the delay period
                await Task.Delay(SearchDelayMilliseconds, _searchCancellationTokenSource.Token);

                // If the delay wasn't cancelled, perform the search
                if (!_searchCancellationTokenSource.Token.IsCancellationRequested)
                {
                    await SearchProductsAsync();
                }
            }
            catch (TaskCanceledException)
            {
                // Search was cancelled, ignore
            }
        }

        private async Task SearchProductsAsync()
        {
            try
            {
                // Clear selection if search term is empty and we're not setting selected product
                if (string.IsNullOrWhiteSpace(ProductSearchTerm) && !_isSettingSelectedProduct && SelectedProduct != null)
                {
                    SelectedProduct = null;
                }

                // Use SearchProductsAsync for both empty and non-empty search terms
                // Backend now handles empty search terms by returning all products
                var products = await _productService.SearchProductsAsync(ProductSearchTerm ?? "");
                ProductSearchResults = new ObservableCollection<ExactCash.Application.DTOs.ProductDto>(products);
            }
            catch (Exception ex)
            {
                Helpers.BootstrapMessageBoxHelper.ShowError($"حدث خطأ أثناء البحث عن المنتجات: {ex.Message}", "خطأ", owner: FindParentWindow());
                ProductSearchResults = new ObservableCollection<Application.DTOs.ProductDto>();
            }
        }

        public async void LoadProductsOnDropdownOpen()
        {
            // Load first page of products when dropdown is opened
            if (ProductSearchResults == null || !ProductSearchResults.Any())
            {
                await LoadInitialProducts();
            }
        }

        private async Task LoadInitialProducts()
        {
            try
            {
                // Use SearchProductsAsync with empty string to get all products
                var products = await _productService.SearchProductsAsync("");
                ProductSearchResults = new ObservableCollection<Application.DTOs.ProductDto>(products);
            }
            catch (Exception ex)
            {
                Helpers.BootstrapMessageBoxHelper.ShowError($"حدث خطأ أثناء تحميل المنتجات: {ex.Message}", "خطأ", owner: FindParentWindow());
                ProductSearchResults = new ObservableCollection<Application.DTOs.ProductDto>();
            }
        }

        private void AddToCart()
        {
            if (SelectedProduct != null)
            {
                // TODO: Implement add to cart logic
                System.Windows.MessageBox.Show($"Added {SelectedProduct.Name} to cart", "Success", MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }

        private bool CanAddToCart()
        {
            return SelectedProduct != null;
        }

        private async void ExecuteSearchByBarcode()
        {
            if (string.IsNullOrWhiteSpace(BarcodeSearchTerm))
                return;

            try
            {
                var product = await _productService.GetProductByBarcodeAsync(BarcodeSearchTerm);
                if (product != null && product.Id != 0)
                {
                    // Check if product already exists in cart
                    var existingProduct = SelectedProducts.FirstOrDefault(p => p.Id == product.Id);
                    if (existingProduct != null)
                    {
                        // Increment quantity instead of showing error
                        existingProduct.Quantity += 1;
                    }
                    else
                    {
                        // Add new product to cart
                        var cartProduct = new ExactCash.WPF.Models.CartProduct
                        {
                            Id = product.Id,
                            Name = product.Name,
                            UnitId = product.DefaultUnitId, // Set the UnitId from the product's DefaultUnitId
                            SellingPrice = product.SellingPrice,
                            CategoryName = product.CategoryName,
                            BrandName = product.BrandName,
                            ImagePath = product.ImagePath,
                            Quantity = product.StockQuantity,
                            Discount = product.Discount
                        };
                        SelectedProducts.Add(cartProduct);
                    }
                }
                else
                {
                    Helpers.BootstrapMessageBoxHelper.ShowError("لم يتم العثور على المنتج", "خطأ", owner: FindParentWindow());
                }
            }
            catch (Exception ex)
            {
                Helpers.BootstrapMessageBoxHelper.ShowError($"حدث خطأ أثناء البحث عن المنتج: {ex.Message}", "خطأ", owner: FindParentWindow());
            }
            finally
            {
                // Clear the barcode search box
                BarcodeSearchTerm = string.Empty;
            }
        }

        private bool CanAddProductToCart()
        {
            return SelectedProduct != null;
        }

        private void ExecuteAddProductToCart()
        {
            if (SelectedProduct == null) return;

            // Check if the product is already in the cart
            var existingProduct = SelectedProducts.FirstOrDefault(p => p.Id == SelectedProduct.Id);
            if (existingProduct != null)
            {
                Helpers.BootstrapMessageBoxHelper.ShowError("هذا المنتج موجود بالفعل في السلة", "خطأ", owner: FindParentWindow());
                return;
            }

            // Create a new instance of the product for the cart
            var cartProduct = new ExactCash.WPF.Models.CartProduct
            {
                Id = SelectedProduct.Id,
                ProductId = SelectedProduct.Id,
                Name = SelectedProduct.Name,
                UnitId = SelectedProduct.DefaultUnitId, // Set default unit if DefaultUnitId is null
                UnitName = SelectedProduct.UnitName,
                SellingPrice = SelectedProduct.SellingPrice,
                CategoryName = SelectedProduct.CategoryName,
                BrandName = SelectedProduct.BrandName,
                ImagePath = SelectedProduct.ImagePath,
                Quantity = SelectedProduct.StockQuantity,
                Discount = SelectedProduct.Discount
            };

            SelectedProducts.Add(cartProduct);

            // Clear the selection and search
            SelectedProduct = null;
            _isSettingSelectedProduct = true;
            ProductSearchTerm = string.Empty;
            _isSettingSelectedProduct = false;
            ProductSearchResults.Clear();

            // Notify that the command can be executed (or not)
            _addProductToCartCommand?.RaiseCanExecuteChanged();
        }

        private void ExecuteRemoveProduct(ExactCash.WPF.Models.CartProduct product)
        {
            if (product != null)
            {
                SelectedProducts.Remove(product);
            }
        }

        protected bool SetProperty<T>(ref T field, T value, [CallerMemberName] string propertyName = null)
        {
            if (EqualityComparer<T>.Default.Equals(field, value)) return false;
            field = value;
            OnPropertyChanged(propertyName);
            return true;
        }

        public event PropertyChangedEventHandler PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        private void SelectedProducts_CollectionChanged(object sender, System.Collections.Specialized.NotifyCollectionChangedEventArgs e)
        {
            if (e.NewItems != null)
            {
                foreach (Models.CartProduct item in e.NewItems)
                {
                    item.PropertyChanged += CartProduct_PropertyChanged;
                }
            }

            if (e.OldItems != null)
            {
                foreach (Models.CartProduct item in e.OldItems)
                {
                    item.PropertyChanged -= CartProduct_PropertyChanged;
                }
            }

            CalculateTotalAmount();
        }

        private void CartProduct_PropertyChanged(object sender, PropertyChangedEventArgs e)
        {
            if (e.PropertyName == nameof(Models.CartProduct.TotalPrice) ||
                e.PropertyName == nameof(Models.CartProduct.TotalPricePerProduct) ||
                e.PropertyName == nameof(Models.CartProduct.Quantity) ||
                e.PropertyName == nameof(Models.CartProduct.SellingPrice) ||
                e.PropertyName == nameof(Models.CartProduct.Discount) ||
                e.PropertyName == nameof(Models.CartProduct.DiscountAmount) ||
                e.PropertyName == nameof(Models.CartProduct.DiscountValue))
            {
                CalculateTotalAmount();
            }
        }

        private void CalculateTotalAmount()
        {
            if (SelectedProducts == null) return;

            decimal total = 0;
            foreach (var item in SelectedProducts)
            {
                total += item.TotalPrice;
            }
            TotalAmount = total;
        }

        private void CalculateNetAmount()
        {
            NetAmount = TotalAmount - DiscountAmount + TaxAmount;
            CalculateRemainingAmount();
        }

        private void CalculateRemainingAmount()
        {
            RemainingAmount = NetAmount - PaidAmount;
        }

        private bool CanSaveInvoice()
        {
            return SelectedProducts.Any() && NetAmount > 0;
        }

        private string GetEnglishPaymentType()
        {
            if (string.IsNullOrEmpty(_invoiceType))
                return "Cash"; // Default to Cash if no type is set

            return PaymentTypeMapping.TryGetValue(_invoiceType, out var englishType) ? englishType : "Cash";
        }

        private async void ExecuteSaveInvoice()
        {
            try
            {
                if (SelectedCustomer == null)
                {
                    Helpers.BootstrapMessageBoxHelper.ShowError("الرجاء اختيار العميل", owner: FindParentWindow());
                    return;
                }

                if (!SelectedProducts.Any())
                {
                    Helpers.BootstrapMessageBoxHelper.ShowError("الرجاء إضافة منتجات للفاتورة", owner: FindParentWindow());
                    return;
                }

                if (PaidAmount <= 0)
                {
                    Helpers.BootstrapMessageBoxHelper.ShowError("الرجاء إضافة القيمه المدفوعه من العميل", owner: FindParentWindow());
                    return;
                }

                var saleDto = new SaleDto
                {
                    CustomerId = SelectedCustomer.Id,
                    SaleDate = DateTime.UtcNow,
                    InvoiceNumber = InvoiceNumber,
                    InvoiceType = GetEnglishPaymentType(),
                    DiscountAmount = DiscountAmount,
                    TaxAmount = TaxAmount,
                    NetAmount = NetAmount,
                    TotalAmount = TotalAmount,
                    PaidAmount = PaidAmount,
                    RemainingAmount = RemainingAmount,
                    Notes = Notes,
                    SaleItems = SelectedProducts.Select(p => new SaleItemDto
                    {
                        ProductName = p.Name,
                        Unit = new UnitDto { Name = p.UnitName },
                        ProductId = p.ProductId,
                        Quantity = p.Quantity,
                        Price = p.SellingPrice,
                        Discount = p.Discount,
                        UnitId = p.UnitId
                    }).ToList(),
                    Payments = new List<PaymentDto>
                    {
                        new PaymentDto
                        {
                            Amount = PaidAmount,
                            PaymentMethod = GetEnglishPaymentType(),
                            PaymentDate = DateTime.UtcNow
                        }
                    }
                };

                _loadingViewModel.Show("جاري حفظ الفاتورة...");
                BaseResponse<string> result;

                // Check if this is an update or new sale
                if (currentSaleId != 0)
                {
                    // This is an update
                    var existingSale = await _saleService.GetByIdAsync(currentSaleId);
                    if (existingSale != null)
                    {
                        saleDto.Id = existingSale.Id;
                        await _saleService.UpdateAsync(saleDto);
                        result = ResponseHelper.Success(StatusCodes.Status200OK, string.Empty, "تم تحديث الفاتورة بنجاح");
                    }
                    else
                    {
                        result = ResponseHelper.Success(StatusCodes.Status500InternalServerError, string.Empty, "لم يتم العثور على الفاتورة للتحديث");
                    }
                }
                else
                {
                    // This is a new sale
                    result = await _saleService.CreateAsync(saleDto);
                }

                _loadingViewModel.Hide();

                if (result.Success)
                {
                    // Run in background
                    _ = Task.Run(() => SendInvoiceToFdin(saleDto));
                    //_notificationViewModel.ShowSuccess(result.Message);
                    Helpers.BootstrapMessageBoxHelper.ShowSuccess(result.Message, owner: FindParentWindow());
                    // Clear the form only for new sales
                    if (currentSaleId == 0)
                    {
                        ClearForm();
                    }
                    else
                    {
                        FindParentWindow()?.Close();
                    }
                }
                else
                {
                    //_notificationViewModel.ShowError(result.Message);
                    Helpers.BootstrapMessageBoxHelper.ShowError(result.Message, owner: FindParentWindow());
                }
            }
            catch (Exception ex)
            {
                _loadingViewModel.Hide();
                Helpers.BootstrapMessageBoxHelper.ShowError($"حدث خطأ أثناء حفظ الفاتورة: {ex.Message}");
            }
        }

        private string GetRandomStore()
        {
            var stores = new List<string> {
                         "Zara",
                         "defacto",
                         "jarir",
                         "mcdonalds",
                         "nahdi",
                         "virgin"
             };

            var random = new Random();
            int index = random.Next(stores.Count);
            return stores[index];
        }

        private async void SendInvoiceToFdin(SaleDto saleDto)
        {
            try
            {
                // Create HTTP client
                using var httpClient = new HttpClient();
                httpClient.BaseAddress = new Uri(_configuration["ApiSettings:FdinUrl"]);

                // Create the invoice receive DTO
                var invoiceReceiveDto = new Models.Fdin.InvoiceReceiveDto
                {
                    User = new Models.Fdin.User
                    {
                        Phone = _searchResults[0].Phone,
                        FullName = CustomerName,
                        Email = _searchResults[0].Email,
                        Address = _searchResults[0].Address,
                    },
                    Sale = new Models.Fdin.Sale
                    {
                        StoreName = GetRandomStore(),
                        SaleDate = saleDto.SaleDate,
                        InvoiceNumber = saleDto.InvoiceNumber,
                        InvoiceType = saleDto.InvoiceType,
                        DiscountAmount = saleDto.DiscountAmount,
                        TaxAmount = saleDto.TaxAmount,
                        NetAmount = saleDto.NetAmount,
                        TotalAmount = saleDto.TotalAmount,
                        PaidAmount = saleDto.PaidAmount,
                        RemainingAmount = saleDto.RemainingAmount,
                        Notes = saleDto.Notes,
                        CreationDate = DateTime.UtcNow,
                        // Add SaleItems
                        SaleItems = saleDto.SaleItems.Select(item => new Models.Fdin.SaleItem
                        {
                            ProductName = item.ProductName,
                            UnitName = item.Unit?.Name,
                            Quantity = item.Quantity,
                            Price = item.Price,
                            Discount = item.Discount
                        }).ToList()
                    }
                };

                // Convert the DTO to JSON
                var json = System.Text.Json.JsonSerializer.Serialize(invoiceReceiveDto);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                // Send the request
                var response = await httpClient.PostAsync("api/Invoice/receive", content);
            }
            catch (Exception ex)
            {
                //_logger.LogError(ex, $"Error sending invoice {saleDto.InvoiceNumber} to FDIN.web");
            }
        }

        private void ClearForm()
        {
            SelectedCustomer = null;
            CustomerName = string.Empty;
            CustomerPhone = string.Empty;
            CustomerDiscount = 0;
            SelectedProducts.Clear();
            DiscountAmount = 0;
            TaxAmount = 0;
            PaidAmount = 0;
            Notes = string.Empty;
            SearchTerm = string.Empty;
            // Generate new invoice number
            GenerateInvoiceNumberAsync();
        }

        private bool CanPrintInvoice()
        {
            return SelectedProducts.Any() && NetAmount > 0;
        }

        private async void ExecutePrintInvoice()
        {
            try
            {
                if (SelectedCustomer == null)
                {
                    System.Windows.MessageBox.Show("الرجاء اختيار العميل", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                    return;
                }

                if (!SelectedProducts.Any())
                {
                    System.Windows.MessageBox.Show("الرجاء إضافة منتجات للفاتورة", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                    return;
                }

                if (PaidAmount <= 0)
                {
                    System.Windows.MessageBox.Show("الرجاء إضافة القيمه المدفوعه من العميل", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                    return;
                }

                var saleDto = new ExactCash.Application.DTOs.SaleDto
                {
                    CustomerId = SelectedCustomer.Id,
                    Customer = new Application.DTOs.CustomerDto
                    {
                        Id = SelectedCustomer.Id,
                        FullName = SelectedCustomer.FullName,
                        Phone = SelectedCustomer.Phone
                    },
                    SaleDate = DateTime.UtcNow,
                    InvoiceNumber = InvoiceNumber,
                    InvoiceType = GetEnglishPaymentType(), // Convert to English before saving
                    DiscountAmount = DiscountAmount,
                    TaxAmount = TaxAmount,
                    NetAmount = NetAmount,
                    TotalAmount = TotalAmount,
                    PaidAmount = PaidAmount,
                    RemainingAmount = RemainingAmount,
                    Notes = Notes,
                    SaleItems = SelectedProducts.Select(p => new Application.DTOs.SaleItemDto
                    {
                        ProductId = p.Id,
                        Quantity = p.Quantity,
                        Price = p.SellingPrice,
                        ProductName = p.Name,
                        Discount = p.Discount,
                        UnitId = 1,
                        Unit = new UnitDto
                        {
                            Name = p.UnitName
                        }
                    }).ToList(),
                    Payments = new List<Application.DTOs.PaymentDto>
                    {
                        new Application.DTOs.PaymentDto
                        {
                            Amount = PaidAmount,
                            PaymentMethod = GetEnglishPaymentType(),
                            PaymentDate = DateTime.UtcNow
                        }
                    }
                };

                _loadingViewModel.Show("جاري حفظ وطباعة الفاتورة...");
                var result = await _printerService.PrintInvoiceAsync(saleDto);

                if (result)
                {
                    System.Windows.MessageBox.Show("تم حفظ وطباعة الفاتورة بنجاح", "نجاح", MessageBoxButton.OK, MessageBoxImage.Information);
                    ClearForm();
                }
                else
                {
                    System.Windows.MessageBox.Show("تم حفظ الفاتورة ولكن حدث خطأ أثناء الطباعة", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            catch (Exception ex)
            {
                System.Windows.MessageBox.Show($"حدث خطأ أثناء حفظ وطباعة الفاتورة: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                _loadingViewModel.Hide();
            }
        }

        private void ExecuteAddNewProduct()
        {
            var addProductView = new Views.Product.AddProductView(
                _productService,
                _mapper,
                _categoryServiceClient,
                _brandsServiceClient,
                _unitServiceClient);
            addProductView.Owner = FindParentWindow();

            var result = addProductView.ShowDialog();
            if (result == true)
            {
                // If a new product was added successfully, add it to the cart
                var newProduct = (addProductView.DataContext as Product.AddProductViewModel)?.NewProduct;
                if (newProduct != null)
                {
                    var cartProduct = new ExactCash.WPF.Models.CartProduct
                    {
                        Id = newProduct.Id,
                        Name = newProduct.Name,
                        UnitId = newProduct.DefaultUnitId,
                        SellingPrice = newProduct.SellingPrice,
                        CategoryName = newProduct.CategoryName,
                        CategoryId = newProduct.CategoryId,
                        BrandName = newProduct.BrandName,
                        ImagePath = newProduct.ImagePath,
                        Quantity = 1,
                        Discount = newProduct.Discount
                    };

                    SelectedProducts.Add(cartProduct);

                    // Clear the search results to ensure they're refreshed next time
                    ProductSearchResults.Clear();
                    ProductSearchTerm = string.Empty;
                }
            }
        }

        public void LoadSaleData(SaleDto sale)
        {
            if (sale == null) return;

            // Set basic sale information
            currentSaleId = sale.Id;
            InvoiceNumber = sale.InvoiceNumber; // Use the invoice number from the model
            DiscountAmount = sale.DiscountAmount;
            TaxAmount = sale.TaxAmount;
            TotalAmount = sale.TotalAmount;
            PaidAmount = sale.PaidAmount;
            RemainingAmount = sale.RemainingAmount;
            InvoiceType = (sale.InvoiceType == "Cash") ? "نقدي" : "آجل";
            Notes = sale.Notes; // Add notes from the model

            // Load customer information if available
            if (sale.Customer != null)
            {
                SelectedCustomer = new Domain.Entities.Customer
                {
                    Id = sale.Customer.Id,
                    FullName = sale.Customer.FullName,
                    Phone = sale.Customer.Phone,
                };
                CustomerName = sale.Customer.FullName;
                CustomerPhone = sale.Customer.Phone;
            }

            // Load sale items
            if (sale.SaleItems != null && sale.SaleItems.Any())
            {
                SelectedProducts.Clear();
                foreach (var item in sale.SaleItems)
                {
                    var cartProduct = new ExactCash.WPF.Models.CartProduct
                    {
                        ProductId = item.ProductId,
                        Name = item.ProductName,
                        SellingPrice = item.Price,
                        Quantity = item.Quantity,
                        Discount = item.Discount,
                        UnitId = item.UnitId,
                        CategoryName = item.Product?.CategoryName,
                        BrandName = item.Product?.Brand?.Name,
                        Category = item.Product?.CategoryName,
                        CategoryId = item.Product?.CategoryId ?? 0,
                    };
                    SelectedProducts.Add(cartProduct);
                }
            }
        }
    }
}