<Window x:Class="ExactCash.WPF.Views.Dashboard.DashboardView"
		xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
		xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
		xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
		xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
		xmlns:local="clr-namespace:ExactCash.WPF.Views.Dashboard"
		xmlns:common="clr-namespace:ExactCash.WPF.Views.Common"
		mc:Ignorable="d"
		Title="لوحة التحكم"
		Height="800"
		Width="1280"
		WindowStartupLocation="CenterScreen"
		WindowState="Maximized"
		Background="#F5F5F5"
		FlowDirection="RightToLeft">

	<Window.Resources>
		<!-- Module Button Style -->
		<Style x:Key="ModuleButtonStyle"
			   TargetType="Button">
			<Setter Property="Width"
					Value="200"/>
			<Setter Property="Height"
					Value="200"/>
			<Setter Property="Margin"
					Value="15"/>
			<Setter Property="Background"
					Value="White"/>
			<Setter Property="BorderThickness"
					Value="0"/>
			<Setter Property="Cursor"
					Value="Hand"/>
			<Setter Property="Template">
				<Setter.Value>
					<ControlTemplate TargetType="Button">
						<Border Background="{TemplateBinding Background}"
								BorderBrush="{TemplateBinding BorderBrush}"
								BorderThickness="{TemplateBinding BorderThickness}"
								CornerRadius="10"
								Padding="20">
							<Border.Effect>
								<DropShadowEffect BlurRadius="10"
												  ShadowDepth="2"
												  Opacity="0.2"/>
							</Border.Effect>
							<Grid>
								<Grid.RowDefinitions>
									<RowDefinition Height="*"/>
									<RowDefinition Height="Auto"/>
								</Grid.RowDefinitions>

								<Path Grid.Row="0"
									  Data="{Binding Tag, RelativeSource={RelativeSource TemplatedParent}}"
									  Fill="#0078D4"
									  Width="80"
									  Height="80"
									  Stretch="Uniform"/>

								<TextBlock Grid.Row="1"
										   Text="{TemplateBinding Content}"
										   HorizontalAlignment="Center"
										   VerticalAlignment="Bottom"
										   FontSize="16"
										   FontWeight="SemiBold"
										   Margin="0,15,0,0"/>
							</Grid>
						</Border>
						<ControlTemplate.Triggers>
							<Trigger Property="IsMouseOver"
									 Value="True">
								<Setter Property="Background"
										Value="#f8f9fa"/>
							</Trigger>
						</ControlTemplate.Triggers>
					</ControlTemplate>
				</Setter.Value>
			</Setter>
		</Style>
	</Window.Resources>

	<Grid>
		<Grid.RowDefinitions>
			<RowDefinition Height="Auto"/>
			<RowDefinition Height="Auto"/>
			<RowDefinition Height="*"/>
		</Grid.RowDefinitions>

		<!-- Header -->
		<Border Grid.Row="0"
				Background="#0078D4"
				Height="60"
				Margin="0,0,0,20">
			<TextBlock Text="لوحة التحكم"
					   Foreground="White"
					   FontSize="24"
					   FontWeight="SemiBold"
					   VerticalAlignment="Center"
					   Margin="20,0"/>
		</Border>

		<!-- First Row of Modules -->
		<WrapPanel Grid.Row="1"
				   HorizontalAlignment="Center"
				   VerticalAlignment="Center">

			<!-- Sales Module -->
			<Button Style="{StaticResource ModuleButtonStyle}"
					Content="المبيعات"
					Click="SalesButton_Click">
				<Button.Tag>
					M6 2a1 1 0 000 2h2l1.5 9H6a1 1 0 000 2h9a1 1 0 001-.88L17 4h2a1 1 0 100-2H6zM9 21a1.5 1.5 0 110-3 1.5 1.5 0 010 3zM15 21a1.5 1.5 0 110-3 1.5 1.5 0 010 3z
				</Button.Tag>
			</Button>

			<!-- Products Module -->
			<Button Style="{StaticResource ModuleButtonStyle}"
					Content="المنتجات"
					Click="ProductsButton_Click">
				<Button.Tag>
					M4 4h16v2H4V4zm0 4h16v2H4V8zm0 4h16v6H4v-6zm2 2v2h12v-2H6z
				</Button.Tag>
			</Button>

			<!-- Inventory Module -->
			<Button Style="{StaticResource ModuleButtonStyle}"
					Content="المخزون"
					Click="InventoryButton_Click">
				<Button.Tag>
					M4 5a2 2 0 012-2h6a2 2 0 012 2v2h2a2 2 0 012 2v12a2 2 0 01-2 2H4a2 2 0 01-2-2V7a2 2 0 012-2h2V5zm8 0v2H8V5h4zM4 7v12h16V7H4zm4 3a1 1 0 011-1h6a1 1 0 110 2h-6a1 1 0 01-1-1zm0 4a1 1 0 011-1h6a1 1 0 110 2h-6a1 1 0 01-1-1z
				</Button.Tag>
			</Button>

			<!-- Purchasing Module -->
			<Button Style="{StaticResource ModuleButtonStyle}"
					Content="المشتريات"
					Click="PurchasingButton_Click">
				<Button.Tag>
					M3 1a1 1 0 000 2h1.22l.305 1.222a.997.997 0 00.01.042l1.358 5.43-.893.892C3.74 11.846 4.632 14 6.414 14H15a1 1 0 000-2H6.414l1-1H14a1 1 0 00.894-.553l3-6A1 1 0 0017 3H6.28l-.31-1.243A1 1 0 005 1H3zM16 16.5a1.5 1.5 0 11-3 0 1.5 1.5 0 013 0zM6.5 18a1.5 1.5 0 100-3 1.5 1.5 0 000 3z
				</Button.Tag>
			</Button>

			<!-- Suppliers Module -->
			<Button Style="{StaticResource ModuleButtonStyle}"
					Content="الموردين"
					Click="SuppliersButton_Click">
				<Button.Tag>
					M9 6a3 3 0 11-6 0 3 3 0 016 0zM17 6a3 3 0 11-6 0 3 3 0 016 0zM12.93 17c.046-.327.07-.66.07-1a6.97 6.97 0 00-1.5-4.33A5 5 0 0119 16v1h-6.07zM6 11a5 5 0 015 5v1H1v-1a5 5 0 015-5z
				</Button.Tag>
			</Button>

			<!-- User Management Module -->
			<Button Style="{StaticResource ModuleButtonStyle}"
					Content="إدارة المستخدمين"
					Click="UserManagementButton_Click">
				<Button.Tag>
					M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z
				</Button.Tag>
			</Button>
			<!-- Customers Module -->
			<Button Style="{StaticResource ModuleButtonStyle}"
					Content="العملاء"
					Click="CustomersButton_Click">
				<Button.Tag>
					M9 6a3 3 0 11-6 0 3 3 0 016 0zM17 6a3 3 0 11-6 0 3 3 0 016 0zM12.93 17c.046-.327.07-.66.07-1a6.97 6.97 0 00-1.5-4.33A5 5 0 0119 16v1h-6.07zM6 11a5 5 0 015 5v1H1v-1a5 5 0 015-5z
				</Button.Tag>
			</Button>

			<!-- Settings Module -->
			<Button Style="{StaticResource ModuleButtonStyle}"
					Content="الإعدادات"
					Click="SettingsButton_Click">
				<Button.Tag>
					M12 8c-2.21 0-4 1.79-4 4s1.79 4 4 4 4-1.79 4-4-1.79-4-4-4zm0 10c-3.31 0-6-2.69-6-6s2.69-6 6-6 6 2.69 6 6-2.69 6-6 6z
				</Button.Tag>
			</Button>

			<Button Style="{StaticResource ModuleButtonStyle}"
					Content="الفواتير"
					Click="InvoicesButton_Click">
				<Button.Tag>
					M3 6h18v2H3V6zm0 4h18v8H3v-8zm2 2v4h14v-4H5z
				</Button.Tag>
			</Button>

			<Button Style="{StaticResource ModuleButtonStyle}"
					Content="الإرجاع"
					Click="ReturnsButton_Click">
				<Button.Tag>
					M10 9V5l-7 7 7 7v-4.1c4.2 0 7.5 1.3 10 4.1-1.5-5-5-8-10-8z
				</Button.Tag>
			</Button>

			<Button Style="{StaticResource ModuleButtonStyle}"
					Content="المصروفات"
					Click="ExpensesButton_Click">
				<Button.Tag>
					M12 2a10 10 0 100 20 10 10 0 000-20zm-1 15v-4H8l4-6v4h3l-4 6z
				</Button.Tag>
			</Button>

			<!-- Credit Sales Module -->
			<Button Style="{StaticResource ModuleButtonStyle}"
					Content="المبيعات الآجلة"
					Click="CreditSalesButton_Click">
				<Button.Tag>
					M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z
				</Button.Tag>
			</Button>

			<!-- Reporting Module -->
			<Button Style="{StaticResource ModuleButtonStyle}"
					Content="التقارير"
					Click="ReportingButton_Click">
				<Button.Tag>
					M7 12l3-3 3 3 4-4M8 21l4-4 4 4M3 4h18M4 4h16v12a1 1 0 01-1 1H5a1 1 0 01-1-1V4z
				</Button.Tag>
			</Button>
		</WrapPanel>

		<!-- Notification View -->
		<common:NotificationView Grid.Row="2"
								 DataContext="{Binding NotificationViewModel}"
								 HorizontalAlignment="Right"
								 VerticalAlignment="Top"
								 Margin="0,20,20,0"/>

	</Grid>
</Window>