using System;
using System.ComponentModel.DataAnnotations;
using ExactCash.WASM.Application.DTOs.Common;
#nullable disable

namespace ExactCash.WASM.Application.DTOs
{
    public class SupplierDto : BaseEntityDto
    {
        /// <summary>
        /// The name of the supplier.
        /// </summary>
        [Required(ErrorMessage = "اسم المورد مطلوب")]
        public string Name { get; set; }

        /// <summary>
        /// The contact person's name at the supplier.
        /// </summary>
        public string ContactPerson { get; set; }

        /// <summary>
        /// The email address of the supplier.
        /// </summary>
        public string Email { get; set; }

        /// <summary>
        /// The phone number of the supplier.
        /// </summary>
        [Required(ErrorMessage = "رقم الهاتف مطلوب")]
        public string Phone { get; set; }

        /// <summary>
        /// The address of the supplier.
        /// </summary>
        public string Address { get; set; }

        /// <summary>
        /// The tax identification number of the supplier.
        /// </summary>
        public string TaxId { get; set; }

        /// <summary>
        /// The bank account number of the supplier.
        /// </summary>
        public string BankAccountNumber { get; set; }

        /// <summary>
        /// The bank name of the supplier.
        /// </summary>
        public string BankName { get; set; }

        /// <summary>
        /// Indicates whether the supplier is active.
        /// </summary>
        public bool IsActive { get; set; }

        public bool IsDeleted { get; set; } = false;

        public string Representative { get; set; }

        /// <summary>
        /// The notes associated with the supplier.
        /// </summary>
        public string Notes { get; set; }

        /// <summary>
        /// The ID of the supplier category this supplier belongs to.
        /// </summary>
        public int? CategoryId { get; set; }

        /// <summary>
        /// The supplier category this supplier belongs to.
        /// </summary>
        public SupplierCategoryDto Category { get; set; }

        /// <summary>
        /// The name of the supplier category (for display purposes).
        /// </summary>
        public string CategoryName => Category?.Name;
    }
}