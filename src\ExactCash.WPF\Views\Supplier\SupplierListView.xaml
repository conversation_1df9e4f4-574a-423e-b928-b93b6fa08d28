<Window x:Name="SuppliersListView"
                x:Class="ExactCash.WPF.Views.Supplier.SupplierListView"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:ExactCash.WPF.Views.Supplier"
        mc:Ignorable="d"
        Title="قائمة الموردين"
        Height="750"
        Width="1200"
        WindowStartupLocation="CenterOwner"
        FlowDirection="RightToLeft"
        Background="#f8f9fa"
        WindowStyle="None"
        AllowsTransparency="True"
        ResizeMode="CanResize"
        WindowState="Maximized">

        <Window.Resources>
                <Style x:Key="CloseButtonStyle"
                       TargetType="Button">
                        <Setter Property="Template">
                                <Setter.Value>
                                        <ControlTemplate TargetType="Button">
                                                <Border x:Name="border"
                                                        Background="Transparent"
                                                        BorderThickness="0">
                                                        <ContentPresenter HorizontalAlignment="Center"
                                                                          VerticalAlignment="Center"/>
                                                </Border>
                                                <ControlTemplate.Triggers>
                                                        <Trigger Property="IsMouseOver"
                                                                 Value="True">
                                                                <Setter Property="Foreground"
                                                                        Value="#e74c3c"/>
                                                        </Trigger>
                                                </ControlTemplate.Triggers>
                                        </ControlTemplate>
                                </Setter.Value>
                        </Setter>
                </Style>

                <Style x:Key="ModernButton"
                       TargetType="Button">
                        <Setter Property="Background"
                                Value="#0078D4"/>
                        <Setter Property="Foreground"
                                Value="White"/>
                        <Setter Property="BorderThickness"
                                Value="0"/>
                        <Setter Property="Padding"
                                Value="15,8"/>
                        <Setter Property="FontSize"
                                Value="14"/>
                        <Setter Property="Template">
                                <Setter.Value>
                                        <ControlTemplate TargetType="Button">
                                                <Border Background="{TemplateBinding Background}"
                                                        BorderThickness="{TemplateBinding BorderThickness}"
                                                        BorderBrush="{TemplateBinding BorderBrush}"
                                                        CornerRadius="4">
                                                        <ContentPresenter HorizontalAlignment="Center"
                                                                          VerticalAlignment="Center"/>
                                                </Border>
                                        </ControlTemplate>
                                </Setter.Value>
                        </Setter>
                        <Style.Triggers>
                                <Trigger Property="IsMouseOver"
                                         Value="True">
                                        <Setter Property="Background"
                                                Value="#106EBE"/>
                                </Trigger>
                        </Style.Triggers>
                </Style>

                <Style x:Key="SuccessButton"
                       TargetType="Button"
                       BasedOn="{StaticResource ModernButton}">
                        <Setter Property="Background"
                                Value="#28a745"/>
                        <Style.Triggers>
                                <Trigger Property="IsMouseOver"
                                         Value="True">
                                        <Setter Property="Background"
                                                Value="#218838"/>
                                </Trigger>
                        </Style.Triggers>
                </Style>

                <Style x:Key="ModernTextBox"
                       TargetType="TextBox">
                        <Setter Property="Height"
                                Value="35"/>
                        <Setter Property="Padding"
                                Value="10,5"/>
                        <Setter Property="BorderThickness"
                                Value="1"/>
                        <Setter Property="BorderBrush"
                                Value="#CCCCCC"/>
                        <Setter Property="Background"
                                Value="White"/>
                        <Setter Property="FontSize"
                                Value="14"/>
                </Style>

                <Style x:Key="ModernComboBox"
                       TargetType="ComboBox">
                        <Setter Property="Height"
                                Value="35"/>
                        <Setter Property="Padding"
                                Value="10,5"/>
                        <Setter Property="BorderThickness"
                                Value="1"/>
                        <Setter Property="BorderBrush"
                                Value="#CCCCCC"/>
                        <Setter Property="Background"
                                Value="White"/>
                        <Setter Property="FontSize"
                                Value="14"/>
                </Style>
        </Window.Resources>

        <Border BorderThickness="1"
                BorderBrush="#dee2e6"
                Background="White"
                CornerRadius="6">
                <DockPanel>
                        <!-- Header -->
                        <Border DockPanel.Dock="Top"
                                Background="#0078D4"
                                Height="60"
                                BorderThickness="0,0,0,1"
                                BorderBrush="White">
                                <Grid>
                                        <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="*"/>
                                                <ColumnDefinition Width="Auto"/>
                                                <ColumnDefinition Width="Auto"/>
                                        </Grid.ColumnDefinitions>

                                        <!-- Title -->
                                        <TextBlock Text="قائمة الموردين"
                                                   Foreground="White"
                                                   FontSize="20"
                                                   FontWeight="Bold"
                                                   VerticalAlignment="Center"
                                                   Margin="20,0,0,0"/>

                                        <!-- Close Button -->
                                        <Button Grid.Column="1"
                                                Content="✕"
                                                Click="CloseButton_Click"
                                                Style="{StaticResource CloseButtonStyle}"
                                                FontSize="20"
                                                Foreground="White"
                                                Width="60"
                                                Height="60"/>

                                        <!-- Minimize Button -->
                                        <Button Grid.Column="2"
                                                Content="−"
                                                Click="MinimizeButton_Click"
                                                Style="{StaticResource CloseButtonStyle}"
                                                FontSize="20"
                                                Foreground="White"
                                                Width="60"
                                                Height="60"/>
                                </Grid>
                        </Border>

                        <!-- Content -->
                        <Grid Margin="20">
                                <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="*"/>
                                        <RowDefinition Height="Auto"/>
                                </Grid.RowDefinitions>

                                <!-- Search Panel -->
                                <Border Grid.Row="0"
                                        Background="#F8F9FA"
                                        BorderThickness="1"
                                        BorderBrush="#DEE2E6"
                                        CornerRadius="4"
                                        Padding="15"
                                        Margin="0,0,0,20">
                                        <Grid>
                                                <Grid.ColumnDefinitions>
                                                        <ColumnDefinition Width="*"/>
                                                        <ColumnDefinition Width="*"/>
                                                        <ColumnDefinition Width="Auto"/>
                                                </Grid.ColumnDefinitions>

                                                <!-- Name Search -->
                                                <StackPanel Grid.Column="0"
                                                            Margin="0,0,10,0">
                                                        <TextBlock Text="اسم المورد"
                                                                   Margin="0,0,0,5"/>
                                                        <TextBox Text="{Binding SearchName, UpdateSourceTrigger=PropertyChanged}"
                                                                 Style="{StaticResource ModernTextBox}"/>
                                                </StackPanel>

                                                <!-- Phone Search -->
                                                <StackPanel Grid.Column="1"
                                                            Margin="10,0,10,0">
                                                        <TextBlock Text="رقم الهاتف"
                                                                   Margin="0,0,0,5"/>
                                                        <TextBox Text="{Binding SearchPhone, UpdateSourceTrigger=PropertyChanged}"
                                                                 Style="{StaticResource ModernTextBox}"/>
                                                </StackPanel>

                                                <!-- Search Buttons -->
                                                <StackPanel Grid.Column="2"
                                                            Orientation="Horizontal"
                                                            VerticalAlignment="Bottom">
                                                        <Button Content="بحث"
                                                                Command="{Binding SearchCommand}"
                                                                Style="{StaticResource ModernButton}"
                                                                Width="100"
                                                                Height="35"
                                                                Margin="0,0,10,0"/>

                                                        <Button Content="إعادة تعيين"
                                                                Command="{Binding ResetSearchCommand}"
                                                                Style="{StaticResource ModernButton}"
                                                                Width="100"
                                                                Height="35"/>
                                                </StackPanel>
                                        </Grid>
                                </Border>

                                <!-- Action Buttons -->
                                <StackPanel Grid.Row="1"
                                            Orientation="Horizontal"
                                            HorizontalAlignment="Right"
                                            Margin="0,0,0,10">
                                        <Button Content="إضافة مورد جديد"
                                                Command="{Binding AddNewSupplierCommand}"
                                                Style="{StaticResource SuccessButton}"
                                                Height="35"
                                                Width="120"
                                                Margin="0,0,10,0"/>
                                        <Button Content="تحديث"
                                                Command="{Binding RefreshCommand}"
                                                Style="{StaticResource ModernButton}"
                                                Height="35"
                                                Width="80"/>
                                        <Button Content="تصدير إلى Excel"
                                                Width="140"
                                                Height="35"
                                                Margin="10,0,10,0"
                                                Command="{Binding ExportToExcelCommand}"
                                                Style="{StaticResource ModernButton}"/>
                                </StackPanel>

                                <!-- Suppliers Grid -->
                                <DataGrid Grid.Row="2"
                                          ItemsSource="{Binding Suppliers}"
                                          AutoGenerateColumns="False"
                                          CanUserAddRows="False"
                                          CanUserDeleteRows="False"
                                          IsReadOnly="True"
                                          SelectionMode="Single"
                                          SelectionUnit="FullRow"
                                          GridLinesVisibility="None"
                                          BorderThickness="1"
                                          BorderBrush="#DEE2E6"
                                          Background="White"
                                          RowHeight="40"
                                          AlternatingRowBackground="#F8F9FA"
                                          HorizontalScrollBarVisibility="Auto"
                                          VerticalScrollBarVisibility="Auto"
                                          Margin="0,0,0,10">
                                        <DataGrid.RowStyle>
                                                <Style TargetType="DataGridRow">
                                                        <Setter Property="BorderThickness"
                                                                Value="0"/>
                                                        <Setter Property="Margin"
                                                                Value="0"/>
                                                        <Setter Property="Padding"
                                                                Value="0"/>
                                                </Style>
                                        </DataGrid.RowStyle>
                                        <DataGrid.Columns>
                                                <DataGridTextColumn Header="اسم المورد"
                                                                    Binding="{Binding Name}"
                                                                    Width="200"/>
                                                <DataGridTextColumn Header="رقم الهاتف"
                                                                    Binding="{Binding Phone}"
                                                                    Width="100"/>
                                                <DataGridTextColumn Header="البريد الإلكتروني"
                                                                    Binding="{Binding Email, TargetNullValue='-'}"
                                                                    Width="200"/>
                                                <DataGridTextColumn Header="العنوان"
                                                                    Binding="{Binding Address, TargetNullValue='-'}"
                                                                    Width="200"/>
                                                <DataGridTextColumn Header="الشخص المسئول"
                                                                    Binding="{Binding Representative, TargetNullValue='-'}"
                                                                    Width="100"/>
                                                <DataGridTextColumn Header="الرقم الضريبي"
                                                                    Binding="{Binding TaxId, TargetNullValue='-'}"
                                                                    Width="100"/>
                                                <DataGridTextColumn Header="الحساب البنكي"
                                                                    Binding="{Binding BankAccountNumber, TargetNullValue='-'}"
                                                                    Width="100"/>
                                                <DataGridTextColumn Header="اسم البنك"
                                                                    Binding="{Binding BankName, TargetNullValue='-'}"
                                                                    Width="200"/>
                                                <DataGridTextColumn Header="تاريخ الإنشاء"
                                                                    Binding="{Binding CreationDate, StringFormat=dd/MM/yyyy HH:mm}"
                                                                    Width="120"/>
                                                <DataGridTextColumn Header="تم الإنشاء بواسطة"
                                                                    Binding="{Binding CreatedBy, TargetNullValue='-'}"
                                                                    Width="120"/>
                                                <DataGridTemplateColumn Width="Auto">
                                                        <DataGridTemplateColumn.CellTemplate>
                                                                <DataTemplate>
                                                                        <StackPanel Orientation="Horizontal">
                                                                                <Button
                                                                                        Command="{Binding DataContext.EditSupplierCommand, RelativeSource={RelativeSource AncestorType=Window}}"
                                                                                        CommandParameter="{Binding}"
                                                                                        Style="{StaticResource ModernButton}"
                                                                                        Width="35"
                                                                                        Height="30"
                                                                                        Margin="0,0,5,0">
                                                                                        <TextBlock Text="✏️"
                                                                                                   FontSize="16"/>
                                                                                </Button>
                                                                                <Button
                                                                                        Command="{Binding DataContext.DeleteSupplierCommand, RelativeSource={RelativeSource AncestorType=Window}}"
                                                                                        CommandParameter="{Binding}"
                                                                                        Style="{StaticResource ModernButton}"
                                                                                        Background="#DC3545"
                                                                                        Width="35"
                                                                                        Height="30">
                                                                                        <TextBlock Text="🗑️"
                                                                                                   FontSize="16"/>
                                                                                </Button>
                                                                        </StackPanel>
                                                                </DataTemplate>
                                                        </DataGridTemplateColumn.CellTemplate>
                                                </DataGridTemplateColumn>

                                        </DataGrid.Columns>
                                </DataGrid>

                                <!-- Pagination -->
                                <StackPanel Grid.Row="3"
                                            Orientation="Horizontal"
                                            HorizontalAlignment="Center"
                                            Margin="0,10,0,0">
                                        <Button Content="السابق"
                                                Command="{Binding PreviousPageCommand}"
                                                Style="{StaticResource ModernButton}"
                                                Width="100"
                                                Height="35"
                                                Margin="0,0,10,0"/>
                                        <TextBlock VerticalAlignment="Center"
                                                   Margin="0,0,10,0">
                        <Run Text="الصفحة"/>
                        <Run Text="{Binding CurrentPage, Mode=OneWay}"/>
                        <Run Text="من"/>
                        <Run Text="{Binding TotalPages, Mode=OneWay}"/>
                                        </TextBlock>
                                        <Button Content="التالي"
                                                Command="{Binding NextPageCommand}"
                                                Style="{StaticResource ModernButton}"
                                                Width="100"
                                                Height="35"/>
                                </StackPanel>
                        </Grid>
                </DockPanel>
        </Border>
</Window> 