﻿using ExactCash.Domain.Common;
#nullable disable

namespace ExactCash.Domain.Entities
{
    /// <summary>
    /// Represents a record of stock movement or adjustment, such as purchases, sales, returns, etc.
    /// </summary>
    public class StockMovement : BaseEntity
    {
        /// <summary>
        /// The ID of the product whose stock is being adjusted.
        /// </summary>
        public int ProductId { get; set; }

        /// <summary>
        /// Navigation property to the product being adjusted.
        /// </summary>
        public Product Product { get; set; }

        /// <summary>
        /// Unit of measure for this item in the sale (can be different from the product's unit)
        /// </summary>
        public int UnitId { get; set; }
        /// <summary>
        /// Navigation property to the untit of measure for this item.
        /// </summary>
        public Unit Unit { get; set; }

        /// <summary>
        /// The quantity of the product moved (positive for stock added, negative for stock removed).
        /// </summary>
        public int Quantity { get; set; }

        /// <summary>
        /// The type of stock movement (e.g., "Purchase", "Sale", "Manual Adjustment", "Return").
        /// </summary>
        public string Type { get; set; }

        /// <summary>
        /// A reference to the related transaction (e.g., PO# for purchase, Sale# for sales, or reason for manual adjustment).
        /// </summary>
        public string Reference { get; set; }
    }


}
