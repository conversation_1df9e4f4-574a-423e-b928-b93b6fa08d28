using AutoMapper;
using ExactCash.Application.DTOs;
using ExactCash.Application.DTOs.Common;
using ExactCash.Application.Enums;
using ExactCash.Application.Interfaces;
using ExactCash.Application.Responses;
using ExactCash.Domain.Entities;
using ExactCash.Infrastructure.Persistence;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
#nullable disable

namespace ExactCash.Application.Services
{
    public class PurchaseService : IPurchaseService
    {
        private readonly AppPostgreSQLDbContext _context;
        private readonly IMapper _mapper;
        private readonly ILogger<PurchaseService> _logger;

        public PurchaseService(AppPostgreSQLDbContext context, IMapper mapper, ILogger<PurchaseService> logger)
        {
            _context = context;
            _mapper = mapper;
            _logger = logger;
        }

        public async Task<PurchaseDto> GetPurchaseByIdAsync(int id)
        {
            var purchase = await _context.Purchases
                .Include(p => p.Supplier)
                .Include(p => p.Items)
                .ThenInclude(pi => pi.Product)
                .FirstOrDefaultAsync(p => p.Id == id);

            if (purchase == null)
                return null;

            return _mapper.Map<PurchaseDto>(purchase);
        }

        public async Task<PagedResponse<PurchaseDto>> GetAllPurchasesAsync(
            int? supplierId,
            string poNumber,
            DateTime? startDate,
            DateTime? endDate,
            PaginationFilter pagination)
        {
            var query = _context.Purchases
                .Include(p => p.Supplier)
                .Include(p => p.Items)
                .ThenInclude(pi => pi.Product)
                .AsQueryable();

            if (supplierId.HasValue)
                query = query.Where(p => p.SupplierId == supplierId);

            if (startDate.HasValue)
                query = query.Where(p => p.CreationDate >= startDate.Value);

            if (endDate.HasValue)
                query = query.Where(p => p.CreationDate <= endDate.Value);

            if (!string.IsNullOrEmpty(poNumber))
                query = query.Where(p => p.OrderNumber.Contains(poNumber));

            var totalRecords = await query.CountAsync();

            query = pagination.SortField switch
            {
                nameof(Purchase.CreationDate) => pagination.SortOrder == SortOrder.Asc ?
                    query.OrderBy(p => p.CreationDate) : query.OrderByDescending(p => p.CreationDate),
                nameof(Purchase.TotalAmount) => pagination.SortOrder == SortOrder.Asc ?
                    query.OrderBy(p => p.TotalAmount) : query.OrderByDescending(p => p.TotalAmount),
                _ => pagination.SortOrder == SortOrder.Desc ?
                    query.OrderBy(p => p.CreationDate) : query.OrderByDescending(p => p.CreationDate)
            };

            var result = await query
                .Skip((pagination.PageNumber - 1) * pagination.PageSize)
                .Take(pagination.PageSize)
                .ToListAsync();

            return PaginationHelper.CreatePagedResponse(
                _mapper.Map<List<PurchaseDto>>(result),
                pagination.PageNumber,
                pagination.PageSize,
                totalRecords);
        }

        public async Task<BaseResponse<bool>> CreatePurchaseAsync(PurchaseDto purchaseDto, string createdBy)
        {
            using var transaction = await _context.Database.BeginTransactionAsync();
            try
            {
                var purchase = new Purchase
                {
                    SupplierId = purchaseDto.SupplierId,
                    TotalAmount = purchaseDto.TotalAmount,
                    OrderNumber = purchaseDto.OrderNumber,
                    Notes = purchaseDto.Notes,
                    CreationDate = DateTime.UtcNow,
                    CreatedBy = createdBy
                };

                await _context.Purchases.AddAsync(purchase);
                await _context.SaveChangesAsync();

                foreach (var item in purchaseDto.Items)
                {
                    var purchaseItem = new PurchaseItem
                    {
                        PurchaseId = purchase.Id,
                        ProductId = item.ProductId,
                        UnitId = item.UnitId,
                        Quantity = item.Quantity,
                        CostPrice = item.CostPrice
                    };

                    await _context.PurchaseItems.AddAsync(purchaseItem);

                    // Update product stock quantity
                    var product = await _context.Products.FindAsync(item.ProductId);
                    if (product != null)
                    {
                        product.StockQuantity += item.Quantity;
                        _context.Entry(product).State = EntityState.Modified;
                    }
                }

                await _context.SaveChangesAsync();
                await transaction.CommitAsync();

                return ResponseHelper.Success(StatusCodes.Status201Created, true);
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                _logger.LogError(ex, "Error creating purchase");
                return ResponseHelper.Success(StatusCodes.Status500InternalServerError, false, ex.Message);
            }
        }

        public async Task UpdatePurchaseAsync(PurchaseDto purchaseDto)
        {
            var purchase = await _context.Purchases
                .Include(p => p.Items)
                .FirstOrDefaultAsync(p => p.Id == purchaseDto.Id);

            if (purchase == null)
                throw new KeyNotFoundException($"Purchase with ID {purchaseDto.Id} not found.");

            using var transaction = await _context.Database.BeginTransactionAsync();
            try
            {
                // Restore product stock for old items
                foreach (var oldItem in purchase.Items)
                {
                    var product = await _context.Products.FindAsync(oldItem.ProductId);
                    if (product != null)
                    {
                        product.StockQuantity -= oldItem.Quantity;
                        _context.Entry(product).State = EntityState.Modified;
                    }
                }

                // Remove old items
                _context.PurchaseItems.RemoveRange(purchase.Items);

                // Update purchase details
                purchase.SupplierId = purchaseDto.SupplierId;
                purchase.TotalAmount = purchaseDto.TotalAmount;
                purchase.LastUpdatedDate = DateTime.UtcNow;

                // Add new items
                foreach (var item in purchaseDto.Items)
                {
                    var purchaseItem = new PurchaseItem
                    {
                        PurchaseId = purchase.Id,
                        ProductId = item.ProductId,
                        UnitId = item.UnitId,
                        Quantity = item.Quantity,
                        CostPrice = item.CostPrice
                    };

                    await _context.PurchaseItems.AddAsync(purchaseItem);

                    // Update product stock
                    var product = await _context.Products.FindAsync(item.ProductId);
                    if (product != null)
                    {
                        product.StockQuantity += item.Quantity;
                        _context.Entry(product).State = EntityState.Modified;
                    }
                }

                await _context.SaveChangesAsync();
                await transaction.CommitAsync();
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                _logger.LogError(ex, "Error updating purchase");
            }
        }

        public async Task DeletePurchaseAsync(int id)
        {
            var purchase = await _context.Purchases
                .Include(p => p.Items)
                .FirstOrDefaultAsync(p => p.Id == id);

            if (purchase == null)
                throw new KeyNotFoundException($"Purchase with ID {id} not found.");

            using var transaction = await _context.Database.BeginTransactionAsync();
            try
            {
                // Restore product stock
                foreach (var item in purchase.Items)
                {
                    var product = await _context.Products.FindAsync(item.ProductId);
                    if (product != null)
                    {
                        product.StockQuantity -= item.Quantity;
                        _context.Entry(product).State = EntityState.Modified;
                    }
                }

                _context.PurchaseItems.RemoveRange(purchase.Items);
                _context.Purchases.Remove(purchase);
                await _context.SaveChangesAsync();
                await transaction.CommitAsync();
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                _logger.LogError(ex, "Error deleting purchase");
            }
        }

        public async Task<int> GetLastPOIdAsync()
        {
            return await _context.Purchases
                  .OrderByDescending(s => s.Id)
                  .Select(s => s.Id)
                  .FirstOrDefaultAsync();
        }

        public async Task<List<PurchaseOrderReportDto>> GetPurchaseOrderReport(DateTime? startDate, DateTime? endDate)
        {
            var query = from purchase in _context.Purchases
                        join supplier in _context.Suppliers on purchase.SupplierId equals supplier.Id
                        where purchase.CreationDate >= startDate && purchase.CreationDate <= endDate
                        select new PurchaseOrderReportDto
                        {
                            PurchaseId = purchase.Id,
                            PONumber = purchase.OrderNumber,
                            SupplierName = supplier.Name,
                            PurchaseDate = purchase.CreationDate,
                            TotalAmount = purchase.TotalAmount,
                            AmountPaid = purchase.AmountPaid,
                            RemainingAmount = purchase.RemainingAmount,
                            PaymentStatus = purchase.RemainingAmount > 0 ? "مستحق جزئياً" : "مدفوع بالكامل",
                            ItemCount = _context.PurchaseItems.Where(pi => pi.PurchaseId == purchase.Id).Count(),
                            CreatedBy = purchase.CreatedBy,
                            ReportStartDate = startDate ?? DateTime.MinValue,
                            ReportEndDate = endDate ?? DateTime.MaxValue
                        };

            return await query.OrderByDescending(x => x.PurchaseDate).ToListAsync();
        }
    }
}