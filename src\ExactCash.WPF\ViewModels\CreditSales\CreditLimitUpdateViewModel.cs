using ExactCash.Application.DTOs;
using ExactCash.WPF.Commands;
using ExactCash.WPF.Helpers;
using ExactCash.WPF.Models;
using ExactCash.WPF.Services;
using ExactCash.WPF.Services.Interfaces;
using ExactCash.WPF.ViewModels;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using System.Windows;
using System.Windows.Input;

namespace ExactCash.WPF.ViewModels.CreditSales
{
    public class CreditLimitUpdateViewModel : ViewModelBase, INotifyPropertyChanged
    {
        #region Fields
        private readonly ICustomerService _customerService;
        private readonly IPaymentServiceClient _paymentService;

        private string _customerSearchTerm;
        private ObservableCollection<CustomerDto> _customerSearchResults;
        private CustomerDto _selectedCustomer;
        private bool _hasSearchResults;
        private bool _hasSelectedCustomer;

        private int _customerId;
        private string _customerName;
        private string _customerPhone;
        private decimal _currentCreditLimit;
        private decimal _outstandingBalance;
        private decimal _newCreditLimit;
        private DateTime _updateDate;
        private string _reason;
        private string _updatedBy;
        #endregion

        #region Properties
        public string CustomerSearchTerm
        {
            get => _customerSearchTerm;
            set
            {
                if (SetProperty(ref _customerSearchTerm, value))
                {
                    if (!string.IsNullOrWhiteSpace(value) && value.Length >= 2)
                    {
                        SearchCustomersAsync();
                    }
                    else
                    {
                        CustomerSearchResults?.Clear();
                        HasSearchResults = false;
                    }
                }
            }
        }

        public ObservableCollection<CustomerDto> CustomerSearchResults
        {
            get => _customerSearchResults;
            set => SetProperty(ref _customerSearchResults, value);
        }

        public CustomerDto SelectedCustomer
        {
            get => _selectedCustomer;
            set
            {
                if (SetProperty(ref _selectedCustomer, value))
                {
                    if (value != null)
                    {
                        LoadCustomerDetails(value);
                        HasSearchResults = false;
                        CustomerSearchTerm = value.FullName;
                    }
                }
            }
        }

        public bool HasSearchResults
        {
            get => _hasSearchResults;
            set => SetProperty(ref _hasSearchResults, value);
        }

        public bool HasSelectedCustomer
        {
            get => _hasSelectedCustomer;
            set => SetProperty(ref _hasSelectedCustomer, value);
        }

        public int CustomerId
        {
            get => _customerId;
            set => SetProperty(ref _customerId, value);
        }

        public string CustomerName
        {
            get => _customerName;
            set => SetProperty(ref _customerName, value);
        }

        public string CustomerPhone
        {
            get => _customerPhone;
            set => SetProperty(ref _customerPhone, value);
        }

        public decimal CurrentCreditLimit
        {
            get => _currentCreditLimit;
            set
            {
                if (SetProperty(ref _currentCreditLimit, value))
                {
                    OnPropertyChanged(nameof(CreditLimitChange));
                    OnPropertyChanged(nameof(ChangeColor));
                    OnPropertyChanged(nameof(NewAvailableCredit));
                }
            }
        }

        public decimal OutstandingBalance
        {
            get => _outstandingBalance;
            set
            {
                if (SetProperty(ref _outstandingBalance, value))
                {
                    OnPropertyChanged(nameof(NewAvailableCredit));
                    OnPropertyChanged(nameof(ValidationStatus));
                    OnPropertyChanged(nameof(ValidationStatusColor));
                }
            }
        }

        public decimal NewCreditLimit
        {
            get => _newCreditLimit;
            set
            {
                if (SetProperty(ref _newCreditLimit, value))
                {
                    OnPropertyChanged(nameof(CreditLimitChange));
                    OnPropertyChanged(nameof(ChangeColor));
                    OnPropertyChanged(nameof(NewAvailableCredit));
                    OnPropertyChanged(nameof(ValidationStatus));
                    OnPropertyChanged(nameof(ValidationStatusColor));
                    OnPropertyChanged(nameof(IsValidUpdate));
                }
            }
        }

        public DateTime UpdateDate
        {
            get => _updateDate;
            set => SetProperty(ref _updateDate, value);
        }

        public string Reason
        {
            get => _reason;
            set => SetProperty(ref _reason, value);
        }

        public string UpdatedBy
        {
            get => _updatedBy;
            set => SetProperty(ref _updatedBy, value);
        }

        // Calculated Properties
        public decimal CreditLimitChange => NewCreditLimit - CurrentCreditLimit;

        public string ChangeColor
        {
            get
            {
                var change = CreditLimitChange;
                if (change > 0) return "#28A745"; // Green for increase
                if (change < 0) return "#DC3545"; // Red for decrease
                return "#6C757D"; // Gray for no change
            }
        }

        public decimal NewAvailableCredit => NewCreditLimit - OutstandingBalance;

        public string ValidationStatus
        {
            get
            {
                if (NewCreditLimit < 0) return "غير صحيح";
                if (NewCreditLimit < OutstandingBalance) return "أقل من المستحق";
                return "صحيح";
            }
        }

        public string ValidationStatusColor
        {
            get
            {
                if (NewCreditLimit < 0 || NewCreditLimit < OutstandingBalance) return "#DC3545";
                return "#28A745";
            }
        }

        public bool IsValidUpdate => NewCreditLimit >= 0 && NewCreditLimit >= OutstandingBalance;
        #endregion

        #region Commands
        public ICommand SearchCustomerCommand { get; private set; }
        public ICommand UpdateCreditLimitCommand { get; private set; }
        public ICommand CancelCommand { get; private set; }
        #endregion

        #region Constructors
        public CreditLimitUpdateViewModel()
        {
            InitializeViewModel();
        }

        public CreditLimitUpdateViewModel(CustomerCreditLimitDto creditLimit)
        {
            InitializeViewModel();
            LoadExistingCreditLimit(creditLimit);
        }
        #endregion

        #region Methods
        private void InitializeViewModel()
        {
            // Initialize services (these would be injected in a real implementation)
            // _customerService = ServiceLocator.GetService<ICustomerService>();
            // _paymentService = ServiceLocator.GetService<IPaymentServiceClient>();

            // Initialize collections
            CustomerSearchResults = new ObservableCollection<CustomerDto>();

            // Initialize default values
            UpdateDate = DateTime.Today;
            UpdatedBy = Environment.UserName; // Or get from current user session

            // Initialize commands
            SearchCustomerCommand = new Commands.RelayCommand(ExecuteSearchCustomer);
            UpdateCreditLimitCommand = new Commands.RelayCommand(ExecuteUpdateCreditLimit, CanExecuteUpdateCreditLimit);
            CancelCommand = new Commands.RelayCommand(ExecuteCancel);
        }

        private void LoadExistingCreditLimit(CustomerCreditLimitDto creditLimit)
        {
            CustomerId = creditLimit.CustomerId;
            CustomerName = creditLimit.CustomerName;
            CustomerPhone = creditLimit.Phone;
            CurrentCreditLimit = creditLimit.CreditLimit;
            OutstandingBalance = creditLimit.OutstandingBalance;
            NewCreditLimit = creditLimit.CreditLimit;
            HasSelectedCustomer = true;
            CustomerSearchTerm = creditLimit.CustomerName;
        }

        private void LoadCustomerDetails(CustomerDto customer)
        {
            CustomerId = customer.Id;
            CustomerName = customer.FullName;
            CustomerPhone = customer.Phone;
            // CurrentCreditLimit = customer.CreditLimit; // This would come from customer data
            // OutstandingBalance = customer.OutstandingBalance; // This would be calculated

            // For now, use mock data
            CurrentCreditLimit = 5000;
            OutstandingBalance = 1500;
            NewCreditLimit = CurrentCreditLimit;
            HasSelectedCustomer = true;
        }

        private async void SearchCustomersAsync()
        {
            try
            {
                if (string.IsNullOrWhiteSpace(CustomerSearchTerm) || CustomerSearchTerm.Length < 2)
                {
                    CustomerSearchResults.Clear();
                    HasSearchResults = false;
                    return;
                }

                // This would call the actual customer service
                // var customers = await _customerService.SearchCustomersAsync(CustomerSearchTerm);

                // For now, create mock data
                var mockCustomers = new List<CustomerDto>
                {
                    new CustomerDto { Id = 1, FullName = "أحمد محمد علي", Phone = "01234567890" },
                    new CustomerDto { Id = 2, FullName = "فاطمة أحمد محمود", Phone = "01098765432" },
                    new CustomerDto { Id = 3, FullName = "محمد علي حسن", Phone = "01156789012" }
                }.Where(c => c.FullName.Contains(CustomerSearchTerm) || c.Phone.Contains(CustomerSearchTerm)).ToList();

                CustomerSearchResults.Clear();
                foreach (var customer in mockCustomers)
                {
                    CustomerSearchResults.Add(customer);
                }

                HasSearchResults = CustomerSearchResults.Any();
            }
            catch (Exception ex)
            {
                Helpers.BootstrapMessageBoxHelper.ShowError($"حدث خطأ أثناء البحث عن العملاء: {ex.Message}", "خطأ");
            }
        }

        private void ExecuteSearchCustomer()
        {
            SearchCustomersAsync();
        }

        private bool CanExecuteUpdateCreditLimit()
        {
            return HasSelectedCustomer && IsValidUpdate && !string.IsNullOrWhiteSpace(Reason);
        }

        private async void ExecuteUpdateCreditLimit()
        {
            try
            {
                var updateDto = new CreditLimitUpdateDto
                {
                    CustomerId = CustomerId,
                    CustomerName = CustomerName,
                    NewCreditLimit = NewCreditLimit,
                    CurrentCreditLimit = CurrentCreditLimit,
                    OutstandingBalance = OutstandingBalance,
                    Reason = Reason,
                    UpdatedBy = UpdatedBy,
                    UpdateDate = UpdateDate
                };

                // Validate the update
                if (!updateDto.IsValidUpdate)
                {
                    Helpers.BootstrapMessageBoxHelper.ShowError(updateDto.ValidationMessage, "خطأ في التحقق");
                    return;
                }

                // Process the credit limit update
                // var result = await _paymentService.UpdateCreditLimitAsync(updateDto);

                // For now, simulate success
                await Task.Delay(500);

                Helpers.BootstrapMessageBoxHelper.ShowSuccess($"تم تحديث حد الائتمان للعميل {CustomerName} بنجاح", "نجح العملية");

                // Close the window with success result
                var window = System.Windows.Application.Current.Windows.OfType<Window>().FirstOrDefault(w => w.DataContext == this);
                if (window != null)
                {
                    window.DialogResult = true;
                    window.Close();
                }
            }
            catch (Exception ex)
            {
                Helpers.BootstrapMessageBoxHelper.ShowError($"حدث خطأ أثناء تحديث حد الائتمان: {ex.Message}", "خطأ");
            }
        }

        private void ExecuteCancel()
        {
            var window = System.Windows.Application.Current.Windows.OfType<Window>().FirstOrDefault(w => w.DataContext == this);
            if (window != null)
            {
                window.DialogResult = false;
                window.Close();
            }
        }
        #endregion

        #region INotifyPropertyChanged
        public event PropertyChangedEventHandler PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
        #endregion
    }
}
