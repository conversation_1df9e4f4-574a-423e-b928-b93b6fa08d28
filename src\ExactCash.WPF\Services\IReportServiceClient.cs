using ExactCash.Application.DTOs;

namespace ExactCash.WPF.Services
{
    public interface IReportServiceClient
    {
        /// <summary>
        /// Gets customer account statement
        /// </summary>
        Task<CustomerAccountStatementDto> GetCustomerAccountStatement(int customerId, DateTime? startDate, DateTime? endDate);

        /// <summary>
        /// Gets supplier account statement
        /// </summary>
        Task<SupplierAccountStatementDto> GetSupplierAccountStatement(int supplierId, DateTime? startDate, DateTime? endDate);

        /// <summary>
        /// Gets company information from system configurations
        /// </summary>
        Task<CompanyInfoDto> GetCompanyInfoAsync();

        /// <summary>
        /// Gets inventory summary statistics
        /// </summary>
        Task<InventorySummaryDto> GetInventorySummaryAsync();

        /// <summary>
        /// Gets detailed inventory report
        /// </summary>
        Task<List<InventoryReportDto>> GetInventoryReportAsync();

        /// <summary>
        /// Gets low stock report
        /// </summary>
        Task<List<LowStockReportDto>> GetLowStockReportAsync();
    }
}
