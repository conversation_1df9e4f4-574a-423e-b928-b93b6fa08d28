using System.Collections.Generic;
using System.Linq;
using System.Windows;
using ExactCash.Application.DTOs;
using ExactCash.WPF.Services.Interfaces;

namespace ExactCash.WPF.Views.User
{
    public partial class AddUserView : Window
    {
        private readonly IRoleServiceClient _roleService;
        public string UserName => UserNameBox.Text;
        public string Email => EmailBox.Text;
        public string Phone => PhoneBox.Text;
        public string Password => PasswordBox.Password;
        public List<string> SelectedRoles => RolesComboBox.SelectedItems.Cast<string>().ToList();

        public AddUserView(IRoleServiceClient roleService)
        {
            InitializeComponent();
            _roleService = roleService;
            LoadRoles();
        }

        private async void LoadRoles()
        {
            try
            {
                var roles = await _roleService.GetAllRolesAsync();
                RolesComboBox.ItemsSource = roles.Select(r => r.Name);
            }
            catch (System.Exception ex)
            {
                System.Windows.MessageBox.Show($"حدث خطأ أثناء تحميل الأدوار: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void AddButton_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = true;
            Close();
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }
    }
}