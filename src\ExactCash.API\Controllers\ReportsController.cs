﻿using ExactCash.Application.Interfaces;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace ExactCash.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class ReportsController : ControllerBase
    {
        private readonly IReportService _reportService;

        public ReportsController(IReportService reportService)
        {
            _reportService = reportService;
        }

        #region Inventory Reports

        [HttpGet("inventory")]
        public async Task<IActionResult> GetInventoryReport()
        {
            var result = await _reportService.GetInventoryReport();
            return Ok(result);
        }

        [HttpGet("low-stock")]
        public async Task<IActionResult> GetLowStockReport()
        {
            var result = await _reportService.GetLowStockReport();
            return Ok(result);
        }

        [HttpGet("stock-movement")]
        public async Task<IActionResult> GetStockMovementReport(
            [FromQuery] DateTime? startDate,
            [FromQuery] DateTime? endDate)
        {
            var result = await _reportService.GetStockMovementReport(startDate, endDate);
            return Ok(result);
        }

        [HttpGet("inventory-summary")]
        public async Task<IActionResult> GetInventorySummary()
        {
            var result = await _reportService.GetInventorySummary();
            return Ok(result);
        }

        #endregion

        #region Financial Reports

        [HttpGet("profit-loss")]
        public async Task<IActionResult> GetProfitLossReport(
            [FromQuery] DateTime? startDate,
            [FromQuery] DateTime? endDate)
        {
            var result = await _reportService.GetProfitLossReport(startDate, endDate);
            return Ok(result);
        }

        [HttpGet("cash-flow")]
        public async Task<IActionResult> GetCashFlowReport(
            [FromQuery] DateTime? startDate,
            [FromQuery] DateTime? endDate)
        {
            var result = await _reportService.GetCashFlowReport(startDate, endDate);
            return Ok(result);
        }

        [HttpGet("payment-methods")]
        public async Task<IActionResult> GetPaymentMethodReport(
            [FromQuery] DateTime? startDate,
            [FromQuery] DateTime? endDate)
        {
            var result = await _reportService.GetPaymentMethodReport(startDate, endDate);
            return Ok(result);
        }

        [HttpGet("tax")]
        public async Task<IActionResult> GetTaxReport(
            [FromQuery] DateTime? startDate,
            [FromQuery] DateTime? endDate)
        {
            var result = await _reportService.GetTaxReport(startDate, endDate);
            return Ok(result);
        }

        [HttpGet("expenses")]
        public async Task<IActionResult> GetExpenseReport(
            [FromQuery] DateTime? startDate,
            [FromQuery] DateTime? endDate)
        {
            var result = await _reportService.GetExpenseReport(startDate, endDate);
            return Ok(result);
        }

        [HttpGet("expense-summary")]
        public async Task<IActionResult> GetExpenseSummaryReport(
            [FromQuery] DateTime? startDate,
            [FromQuery] DateTime? endDate)
        {
            var result = await _reportService.GetExpenseSummaryReport(startDate, endDate);
            return Ok(result);
        }

        #endregion

        #region Customer Reports

        [HttpGet("customer-analysis")]
        public async Task<IActionResult> GetCustomerAnalysisReport(
            [FromQuery] DateTime? startDate,
            [FromQuery] DateTime? endDate)
        {
            var result = await _reportService.GetCustomerAnalysisReport(startDate, endDate);
            return Ok(result);
        }

        [HttpGet("top-customers")]
        public async Task<IActionResult> GetTopCustomersReport(
            [FromQuery] DateTime? startDate,
            [FromQuery] DateTime? endDate,
            [FromQuery] int topCount = 10)
        {
            var result = await _reportService.GetTopCustomersReport(startDate, endDate, topCount);
            return Ok(result);
        }

        [HttpGet("customer-purchase-history/{customerId}")]
        public async Task<IActionResult> GetCustomerPurchaseHistory(
            int customerId,
            [FromQuery] DateTime? startDate,
            [FromQuery] DateTime? endDate)
        {
            var result = await _reportService.GetCustomerPurchaseHistory(customerId, startDate, endDate);
            return Ok(result);
        }

        [HttpGet("customer-account-statement/{customerId}")]
        public async Task<IActionResult> GetCustomerAccountStatement(
            int customerId,
            [FromQuery] DateTime? startDate,
            [FromQuery] DateTime? endDate)
        {
            try
            {
                var result = await _reportService.GetCustomerAccountStatement(customerId, startDate, endDate);
                if (result == null)
                {
                    return NotFound("العميل غير موجود");
                }
                return Ok(result);
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"خطأ في الخادم: {ex.Message}");
            }
        }

        #endregion

        #region Supplier Reports

        [HttpGet("supplier-account-statement/{supplierId}")]
        public async Task<IActionResult> GetSupplierAccountStatement(
            int supplierId,
            [FromQuery] DateTime? startDate,
            [FromQuery] DateTime? endDate)
        {
            try
            {
                var result = await _reportService.GetSupplierAccountStatement(supplierId, startDate, endDate);
                if (result == null)
                {
                    return NotFound("المورد غير موجود");
                }
                return Ok(result);
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"خطأ في الخادم: {ex.Message}");
            }
        }

        #endregion

        #region Performance Reports

        [HttpGet("hourly-sales")]
        public async Task<IActionResult> GetHourlySalesReport([FromQuery] DateTime? date)
        {
            var result = await _reportService.GetHourlySalesReport(date);
            return Ok(result);
        }

        [HttpGet("cashier-performance")]
        public async Task<IActionResult> GetCashierPerformanceReport(
            [FromQuery] DateTime? startDate,
            [FromQuery] DateTime? endDate)
        {
            var result = await _reportService.GetCashierPerformanceReport(startDate, endDate);
            return Ok(result);
        }

        [HttpGet("product-performance")]
        public async Task<IActionResult> GetProductPerformanceReport(
            [FromQuery] DateTime? startDate,
            [FromQuery] DateTime? endDate)
        {
            var result = await _reportService.GetProductPerformanceReport(startDate, endDate);
            return Ok(result);
        }

        #endregion

        #region Supplier Reports

        [HttpGet("supplier-performance")]
        public async Task<IActionResult> GetSupplierPerformanceReport(
            [FromQuery] DateTime? startDate,
            [FromQuery] DateTime? endDate)
        {
            var result = await _reportService.GetSupplierPerformanceReport(startDate, endDate);
            return Ok(result);
        }

        [HttpGet("supplier-payment-status")]
        public async Task<IActionResult> GetSupplierPaymentStatusReport()
        {
            var result = await _reportService.GetSupplierPaymentStatusReport();
            return Ok(result);
        }

        #endregion

        #region Company Information

        [HttpGet("company-info")]
        public async Task<IActionResult> GetCompanyInfo()
        {
            try
            {
                var result = await _reportService.GetCompanyInfoAsync();
                return Ok(result);
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"خطأ في الخادم: {ex.Message}");
            }
        }

        #endregion

        #region Audit Reports

        [HttpGet("transaction-log")]
        public async Task<IActionResult> GetTransactionLogReport(
            [FromQuery] DateTime? startDate,
            [FromQuery] DateTime? endDate)
        {
            var result = await _reportService.GetTransactionLogReport(startDate, endDate);
            return Ok(result);
        }

        [HttpGet("system-activity")]
        public async Task<IActionResult> GetSystemActivityReport(
            [FromQuery] DateTime? startDate,
            [FromQuery] DateTime? endDate)
        {
            var result = await _reportService.GetSystemActivityReport(startDate, endDate);
            return Ok(result);
        }

        #endregion
    }
}
