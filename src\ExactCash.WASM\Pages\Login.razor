@page "/login"
@inject IAuthenticationService AuthService
@inject NavigationManager Navigation
@inject INotificationService NotificationService
@layout EmptyLayout

<PageTitle>تسجيل الدخول - ExactCash</PageTitle>

<div class="login-container">
    <div class="login-card">
        <div class="login-header">
            <img src="assets/images/bill.png" alt="ExactCash" class="login-logo" />
            <h2>تسجيل الدخول</h2>
            <p class="text-muted">مرحباً بك في نظام ExactCash</p>
        </div>

        <EditForm Model="loginRequest" OnValidSubmit="HandleLogin" class="login-form">
            <DataAnnotationsValidator />

            <div class="form-group mb-3">
                <label for="username" class="form-label">اسم المستخدم</label>
                <InputText id="username" @bind-Value="loginRequest.Username" class="form-control"
                    placeholder="أدخل اسم المستخدم" />
                <ValidationMessage For="@(() => loginRequest.Username)" />
            </div>

            <div class="form-group mb-3">
                <label for="password" class="form-label">كلمة المرور</label>
                <InputText id="password" @bind-Value="loginRequest.Password" type="password" class="form-control"
                    placeholder="أدخل كلمة المرور" />
                <ValidationMessage For="@(() => loginRequest.Password)" />
            </div>

            <div class="form-check mb-3">
                <InputCheckbox id="rememberMe" @bind-Value="loginRequest.RememberMe" class="form-check-input" />
                <label class="form-check-label" for="rememberMe">
                    تذكرني
                </label>
            </div>

            <button type="submit" class="btn btn-primary w-100" disabled="@isLoading">
                @if (isLoading)
                {
                    <span class="spinner-border spinner-border-sm me-2" role="status"></span>
                    <span>جاري تسجيل الدخول...</span>
                }
                else
                {
                    <span>تسجيل الدخول</span>
                }
            </button>
        </EditForm>

        <div class="login-footer">
            <p class="text-muted text-center mt-3">
                <small>© 2024 ExactCash. جميع الحقوق محفوظة.</small>
            </p>
        </div>
    </div>
</div>

<style>
    /* Remove scrollbars from body and html */
    html,
    body {
        overflow: hidden;
        height: 100%;
        margin: 0;
        padding: 0;
    }

    .login-container {
        min-height: 100vh;
        height: 100vh;
        overflow: hidden;
        display: flex;
        align-items: center;
        justify-content: center;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        padding: 20px;
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
    }

    .login-card {
        background: white;
        border-radius: 10px;
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
        padding: 40px;
        width: 100%;
        max-width: 400px;
    }

    .login-header {
        text-align: center;
        margin-bottom: 30px;
    }

    .login-logo {
        height: 60px;
        margin-bottom: 20px;
    }

    .login-header h2 {
        color: #333;
        margin-bottom: 10px;
    }

    .login-form .form-control {
        border-radius: 8px;
        border: 1px solid #ddd;
        padding: 12px 15px;
        font-size: 16px;
    }

    .login-form .form-control:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    }

    .login-form .btn-primary {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        border-radius: 8px;
        padding: 12px;
        font-size: 16px;
        font-weight: 600;
    }

    .login-form .btn-primary:hover {
        transform: translateY(-1px);
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
    }

    /* Fix Remember Me checkbox alignment and appearance */
    .form-check {
        display: flex;
        align-items: center;
        justify-content: flex-start;
        gap: 10px;
        direction: rtl;
        text-align: right;
        margin-bottom: 1rem;
    }

    .form-check-input {
        width: 20px;
        height: 20px;
        margin: 0;
        accent-color: #667eea;
        vertical-align: middle;
        cursor: pointer;
        border: 2px solid #ddd;
        border-radius: 4px;
        background-color: white;
        order: 2;
    }

    .form-check-label {
        font-size: 15px;
        color: #333;
        margin: 0;
        vertical-align: middle;
        cursor: pointer;
        order: 1;
        user-select: none;
        margin-right: 25px;
    }

    .form-check-input:checked {
        background-color: #667eea;
        border-color: #667eea;
    }

    .form-check-input:focus {
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        border-color: #667eea;
    }
</style>

@code {
    private LoginRequest loginRequest = new();
    private bool isLoading = false;

    protected override async Task OnInitializedAsync()
    {
        // Check if user is already authenticated
        if (await AuthService.IsAuthenticatedAsync())
        {
            Navigation.NavigateTo("/");
        }
    }

    private async Task HandleLogin()
    {
        Console.WriteLine("HandleLogin called"); // Debug log
        Console.WriteLine($"Username: {loginRequest.Username}, Password length: {loginRequest.Password?.Length}"); // Debug log

        try
        {
            isLoading = true;
            StateHasChanged();
            Console.WriteLine("Loading state set to true"); // Debug log

            var result = await AuthService.LoginAsync(loginRequest);
            Console.WriteLine($"Login result: Success={result.Success}, Message={result.Message}"); // Debug log

            if (result.Success)
            {
                Console.WriteLine("Login successful, navigating to home"); // Debug log
                NotificationService.ShowSuccess("تم تسجيل الدخول بنجاح");
                Navigation.NavigateTo("/");
            }
            else
            {
                Console.WriteLine($"Login failed: {result.Message}"); // Debug log
                NotificationService.ShowError(result.Message);
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Login exception: {ex}"); // Debug log
            NotificationService.ShowError("حدث خطأ أثناء تسجيل الدخول");
        }
        finally
        {
            isLoading = false;
            StateHasChanged();
            Console.WriteLine("HandleLogin completed, loading set to false"); // Debug log
        }
    }
}
