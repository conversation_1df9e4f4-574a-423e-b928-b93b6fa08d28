@page "/login"
@inject IAuthenticationService AuthService
@inject NavigationManager Navigation
@inject INotificationService NotificationService
@layout EmptyLayout

<PageTitle>تسجيل الدخول - ExactCash</PageTitle>

<div class="login-container">
    <div class="login-card">
        <div class="login-header">
            <img src="images/logo.png" alt="ExactCash" class="login-logo" />
            <h2>تسجيل الدخول</h2>
            <p class="text-muted">مرحباً بك في نظام ExactCash</p>
        </div>

        <EditForm Model="loginRequest" OnValidSubmit="HandleLogin" class="login-form">
            <DataAnnotationsValidator />
            
            <div class="form-group mb-3">
                <label for="username" class="form-label">اسم المستخدم</label>
                <InputText id="username" @bind-Value="loginRequest.Username" class="form-control" placeholder="أدخل اسم المستخدم" />
                <ValidationMessage For="@(() => loginRequest.Username)" />
            </div>

            <div class="form-group mb-3">
                <label for="password" class="form-label">كلمة المرور</label>
                <InputText id="password" @bind-Value="loginRequest.Password" type="password" class="form-control" placeholder="أدخل كلمة المرور" />
                <ValidationMessage For="@(() => loginRequest.Password)" />
            </div>

            <div class="form-check mb-3">
                <InputCheckbox id="rememberMe" @bind-Value="loginRequest.RememberMe" class="form-check-input" />
                <label class="form-check-label" for="rememberMe">
                    تذكرني
                </label>
            </div>

            <button type="submit" class="btn btn-primary w-100" disabled="@isLoading">
                @if (isLoading)
                {
                    <span class="spinner-border spinner-border-sm me-2" role="status"></span>
                    <span>جاري تسجيل الدخول...</span>
                }
                else
                {
                    <span>تسجيل الدخول</span>
                }
            </button>
        </EditForm>

        <div class="login-footer">
            <p class="text-muted text-center mt-3">
                <small>© 2024 ExactCash. جميع الحقوق محفوظة.</small>
            </p>
        </div>
    </div>
</div>

<style>
    .login-container {
        min-height: 100vh;
        display: flex;
        align-items: center;
        justify-content: center;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        padding: 20px;
    }

    .login-card {
        background: white;
        border-radius: 10px;
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
        padding: 40px;
        width: 100%;
        max-width: 400px;
    }

    .login-header {
        text-align: center;
        margin-bottom: 30px;
    }

    .login-logo {
        height: 60px;
        margin-bottom: 20px;
    }

    .login-header h2 {
        color: #333;
        margin-bottom: 10px;
    }

    .login-form .form-control {
        border-radius: 8px;
        border: 1px solid #ddd;
        padding: 12px 15px;
        font-size: 16px;
    }

    .login-form .form-control:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    }

    .login-form .btn-primary {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        border-radius: 8px;
        padding: 12px;
        font-size: 16px;
        font-weight: 600;
    }

    .login-form .btn-primary:hover {
        transform: translateY(-1px);
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
    }

    .form-check-input:checked {
        background-color: #667eea;
        border-color: #667eea;
    }
</style>

@code {
    private LoginRequest loginRequest = new();
    private bool isLoading = false;

    protected override async Task OnInitializedAsync()
    {
        // Check if user is already authenticated
        if (await AuthService.IsAuthenticatedAsync())
        {
            Navigation.NavigateTo("/");
        }
    }

    private async Task HandleLogin()
    {
        try
        {
            isLoading = true;
            StateHasChanged();

            var result = await AuthService.LoginAsync(loginRequest);

            if (result.Success)
            {
                NotificationService.ShowSuccess("تم تسجيل الدخول بنجاح");
                Navigation.NavigateTo("/");
            }
            else
            {
                NotificationService.ShowError(result.Message);
            }
        }
        catch (Exception ex)
        {
            NotificationService.ShowError("حدث خطأ أثناء تسجيل الدخول");
            Console.WriteLine($"Login error: {ex.Message}");
        }
        finally
        {
            isLoading = false;
            StateHasChanged();
        }
    }
}
