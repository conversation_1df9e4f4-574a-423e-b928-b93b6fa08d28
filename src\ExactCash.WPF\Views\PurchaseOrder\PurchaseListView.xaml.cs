using AutoMapper;
using ExactCash.WPF.Services;
using ExactCash.WPF.Services.Product;
using ExactCash.WPF.ViewModels.Common;
using System.Windows;

namespace ExactCash.WPF.Views.PurchaseOrder
{
    public partial class PurchaseListView : Window
    {
        public PurchaseListView(IPurchaseServiceClient purchaseOrderService,
            IProductService productService,
            ISupplierServiceClient supplierService,
            IUnitServiceClient unitService,
            IMapper mapper,
            NotificationViewModel notificationViewModel)
        {
            DataContext = new ViewModels.PurchaseOrder.PurchaseListViewModel(purchaseOrderService,
                           productService,
                           supplierService,
                           mapper, notificationViewModel, unitService,null);

            InitializeComponent();
        }

        private void MinimizeButton_Click(object sender, RoutedEventArgs e)
        {
            WindowState = WindowState.Minimized;
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            Close();
        }
    }
}