﻿using ExactCash.Application.DTOs.Common;
using ExactCash.Application.DTOs;
using ExactCash.Application.Responses;
using Microsoft.AspNetCore.Identity;

namespace ExactCash.WPF.Services
{
    public interface IUserServiceClient
    {
        /// <summary>
        /// Gets a user by their ID.
        /// </summary>
        Task<UserDto> GetUserByIdAsync(string id);

        /// <summary>
        /// Gets all users with optional filtering.
        /// </summary>
        Task<PagedResponse<UserDto>> GetAllUsersAsync(string email, string username, string phoneNumber, PaginationFilter pagination);

        /// <summary>
        /// Creates a new user.
        /// </summary>
        Task<IdentityResult> CreateUserAsync(CreateUserDto userDto);

        /// <summary>
        /// Updates an existing user.
        /// </summary>
        Task<IdentityResult> UpdateUserAsync(string id, UpdateUserDto userDto);

        /// <summary>
        /// Deletes a user.
        /// </summary>
        Task<IdentityResult> DeleteUserAsync(string id);

        /// <summary>
        /// Changes a user's password.
        /// </summary>
        Task<IdentityResult> ChangePasswordAsync(string id, ChangePasswordDto passwordDto);

        /// <summary>
        /// Resets a user's password.
        /// </summary>
        Task<IdentityResult> ResetPasswordAsync(ResetPasswordDto resetPasswordDto);

        /// <summary>
        /// Generates a password reset token for a user.
        /// </summary>
        Task<string> GeneratePasswordResetTokenAsync(string email);

        /// <summary>
        /// Locks a user account.
        /// </summary>
        Task<IdentityResult> LockUserAsync(string id);

        /// <summary>
        /// Unlocks a user account.
        /// </summary>
        Task<IdentityResult> UnlockUserAsync(string id);

        /// <summary>
        /// Confirms a user's email.
        /// </summary>
        Task<IdentityResult> ConfirmEmailAsync(string id, string token);

        /// <summary>
        /// Generates an email confirmation token for a user.
        /// </summary>
        Task<string> GenerateEmailConfirmationTokenAsync(string id);
    }
}
