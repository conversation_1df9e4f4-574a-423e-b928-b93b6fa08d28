﻿using ExactCash.WASM.Application.DTOs;

namespace ExactCash.WASM.Services.Interfaces
{
    public interface IAuthServiceClient
    {
        Task<AuthResponseDto> LoginAsync(LoginDto loginDto);
        Task<AuthResponseDto> RegisterAsync(RegisterDto registerDto);
        Task<AuthResponseDto> RefreshTokenAsync(RefreshTokenDto refreshTokenDto);
        Task LogoutAsync(string userId);
        Task<bool> ValidateTokenAsync(string token);
    }
}
