﻿using ExactCash.Domain.Common;
#nullable disable

namespace ExactCash.Domain.Entities
{
    /// <summary>
    /// Represents a supplier or vendor who provides products to the business.
    /// </summary>
    public class Supplier : BaseEntity
    {
        /// <summary>
        /// The name of the supplier company or individual.
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// The phone number of the supplier for contact.
        /// </summary>
        public string Phone { get; set; }

        /// <summary>
        /// The email address of the supplier for communication.
        /// </summary>
        public string Email { get; set; }

        /// <summary>
        /// The physical address of the supplier.
        /// </summary>
        public string Address { get; set; }

        public string Representative { get; set; }

        public string TaxId { get; set; }

        public string BankAccountNumber { get; set; }

        public string BankName { get; set; }

        public string Notes { get; set; }

        public bool IsActive { get; set; }

        public bool IsDeleted { get; set; } = false;

        /// <summary>
        /// The ID of the supplier category this supplier belongs to.
        /// </summary>
        public int? CategoryId { get; set; }

        /// <summary>
        /// Navigation property for the supplier category.
        /// </summary>
        public SupplierCategory Category { get; set; }
        /// <summary>
        /// Navigation property for all purchases made from this supplier.
        /// </summary>
        public ICollection<Purchase> Purchases { get; set; } = new List<Purchase>();
    }

}
