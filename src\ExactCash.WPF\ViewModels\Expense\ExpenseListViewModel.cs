using ExactCash.Application.DTOs;
using ExactCash.WPF.Commands;
using ExactCash.WPF.Services.Interfaces;
using ExactCash.WPF.Views.Expense;
using System.Collections.ObjectModel;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Input;
using System.Windows.Media;
using System.Linq;
using System.Collections.Generic;
using Microsoft.Win32;
using System.IO;
using System.Text;

namespace ExactCash.WPF.ViewModels.Expense
{
    public class ExpenseListViewModel : ViewModelBase
    {
        private readonly IExpenseServiceClient _service;
        private readonly IExpenseCategoryServiceClient _categoryService;

        public ObservableCollection<ExpenseDto> Expenses { get; set; } = new ObservableCollection<ExpenseDto>();
        public ObservableCollection<ExpenseCategoryDto> Categories { get; set; } = new ObservableCollection<ExpenseCategoryDto>();
        private ExpenseCategoryDto _selectedCategory;
        public ExpenseCategoryDto SelectedCategory
        {
            get => _selectedCategory;
            set { _selectedCategory = value; OnPropertyChanged(); }
        }
        private System.DateTime? _dateFrom;
        public System.DateTime? DateFrom
        {
            get => _dateFrom;
            set { _dateFrom = value; OnPropertyChanged(); }
        }
        private System.DateTime? _dateTo;
        public System.DateTime? DateTo
        {
            get => _dateTo;
            set { _dateTo = value; OnPropertyChanged(); }
        }
        public ICommand SearchCommand { get; }
        public ICommand ClearSearchCommand { get; }
        private List<ExpenseDto> _allExpenses = new List<ExpenseDto>();
        private decimal _totalAmount;
        public decimal TotalAmount { get => _totalAmount; set { _totalAmount = value; OnPropertyChanged(); } }
        private int _expenseCount;
        public int ExpenseCount { get => _expenseCount; set { _expenseCount = value; OnPropertyChanged(); } }
        private decimal _averageAmount;
        public decimal AverageAmount { get => _averageAmount; set { _averageAmount = value; OnPropertyChanged(); } }
        private decimal _filteredTotalAmount;
        public decimal FilteredTotalAmount
        {
            get => _filteredTotalAmount;
            set { _filteredTotalAmount = value; OnPropertyChanged(); }
        }
        public ICommand ExportToExcelCommand { get; }

        public ExpenseListViewModel(IExpenseServiceClient service, IExpenseCategoryServiceClient categoryService)
        {
            _service = service;
            _categoryService = categoryService;
            AddExpenseCommand = new RelayCommand(OpenAddExpenseDialog);
            LoadExpensesCommand = new RelayCommand(async () => await LoadExpensesAsync());
            EditCommand = new RelayCommand<ExpenseDto>(EditExpense);
            DeleteCommand = new RelayCommand<ExpenseDto>(DeleteExpense);
            SearchCommand = new RelayCommand(ApplySearch);
            ClearSearchCommand = new RelayCommand(ClearSearch);
            ExportToExcelCommand = new RelayCommand(ExportToExcel);
            // Load categories and expenses on construction
            LoadCategoriesAsync();
            LoadExpensesCommand.Execute(null);
        }

        private Window FindParentWindow()
        {
            foreach (Window window in System.Windows.Application.Current.Windows)
            {
                if (window.Content != null &&
                    VisualTreeHelper.GetChild(window.Content as DependencyObject, 0) is FrameworkElement content &&
                    window.Name == "ExpenseMainViewScreen")
                {
                    return window;
                }
            }
            return System.Windows.Application.Current.MainWindow;
        }

        public ICommand AddExpenseCommand { get; }
        public ICommand LoadExpensesCommand { get; }
        public ICommand EditCommand { get; }
        public ICommand DeleteCommand { get; }

        private void OpenAddExpenseDialog()
        {
            var addExpenseView = new AddExpenseView(_service, _categoryService);
            addExpenseView.Owner = FindParentWindow();
            if (addExpenseView.ShowDialog() == true)
            {
                LoadExpensesCommand.Execute(null);
            }
        }

        private async void EditExpense(ExpenseDto expense)
        {
            try
            {
                var addExpenseView = new AddExpenseView(_service, _categoryService, expense);
                addExpenseView.Owner = FindParentWindow();
                if (addExpenseView.ShowDialog() == true)
                {
                    await LoadExpensesAsync();
                    System.Windows.MessageBox.Show("تم تحديث المصروف بنجاح!", "نجاح", MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                System.Windows.MessageBox.Show($"خطأ في تحديث المصروف: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void DeleteExpense(ExpenseDto expense)
        {
            var result = System.Windows.MessageBox.Show(
                $"هل أنت متأكد من حذف المصروف '{expense.Description}'؟",
                "تأكيد الحذف",
                MessageBoxButton.YesNo,
                MessageBoxImage.Warning);

            if (result == MessageBoxResult.Yes)
            {
                try
                {
                    await _service.DeleteAsync(expense.Id);
                    ApplySearch();
                    System.Windows.MessageBox.Show("تم حذف المصروف بنجاح!", "نجاح", MessageBoxButton.OK, MessageBoxImage.Information);
                }
                catch (Exception ex)
                {
                    System.Windows.MessageBox.Show($"خطأ في حذف المصروف: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        private async void LoadCategoriesAsync()
        {
            Categories.Clear();
            var cats = await _categoryService.GetAllAsync();
            foreach (var cat in cats)
                Categories.Add(cat);
        }

        public async Task LoadExpensesAsync()
        {
            try
            {
                Expenses.Clear();
                _allExpenses.Clear();
                var expenses = await _service.GetAllAsync(new ExpenseSearchDto());
                foreach (var expense in expenses)
                {
                    Expenses.Add(expense);
                    _allExpenses.Add(expense);
                }
                UpdateStatistics();
                FilteredTotalAmount = Expenses.Sum(e => e.Amount);
            }
            catch (Exception ex)
            {
                System.Windows.MessageBox.Show($"خطأ في تحميل المصروفات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void ApplySearch()
        {
            var search = new ExpenseSearchDto
            {
                CategoryId = SelectedCategory?.Id,
                ExpenseDateFrom = DateFrom?.ToUniversalTime(),
                ExpenseDateTo = DateTo?.ToUniversalTime()
            };
            Expenses.Clear();
            var filtered = await _service.GetAllAsync(search);
            foreach (var expense in filtered)
                Expenses.Add(expense);
            UpdateStatistics();
            FilteredTotalAmount = Expenses.Sum(e => e.Amount);
        }

        private async void ClearSearch()
        {
            SelectedCategory = null;
            DateFrom = null;
            DateTo = null;
            Expenses.Clear();
            var all = await _service.GetAllAsync(new ExpenseSearchDto());
            foreach (var expense in all)
                Expenses.Add(expense);
            UpdateStatistics();
            FilteredTotalAmount = Expenses.Sum(e => e.Amount);
        }

        private void UpdateStatistics()
        {
            TotalAmount = Expenses.Sum(e => e.Amount);
            ExpenseCount = Expenses.Count;
            AverageAmount = Expenses.Count > 0 ? Expenses.Average(e => e.Amount) : 0;
        }

        private void ExportToExcel()
        {
            var saveFileDialog = new Microsoft.Win32.SaveFileDialog
            {
                Filter = "Excel Files (*.xlsx)|*.xlsx|CSV Files (*.csv)|*.csv",
                FileName = $"المصروفات_{DateTime.Now:yyyyMMdd_HHmmss}.csv"
            };

            if (saveFileDialog.ShowDialog() == true)
            {
                try
                {
                    using (var writer = new StreamWriter(saveFileDialog.FileName, false, Encoding.UTF8))
                    {
                        // Write header (adjust columns as per your DataGrid)
                        writer.WriteLine("التاريخ,المبلغ,الوصف,التصنيف,طريقة الدفع,المرجع");
                        foreach (var expense in Expenses)
                        {
                            writer.WriteLine($"{expense.ExpenseDate:yyyy-MM-dd},{expense.Amount},{expense.Description},{expense.CategoryName},{expense.Description},{expense.ReferenceNumber}");
                        }
                    }
                    Helpers.BootstrapMessageBoxHelper.Show(owner: FindParentWindow(), message: "تم تصدير البيانات بنجاح!");
                }
                catch (Exception ex)
                {
                    Helpers.BootstrapMessageBoxHelper.Show(owner: FindParentWindow(), message: $"حدث خطأ أثناء التصدير: {ex.Message}");
                }
            }
        }
    }
}