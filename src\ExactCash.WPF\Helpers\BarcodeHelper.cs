﻿using System.Drawing;
using System.Drawing.Printing;
using System.IO;
using System.Threading.Tasks;
using ExactCash.Application.DTOs;

namespace ExactCash.WPF.Helpers
{
    public static class BarcodeHelper
    {
        // Convert cm to hundredths of an inch (standard printer units)
        private const float CM_TO_INCH = 0.393701f;
        private const int DPI = 203; // XP-80C printer DPI

        public static async Task PrintBarcode(ProductDto product, byte[] barcodeImageBytes)
        {
            if (product == null || barcodeImageBytes == null || barcodeImageBytes.Length == 0)
                return;

            var tempImagePath = Path.Combine(Path.GetTempPath(), $"barcode_{product.Id}.png");
            try
            {
                // Save barcode image to temp file
                await File.WriteAllBytesAsync(tempImagePath, barcodeImageBytes);

                // Set up printer
                var printDocument = new PrintDocument();
                printDocument.PrinterSettings.PrinterName = "XP-80C";

                if (!printDocument.PrinterSettings.IsValid)
                {
                    BootstrapMessageBoxHelper.ShowError("الطابعة XP-80C غير متوفرة. يرجى التحقق من اتصال الطابعة.", "خطأ");
                    return;
                }

                // Calculate dimensions in printer units
                int barcodeWidthInPixels = (int)(2.5 * CM_TO_INCH * DPI);  // 2.5 cm
                int barcodeHeightInPixels = (int)(3.8 * CM_TO_INCH * DPI); // 3.8 cm

                // Configure page settings for XP-80C
                // Add some margin to accommodate the text below barcode
                var paperWidth = (int)(3.0 * CM_TO_INCH * DPI);  // 3.0 cm width to give some margin
                var paperHeight = (int)(5.0 * CM_TO_INCH * DPI); // 5.0 cm height to accommodate text
                printDocument.DefaultPageSettings.PaperSize = new PaperSize("Custom", paperWidth, paperHeight);

                // Small margins to center the content
                int margin = (int)(0.25 * CM_TO_INCH * DPI); // 0.25 cm margins
                printDocument.DefaultPageSettings.Margins = new Margins(margin, margin, margin, margin);

                // Handle printing
                printDocument.PrintPage += (sender, e) =>
                {
                    using (var image = Image.FromFile(tempImagePath))
                    {
                        // Center position calculation
                        var x = (e.PageBounds.Width - barcodeWidthInPixels) / 2;
                        var y = margin; // Start from top margin

                        // Draw barcode image at exact size
                        e.Graphics.DrawImage(image, x, y, barcodeWidthInPixels, barcodeHeightInPixels);

                        // Configure font sizes based on DPI
                        float nameFontSize = 8;  // Smaller font for name
                        float priceFontSize = 7; // Even smaller for price and barcode

                        // Add product information below barcode
                        using (var nameFont = new Font("Arial", nameFontSize, FontStyle.Bold))
                        using (var infoFont = new Font("Arial", priceFontSize))
                        {
                            var brush = Brushes.Black;
                            float textY = y + barcodeHeightInPixels + 5; // Small gap after barcode

                            // Product name - centered
                            var nameSize = e.Graphics.MeasureString(product.Name, nameFont);
                            var nameX = (e.PageBounds.Width - nameSize.Width) / 2;
                            e.Graphics.DrawString(product.Name, nameFont, brush, nameX, textY);

                            // Price - centered
                            textY += nameSize.Height + 2;
                            var priceText = $"السعر: {product.SellingPrice:N2} ريال";
                            var priceSize = e.Graphics.MeasureString(priceText, infoFont);
                            var priceX = (e.PageBounds.Width - priceSize.Width) / 2;
                            e.Graphics.DrawString(priceText, infoFont, brush, priceX, textY);

                            // Barcode number - centered
                            textY += priceSize.Height + 2;
                            var barcodeSize = e.Graphics.MeasureString(product.Barcode, infoFont);
                            var barcodeX = (e.PageBounds.Width - barcodeSize.Width) / 2;
                            e.Graphics.DrawString(product.Barcode, infoFont, brush, barcodeX, textY);
                        }
                    }
                };

                // Print
                printDocument.Print();
                BootstrapMessageBoxHelper.ShowSuccess("تمت طباعة الباركود بنجاح", "نجاح");
            }
            catch (Exception ex)
            {
                BootstrapMessageBoxHelper.ShowError($"خطأ في الطباعة: {ex.Message}", "خطأ");
            }
            finally
            {
                // Cleanup
                if (File.Exists(tempImagePath))
                {
                    try { File.Delete(tempImagePath); }
                    catch { /* Ignore cleanup errors */ }
                }
            }
        }
    }
}