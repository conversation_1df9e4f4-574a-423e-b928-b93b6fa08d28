﻿#nullable disable

namespace ExactCash.Domain.Entities
{
    /// <summary>
    /// Optional if you want to track how the customer paid
    /// </summary>
    public class PaymentMethod
    {
        /// <summary>
        /// Primary key of the payment method.
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// Name of the payment method (e.g., Cash, Card, Mobile Wallet).
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// Description or notes.
        /// </summary>
        public string Description { get; set; }

        /// <summary>
        /// Navigation to invoices using this payment method.
        /// </summary>
        public ICollection<Sale> SalesInvoices { get; set; }
    }

}
