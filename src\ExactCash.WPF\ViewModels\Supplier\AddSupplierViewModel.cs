using System;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using AutoMapper;
using ExactCash.Application.DTOs;
using ExactCash.Application.Interfaces;
using ExactCash.WPF.Commands;
using ExactCash.WPF.Services;

namespace ExactCash.WPF.ViewModels.Supplier
{
    public class AddSupplierViewModel : ViewModelBase
    {
        private readonly ISupplierServiceClient _supplierService;
        private readonly ISupplierCategoryServiceClient _supplierCategoryService;
        private readonly IMapper _mapper;
        private bool _isEditMode;
        private int? _supplierId;
        private string _windowTitle;
        private string _saveButtonText;

        private string _name;
        private string _phone;
        private string _email;
        private string _contactPerson;
        private string _address;
        private string _taxId;
        private string _bankAccountNumber;
        private string _bankName;
        private string _notes;
        private SupplierCategoryDto _selectedCategory;
        private ObservableCollection<SupplierCategoryDto> _categories;

        public event EventHandler SupplierAdded;
        public event EventHandler SupplierUpdated;
        public event EventHandler<bool> RequestClose;

        public AddSupplierViewModel(ISupplierServiceClient supplierService, ISupplierCategoryServiceClient supplierCategoryService, IMapper mapper)
        {
            _supplierService = supplierService;
            _supplierCategoryService = supplierCategoryService;
            _mapper = mapper;
            _isEditMode = false;
            WindowTitle = "إضافة مورد جديد";
            SaveButtonText = "إضافة";
            SaveCommand = new RelayCommand(async () => await ExecuteSave());

            Categories = new ObservableCollection<SupplierCategoryDto>();
            LoadCategoriesAsync();
        }

        public string WindowTitle
        {
            get => _windowTitle;
            set
            {
                _windowTitle = value;
                OnPropertyChanged();
            }
        }

        public string SaveButtonText
        {
            get => _saveButtonText;
            set
            {
                _saveButtonText = value;
                OnPropertyChanged();
            }
        }

        public string Name
        {
            get => _name;
            set
            {
                _name = value;
                OnPropertyChanged();
            }
        }

        public string Phone
        {
            get => _phone;
            set
            {
                _phone = value;
                OnPropertyChanged();
            }
        }

        public string Email
        {
            get => _email;
            set
            {
                _email = value;
                OnPropertyChanged();
            }
        }

        public string ContactPerson
        {
            get => _contactPerson;
            set
            {
                _contactPerson = value;
                OnPropertyChanged();
            }
        }

        public string Address
        {
            get => _address;
            set
            {
                _address = value;
                OnPropertyChanged();
            }
        }

        public string TaxId
        {
            get => _taxId;
            set
            {
                _taxId = value;
                OnPropertyChanged();
            }
        }

        public string BankAccountNumber
        {
            get => _bankAccountNumber;
            set
            {
                _bankAccountNumber = value;
                OnPropertyChanged();
            }
        }

        public string BankName
        {
            get => _bankName;
            set
            {
                _bankName = value;
                OnPropertyChanged();
            }
        }

        public string Notes
        {
            get => _notes;
            set
            {
                _notes = value;
                OnPropertyChanged();
            }
        }

        public ObservableCollection<SupplierCategoryDto> Categories
        {
            get => _categories;
            set
            {
                _categories = value;
                OnPropertyChanged();
            }
        }

        public SupplierCategoryDto SelectedCategory
        {
            get => _selectedCategory;
            set
            {
                _selectedCategory = value;
                OnPropertyChanged();
            }
        }

        public RelayCommand SaveCommand { get; }

        public void SetSupplier(SupplierDto supplier)
        {
            _isEditMode = true;
            _supplierId = supplier.Id;

            WindowTitle = "تعديل مورد";
            SaveButtonText = "حفظ التعديلات";

            Name = supplier.Name;
            Phone = supplier.Phone;
            Email = supplier.Email;
            ContactPerson = supplier.ContactPerson;
            Address = supplier.Address;
            TaxId = supplier.TaxId;
            BankAccountNumber = supplier.BankAccountNumber;
            BankName = supplier.BankName;
            Notes = supplier.Notes;

            // Set the selected category if it exists
            if (supplier.CategoryId.HasValue && Categories != null)
            {
                SelectedCategory = Categories.FirstOrDefault(c => c.Id == supplier.CategoryId.Value);
            }
        }

        private async Task ExecuteSave()
        {
            if (string.IsNullOrWhiteSpace(Name))
            {
                Helpers.BootstrapMessageBoxHelper.Show("الرجاء إدخال اسم المورد", "تنبيه");
                return;
            }

            if (string.IsNullOrWhiteSpace(Phone))
            {
                Helpers.BootstrapMessageBoxHelper.Show("الرجاء إدخال رقم الهاتف", "تنبيه");
                return;
            }

            try
            {
                var supplier = new SupplierDto
                {
                    Id = _supplierId ?? 0,
                    Name = Name,
                    Phone = Phone,
                    Email = Email,
                    ContactPerson = ContactPerson,
                    Address = Address,
                    TaxId = TaxId,
                    BankAccountNumber = BankAccountNumber,
                    BankName = BankName,
                    Notes = Notes,
                    CategoryId = SelectedCategory?.Id == 0 ? null : SelectedCategory?.Id,
                    IsActive = true
                };

                var result = _isEditMode
                    ? await _supplierService.UpdateSupplierAsync(supplier)
                    : await _supplierService.CreateSupplierAsync(supplier);

                if (result.Success)
                {
                    if (_isEditMode)
                    {
                        SupplierUpdated?.Invoke(this, EventArgs.Empty);
                    }
                    else
                    {
                        SupplierAdded?.Invoke(this, EventArgs.Empty);
                    }

                    RequestClose?.Invoke(this, true);
                }
                else
                {
                    Helpers.BootstrapMessageBoxHelper.Show(result.Message, "خطأ");
                }
            }
            catch (Exception ex)
            {
                Helpers.BootstrapMessageBoxHelper.Show($"حدث خطأ أثناء حفظ البيانات: {ex.Message}", "خطأ");
            }
        }

        private async void LoadCategoriesAsync()
        {
            try
            {
                var categories = await _supplierCategoryService.GetActiveAsync();

                // Add a "No Category" option at the beginning
                Categories.Clear();
                Categories.Add(new SupplierCategoryDto { Id = 0, Name = "-- بدون تصنيف --" });

                foreach (var category in categories)
                {
                    Categories.Add(category);
                }
            }
            catch (Exception ex)
            {
                // If categories fail to load, just show the "No Category" option
                Categories.Clear();
                Categories.Add(new SupplierCategoryDto { Id = 0, Name = "-- بدون تصنيف --" });
            }
        }
    }
}