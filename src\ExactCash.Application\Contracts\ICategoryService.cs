﻿using ExactCash.Application.DTOs;
using ExactCash.Domain.Entities;

namespace ExactCash.Application.Contracts
{
    public interface ICategoryService
    {
        Task<Category> CreateAsync(Category category);
        Task<Category> GetByIdAsync(int id);
        Task<IEnumerable<CategoryDto>> GetAllAsync();
        Task<Category> UpdateAsync(Category category);
        Task<bool> DeleteAsync(int id);
    }
}