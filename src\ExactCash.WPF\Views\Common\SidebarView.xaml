<UserControl x:Class="ExactCash.WPF.Views.Common.SidebarView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:local="clr-namespace:ExactCash.WPF.Views.Common"
             mc:Ignorable="d"
             d:DesignHeight="600"
        d:DesignWidth="200">
    <Grid Background="#F5F5F5">
        <StackPanel Margin="10">
            <!-- Sales Section -->
            <TextBlock Text="المبيعات"
                       FontFamily="Droid Arabic Kufi"
                       FontSize="16"
                       FontWeight="Bold"
                       Margin="0,10,0,5"
                       Foreground="#0078D4"/>

            <Button Content="نقطة البيع"
                    Command="{Binding NavigateToPosCommand}"
                    Style="{StaticResource SidebarButtonStyle}"
                    Margin="0,5"/>

            <Button Content="المبيعات اليومية"
                    Command="{Binding NavigateToDailySalesCommand}"
                    Style="{StaticResource SidebarButtonStyle}"
                    Margin="0,5"/>

            <Button Content="تقارير المبيعات"
                    Command="{Binding NavigateToSalesReportsCommand}"
                    Style="{StaticResource SidebarButtonStyle}"
                    Margin="0,5"/>

            <!-- Inventory Section -->
            <TextBlock Text="المخزون"
                       FontFamily="Droid Arabic Kufi"
                       FontSize="16"
                       FontWeight="Bold"
                       Margin="0,20,0,5"
                       Foreground="#0078D4"/>

            <Button Content="إدارة المخزون"
                    Command="{Binding NavigateToInventoryCommand}"
                    Style="{StaticResource SidebarButtonStyle}"
                    Margin="0,5"/>

            <Button Content="المنتجات"
                    Command="{Binding NavigateToProductsCommand}"
                    Style="{StaticResource SidebarButtonStyle}"
                    Margin="0,5"/>

            <!-- Settings Section -->
            <TextBlock Text="الإعدادات"
                       FontFamily="Droid Arabic Kufi"
                       FontSize="16"
                       FontWeight="Bold"
                       Margin="0,20,0,5"
                       Foreground="#0078D4"/>

            <Button Content="إعدادات النظام"
                    Command="{Binding NavigateToSettingsCommand}"
                    Style="{StaticResource SidebarButtonStyle}"
                    Margin="0,5"/>

            <Button Content="المستخدمين"
                    Command="{Binding NavigateToUsersCommand}"
                    Style="{StaticResource SidebarButtonStyle}"
                    Margin="0,5"/>
        </StackPanel>
    </Grid>
</UserControl> 