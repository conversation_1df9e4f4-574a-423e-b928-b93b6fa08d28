﻿using FDIN.Web.Data;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using FDIN.Web.ViewModels;

namespace FDIN.Web.Controllers
{
    public class UserInvoicesController : Controller
    {
        private readonly FDINDbContext _dbContext;
        private readonly ILogger<UserInvoicesController> _logger;

        public UserInvoicesController(FDINDbContext dbContext, ILogger<UserInvoicesController> logger)
        {
            _dbContext = dbContext;
            _logger = logger;
        }
        
        public async Task<IActionResult> Index(int userId)
        {
            // In your controller action:
            var groupedSales = await _dbContext.Sales
                .Include(s => s.SaleItems)
                .Include(s => s.User)
                .Where(s => s.UserId == userId)
                .OrderByDescending(s => s.SaleDate)
                .GroupBy(s => s.StoreName)
                .Select(g => new StoreSalesGroup
                {
                    StoreName = g.Key,
                    Sales = g.Select(s => new SaleWithCount
                    {
                        Sale = s,
                        SaleItemCount = s.SaleItems.Count
                    }).ToList()
                })
                .ToListAsync();

            return View(groupedSales);
        }

        public async Task<IActionResult> Invoices(string storeName, int userId)
        {
            // In your controller action:
            var sales = await _dbContext.Sales
                .Include(s => s.SaleItems)
                .Include(s => s.User)
                .Where(s => s.UserId == userId && s.StoreName == storeName)
                .OrderByDescending(s => s.SaleDate)
                .ToListAsync();
            ViewBag.StoreName = storeName;
            ViewBag.UserName = sales?.FirstOrDefault()?.User?.FullName;
            return View(sales);
        }

        // GET: UserInvoices/Details/5
        public async Task<IActionResult> Details(int id)
        {
            var Invoice = await _dbContext.Sales
               .Include(s => s.SaleItems)
               .Include(s => s.User)
               .SingleOrDefaultAsync(s => s.Id == id);
            return View(Invoice);
        }

        // GET: UserInvoices/Download/5
        public async Task<IActionResult> Download(int id)
        {
            var invoice = await _dbContext.Sales
                .Include(s => s.SaleItems)
                .Include(s => s.User)
                .FirstOrDefaultAsync(s => s.Id == id);

            if (invoice == null)
            {
                return NotFound();
            }

            // TODO: Generate PDF and return as file
            return File(new byte[0], "application/pdf", $"Invoice_{invoice.InvoiceNumber}.pdf");
        }

        // GET: UserInvoices/Print/5
        public async Task<IActionResult> Print(int id)
        {
            var invoice = await _dbContext.Sales
                .Include(s => s.SaleItems)
                .Include(s => s.User)
                .FirstOrDefaultAsync(s => s.Id == id);

            if (invoice == null)
            {
                return NotFound();
            }

            return View("Details", invoice);
        }
    }
}
