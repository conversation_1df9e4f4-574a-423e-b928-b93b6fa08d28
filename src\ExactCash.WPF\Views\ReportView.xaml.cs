using System;
using System.Windows;
using Microsoft.Reporting.WinForms;
using System.Data;
using System.Windows.Forms.Integration;
using System.Windows.Media;
using ExactCash.WPF.Services;
using System.Threading.Tasks;
using ExactCash.Application.DTOs;
using System.Linq;
using System.Collections.Generic;

namespace ExactCash.WPF.Views
{
    public partial class ReportView : Window
    {
        private readonly ISaleServiceClient _saleServiceClient;
        private readonly IPurchaseServiceClient _purchaseServiceClient;
        private readonly IReportServiceClient _reportServiceClient;
        private readonly ICustomerService _customerServiceClient;
        private readonly ISupplierServiceClient _supplierServiceClient;

        public ReportView(ISaleServiceClient saleServiceClient, IPurchaseServiceClient purchaseServiceClient,
                         IReportServiceClient reportServiceClient, ICustomerService customerServiceClient,
                         ISupplierServiceClient supplierServiceClient)
        {
            _saleServiceClient = saleServiceClient;
            _purchaseServiceClient = purchaseServiceClient;
            _reportServiceClient = reportServiceClient;
            _customerServiceClient = customerServiceClient;
            _supplierServiceClient = supplierServiceClient;
            InitializeComponent();
            ConfigureReportViewer();
            Loaded += ReportView_Loaded;
            GenerateReportButton.Click += GenerateReportButton_Click;
            ExportPdfButton.Click += ExportPdfButton_Click;
            ExportExcelButton.Click += ExportExcelButton_Click;
            PrintButton.Click += PrintButton_Click;
        }

        private void ConfigureReportViewer()
        {
            // Configure ReportViewer for full width utilization
            ReportViewerControl.ZoomMode = Microsoft.Reporting.WinForms.ZoomMode.PageWidth;
            ReportViewerControl.ShowToolBar = true;
            ReportViewerControl.ShowParameterPrompts = false;
            ReportViewerControl.ShowCredentialPrompts = false;
            ReportViewerControl.ShowFindControls = false;
            ReportViewerControl.ShowPrintButton = true;
            ReportViewerControl.ShowRefreshButton = true;
            ReportViewerControl.ShowZoomControl = true;
            ReportViewerControl.ShowPageNavigationControls = true;
            ReportViewerControl.ShowBackButton = false;
            ReportViewerControl.ShowStopButton = false;
        }

        private async void ReportView_Loaded(object sender, RoutedEventArgs e)
        {
            // Populate the ComboBox with report types
            ReportTypeComboBox.Items.Add("تقرير المبيعات اليومية");
            ReportTypeComboBox.Items.Add("تقرير المبيعات حسب المنتج");
            ReportTypeComboBox.Items.Add("تقرير المبيعات حسب الفئة");
            ReportTypeComboBox.Items.Add("تقرير أوامر الشراء");
            ReportTypeComboBox.Items.Add("كشف حساب عميل");
            ReportTypeComboBox.Items.Add("كشف حساب مورد");
            ReportTypeComboBox.SelectedIndex = 0;
            StartDatePicker.SelectedDate = DateTime.Today;
            EndDatePicker.SelectedDate = DateTime.Today;

            // Load customers and suppliers for account statements
            await LoadCustomersAsync();
            await LoadSuppliersAsync();
        }

        private async Task LoadCustomersAsync()
        {
            try
            {
                var customers = await _customerServiceClient.GetAllCustomersAsync();
                CustomerComboBox.ItemsSource = customers;
            }
            catch (Exception ex)
            {
                // Handle error silently for now
                System.Diagnostics.Debug.WriteLine($"Error loading customers: {ex.Message}");
            }
        }

        private async Task LoadSuppliersAsync()
        {
            try
            {
                var suppliers = await _supplierServiceClient.GetAllSuppliersAsync(null, null);
                SupplierComboBox.ItemsSource = suppliers;
            }
            catch (Exception ex)
            {
                // Handle error silently for now
                System.Diagnostics.Debug.WriteLine($"Error loading suppliers: {ex.Message}");
            }
        }

        private void ReportTypeComboBox_SelectionChanged(object sender, System.Windows.Controls.SelectionChangedEventArgs e)
        {
            string selectedReport = ReportTypeComboBox.SelectedItem as string;

            // Show/hide customer selection based on report type
            if (selectedReport == "كشف حساب عميل")
            {
                CustomerLabel.Visibility = Visibility.Visible;
                CustomerComboBox.Visibility = Visibility.Visible;
                SupplierLabel.Visibility = Visibility.Collapsed;
                SupplierComboBox.Visibility = Visibility.Collapsed;
            }
            else if (selectedReport == "كشف حساب مورد")
            {
                CustomerLabel.Visibility = Visibility.Collapsed;
                CustomerComboBox.Visibility = Visibility.Collapsed;
                SupplierLabel.Visibility = Visibility.Visible;
                SupplierComboBox.Visibility = Visibility.Visible;
            }
            else
            {
                CustomerLabel.Visibility = Visibility.Collapsed;
                CustomerComboBox.Visibility = Visibility.Collapsed;
                SupplierLabel.Visibility = Visibility.Collapsed;
                SupplierComboBox.Visibility = Visibility.Collapsed;
            }
        }

        private Window FindParentWindow()
        {
            // Find all windows
            foreach (Window window in System.Windows.Application.Current.Windows)
            {
                // Look for the window that contains a SaleView
                if (window.Content != null &&
                    VisualTreeHelper.GetChild(window.Content as DependencyObject, 0) is FrameworkElement content &&
                    window.Name == "ReportViewScreen")
                {
                    return window;
                }
            }
            return System.Windows.Application.Current.MainWindow;
        }

        private async void GenerateReportButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // Show loading indicator
                LoadingPanel.Visibility = Visibility.Visible;
                GenerateReportButton.IsEnabled = false;

                string selectedReport = ReportTypeComboBox.SelectedItem as string;
                DateTime? startDate = StartDatePicker.SelectedDate;
                DateTime? endDate = EndDatePicker.SelectedDate;
                string reportPath = string.Empty;
                DataTable data = null;
                string dataSetName = string.Empty;

                switch (selectedReport)
                {
                    case "تقرير المبيعات اليومية":
                        reportPath = "Reports/DailySalesReport.rdlc";
                        data = await GetDailySalesDataAsync(startDate, endDate);
                        dataSetName = "DailySalesDataSet";
                        break;
                    case "تقرير المبيعات حسب المنتج":
                        reportPath = "Reports/SalesByProductReport.rdlc";
                        data = await GetSalesByProductDataAsync(startDate, endDate);
                        dataSetName = "SalesByProductDataSet";
                        break;
                    case "تقرير المبيعات حسب الفئة":
                        reportPath = "Reports/SalesByCategoryReport.rdlc";
                        data = await GetSalesByCategoryDataAsync(startDate, endDate);
                        dataSetName = "SalesByCategoryDataSet";
                        break;
                    case "تقرير أوامر الشراء":
                        reportPath = "Reports/PurchaseOrderReport.rdlc";
                        data = await GetPurchaseOrderDataAsync(startDate, endDate);
                        dataSetName = "PurchaseOrderDataSet";
                        break;
                    case "كشف حساب عميل":
                        if (CustomerComboBox.SelectedValue != null)
                        {
                            int customerId = (int)CustomerComboBox.SelectedValue;
                            var customerData = await GetCustomerAccountStatementDataAsync(customerId, startDate, endDate);
                            if (customerData != null)
                            {
                                try
                                {
                                    // Set processing mode and report path first
                                    ReportViewerControl.ProcessingMode = ProcessingMode.Local;
                                    ReportViewerControl.LocalReport.ReportPath = "Reports/CustomerAccountStatementReport.rdlc";

                                    // Clear existing data sources
                                    ReportViewerControl.LocalReport.DataSources.Clear();

                                    // Get company information
                                    var companyInfo = await GetCompanyInfoAsync();

                                    // Add customer statement data
                                    var customerTable = CreateCustomerStatementTable(customerData);
                                    ReportViewerControl.LocalReport.DataSources.Add(new ReportDataSource("CustomerAccountStatementDataSet", customerTable));

                                    // Add transactions data
                                    var transactionsTable = CreateTransactionsTable(customerData.Transactions);
                                    ReportViewerControl.LocalReport.DataSources.Add(new ReportDataSource("CustomerTransactionsDataSet", transactionsTable));

                                    // Add company information data
                                    var companyTable = CreateCompanyInfoTable(companyInfo);
                                    ReportViewerControl.LocalReport.DataSources.Add(new ReportDataSource("CompanyInfoDataSet", companyTable));

                                    // Refresh the report
                                    ReportViewerControl.RefreshReport();
                                    return; // Exit early since we handled the report generation here
                                }
                                catch (Exception ex)
                                {
                                    Helpers.BootstrapMessageBoxHelper.ShowError($"خطأ في عرض التقرير: {ex.Message}", "خطأ", owner: FindParentWindow());
                                    return;
                                }
                            }
                            else
                            {
                                Helpers.BootstrapMessageBoxHelper.ShowError("لا توجد بيانات للعميل المحدد.", "تنبيه", owner: FindParentWindow());
                                return;
                            }
                        }
                        else
                        {
                            Helpers.BootstrapMessageBoxHelper.ShowError("يرجى اختيار عميل أولاً.", "تنبيه", owner: FindParentWindow());
                            return;
                        }
                        break;
                    case "كشف حساب مورد":
                        if (SupplierComboBox.SelectedValue != null)
                        {
                            int supplierId = (int)SupplierComboBox.SelectedValue;
                            var supplierData = await GetSupplierAccountStatementDataAsync(supplierId, startDate, endDate);
                            if (supplierData != null)
                            {
                                try
                                {
                                    // Set processing mode and report path first
                                    ReportViewerControl.ProcessingMode = ProcessingMode.Local;
                                    ReportViewerControl.LocalReport.ReportPath = "Reports/SupplierAccountStatementReport.rdlc";

                                    // Clear existing data sources
                                    ReportViewerControl.LocalReport.DataSources.Clear();

                                    // Get company information
                                    var companyInfo = await GetCompanyInfoAsync();

                                    // Add supplier statement data
                                    var supplierTable = CreateSupplierStatementTable(supplierData);
                                    ReportViewerControl.LocalReport.DataSources.Add(new ReportDataSource("SupplierAccountStatementDataSet", supplierTable));

                                    // Add transactions data
                                    var transactionsTable = CreateSupplierTransactionsTable(supplierData.Transactions);
                                    ReportViewerControl.LocalReport.DataSources.Add(new ReportDataSource("SupplierTransactionsDataSet", transactionsTable));

                                    // Add company information data
                                    var companyTable = CreateCompanyInfoTable(companyInfo);
                                    ReportViewerControl.LocalReport.DataSources.Add(new ReportDataSource("CompanyInfoDataSet", companyTable));

                                    // Refresh the report
                                    ReportViewerControl.RefreshReport();
                                    return; // Exit early since we handled the report generation here
                                }
                                catch (Exception ex)
                                {
                                    Helpers.BootstrapMessageBoxHelper.ShowError($"خطأ في عرض التقرير: {ex.Message}", "خطأ", owner: FindParentWindow());
                                    return;
                                }
                            }
                            else
                            {
                                Helpers.BootstrapMessageBoxHelper.ShowError("لا توجد بيانات للمورد المحدد.", "تنبيه", owner: FindParentWindow());
                                return;
                            }
                        }
                        else
                        {
                            Helpers.BootstrapMessageBoxHelper.ShowError("يرجى اختيار مورد أولاً.", "تنبيه", owner: FindParentWindow());
                            return;
                        }
                        break;
                }

                if (!string.IsNullOrEmpty(reportPath) && data != null)
                {
                    ReportViewerControl.ProcessingMode = ProcessingMode.Local;
                    ReportViewerControl.LocalReport.ReportPath = reportPath;
                    ReportViewerControl.LocalReport.DataSources.Clear();

                    // Add main report data
                    ReportViewerControl.LocalReport.DataSources.Add(new ReportDataSource(dataSetName, data));

                    // Add company information to all reports
                    var companyInfo = await GetCompanyInfoAsync();
                    var companyTable = CreateCompanyInfoTable(companyInfo);
                    ReportViewerControl.LocalReport.DataSources.Add(new ReportDataSource("CompanyInfoDataSet", companyTable));

                    // Add date parameters if needed
                    ReportViewerControl.LocalReport.SetParameters(new ReportParameter[]
                    {
                        new ReportParameter("StartDate", startDate?.ToString("yyyy-MM-dd") ?? ""),
                        new ReportParameter("EndDate", endDate?.ToString("yyyy-MM-dd") ?? "")
                    });
                    ReportViewerControl.RefreshReport();
                }
                else
                {
                    Helpers.BootstrapMessageBoxHelper.ShowError("لا توجد بيانات للعرض في الفترة المحددة.", "تنبيه", owner: FindParentWindow());
                }
            }
            catch (Exception ex)
            {
                Helpers.BootstrapMessageBoxHelper.ShowError($"خطأ في إنشاء التقرير: {ex.Message}", "خطأ", owner: FindParentWindow());
            }
            finally
            {
                // Hide loading indicator
                LoadingPanel.Visibility = Visibility.Collapsed;
                GenerateReportButton.IsEnabled = true;
            }
        }

        private async Task<DataTable> GetDailySalesDataAsync(DateTime? startDate, DateTime? endDate)
        {
            var dt = new DataTable();
            dt.Columns.Add("SaleDate", typeof(DateTime));
            dt.Columns.Add("FormattedTotalSales", typeof(string));
            dt.Columns.Add("InvoiceCount", typeof(int));
            dt.Columns.Add("CashierName", typeof(string));
            dt.Columns.Add("AverageTransactionValue", typeof(string));
            dt.Columns.Add("TotalItemsSold", typeof(int));
            dt.Columns.Add("FormattedDiscountAmount", typeof(string));
            dt.Columns.Add("FormattedTaxAmount", typeof(string));

            try
            {
                var dailySalesReport = await _saleServiceClient.GetDailySalesReport(startDate, endDate);
                foreach (var item in dailySalesReport)
                {
                    dt.Rows.Add(
                        item.SaleDate,
                        item.FormattedTotalSales,
                        item.InvoiceCount,
                        item.CashierName,
                        item.FormattedAverageTransactionValue,
                        item.TotalItemsSold,
                        item.FormattedDiscountAmount,
                        item.FormattedTaxAmount
                    );
                }
            }
            catch (Exception ex)
            {
                Helpers.BootstrapMessageBoxHelper.ShowError($"خطأ في جلب بيانات التقرير: {ex.Message}", "خطأ", owner: FindParentWindow());
            }
            return dt;
        }

        private async Task<DataTable> GetSalesByProductDataAsync(DateTime? startDate, DateTime? endDate)
        {
            var dt = new DataTable();
            dt.Columns.Add("ProductName", typeof(string));
            dt.Columns.Add("ProductSKU", typeof(string));
            dt.Columns.Add("CategoryName", typeof(string));
            dt.Columns.Add("BrandName", typeof(string));
            dt.Columns.Add("FormattedQuantitySold", typeof(string));
            dt.Columns.Add("FormattedTotalSales", typeof(string));
            dt.Columns.Add("FormattedAveragePrice", typeof(string));
            dt.Columns.Add("TransactionCount", typeof(int));

            try
            {
                var salesByProduct = await _saleServiceClient.GetSalesByProductReport(startDate, endDate);
                foreach (var item in salesByProduct)
                {
                    dt.Rows.Add(
                        item.ProductName,
                        item.ProductSKU,
                        item.CategoryName,
                        item.BrandName,
                        item.FormattedQuantitySold,
                        item.FormattedTotalSales,
                        item.FormattedAveragePrice,
                        item.TransactionCount
                    );
                }
            }
            catch (Exception ex)
            {
                Helpers.BootstrapMessageBoxHelper.ShowError($"خطأ في جلب بيانات تقرير المبيعات حسب المنتج: {ex.Message}", "خطأ", owner: FindParentWindow());
            }
            return dt;
        }

        private async Task<DataTable> GetSalesByCategoryDataAsync(DateTime? startDate, DateTime? endDate)
        {
            var dt = new DataTable();
            dt.Columns.Add("CategoryName", typeof(string));
            dt.Columns.Add("FormattedTotalSales", typeof(string));
            dt.Columns.Add("FormattedQuantitySold", typeof(string));
            dt.Columns.Add("ProductCount", typeof(int));
            dt.Columns.Add("TransactionCount", typeof(int));
            dt.Columns.Add("FormattedAverageTransactionValue", typeof(string));
            dt.Columns.Add("FormattedPercentageOfTotalSales", typeof(string));

            try
            {
                var salesByCategory = await _saleServiceClient.GetSalesByCategoryReport(startDate, endDate);
                foreach (var item in salesByCategory)
                {
                    dt.Rows.Add(
                        item.CategoryName,
                        item.FormattedTotalSales,
                        item.FormattedQuantitySold,
                        item.ProductCount,
                        item.TransactionCount,
                        item.FormattedAverageTransactionValue,
                        item.FormattedPercentageOfTotalSales
                    );
                }
            }
            catch (Exception ex)
            {
                Helpers.BootstrapMessageBoxHelper.ShowError($"خطأ في جلب بيانات تقرير المبيعات حسب الفئة: {ex.Message}", "خطأ", owner: FindParentWindow());
            }
            return dt;
        }

        private async Task<DataTable> GetPurchaseOrderDataAsync(DateTime? startDate, DateTime? endDate)
        {
            var dt = new DataTable();
            dt.Columns.Add("PONumber", typeof(string));
            dt.Columns.Add("SupplierName", typeof(string));
            dt.Columns.Add("PurchaseDate", typeof(DateTime));
            dt.Columns.Add("FormattedTotalAmount", typeof(string));
            dt.Columns.Add("FormattedAmountPaid", typeof(string));
            dt.Columns.Add("FormattedRemainingAmount", typeof(string));
            dt.Columns.Add("PaymentStatus", typeof(string));
            dt.Columns.Add("ItemCount", typeof(int));
            dt.Columns.Add("CreatedBy", typeof(string));

            try
            {
                var purchaseOrders = await _purchaseServiceClient.GetPurchaseOrderReport(startDate, endDate);
                foreach (var item in purchaseOrders)
                {
                    dt.Rows.Add(
                        item.PONumber,
                        item.SupplierName,
                        item.PurchaseDate,
                        item.FormattedTotalAmount,
                        item.FormattedAmountPaid,
                        item.FormattedRemainingAmount,
                        item.PaymentStatus,
                        item.ItemCount,
                        item.CreatedBy
                    );
                }
            }
            catch (Exception ex)
            {
                Helpers.BootstrapMessageBoxHelper.ShowError($"خطأ في جلب بيانات تقرير أوامر الشراء: {ex.Message}", "خطأ", owner: FindParentWindow());
            }
            return dt;
        }

        private async Task<CustomerAccountStatementDto> GetCustomerAccountStatementDataAsync(int customerId, DateTime? startDate, DateTime? endDate)
        {
            try
            {
                return await _reportServiceClient.GetCustomerAccountStatement(customerId, startDate, endDate);
            }
            catch (Exception ex)
            {
                Helpers.BootstrapMessageBoxHelper.ShowError($"خطأ في جلب بيانات كشف حساب العميل: {ex.Message}", "خطأ", owner: FindParentWindow());
                return null;
            }
        }

        private DataTable CreateCustomerStatementTable(CustomerAccountStatementDto statement)
        {
            var dt = new DataTable();
            dt.Columns.Add("CustomerName", typeof(string));
            dt.Columns.Add("Phone", typeof(string));
            dt.Columns.Add("Email", typeof(string));
            dt.Columns.Add("Address", typeof(string));
            dt.Columns.Add("FormattedStatementDate", typeof(string));
            dt.Columns.Add("FormattedFromDate", typeof(string));
            dt.Columns.Add("FormattedToDate", typeof(string));
            dt.Columns.Add("FormattedOpeningBalance", typeof(string));
            dt.Columns.Add("FormattedTotalSales", typeof(string));
            dt.Columns.Add("FormattedTotalPayments", typeof(string));
            dt.Columns.Add("FormattedClosingBalance", typeof(string));
            dt.Columns.Add("FormattedOutstandingBalance", typeof(string));

            dt.Rows.Add(
                statement.CustomerName,
                statement.Phone,
                statement.Email,
                statement.Address,
                statement.FormattedStatementDate,
                statement.FormattedFromDate,
                statement.FormattedToDate,
                statement.FormattedOpeningBalance,
                statement.FormattedTotalSales,
                statement.FormattedTotalPayments,
                statement.FormattedClosingBalance,
                statement.FormattedOutstandingBalance
            );

            return dt;
        }

        private DataTable CreateTransactionsTable(List<CustomerAccountTransactionDto> transactions)
        {
            var dt = new DataTable();
            dt.Columns.Add("FormattedTransactionDate", typeof(string));
            dt.Columns.Add("TransactionType", typeof(string));
            dt.Columns.Add("ReferenceNumber", typeof(string));
            dt.Columns.Add("Description", typeof(string));
            dt.Columns.Add("FormattedDebitAmount", typeof(string));
            dt.Columns.Add("FormattedCreditAmount", typeof(string));
            dt.Columns.Add("FormattedRunningBalance", typeof(string));

            foreach (var transaction in transactions)
            {
                dt.Rows.Add(
                    transaction.FormattedTransactionDate,
                    transaction.TransactionType,
                    transaction.ReferenceNumber,
                    transaction.Description,
                    transaction.FormattedDebitAmount,
                    transaction.FormattedCreditAmount,
                    transaction.FormattedRunningBalance
                );
            }

            return dt;
        }

        private async Task<CompanyInfoDto> GetCompanyInfoAsync()
        {
            try
            {
                return await _reportServiceClient.GetCompanyInfoAsync();
            }
            catch (Exception ex)
            {
                Helpers.BootstrapMessageBoxHelper.ShowError($"خطأ في جلب بيانات الشركة: {ex.Message}", "خطأ", owner: FindParentWindow());
                return new CompanyInfoDto(); // Return empty DTO with default values
            }
        }

        private DataTable CreateCompanyInfoTable(CompanyInfoDto companyInfo)
        {
            var dt = new DataTable();
            dt.Columns.Add("CompanyName", typeof(string));
            dt.Columns.Add("CompanyAddress", typeof(string));
            dt.Columns.Add("CompanyPhone", typeof(string));

            dt.Rows.Add(
                companyInfo.CompanyName,
                companyInfo.CompanyAddress,
                companyInfo.CompanyPhone
            );

            return dt;
        }

        private void ExportPdfButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var reportViewer = ReportViewerControl;
                if (reportViewer.LocalReport.DataSources.Count > 0)
                {
                    var deviceInfo = "<DeviceInfo><OutputFormat>PDF</OutputFormat></DeviceInfo>";
                    var bytes = reportViewer.LocalReport.Render("PDF", deviceInfo);

                    var saveDialog = new Microsoft.Win32.SaveFileDialog
                    {
                        Filter = "PDF files (*.pdf)|*.pdf",
                        DefaultExt = "pdf",
                        FileName = $"Report_{DateTime.Now:yyyyMMdd_HHmmss}.pdf"
                    };

                    if (saveDialog.ShowDialog() == true)
                    {
                        System.IO.File.WriteAllBytes(saveDialog.FileName, bytes);
                        Helpers.BootstrapMessageBoxHelper.ShowSuccess("تم تصدير التقرير بنجاح!", "نجح", owner: FindParentWindow());
                    }
                }
                else
                {
                    Helpers.BootstrapMessageBoxHelper.ShowError("يرجى إنشاء تقرير أولاً قبل التصدير.", "تنبيه", owner: FindParentWindow());
                }
            }
            catch (Exception ex)
            {
                Helpers.BootstrapMessageBoxHelper.ShowError($"خطأ في تصدير PDF: {ex.Message}", "خطأ", owner: FindParentWindow());
            }
        }

        private void ExportExcelButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var reportViewer = ReportViewerControl;
                if (reportViewer.LocalReport.DataSources.Count > 0)
                {
                    var deviceInfo = "<DeviceInfo><OutputFormat>Excel</OutputFormat></DeviceInfo>";
                    var bytes = reportViewer.LocalReport.Render("Excel", deviceInfo);

                    var saveDialog = new Microsoft.Win32.SaveFileDialog
                    {
                        Filter = "Excel files (*.xls)|*.xls",
                        DefaultExt = "xls",
                        FileName = $"Report_{DateTime.Now:yyyyMMdd_HHmmss}.xls"
                    };

                    if (saveDialog.ShowDialog() == true)
                    {
                        System.IO.File.WriteAllBytes(saveDialog.FileName, bytes);
                        Helpers.BootstrapMessageBoxHelper.ShowSuccess("تم تصدير التقرير بنجاح!", "نجح", owner: FindParentWindow());
                    }
                }
                else
                {
                    Helpers.BootstrapMessageBoxHelper.ShowError("يرجى إنشاء تقرير أولاً قبل التصدير.", "تنبيه", owner: FindParentWindow());
                }
            }
            catch (Exception ex)
            {
                Helpers.BootstrapMessageBoxHelper.ShowError($"خطأ في تصدير Excel: {ex.Message}", "خطأ", owner: FindParentWindow());
            }
        }

        private void PrintButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var reportViewer = ReportViewerControl;
                if (reportViewer.LocalReport.DataSources.Count > 0)
                {
                    reportViewer.PrintDialog();
                }
                else
                {
                    Helpers.BootstrapMessageBoxHelper.ShowError("يرجى إنشاء تقرير أولاً قبل الطباعة.", "تنبيه", owner: FindParentWindow());
                }
            }
            catch (Exception ex)
            {
                Helpers.BootstrapMessageBoxHelper.ShowError($"خطأ في الطباعة: {ex.Message}", "خطأ", owner: FindParentWindow());
            }
        }

        private async Task<SupplierAccountStatementDto> GetSupplierAccountStatementDataAsync(int supplierId, DateTime? startDate, DateTime? endDate)
        {
            try
            {
                return await _reportServiceClient.GetSupplierAccountStatement(supplierId, startDate, endDate);
            }
            catch (Exception ex)
            {
                Helpers.BootstrapMessageBoxHelper.ShowError($"خطأ في جلب بيانات كشف حساب المورد: {ex.Message}", "خطأ", owner: FindParentWindow());
                return null;
            }
        }

        private DataTable CreateSupplierStatementTable(SupplierAccountStatementDto statement)
        {
            var dt = new DataTable();
            dt.Columns.Add("SupplierId", typeof(int));
            dt.Columns.Add("SupplierName", typeof(string));
            dt.Columns.Add("FormattedStatementDate", typeof(string));
            dt.Columns.Add("FormattedFromDate", typeof(string));
            dt.Columns.Add("FormattedToDate", typeof(string));
            dt.Columns.Add("FormattedOpeningBalance", typeof(string));
            dt.Columns.Add("FormattedTotalDebits", typeof(string));
            dt.Columns.Add("FormattedTotalCredits", typeof(string));
            dt.Columns.Add("FormattedClosingBalance", typeof(string));
            dt.Columns.Add("OpeningBalance", typeof(decimal));
            dt.Columns.Add("TotalDebits", typeof(decimal));
            dt.Columns.Add("TotalCredits", typeof(decimal));
            dt.Columns.Add("ClosingBalance", typeof(decimal));

            dt.Rows.Add(
                statement.SupplierId,
                statement.SupplierName,
                statement.FormattedStatementDate,
                statement.FormattedFromDate,
                statement.FormattedToDate,
                statement.FormattedOpeningBalance,
                statement.FormattedTotalDebits,
                statement.FormattedTotalCredits,
                statement.FormattedClosingBalance,
                statement.OpeningBalance,
                statement.TotalPurchases, // TotalDebits
                statement.TotalPayments + statement.TotalReturns, // TotalCredits
                statement.ClosingBalance
            );

            return dt;
        }

        private DataTable CreateSupplierTransactionsTable(List<SupplierAccountTransactionDto> transactions)
        {
            var dt = new DataTable();
            dt.Columns.Add("FormattedTransactionDate", typeof(string));
            dt.Columns.Add("TransactionType", typeof(string));
            dt.Columns.Add("ReferenceNumber", typeof(string));
            dt.Columns.Add("Description", typeof(string));
            dt.Columns.Add("FormattedDebitAmount", typeof(string));
            dt.Columns.Add("FormattedCreditAmount", typeof(string));
            dt.Columns.Add("FormattedBalance", typeof(string));
            dt.Columns.Add("Balance", typeof(decimal));

            foreach (var transaction in transactions)
            {
                dt.Rows.Add(
                    transaction.FormattedTransactionDate,
                    transaction.TransactionType,
                    transaction.ReferenceNumber,
                    transaction.Description,
                    transaction.FormattedDebitAmount,
                    transaction.FormattedCreditAmount,
                    transaction.FormattedBalance,
                    transaction.Balance
                );
            }

            return dt;
        }
    }
}