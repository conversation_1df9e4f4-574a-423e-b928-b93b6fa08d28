using Microsoft.EntityFrameworkCore;
using ExactCash.Application.DTOs.Inventory;
using ExactCash.Application.Interfaces;
using ExactCash.Application.Responses;
using ExactCash.Infrastructure.Persistence;

namespace ExactCash.Application.Services
{
    public class InventoryService : IInventoryService
    {
        private readonly AppPostgreSQLDbContext _context;

        public InventoryService(AppPostgreSQLDbContext context)
        {
            _context = context;
        }

        public async Task<BaseResponse<InventoryStatsDto>> GetInventoryStatsAsync()
        {
            try
            {
                var totalProducts = await _context.Products.CountAsync();
                var totalCategories = await _context.Categories.CountAsync();
                var totalBrands = await _context.Brands.CountAsync();

                var lowStockProducts = await _context.Products
                    .Where(p => p.StockQuantity <= p.MinStock)
                    .CountAsync();

                var outOfStockProducts = await _context.Products
                    .Where(p => p.StockQuantity <= 0)
                    .CountAsync();

                var totalInventoryValue = await _context.Products
                    .SumAsync(p => p.StockQuantity * p.SellingPrice);

                var stats = new InventoryStatsDto
                {
                    TotalProducts = totalProducts,
                    LowStockProducts = lowStockProducts,
                    OutOfStockProducts = outOfStockProducts,
                    TotalInventoryValue = totalInventoryValue,
                    TotalCategories = totalCategories,
                    TotalBrands = totalBrands,
                    CurrencySymbol = "ج.م"
                };

                return ResponseHelper.Success(200, stats, "Inventory statistics retrieved successfully");
            }
            catch (Exception ex)
            {
                return ResponseHelper.Failure<InventoryStatsDto>(500, null, $"Error retrieving inventory statistics: {ex.Message}");
            }
        }

        public async Task<BaseResponse<List<LowStockItemDto>>> GetLowStockItemsAsync(int threshold = 10)
        {
            try
            {
                var lowStockItems = await _context.Products
                    .Include(p => p.Category)
                    .Include(p => p.Brand)
                    .Include(p => p.Unit)
                    .Where(p => p.StockQuantity <= threshold)
                    .Select(p => new LowStockItemDto
                    {
                        Id = p.Id,
                        Name = p.Name,
                        Code = p.SKU,
                        CurrentStock = (int)p.StockQuantity,
                        MinimumStock = p.MinStock,
                        CategoryName = p.Category != null ? p.Category.Name : "N/A",
                        BrandName = p.Brand != null ? p.Brand.Name : "N/A",
                        UnitPrice = p.SellingPrice,
                        UnitName = p.Unit != null ? p.Unit.Name : "N/A"
                    })
                    .OrderBy(p => p.CurrentStock)
                    .ToListAsync();

                return ResponseHelper.Success(200, lowStockItems, $"Found {lowStockItems.Count} low stock items");
            }
            catch (Exception ex)
            {
                return ResponseHelper.Failure<List<LowStockItemDto>>(500, new List<LowStockItemDto>(), $"Error retrieving low stock items: {ex.Message}");
            }
        }
    }
}
