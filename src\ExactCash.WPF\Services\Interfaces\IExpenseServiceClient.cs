﻿using ExactCash.Application.DTOs;

namespace ExactCash.WPF.Services.Interfaces
{
    public interface IExpenseServiceClient
    {
        Task<IEnumerable<ExpenseDto>> GetAllAsync(ExpenseSearchDto search);
        Task<ExpenseDto> GetByIdAsync(int id);
        Task<ExpenseDto> CreateAsync(ExpenseDto dto);
        Task<bool> UpdateAsync(ExpenseDto dto);
        Task<bool> DeleteAsync(int id);
    }
}
