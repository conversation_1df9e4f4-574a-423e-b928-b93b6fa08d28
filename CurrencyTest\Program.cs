using System;
using ExactCash.Application.DTOs;

class Program
{
    static void Main()
    {
        Console.WriteLine("Testing Currency Formatting in DTOs");
        Console.WriteLine("====================================");

        // Test SalesByProductReportDto
        var productReport = new SalesByProductReportDto
        {
            ProductName = "Test Product",
            QuantitySold = 10.50m,
            TotalSales = 1234.56m,
            AveragePrice = 117.58m
        };

        Console.WriteLine("SalesByProductReportDto:");
        Console.WriteLine($"  FormattedQuantitySold: {productReport.FormattedQuantitySold}");
        Console.WriteLine($"  FormattedTotalSales: {productReport.FormattedTotalSales}");
        Console.WriteLine($"  FormattedAveragePrice: {productReport.FormattedAveragePrice}");
        Console.WriteLine();

        // Test SalesByCategoryReportDto
        var categoryReport = new SalesByCategoryReportDto
        {
            CategoryName = "Test Category",
            TotalSales = 5678.90m,
            QuantitySold = 25.75m,
            AverageTransactionValue = 220.35m,
            PercentageOfTotalSales = 15.25m
        };

        Console.WriteLine("SalesByCategoryReportDto:");
        Console.WriteLine($"  FormattedTotalSales: {categoryReport.FormattedTotalSales}");
        Console.WriteLine($"  FormattedQuantitySold: {categoryReport.FormattedQuantitySold}");
        Console.WriteLine($"  FormattedAverageTransactionValue: {categoryReport.FormattedAverageTransactionValue}");
        Console.WriteLine($"  FormattedPercentageOfTotalSales: {categoryReport.FormattedPercentageOfTotalSales}");
        Console.WriteLine();

        // Test DailySalesReportDto
        var dailyReport = new DailySalesReportDto
        {
            SaleDate = DateTime.Now,
            TotalSales = 9876.54m,
            InvoiceCount = 42,
            CashierName = "Test Cashier"
        };

        Console.WriteLine("DailySalesReportDto:");
        Console.WriteLine($"  FormattedTotalSales: {dailyReport.FormattedTotalSales}");
        Console.WriteLine();

        // Test PurchaseOrderReportDto
        var purchaseReport = new PurchaseOrderReportDto
        {
            PONumber = "PO-001",
            TotalAmount = 3456.78m,
            AmountPaid = 2000.00m,
            RemainingAmount = 1456.78m
        };

        Console.WriteLine("PurchaseOrderReportDto:");
        Console.WriteLine($"  FormattedTotalAmount: {purchaseReport.FormattedTotalAmount}");
        Console.WriteLine($"  FormattedAmountPaid: {purchaseReport.FormattedAmountPaid}");
        Console.WriteLine($"  FormattedRemainingAmount: {purchaseReport.FormattedRemainingAmount}");

        Console.WriteLine("\nAll tests completed successfully!");
        Console.WriteLine("Currency formatting is working correctly with Egyptian Pound (ج.م) and 2 decimal places.");
    }
}
