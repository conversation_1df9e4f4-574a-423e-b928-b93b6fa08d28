using AutoMapper;
using ExactCash.Application.DTOs;
using ExactCash.Application.DTOs.Common;
using ExactCash.Application.Enums;
using ExactCash.Application.Interfaces;
using ExactCash.Application.Responses;
using ExactCash.Domain.Entities;
using ExactCash.Infrastructure.Persistence;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using System.Linq;

namespace ExactCash.Application.Services
{
    public class CustomerService : ICustomerService
    {
        private readonly AppPostgreSQLDbContext _context;
        private readonly IMapper _mapper;
        public CustomerService(AppPostgreSQLDbContext context, IMapper mapper)
        {
            _context = context;
            _mapper = mapper;
        }

        public async Task<CustomerDto> GetCustomerByIdAsync(int id)
        {
            var customer = await _context.Customers.FindAsync(id);
            if (customer == null)
                return null;

            return MapToDto(customer);
        }

        public async Task<PagedResponse<CustomerDto>> GetAllCustomersAsync(string name, string phone, int? categoryId, PaginationFilter pagination)
        {
            var query = _context.Customers
                .Include(c => c.Category)
                .AsQueryable();

            if (!string.IsNullOrEmpty(name))
                query = query.Where(c => c.FullName.ToLower().Contains(name.ToLower()));

            if (!string.IsNullOrEmpty(phone))
                query = query.Where(c => c.Phone.Contains(phone));

            if (categoryId.HasValue && categoryId.Value > 0)
                query = query.Where(c => c.CategoryId == categoryId.Value);

            var totalRecords = await query.CountAsync();

            query = pagination.SortField switch
            {
                nameof(Customer.CreationDate) => pagination.SortOrder == SortOrder.Asc ? query.OrderBy(a => a.CreationDate) : query.OrderByDescending(a => a.CreationDate),
                nameof(Customer.FullName) => pagination.SortOrder == SortOrder.Asc ? query.OrderBy(a => a.FullName) : query.OrderByDescending(a => a.FullName),
                nameof(Customer.Phone) => pagination.SortOrder == SortOrder.Asc ? query.OrderBy(a => a.Phone) : query.OrderByDescending(a => a.Phone),
                _ => pagination.SortOrder == SortOrder.Desc ? query.OrderBy(a => a.CreationDate) : query.OrderByDescending(a => a.CreationDate)
            };

            var result = await query
            .Skip((pagination.PageNumber - 1) * pagination.PageSize)
            .Take(pagination.PageSize)
            .ToListAsync();

            var customerDtos = result.Select(c => new CustomerDto
            {
                Id = c.Id,
                FullName = c.FullName,
                Phone = c.Phone,
                Email = c.Email,
                Address = c.Address,
                TotalSpent = c.TotalSpent,
                CategoryId = c.CategoryId,
                CategoryName = c.Category?.Name,
                CreationDate = c.CreationDate,
                LastUpdatedDate = c.LastUpdatedDate,
                CreatedBy = c.CreatedBy,
                LastUpdatedBy = c.LastUpdatedBy
            }).ToList();

            return PaginationHelper.CreatePagedResponse(customerDtos, pagination.PageNumber, pagination.PageSize, totalRecords);
        }


        public async Task<List<CustomerDto>> GetAllCustomersAsync()
        {
            var query = _context.Customers.AsQueryable();
            var result = await query
            .ToListAsync();

            return _mapper.Map<List<CustomerDto>>(result);
        }


        public async Task<CustomerDto> SearchCustomersAsync(string searchTerm)
        {
            if (string.IsNullOrWhiteSpace(searchTerm))
                return new CustomerDto();

            var customers = await _context.Customers
                .Where(c => c.FullName.ToLower().Contains(searchTerm.ToLower()) ||
                           c.Phone.Contains(searchTerm) ||
                           c.Email.ToLower().Contains(searchTerm.ToLower()))
                .FirstOrDefaultAsync();

            if (customers == null)
                return new CustomerDto();

            return _mapper.Map<CustomerDto>(customers);
        }

        public async Task<CustomerDto> CreateCustomerAsync(CustomerDto customerDto, string createdBy)
        {
            if (await _context.Customers.AnyAsync(x => x.Phone == customerDto.Phone))
                return _mapper.Map<CustomerDto>(await _context.Customers.FirstOrDefaultAsync(x => x.Phone == customerDto.Phone));
            var customer = new Customer(
                customerDto.FullName,
                customerDto.Phone,
                customerDto.Email,
                customerDto.Address,
                createdBy,
                customerDto.CategoryId
            );
            await _context.Customers.AddAsync(customer);
            await _context.SaveChangesAsync();
            return MapToDto(customer);
        }

        public async Task<BaseResponse<bool>> UpdateCustomerAsync(CustomerDto customerDto)
        {
            var customer = await _context.Customers.FindAsync(customerDto.Id);
            if (customer == null)
                throw new KeyNotFoundException($"Customer with ID {customerDto.Id} not found.");

            customer.Update(
                customerDto.FullName,
                customerDto.Phone,
                customerDto.Email,
                customerDto.Address,
                customerDto.CategoryId
            );

            _context.Entry(customer).State = EntityState.Modified;
            await _context.SaveChangesAsync();
            return ResponseHelper.Success(StatusCodes.Status200OK, true, "Customer updated successfully.");
        }

        public async Task DeleteCustomerAsync(int id)
        {
            var customer = await _context.Customers.FindAsync(id);
            if (customer == null)
                throw new KeyNotFoundException($"Customer with ID {id} not found.");

            _context.Customers.Remove(customer);
            await _context.SaveChangesAsync();
        }

        /// <summary>
        /// Updates a customer's credit limit and records the change in history.
        /// </summary>
        /// <param name="customerId">Customer ID</param>
        /// <param name="newCreditLimit">New credit limit amount</param>
        /// <param name="reason">Reason for the change</param>
        /// <param name="approvedBy">User who approved the change</param>
        /// <returns></returns>
        public async Task<BaseResponse<bool>> UpdateCustomerCreditLimitAsync(
            int customerId,
            decimal newCreditLimit,
            string reason,
            string approvedBy)
        {
            using var transaction = await _context.Database.BeginTransactionAsync();
            try
            {
                var customer = await _context.Customers.FindAsync(customerId);
                if (customer == null)
                    return ResponseHelper.Failure<bool>(StatusCodes.Status404NotFound, false, "Customer not found");

                if (newCreditLimit < 0)
                    return ResponseHelper.Failure<bool>(StatusCodes.Status400BadRequest, false, "Credit limit cannot be negative");

                // Check if customer has outstanding balance greater than new limit
                if (customer.OutstandingBalance > newCreditLimit)
                    return ResponseHelper.Failure<bool>(StatusCodes.Status400BadRequest, false,
                        $"Cannot set credit limit below outstanding balance of {customer.OutstandingBalance:C}");

                var previousLimit = customer.CreditLimit;

                // Create credit limit history record
                var creditLimitHistory = new CustomerCreditLimit(
                    customerId: customerId,
                    previousLimit: previousLimit,
                    newLimit: newCreditLimit,
                    reason: reason,
                    approvedBy: approvedBy
                );

                await _context.CustomerCreditLimits.AddAsync(creditLimitHistory);

                // Update customer's credit limit
                customer.UpdateCreditLimit(newCreditLimit);
                _context.Entry(customer).State = EntityState.Modified;

                await _context.SaveChangesAsync();
                await transaction.CommitAsync();

                return ResponseHelper.Success(StatusCodes.Status200OK, true, "Credit limit updated successfully");
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                return ResponseHelper.Failure<bool>(StatusCodes.Status500InternalServerError, false, $"Error updating credit limit: {ex.Message}");
            }
        }

        /// <summary>
        /// Gets the credit limit history for a customer.
        /// </summary>
        /// <param name="customerId">Customer ID</param>
        /// <returns>List of credit limit changes</returns>
        public async Task<List<CustomerCreditLimit>> GetCustomerCreditLimitHistoryAsync(int customerId)
        {
            return await _context.CustomerCreditLimits
                .Where(cl => cl.CustomerId == customerId)
                .OrderByDescending(cl => cl.CreationDate)
                .ToListAsync();
        }

        private CustomerDto MapToDto(Customer customer)
        {
            return new CustomerDto
            {
                Id = customer.Id,
                FullName = customer.FullName,
                Phone = customer.Phone,
                Email = customer.Email,
                Address = customer.Address,
                TotalSpent = customer.TotalSpent,
                CategoryId = customer.CategoryId,
                CategoryName = customer.Category?.Name,
                CreationDate = customer.CreationDate,
                LastUpdatedDate = customer.LastUpdatedDate,
                CreatedBy = customer.CreatedBy,
                LastUpdatedBy = customer.LastUpdatedBy
            };
        }
    }
}