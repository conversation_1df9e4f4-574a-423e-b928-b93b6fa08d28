using AutoMapper;
using ExactCash.WPF.Services;
using ExactCash.WPF.Services.Interfaces;
using ExactCash.WPF.Services.Product;
using ExactCash.WPF.ViewModels.Common;
using ExactCash.WPF.ViewModels.Sale;
using ExactCash.WPF.ViewModels.Supplier;
using Microsoft.Extensions.Configuration;
using System.Windows;

namespace ExactCash.WPF.Views.Sale
{
    public partial class SaleListView : Window
    {
        public SaleListView(
            ISaleServiceClient saleServiceClient,
            IMapper mapper,
            IApiUserService apiService,
            LoadingViewModel loadingViewModel,
            ICustomerService customerService,
            IProductService productService,
            ISaleServiceClient saleService,
            IUnitServiceClient unitServiceClient,
            ICategoryServiceClient categoryServiceClient,
            IBrandsServiceClient brandsServiceClient,
            ISupplierServiceClient supplierServiceClient,
            NotificationViewModel notificationViewModel,
            IPurchaseServiceClient purchaseOrderServiceClient,
            IUserServiceClient userServiceClient,
            IRoleServiceClient roleServiceClient,
            IConfiguration configuration,
            IExpenseCategoryServiceClient expenseCategoryServiceClient,
            IExpenseServiceClient expenseServiceClient,
            ISystemConfigurationServiceClient systemConfigurationServiceClient,
            IReportServiceClient reportServiceClient,
            ISupplierCategoryServiceClient supplierCategoryServiceClient,
            ICustomerCategoryServiceClient customerCategoryServiceClient
            )
        {
            DataContext = new SaleListViewModel(
                saleServiceClient,
                mapper,apiService,
                customerService,productService,saleService,
                loadingViewModel,unitServiceClient,
                categoryServiceClient,brandsServiceClient,
                supplierServiceClient,notificationViewModel,
                purchaseOrderServiceClient,userServiceClient,
                roleServiceClient,configuration,
                expenseCategoryServiceClient,
                expenseServiceClient,
                systemConfigurationServiceClient,
                reportServiceClient,
                supplierCategoryServiceClient,
                customerCategoryServiceClient
                );
            InitializeComponent();
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            Close();
        }

        private void MinimizeButton_Click(object sender, RoutedEventArgs e)
        {
            WindowState = WindowState.Minimized;
        }
    }
}