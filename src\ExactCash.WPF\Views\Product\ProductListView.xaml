<Window x:Class="ExactCash.WPF.Views.Product.ProductListView"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:ExactCash.WPF.Views.Product"
        mc:Ignorable="d"
        Title="قائمة المنتجات"
        Height="750"
        Width="1200"
        WindowStartupLocation="CenterOwner"
        FlowDirection="RightToLeft"
        Background="#f8f9fa"
        WindowStyle="None"
        AllowsTransparency="True"
        ResizeMode="CanResize"
        WindowState="Maximized">

    <Window.Resources>
        <Style x:Key="CloseButtonStyle"
               TargetType="Button">
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border x:Name="border"
                                Background="Transparent"
                                BorderThickness="0">
                            <ContentPresenter HorizontalAlignment="Center"
                                              VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver"
                                     Value="True">
                                <Setter Property="Foreground"
                                        Value="#e74c3c"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <Style x:Key="ModernButton"
               TargetType="Button">
            <Setter Property="Background"
                    Value="#0078D4"/>
            <Setter Property="Foreground"
                    Value="White"/>
            <Setter Property="BorderThickness"
                    Value="0"/>
            <Setter Property="Padding"
                    Value="15,8"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="4">
                            <ContentPresenter HorizontalAlignment="Center"
                                              VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver"
                                     Value="True">
                                <Setter Property="Background"
                                        Value="#106EBE"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <Style x:Key="SuccessButton"
               TargetType="Button">
            <Setter Property="Background"
                    Value="#28a745"/>
            <Setter Property="Foreground"
                    Value="White"/>
            <Setter Property="BorderThickness"
                    Value="0"/>
            <Setter Property="Padding"
                    Value="15,8"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="4">
                            <ContentPresenter HorizontalAlignment="Center"
                                              VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver"
                                     Value="True">
                                <Setter Property="Background"
                                        Value="#218838"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <Style x:Key="ModernTextBox"
               TargetType="TextBox">
            <Setter Property="Height"
                    Value="35"/>
            <Setter Property="Padding"
                    Value="8,5"/>
            <Setter Property="Background"
                    Value="White"/>
            <Setter Property="BorderBrush"
                    Value="#ced4da"/>
            <Setter Property="BorderThickness"
                    Value="1"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="TextBox">
                        <Border Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="4">
                            <ScrollViewer x:Name="PART_ContentHost"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver"
                                     Value="True">
                                <Setter Property="BorderBrush"
                                        Value="#80bdff"/>
                            </Trigger>
                            <Trigger Property="IsFocused"
                                     Value="True">
                                <Setter Property="BorderBrush"
                                        Value="#80bdff"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <Style x:Key="ModernComboBox"
               TargetType="ComboBox">
            <Setter Property="Height"
                    Value="35"/>
            <Setter Property="Padding"
                    Value="8,5"/>
            <Setter Property="Background"
                    Value="White"/>
            <Setter Property="BorderBrush"
                    Value="#ced4da"/>
            <Setter Property="BorderThickness"
                    Value="1"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="ComboBox">
                        <Grid>
                            <ToggleButton x:Name="ToggleButton"
                                          Background="{TemplateBinding Background}"
                                          BorderBrush="{TemplateBinding BorderBrush}"
                                          BorderThickness="{TemplateBinding BorderThickness}"
                                          Focusable="False"
                                          IsChecked="{Binding Path=IsDropDownOpen, Mode=TwoWay, RelativeSource={RelativeSource TemplatedParent}}">
                                <ToggleButton.Template>
                                    <ControlTemplate TargetType="ToggleButton">
                                        <Border x:Name="templateRoot"
                                                Background="{TemplateBinding Background}"
                                                BorderBrush="{TemplateBinding BorderBrush}"
                                                BorderThickness="{TemplateBinding BorderThickness}"
                                                CornerRadius="4">
                                            <Grid>
                                                <Grid.ColumnDefinitions>
                                                    <ColumnDefinition/>
                                                    <ColumnDefinition Width="30"/>
                                                </Grid.ColumnDefinitions>
                                                <ContentPresenter Margin="{TemplateBinding Padding}"
                                                                  Content="{TemplateBinding Content}"
                                                                  ContentTemplate="{TemplateBinding ContentTemplate}"
                                                                  HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                                                  VerticalAlignment="{TemplateBinding VerticalContentAlignment}"/>
                                                <Path x:Name="arrow"
                                                      Grid.Column="1"
                                                      Data="M0,0 L8,0 L4,4 Z"
                                                      Fill="#495057"
                                                      HorizontalAlignment="Center"
                                                      Margin="0,0,0,0"
                                                      VerticalAlignment="Center"/>
                                            </Grid>
                                        </Border>
                                        <ControlTemplate.Triggers>
                                            <Trigger Property="IsMouseOver"
                                                     Value="True">
                                                <Setter Property="BorderBrush"
                                                        Value="#80bdff"/>
                                            </Trigger>
                                        </ControlTemplate.Triggers>
                                    </ControlTemplate>
                                </ToggleButton.Template>
                            </ToggleButton>
                            <ContentPresenter x:Name="contentPresenter"
                                              Content="{TemplateBinding SelectionBoxItem}"
                                              ContentTemplate="{TemplateBinding SelectionBoxItemTemplate}"
                                              ContentStringFormat="{TemplateBinding SelectionBoxItemStringFormat}"
                                              HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                              IsHitTestVisible="False"
                                              Margin="{TemplateBinding Padding}"
                                              SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}"
                                              VerticalAlignment="{TemplateBinding VerticalContentAlignment}"/>
                            <Popup x:Name="PART_Popup"
                                   AllowsTransparency="True"
                                   IsOpen="{Binding IsDropDownOpen, RelativeSource={RelativeSource TemplatedParent}}"
                                   Placement="Bottom"
                                   PopupAnimation="Slide">
                                <Border x:Name="dropDownBorder"
                                        Background="{TemplateBinding Background}"
                                        BorderBrush="{TemplateBinding BorderBrush}"
                                        BorderThickness="1"
                                        CornerRadius="0,0,4,4"
                                        MaxHeight="{TemplateBinding MaxDropDownHeight}"
                                        Width="{Binding ActualWidth, RelativeSource={RelativeSource TemplatedParent}}">
                                    <ScrollViewer>
                                        <ItemsPresenter/>
                                    </ScrollViewer>
                                </Border>
                            </Popup>
                        </Grid>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </Window.Resources>

    <Border BorderThickness="1"
            BorderBrush="#dee2e6"
            Background="White"
            CornerRadius="6">
        <DockPanel>
            <!-- Header -->
            <Border DockPanel.Dock="Top"
                    Background="#0078D4"
                    Height="60"
                    BorderThickness="0,0,0,1"
                    BorderBrush="White">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <!-- Title -->
                    <TextBlock Text="قائمة المنتجات"
                               Foreground="White"
                               FontSize="20"
                               FontWeight="Bold"
                               VerticalAlignment="Center"
                               Margin="20,0,0,0"/>

                    <!-- Close Button -->
                    <Button Grid.Column="1"
                            Content="✕"
                            Click="CloseButton_Click"
                            Style="{StaticResource CloseButtonStyle}"
                            FontSize="20"
                            Foreground="White"
                            Width="60"
                            Height="60"/>

                    <!-- Minimize Button -->
                    <Button Grid.Column="2"
                            Content="−"
                            Click="MinimizeButton_Click"
                            Style="{StaticResource CloseButtonStyle}"
                            FontSize="20"
                            Foreground="White"
                            Width="60"
                            Height="60"/>
                </Grid>
            </Border>

            <!-- Main Content -->
            <Grid Margin="20">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>

                <!-- Search Panel -->
                <GroupBox Header="بحث"
                          Margin="0,0,0,20"
                          BorderBrush="#dee2e6">
                    <Grid Margin="10">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>

                        <!-- Name Search -->
                        <StackPanel Grid.Column="0"
                                    Margin="0,0,10,0">
                            <TextBlock Text="اسم المنتج"/>
                            <TextBox Text="{Binding NameSearch, UpdateSourceTrigger=PropertyChanged}"
                                     Style="{StaticResource ModernTextBox}"/>
                        </StackPanel>

                        <!-- SKU Search -->
                        <StackPanel Grid.Column="1"
                                    Margin="0,0,10,0">
                            <TextBlock Text="رمز التخزين (SKU)"/>
                            <TextBox Text="{Binding SkuSearch, UpdateSourceTrigger=PropertyChanged}"
                                     Style="{StaticResource ModernTextBox}"/>
                        </StackPanel>

                        <!-- Barcode Search -->
                        <StackPanel Grid.Column="2"
                                    Margin="0,0,10,0">
                            <TextBlock Text="الباركود"/>
                            <TextBox Text="{Binding BarcodeSearch, UpdateSourceTrigger=PropertyChanged}"
                                     Style="{StaticResource ModernTextBox}"/>
                        </StackPanel>

                        <!-- Category Filter -->
                        <StackPanel Grid.Column="3"
                                    Margin="0,0,10,0">
                            <TextBlock Text="الفئة"/>
                            <ComboBox ItemsSource="{Binding Categories}"
                                      SelectedValuePath="Id"
                                      SelectedValue="{Binding CategoryIdSearch}"
                                      Style="{StaticResource ModernComboBox}">
                                <ComboBox.ItemTemplate>
                                    <DataTemplate>
                                        <TextBlock Text="{Binding Name}"/>
                                    </DataTemplate>
                                </ComboBox.ItemTemplate>
                            </ComboBox>
                        </StackPanel>

                        <!-- Brand Filter -->
                        <StackPanel Grid.Column="4"
                                    Margin="0,0,10,0">
                            <TextBlock Text="العلامة التجارية"/>
                            <ComboBox ItemsSource="{Binding Brands}"
                                      SelectedValuePath="Id"
                                      SelectedValue="{Binding BrandIdSearch}"
                                      Style="{StaticResource ModernComboBox}">
                                <ComboBox.ItemTemplate>
                                    <DataTemplate>
                                        <TextBlock Text="{Binding Name}"/>
                                    </DataTemplate>
                                </ComboBox.ItemTemplate>
                            </ComboBox>
                        </StackPanel>

                        <!-- Search and Reset Buttons -->
                        <StackPanel Grid.Column="5"
                                    Orientation="Horizontal"
                                    HorizontalAlignment="Right"
                                    VerticalAlignment="Bottom"
                                    Margin="0,0,0,0">
                            <Button Content="بحث"
                                    Command="{Binding SearchCommand}"
                                    Style="{StaticResource ModernButton}"
                                    Width="100"
                                    Height="35"
                                    Margin="0,0,10,0"/>
                            <Button Content="إعادة تعيين"
                                    Command="{Binding ResetSearchCommand}"
                                    Style="{StaticResource ModernButton}"
                                    Width="100"
                                    Height="35"/>
                        </StackPanel>
                    </Grid>
                </GroupBox>

                <!-- Products Grid -->
                <Grid Grid.Row="2">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <!-- Action Buttons -->
                    <StackPanel Grid.Row="0"
                                Orientation="Horizontal"
                                HorizontalAlignment="Right"
                                Margin="0,0,0,10">
                        <Button Content="إضافة منتج جديد"
                                Command="{Binding AddNewProductCommand}"
                                Style="{StaticResource SuccessButton}"
                                Height="35"
                                Width="120"/>
                        <Button Content="تحديث"
                                Command="{Binding RefreshCommand}"
                                Style="{StaticResource ModernButton}"
                                Height="35"
                                Margin="10,0,10,0"
                                Width="80"/>
                        <Button Content="تصدير إلى Excel"
                                Width="140"
                                Height="35"
                                Margin="10,0,10,0"
                                Command="{Binding ExportToExcelCommand}"
                                Style="{StaticResource ModernButton}"/>
                    </StackPanel>

                    <DataGrid Grid.Row="1"
                              ItemsSource="{Binding Products}"
                              AutoGenerateColumns="False"
                              IsReadOnly="True"
                              GridLinesVisibility="None"
                              BorderThickness="1"
                              BorderBrush="#dee2e6"
                              Background="White"
                              RowBackground="White"
                              AlternatingRowBackground="#f8f9fa"
                              RowHeight="40"
                              CanUserAddRows="False"
                              CanUserDeleteRows="False"
                              CanUserReorderColumns="False"
                              CanUserSortColumns="True"
                              SelectionMode="Single"
                              SelectionUnit="FullRow">
                        <DataGrid.Resources>
                            <Style TargetType="DataGridRow">
                                <Setter Property="BorderThickness"
                                        Value="0"/>
                                <Setter Property="BorderBrush"
                                        Value="Transparent"/>
                            </Style>
                        </DataGrid.Resources>
                        <DataGrid.Columns>
                            <!-- Image Column -->
                            <DataGridTemplateColumn Header="الصورة"
                                                    Width="80">
                                <DataGridTemplateColumn.CellTemplate>
                                    <DataTemplate>
                                        <Image Source="{Binding ImagePath, TargetNullValue='/Assets/Images/default-product.png'}"
                                               Width="50"
                                               Height="50"
                                               Stretch="Uniform"
                                               RenderOptions.BitmapScalingMode="HighQuality"/>
                                    </DataTemplate>
                                </DataGridTemplateColumn.CellTemplate>
                            </DataGridTemplateColumn>

                            <DataGridTextColumn Header="اسم المنتج"
                                                Binding="{Binding Name}"
                                                Width="250"/>
                            <DataGridTextColumn Header="رمز التخزين"
                                                Binding="{Binding SKU}"
                                                Width="200"/>
                            <DataGridTextColumn Header="الباركود"
                                                Binding="{Binding Barcode}"
                                                Width="200"/>
                            <DataGridTextColumn Header="الفئة"
                                                Binding="{Binding CategoryName, TargetNullValue='-'}"
                                                Width="120"/>
                            <DataGridTextColumn Header="العلامة التجارية"
                                                Binding="{Binding BrandName, TargetNullValue='-'}"
                                                Width="120"/>
                            <DataGridTextColumn Header="سعر البيع"
                                                Binding="{Binding SellingPrice, StringFormat='{}{0:N2} ج.م', TargetNullValue='0.00 ج.م'}"
                                                Width="100"/>
                            <DataGridTextColumn Header="سعر الشراء"
                                                Binding="{Binding CostPrice, StringFormat='{}{0:N2} ج.م', TargetNullValue='0.00 ج.م'}"
                                                Width="100"/>
                            <DataGridTextColumn Header="الكمية المتوفرة"
                                                Binding="{Binding StockQuantity, TargetNullValue='0'}"
                                                Width="100"/>
                            <DataGridTextColumn Header="الوحدة"
                                                Binding="{Binding UnitName, TargetNullValue='-'}"
                                                Width="100"/>
                            <DataGridTextColumn Header="تاريخ الإنشاء"
                                                Binding="{Binding CreationDate, StringFormat=dd/MM/yyyy HH:mm}"
                                                Width="150"/>
                            <DataGridTextColumn Header="تم الإنشاء بواسطة"
                                                Binding="{Binding CreatedBy, TargetNullValue='-'}"
                                                Width="120"/>

                            <!-- Actions Column -->
                            <DataGridTemplateColumn Header="الإجراءات"
                                                    Width="160">
                                <DataGridTemplateColumn.CellTemplate>
                                    <DataTemplate>
                                        <StackPanel Orientation="Horizontal"
                                                    HorizontalAlignment="Center">
                                            <Button Style="{StaticResource ModernButton}"
                                                    Command="{Binding DataContext.PrintBarcodeCommand, RelativeSource={RelativeSource AncestorType=DataGrid}}"
                                                    CommandParameter="{Binding}"
                                                    ToolTip="طباعة الباركود"
                                                    Width="35"
                                                    Height="30"
                                                    Margin="0,0,5,0">
                                                <TextBlock Text="🏷️"
                                                           FontSize="16"/>
                                            </Button>
                                            <Button Style="{StaticResource ModernButton}"
                                                    Command="{Binding DataContext.EditProductCommand, RelativeSource={RelativeSource AncestorType=DataGrid}}"
                                                    CommandParameter="{Binding}"
                                                    ToolTip="تعديل"
                                                    Width="35"
                                                    Height="30"
                                                    Margin="0,0,5,0">
                                                <TextBlock Text="✏️"
                                                           FontSize="16"/>
                                            </Button>
                                            <Button Style="{StaticResource ModernButton}"
                                                    Background="#dc3545"
                                                    Command="{Binding DataContext.DeleteProductCommand, RelativeSource={RelativeSource AncestorType=DataGrid}}"
                                                    CommandParameter="{Binding}"
                                                    ToolTip="حذف"
                                                    Width="35"
                                                    Height="30">
                                                <TextBlock Text="🗑️"
                                                           FontSize="16"/>
                                            </Button>
                                        </StackPanel>
                                    </DataTemplate>
                                </DataGridTemplateColumn.CellTemplate>
                            </DataGridTemplateColumn>
                        </DataGrid.Columns>
                    </DataGrid>

                    <!-- Pagination Controls -->
                    <StackPanel Grid.Row="2"
                                Orientation="Horizontal"
                                HorizontalAlignment="Center"
                                Margin="0,10,0,0">
                        <Button Content="▶"
                                Command="{Binding PreviousPageCommand}"
                                Style="{StaticResource ModernButton}"
                                Width="40"
                                Height="35"
                                Margin="0,0,10,0"/>
                        <TextBlock Text="{Binding PaginationInfo}"
                                   VerticalAlignment="Center"
                                   Margin="10,0"/>
                        <Button Content="◀"
                                Command="{Binding NextPageCommand}"
                                Style="{StaticResource ModernButton}"
                                Width="40"
                                Height="35"
                                Margin="10,0,0,0"/>
                    </StackPanel>
                </Grid>
            </Grid>
        </DockPanel>
    </Border>
</Window>