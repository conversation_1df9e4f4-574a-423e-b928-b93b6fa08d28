<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">

        <!-- Converters -->
        <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>

        <!-- Arabic Text Style -->
        <Style x:Key="ArabicTextStyle"
               TargetType="TextBlock">
                <Setter Property="FontFamily"
                        Value="Droid Arabic Kufi"/>
                <Setter Property="FlowDirection"
                        Value="RightToLeft"/>
        </Style>

        <!-- PasswordBox Style -->
        <Style x:Key="PasswordBoxStyle"
               TargetType="PasswordBox">
                <Setter Property="Background"
                        Value="#F5F5F5"/>
                <Setter Property="BorderThickness"
                        Value="0"/>
                <Setter Property="Padding"
                        Value="15,12"/>
                <Setter Property="FontSize"
                        Value="14"/>
                <Setter Property="FontFamily"
                        Value="Droid Arabic Kufi"/>
                <Setter Property="Template">
                        <Setter.Value>
                                <ControlTemplate TargetType="PasswordBox">
                                        <Border Background="{TemplateBinding Background}"
                                                CornerRadius="5">
                                                <ScrollViewer x:Name="PART_ContentHost"
                                                              Margin="{TemplateBinding Padding}"
                                                              FlowDirection="RightToLeft"/>
                                        </Border>
                                </ControlTemplate>
                        </Setter.Value>
                </Setter>
        </Style>

        <!-- Button Style -->
        <Style x:Key="ButtonStyle"
               TargetType="Button">
                <Setter Property="Background"
                        Value="#0078D4"/>
                <Setter Property="Foreground"
                        Value="White"/>
                <Setter Property="Padding"
                        Value="20,10"/>
                <Setter Property="BorderThickness"
                        Value="0"/>
                <Setter Property="Template">
                        <Setter.Value>
                                <ControlTemplate TargetType="Button">
                                        <Border Background="{TemplateBinding Background}"
                                                CornerRadius="5"
                                                Padding="{TemplateBinding Padding}">
                                                <ContentPresenter HorizontalAlignment="Center"
                                                                  VerticalAlignment="Center"/>
                                        </Border>
                                </ControlTemplate>
                        </Setter.Value>
                </Setter>
                <Style.Triggers>
                        <Trigger Property="IsMouseOver"
                                 Value="True">
                                <Setter Property="Background"
                                        Value="#106EBE"/>
                        </Trigger>
                </Style.Triggers>
        </Style>

        <!-- TextBox Style -->
        <Style x:Key="TextBoxStyle"
               TargetType="TextBox">
                <Setter Property="Background"
                        Value="#F5F5F5"/>
                <Setter Property="BorderThickness"
                        Value="0"/>
                <Setter Property="Padding"
                        Value="15,12"/>
                <Setter Property="FontSize"
                        Value="14"/>
                <Setter Property="FontFamily"
                        Value="Droid Arabic Kufi"/>
                <Setter Property="Template">
                        <Setter.Value>
                                <ControlTemplate TargetType="TextBox">
                                        <Border Background="{TemplateBinding Background}"
                                                CornerRadius="5">
                                                <ScrollViewer x:Name="PART_ContentHost"
                                                              Margin="{TemplateBinding Padding}"
                                                              FlowDirection="RightToLeft"/>
                                        </Border>
                                </ControlTemplate>
                        </Setter.Value>
                </Setter>
        </Style>

</ResourceDictionary> 