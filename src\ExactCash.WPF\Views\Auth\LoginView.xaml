<Window x:Class="ExactCash.WPF.Views.Auth.LoginView"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:ExactCash.WPF.Views.Auth"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        mc:Ignorable="d"
        Title="تسجيل الدخول - ExactCash"
        Height="600"
        Width="1000"
        WindowStartupLocation="CenterScreen"
        ResizeMode="NoResize"
        WindowStyle="None"
        AllowsTransparency="True"
        Background="Transparent"
        FlowDirection="RightToLeft">

        <Window.Resources>
                <Style x:Key="ArabicTextStyle"
                       TargetType="TextBlock">
                        <Setter Property="FontFamily"
                                Value="Droid Arabic Kufi"/>
                </Style>

                <Style TargetType="TextBox">
                        <Setter Property="Background"
                                Value="#F5F5F5"/>
                        <Setter Property="BorderThickness"
                                Value="0"/>
                        <Setter Property="Padding"
                                Value="15,12"/>
                        <Setter Property="FontSize"
                                Value="14"/>
                        <Setter Property="TextAlignment"
                                Value="Right"/>
                        <Setter Property="FontFamily"
                                Value="Droid Arabic Kufi"/>
                        <Setter Property="Template">
                                <Setter.Value>
                                        <ControlTemplate TargetType="TextBox">
                                                <Border Background="{TemplateBinding Background}"
                                                        CornerRadius="5">
                                                        <Grid>
                                                                <ScrollViewer x:Name="PART_ContentHost"
                                                                              Margin="{TemplateBinding Padding}"/>
                                                        </Grid>
                                                </Border>
                                        </ControlTemplate>
                                </Setter.Value>
                        </Setter>
                </Style>

                <Style x:Key="PasswordBoxStyle"
                       TargetType="PasswordBox">
                        <Setter Property="Background"
                                Value="#F5F5F5"/>
                        <Setter Property="BorderThickness"
                                Value="0"/>
                        <Setter Property="Padding"
                                Value="15,12"/>
                        <Setter Property="FontSize"
                                Value="14"/>
                        <Setter Property="FontFamily"
                                Value="Droid Arabic Kufi"/>
                        <Setter Property="Template">
                                <Setter.Value>
                                        <ControlTemplate TargetType="PasswordBox">
                                                <Border Background="{TemplateBinding Background}"
                                                        CornerRadius="5">
                                                        <ScrollViewer x:Name="PART_ContentHost"
                                                                      Margin="{TemplateBinding Padding}"
                                                                      FlowDirection="LeftToRight"/>
                                                </Border>
                                        </ControlTemplate>
                                </Setter.Value>
                        </Setter>
                </Style>

                <Style TargetType="Button">
                        <Setter Property="Background"
                                Value="#0078D4"/>
                        <Setter Property="Foreground"
                                Value="White"/>
                        <Setter Property="BorderThickness"
                                Value="0"/>
                        <Setter Property="Padding"
                                Value="15,12"/>
                        <Setter Property="FontSize"
                                Value="14"/>
                        <Setter Property="FontWeight"
                                Value="SemiBold"/>
                        <Setter Property="FontFamily"
                                Value="Droid Arabic Kufi"/>
                        <Setter Property="Template">
                                <Setter.Value>
                                        <ControlTemplate TargetType="Button">
                                                <Border Background="{TemplateBinding Background}"
                                                        CornerRadius="5">
                                                        <ContentPresenter HorizontalAlignment="Center"
                                                                          VerticalAlignment="Center"/>
                                                </Border>
                                        </ControlTemplate>
                                </Setter.Value>
                        </Setter>
                        <Style.Triggers>
                                <Trigger Property="IsMouseOver"
                                         Value="True">
                                        <Setter Property="Background"
                                                Value="#106EBE"/>
                                </Trigger>
                        </Style.Triggers>
                </Style>
        </Window.Resources>

        <Grid>
                <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="400"/>
                        <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <!-- Right Side - Login Form -->
                <Grid Grid.Column="0"
                      Background="White">
                        <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="*"/>
                        </Grid.RowDefinitions>

                        <!-- Close Button -->
                        <Button Grid.Row="0"
                                Content="✕"
                                Background="Transparent"
                                Foreground="#666666"
                                HorizontalAlignment="Left"
                                Margin="10"
                                Width="30"
                                Height="30"
                                Click="CloseButton_Click"/>

                        <!-- Login Form -->
                        <StackPanel Grid.Row="1"
                                    VerticalAlignment="Center"
                                    Margin="40,0">
                                <TextBlock Text="مرحباً بعودتك"
                                           FontSize="24"
                                           FontWeight="Bold"
                                           Margin="0,0,0,30"
                                           Style="{StaticResource ArabicTextStyle}"/>

                                <TextBlock Text="اسم المستخدم"
                                           FontSize="14"
                                           Margin="0,0,0,5"
                                           Style="{StaticResource ArabicTextStyle}"/>
                <TextBox Text="{Binding Email, UpdateSourceTrigger=PropertyChanged}" Height="40" Padding="5"
                                         Margin="0,0,0,20"/>

                                <TextBlock Text="كلمة المرور"
                                           FontSize="14"
                                           Margin="0,0,0,5"
                                           Style="{StaticResource ArabicTextStyle}"/>
                <PasswordBox x:Name="PasswordBox" Height="40" Padding="5"
                                             PasswordChanged="PasswordBox_PasswordChanged"
                                             Style="{StaticResource PasswordBoxStyle}"
                                             Margin="0,0,0,20">
                                        <PasswordBox.InputBindings>
                                                <KeyBinding Key="Enter"
                                                            Command="{Binding LoginCommand}"/>
                                        </PasswordBox.InputBindings>
                                </PasswordBox>

                                <CheckBox Content="تذكرني"
                                          IsChecked="{Binding RememberMe}"
                                          Margin="0,0,0,20"
                                          FontFamily="Droid Arabic Kufi"/>

                                <TextBlock Text="{Binding ErrorMessage}"
                                           Foreground="Red"
                                           TextWrapping="Wrap"
                                           Margin="0,0,0,20"
                                           Style="{StaticResource ArabicTextStyle}"
                                           Visibility="{Binding HasError, Converter={StaticResource BooleanToVisibilityConverter}}"/>

                                <Button Content="تسجيل الدخول"
                                        Grid.Row="6"
                                        Command="{Binding LoginCommand}"
                                        Height="45"
                                        FontSize="16"/>

                                <TextBlock Text="نسيت كلمة المرور؟"
                                           Foreground="#0078D4"
                                           Margin="0,20,0,0"
                                           HorizontalAlignment="Center"
                                           Cursor="Hand"
                                           Style="{StaticResource ArabicTextStyle}"
                                           MouseLeftButtonDown="ForgotPassword_MouseLeftButtonDown"/>
                        </StackPanel>
                </Grid>

                <!-- Left Side - Branding -->
                <Grid Grid.Column="1"
                      Background="#0078D4">
                        <StackPanel VerticalAlignment="Center"
                                    HorizontalAlignment="Center"
                                    Margin="50">
                                <TextBlock Text="ExactCash"
                                           Foreground="White"
                                           FontSize="48"
                                           FontWeight="Bold"
                                           Margin="0,0,0,20"/>
                                <TextBlock Text="تبسيط عملياتك المالية"
                                           Foreground="White"
                                           FontSize="24"
                                           TextWrapping="Wrap"
                                           TextAlignment="Center"
                                           Style="{StaticResource ArabicTextStyle}"/>
                        </StackPanel>
                </Grid>
        </Grid>
</Window> 