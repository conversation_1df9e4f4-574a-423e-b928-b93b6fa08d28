<Window x:Class="ExactCash.WPF.Views.Customer.AddCustomerCategoryView"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        mc:Ignorable="d"
        Title="إضافة تصنيف عميل"
        Height="400"
        Width="500"
        WindowStartupLocation="CenterOwner"
        FlowDirection="RightToLeft"
        Background="#f8f9fa"
        WindowStyle="None"
        AllowsTransparency="True"
        ResizeMode="NoResize">

    <Window.Resources>
        <Style x:Key="ModernButton" TargetType="Button">
            <Setter Property="Background" Value="#0078D4"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="15,8"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                CornerRadius="4">
                            <ContentPresenter HorizontalAlignment="Center"
                                              VerticalAlignment="Center"/>
                        </Border>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#106EBE"/>
                </Trigger>
            </Style.Triggers>
        </Style>

        <Style x:Key="SuccessButton" TargetType="Button" BasedOn="{StaticResource ModernButton}">
            <Setter Property="Background" Value="#28a745"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#218838"/>
                </Trigger>
            </Style.Triggers>
        </Style>

        <Style x:Key="ModernTextBox" TargetType="TextBox">
            <Setter Property="Height" Value="35"/>
            <Setter Property="Padding" Value="10,5"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="BorderBrush" Value="#CCCCCC"/>
            <Setter Property="Background" Value="White"/>
            <Setter Property="FontSize" Value="14"/>
        </Style>

        <DropShadowEffect x:Key="DropShadowEffect"
                          ShadowDepth="2"
                          Direction="270"
                          Color="Black"
                          Opacity="0.3"
                          BlurRadius="5"/>
    </Window.Resources>

    <Border Background="White"
            CornerRadius="8"
            BorderBrush="#DDDDDD"
            BorderThickness="1"
            Effect="{StaticResource DropShadowEffect}">
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <!-- Header -->
            <Border Grid.Row="0"
                    Background="#0078D4"
                    BorderThickness="0"
                    CornerRadius="8,8,0,0">
                <Grid Margin="20,15">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <TextBlock x:Name="TitleTextBlock"
                               Text="إضافة تصنيف عميل"
                               FontSize="18"
                               FontWeight="SemiBold"
                               Foreground="White"/>

                    <Button Grid.Column="1"
                            Content="✕"
                            Click="CancelButton_Click"
                            Style="{StaticResource NotificationCloseButtonStyle}"/>
                </Grid>
            </Border>

            <!-- Content -->
            <StackPanel Grid.Row="1" Margin="30,20">
                <!-- Name Field -->
                <StackPanel Margin="0,0,0,20">
                    <TextBlock Text="اسم التصنيف *"
                               FontWeight="SemiBold"
                               Margin="0,0,0,5"/>
                    <TextBox x:Name="NameTextBox"
                             Style="{StaticResource ModernTextBox}"
                             FlowDirection="RightToLeft"/>
                </StackPanel>

                <!-- Description Field -->
                <StackPanel Margin="0,0,0,20">
                    <TextBlock Text="الوصف"
                               FontWeight="SemiBold"
                               Margin="0,0,0,5"/>
                    <TextBox x:Name="DescriptionTextBox"
                             Style="{StaticResource ModernTextBox}"
                             Height="80"
                             TextWrapping="Wrap"
                             AcceptsReturn="True"
                             VerticalScrollBarVisibility="Auto"
                             FlowDirection="RightToLeft"/>
                </StackPanel>

                <!-- Required Field Note -->
                <TextBlock Text="* الحقول المطلوبة"
                           FontSize="12"
                           Foreground="Gray"
                           Margin="0,10,0,0"/>
            </StackPanel>

            <!-- Footer Buttons -->
            <Border Grid.Row="2"
                    Background="#F8F9FA"
                    BorderThickness="0,1,0,0"
                    BorderBrush="#DDDDDD"
                    CornerRadius="0,0,8,8">
                <StackPanel Orientation="Horizontal"
                            HorizontalAlignment="Left"
                            Margin="30,15">
                    <Button x:Name="SaveButton"
                            Content="حفظ"
                            Click="SaveButton_Click"
                            Style="{StaticResource SuccessButton}"
                            Width="100"
                            Margin="0,0,10,0"/>
                    <Button Content="إلغاء"
                            Click="CancelButton_Click"
                            Style="{StaticResource ModernButton}"
                            Background="#6c757d"
                            Width="100"/>
                </StackPanel>
            </Border>
        </Grid>
    </Border>
</Window>
