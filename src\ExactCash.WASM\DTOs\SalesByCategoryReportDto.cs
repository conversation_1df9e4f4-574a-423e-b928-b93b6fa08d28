﻿#nullable disable

namespace ExactCash.WASM.Application.DTOs
{
    public class SalesByCategoryReportDto
    {
        public int CategoryId { get; set; }
        public string CategoryName { get; set; }
        public decimal TotalSales { get; set; }
        public decimal QuantitySold { get; set; }
        public int ProductCount { get; set; }
        public int TransactionCount { get; set; }
        public decimal AverageTransactionValue { get; set; }
        public decimal PercentageOfTotalSales { get; set; }
        public DateTime ReportStartDate { get; set; }
        public DateTime ReportEndDate { get; set; }

        // Formatted properties for display
        public string FormattedTotalSales => $"{TotalSales:N2} ج.م";
        public string FormattedQuantitySold => $"{QuantitySold:N2}";
        public string FormattedAverageTransactionValue => $"{AverageTransactionValue:N2} ج.م";
        public string FormattedPercentageOfTotalSales => $"{PercentageOfTotalSales:N2}%";

        public SalesByCategoryReportDto()
        {

        }
    }
}
