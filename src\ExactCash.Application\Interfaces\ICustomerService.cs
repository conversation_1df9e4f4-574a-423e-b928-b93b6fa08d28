using ExactCash.Application.DTOs;
using ExactCash.Application.DTOs.Common;
using ExactCash.Application.Responses;

namespace ExactCash.Application.Interfaces
{
    public interface ICustomerService
    {
        Task<PagedResponse<CustomerDto>> GetAllCustomersAsync(string name, string phone, int? categoryId, PaginationFilter pagination);
        Task<List<CustomerDto>> GetAllCustomersAsync();
        Task<CustomerDto> GetCustomerByIdAsync(int id);
        Task<CustomerDto> CreateCustomerAsync(CustomerDto customerDto, string createdBy);
        Task<BaseResponse<bool>> UpdateCustomerAsync(CustomerDto customerDto);
        Task DeleteCustomerAsync(int id);
        Task<CustomerDto> SearchCustomersAsync(string searchTerm);
    }
}