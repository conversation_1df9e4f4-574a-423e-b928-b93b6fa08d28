﻿using System;
using System.Text.Json;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Media;
using ExactCash.WPF.Services;
using MessageBox = System.Windows.MessageBox;
using Color = System.Windows.Media.Color;
using ColorConverter = System.Windows.Media.ColorConverter;

namespace ExactCash.WPF.Views.Reports
{
    public partial class ProfitLossReportWindow : Window
    {
        private readonly ApiService _apiService;
        private readonly DateTime? _startDate;
        private readonly DateTime? _endDate;

        public ProfitLossReportWindow(DateTime? startDate, DateTime? endDate)
        {
            InitializeComponent();
            _apiService = new ApiService();
            _startDate = startDate;
            _endDate = endDate;

            SetDateRangeText();
            LoadProfitLossData();
        }

        private void SetDateRangeText()
        {
            if (_startDate.HasValue && _endDate.HasValue)
            {
                DateRangeText.Text = $"من {_startDate.Value:yyyy-MM-dd} إلى {_endDate.Value:yyyy-MM-dd}";
            }
            else
            {
                DateRangeText.Text = "جميع الفترات";
            }
        }

        private async void LoadProfitLossData()
        {
            try
            {
                // Build query parameters
                var queryParams = "";
                if (_startDate.HasValue)
                    queryParams += $"startDate={_startDate.Value:yyyy-MM-dd}&";
                if (_endDate.HasValue)
                    queryParams += $"endDate={_endDate.Value:yyyy-MM-dd}&";

                queryParams = queryParams.TrimEnd('&');

                var endpoint = $"api/Reports/profit-loss";
                if (!string.IsNullOrEmpty(queryParams))
                    endpoint += $"?{queryParams}";

                // Call the API to get profit loss data
                var response = await _apiService.GetAsync(endpoint);

                if (response.IsSuccessStatusCode)
                {
                    var jsonContent = await response.Content.ReadAsStringAsync();
                    var options = new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    };

                    var profitLossData = JsonSerializer.Deserialize<ProfitLossReportData>(jsonContent, options);

                    // Update UI with data
                    UpdateUI(profitLossData);
                }
                else
                {
                    MessageBox.Show("فشل في تحميل بيانات الأرباح والخسائر", "خطأ",
                                  MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء تحميل البيانات: {ex.Message}", "خطأ",
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void UpdateUI(ProfitLossReportData data)
        {
            // Revenue section
            TotalRevenueText.Text = $"{data.TotalRevenue:N2} ج.م";
            TotalDiscountsText.Text = $"{data.TotalDiscounts:N2} ج.م";

            // Cost of goods sold
            TotalCOGSText.Text = $"{data.TotalCostOfGoodsSold:N2} ج.م";

            // Gross profit
            GrossProfitText.Text = $"{data.GrossProfit:N2} ج.م";
            GrossProfitText.Foreground = data.GrossProfit >= 0 ?
                new SolidColorBrush((Color)ColorConverter.ConvertFromString("#4CAF50")) :
                new SolidColorBrush((Color)ColorConverter.ConvertFromString("#F44336"));

            // Operating expenses
            TotalExpensesText.Text = $"{data.TotalExpenses:N2} ج.م";
            TotalTaxText.Text = $"{data.TotalTax:N2} ج.م";

            // Net profit
            NetProfitText.Text = $"{data.NetProfit:N2} ج.م";
            NetProfitText.Foreground = data.NetProfit >= 0 ?
                new SolidColorBrush((Color)ColorConverter.ConvertFromString("#4CAF50")) :
                new SolidColorBrush((Color)ColorConverter.ConvertFromString("#F44336"));

            // Key metrics
            GrossProfitMarginText.Text = $"{data.GrossProfitMargin:N1}%";
            NetProfitMarginText.Text = $"{data.NetProfitMargin:N1}%";
            TotalTransactionsText.Text = data.TotalTransactions.ToString();
            AverageTransactionValueText.Text = $"{data.AverageTransactionValue:N2} ج.م";
        }

        private void RefreshData_Click(object sender, RoutedEventArgs e)
        {
            LoadProfitLossData();
        }

        private void PrintReport_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // For now, just show a message
                MessageBox.Show("سيتم تطوير وظيفة الطباعة قريباً", "طباعة",
                              MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء الطباعة: {ex.Message}", "خطأ",
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void Close_Click(object sender, RoutedEventArgs e)
        {
            this.Close();
        }
    }

    // Data model for profit loss report
    public class ProfitLossReportData
    {
        public DateTime ReportDate { get; set; }
        public decimal TotalRevenue { get; set; }
        public decimal TotalCostOfGoodsSold { get; set; }
        public decimal GrossProfit { get; set; }
        public decimal TotalExpenses { get; set; }
        public decimal NetProfit { get; set; }
        public decimal GrossProfitMargin { get; set; }
        public decimal NetProfitMargin { get; set; }
        public decimal TotalTax { get; set; }
        public decimal TotalDiscounts { get; set; }
        public int TotalTransactions { get; set; }
        public decimal AverageTransactionValue { get; set; }

        // Formatted properties for display
        public string FormattedTotalRevenue => $"{TotalRevenue:N2} ج.م";
        public string FormattedTotalCostOfGoodsSold => $"{TotalCostOfGoodsSold:N2} ج.م";
        public string FormattedGrossProfit => $"{GrossProfit:N2} ج.م";
        public string FormattedTotalExpenses => $"{TotalExpenses:N2} ج.م";
        public string FormattedNetProfit => $"{NetProfit:N2} ج.م";
        public string FormattedGrossProfitMargin => $"{GrossProfitMargin:N1}%";
        public string FormattedNetProfitMargin => $"{NetProfitMargin:N1}%";
        public string FormattedTotalTax => $"{TotalTax:N2} ج.م";
        public string FormattedTotalDiscounts => $"{TotalDiscounts:N2} ج.م";
        public string FormattedAverageTransactionValue => $"{AverageTransactionValue:N2} ج.م";

        public string ProfitColor => NetProfit >= 0 ? "#28A745" : "#DC3545";
    }
}
