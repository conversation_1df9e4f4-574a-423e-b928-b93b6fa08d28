using ExactCash.Application.DTOs;
using ExactCash.Application.Interfaces;
using Microsoft.AspNetCore.Identity;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.IdentityModel.Tokens;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Text;
#nullable disable

namespace ExactCash.Application.Services
{
    public class AuthService : IAuthService
    {
        private readonly UserManager<IdentityUser> _userManager;
        private readonly IConfiguration _configuration;
        private readonly ILogger<AuthService> _logger;
        private readonly IUserService _userService;

        public AuthService(
            UserManager<IdentityUser> userManager,
            IConfiguration configuration,
            ILogger<AuthService> logger,
            IUserService userService)
        {
            _userManager = userManager;
            _configuration = configuration;
            _logger = logger;
            _userService = userService;
        }

        public async Task<AuthResponseDto> LoginAsync(LoginDto loginDto)
        {
            var user = await _userManager.FindByEmailAsync(loginDto.Email);
            if (user == null)
            {
                _logger.LogWarning("Login attempt failed: User not found with email {Email}", loginDto.Email);
                return null;
            }

            if (!await _userManager.CheckPasswordAsync(user, loginDto.Password))
            {
                _logger.LogWarning("Login attempt failed: Invalid password for user {UserId}", user.Id);
                return null;
            }

            var userDto = await _userService.GetUserByIdAsync(user.Id);
            var token = await GenerateJwtToken(user);
            var refreshToken = GenerateRefreshToken();

            // Store refresh token in user claims
            await _userManager.RemoveClaimsAsync(user, new[] { new Claim("RefreshToken", "") });
            await _userManager.AddClaimAsync(user, new Claim("RefreshToken", refreshToken));

            _logger.LogInformation("User {UserId} logged in successfully", user.Id);

            return new AuthResponseDto
            {
                Token = token,
                RefreshToken = refreshToken,
                Expiration = DateTime.UtcNow.AddDays(Convert.ToDouble(_configuration["JWT:DurationInDays"])),
                User = userDto
            };
        }

        public async Task<AuthResponseDto> RegisterAsync(RegisterDto registerDto)
        {
            var user = new IdentityUser
            {
                UserName = registerDto.UserName,
                Email = registerDto.Email,
                PhoneNumber = registerDto.PhoneNumber
            };

            var result = await _userManager.CreateAsync(user, registerDto.Password);
            if (!result.Succeeded)
            {
                _logger.LogError("User registration failed: {Errors}",
                    string.Join(", ", result.Errors.Select(e => e.Description)));
                return null;
            }

            // Assign default role
            await _userManager.AddToRoleAsync(user, "User");

            var userDto = await _userService.GetUserByIdAsync(user.Id);
            var token = await GenerateJwtToken(user);
            var refreshToken = GenerateRefreshToken();

            await _userManager.AddClaimAsync(user, new Claim("RefreshToken", refreshToken));

            _logger.LogInformation("User {UserId} registered successfully", user.Id);

            return new AuthResponseDto
            {
                Token = token,
                RefreshToken = refreshToken,
                Expiration = DateTime.UtcNow.AddDays(Convert.ToDouble(_configuration["JWT:DurationInDays"])),
                User = userDto
            };
        }

        public async Task<AuthResponseDto> RefreshTokenAsync(RefreshTokenDto refreshTokenDto)
        {
            var principal = GetPrincipalFromExpiredToken(refreshTokenDto.Token);
            var userId = principal.FindFirst(ClaimTypes.NameIdentifier)?.Value;

            if (string.IsNullOrEmpty(userId))
            {
                _logger.LogWarning("Token refresh failed: Invalid token");
                return null;
            }

            var user = await _userManager.FindByIdAsync(userId);
            if (user == null)
            {
                _logger.LogWarning("Token refresh failed: User not found");
                return null;
            }

            var refreshTokenClaim = (await _userManager.GetClaimsAsync(user))
                .FirstOrDefault(c => c.Type == "RefreshToken");

            if (refreshTokenClaim == null || refreshTokenClaim.Value != refreshTokenDto.RefreshToken)
            {
                _logger.LogWarning("Token refresh failed: Invalid refresh token");
                return null;
            }

            var userDto = await _userService.GetUserByIdAsync(user.Id);
            var newToken = await GenerateJwtToken(user);
            var newRefreshToken = GenerateRefreshToken();

            await _userManager.RemoveClaimsAsync(user, new[] { refreshTokenClaim });
            await _userManager.AddClaimAsync(user, new Claim("RefreshToken", newRefreshToken));

            _logger.LogInformation("Token refreshed successfully for user {UserId}", user.Id);

            return new AuthResponseDto
            {
                Token = newToken,
                RefreshToken = newRefreshToken,
                Expiration = DateTime.UtcNow.AddDays(Convert.ToDouble(_configuration["JWT:DurationInDays"])),
                User = userDto
            };
        }

        public async Task LogoutAsync(string userId)
        {
            var user = await _userManager.FindByIdAsync(userId);
            if (user != null)
            {
                var refreshTokenClaim = (await _userManager.GetClaimsAsync(user))
                    .FirstOrDefault(c => c.Type == "RefreshToken");

                if (refreshTokenClaim != null)
                {
                    await _userManager.RemoveClaimAsync(user, refreshTokenClaim);
                    _logger.LogInformation("User {UserId} logged out successfully", userId);
                }
            }
        }

        public async Task<bool> ValidateTokenAsync(string token)
        {
            try
            {
                var tokenHandler = new JwtSecurityTokenHandler();
                var key = Encoding.ASCII.GetBytes(_configuration["JWT:Key"]);

                tokenHandler.ValidateToken(token, new TokenValidationParameters
                {
                    ValidateIssuerSigningKey = true,
                    IssuerSigningKey = new SymmetricSecurityKey(key),
                    ValidateIssuer = true,
                    ValidateAudience = true,
                    ValidIssuer = _configuration["JWT:Issuer"],
                    ValidAudience = _configuration["JWT:Audience"],
                    ValidateLifetime = true,
                    ClockSkew = TimeSpan.Zero
                }, out SecurityToken validatedToken);

                return true;
            }
            catch
            {
                return false;
            }
        }

        private async Task<string> GenerateJwtToken(IdentityUser user)
        {
            var claims = new List<Claim>
            {
                new Claim(ClaimTypes.NameIdentifier, user.Id),
                new Claim(ClaimTypes.Email, user.Email),
                new Claim(ClaimTypes.Name, user.UserName)
            };

            var roles = await _userManager.GetRolesAsync(user);
            foreach (var role in roles)
            {
                claims.Add(new Claim(ClaimTypes.Role, role));
            }

            var key = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(_configuration["JWT:Key"]));
            var creds = new SigningCredentials(key, SecurityAlgorithms.HmacSha256);

            var token = new JwtSecurityToken(
                issuer: _configuration["JWT:Issuer"],
                audience: _configuration["JWT:Audience"],
                claims: claims,
                expires: DateTime.UtcNow.AddDays(Convert.ToDouble(_configuration["JWT:DurationInDays"])),
                signingCredentials: creds
            );

            return new JwtSecurityTokenHandler().WriteToken(token);
        }

        private string GenerateRefreshToken()
        {
            return Guid.NewGuid().ToString();
        }

        private ClaimsPrincipal GetPrincipalFromExpiredToken(string token)
        {
            var tokenValidationParameters = new TokenValidationParameters
            {
                ValidateIssuerSigningKey = true,
                IssuerSigningKey = new SymmetricSecurityKey(Encoding.ASCII.GetBytes(_configuration["JWT:Key"])),
                ValidateIssuer = true,
                ValidateAudience = true,
                ValidIssuer = _configuration["JWT:Issuer"],
                ValidAudience = _configuration["JWT:Audience"],
                ValidateLifetime = false
            };

            var tokenHandler = new JwtSecurityTokenHandler();
            var principal = tokenHandler.ValidateToken(token, tokenValidationParameters, out SecurityToken securityToken);

            if (!(securityToken is JwtSecurityToken jwtSecurityToken) ||
                !jwtSecurityToken.Header.Alg.Equals(SecurityAlgorithms.HmacSha256, StringComparison.InvariantCultureIgnoreCase))
            {
                throw new SecurityTokenException("Invalid token");
            }

            return principal;
        }
    }
}