using ExactCash.Application.DTOs;
using ExactCash.WPF.ViewModels.Sale;
using ExactCash.WPF.Services;
using Microsoft.Extensions.DependencyInjection;
using System.Windows;

namespace ExactCash.WPF.Views.Sale
{
    public partial class CustomerSelectionView : Window
    {
        public CustomerDto SelectedCustomer { get; private set; }

        public CustomerSelectionView()
        {
            InitializeComponent();
            
            var customerService = ((App)System.Windows.Application.Current).ServiceProvider.GetRequiredService<ICustomerService>();
            var customerCategoryService = ((App)System.Windows.Application.Current).ServiceProvider.GetRequiredService<ICustomerCategoryServiceClient>();
            
            var viewModel = new CustomerSelectionViewModel(customerService, customerCategoryService);
            viewModel.CustomerSelected += OnCustomerSelected;
            viewModel.CancelRequested += OnCancelRequested;
            
            DataContext = viewModel;
        }

        private void OnCustomerSelected(CustomerDto customer)
        {
            SelectedCustomer = customer;
            DialogResult = true;
            Close();
        }

        private void OnCancelRequested()
        {
            DialogResult = false;
            Close();
        }
    }
}
