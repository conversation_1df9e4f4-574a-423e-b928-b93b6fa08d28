<UserControl x:Class="ExactCash.WPF.Views.Common.LoadingView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:local="clr-namespace:ExactCash.WPF.Views.Common"
             mc:Ignorable="d"
             d:DesignHeight="100"
        d:DesignWidth="300">
    <Grid>
        <Border Background="#CC000000"
                CornerRadius="5">
            <StackPanel HorizontalAlignment="Center"
                        VerticalAlignment="Center"
                        Margin="20">
                <!-- Loading Spinner -->
                <ProgressBar Style="{StaticResource LoadingSpinnerStyle}"
                             Width="40"
                             Height="40"
                             Margin="0,0,0,10"/>

                <!-- Loading Message -->
                <TextBlock Text="{Binding Message}"
                           FontFamily="Droid Arabic Kufi"
                           FontSize="14"
                           Foreground="White"
                           HorizontalAlignment="Center"/>
            </StackPanel>
        </Border>
    </Grid>
</UserControl> 