﻿#nullable disable
using ExactCash.Domain.Common;
using System.Text.Json.Serialization;

namespace ExactCash.Domain.Entities
{
    /// <summary>
    /// Represents a unit of measurement in the system (e.g., pcs, kg, liter).
    /// </summary>
    public class Unit : BaseEntity
    {
        /// <summary>
        /// The name of the unit (e.g., "Piece", "Kilogram", "Liter").
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// The symbol or abbreviation of the unit (e.g., "pcs", "kg", "L").
        /// </summary>
        public string Symbol { get; set; }

        /// <summary>
        /// Indicates whether the unit is active and available for use.
        /// </summary>
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// // If you need to convert units (optional)
        /// </summary>
        public decimal ConversionRateToBaseUnit { get; set; }

        /// <summary>
        /// Description
        /// </summary>
        public string Description { get; set; }

        /// <summary>
        /// Collection of products that use this unit.
        /// </summary>
        [JsonIgnore]
        public ICollection<Product> Products { get; set; } = new List<Product>();

        public Unit() { }

        public Unit(string name, string symbol)
        {
            Name = name;
            Symbol = symbol;
            CreationDate = DateTime.UtcNow;
        }

        public void Update(decimal conversionRateToBaseUnit, string name, string symbol, bool isActive)
        {
            ConversionRateToBaseUnit = conversionRateToBaseUnit;
            Name = name;
            Symbol = symbol;
            IsActive = isActive;
            LastUpdatedDate = DateTime.UtcNow;
        }
    }
}
