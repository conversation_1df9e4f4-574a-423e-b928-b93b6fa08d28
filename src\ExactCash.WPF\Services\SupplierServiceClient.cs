﻿using ExactCash.Application.DTOs;
using ExactCash.Application.DTOs.Common;
using ExactCash.Application.Responses;

namespace ExactCash.WPF.Services
{
    public class SupplierServiceClient : ISupplierServiceClient
    {
        private readonly HttpService _httpService;

        public SupplierServiceClient(string baseUrl)
        {
            _httpService = new HttpService(baseUrl);
        }
        public async Task<BaseResponse<SupplierDto>> CreateSupplierAsync(SupplierDto supplierDto)
        {
            return await _httpService.PostAsync<BaseResponse<SupplierDto>>("api/Suppliers", supplierDto);
        }

        public async Task DeleteSupplierAsync(int id)
        {
            await _httpService.DeleteAsync($"api/Suppliers/{id}");
        }

        public async Task<PagedResponse<SupplierDto>> GetAllSuppliersAsync(string name, string phone, PaginationFilter pagination)
        {
            var queryParams = new List<string>();

            if (!string.IsNullOrEmpty(name)) queryParams.Add($"name={Uri.EscapeDataString(name)}");
            if (!string.IsNullOrEmpty(phone)) queryParams.Add($"phone={Uri.EscapeDataString(phone)}");

            if (pagination != null)
            {
                queryParams.Add($"pageNumber={pagination.PageNumber}");
                queryParams.Add($"pageSize={pagination.PageSize}");
            }

            var queryString = string.Join("&", queryParams);
            var url = $"api/Suppliers?{queryString}";

            return await _httpService.GetAsync<PagedResponse<SupplierDto>>(url);
        }

        public async Task<IEnumerable<SupplierDto>> GetAllSuppliersAsync(string name, string phone) {
            var queryParams = new List<string>();

            if (!string.IsNullOrEmpty(name)) queryParams.Add($"name={Uri.EscapeDataString(name)}");
            if (!string.IsNullOrEmpty(phone)) queryParams.Add($"phone={Uri.EscapeDataString(phone)}");

            var queryString = string.Join("&", queryParams);
            var url = $"api/Suppliers/get-all-suppliers?{queryString}";

            return await _httpService.GetAsync<IEnumerable<SupplierDto>>(url);
        }


        public async Task<SupplierDto> GetSupplierByIdAsync(int id)
        {
           return await _httpService.GetAsync<SupplierDto>($"api/Suppliers/{id}");
        }

        public async Task<List<SupplierDto>> SearchSuppliersAsync(string searchTerm)
        {
            return await _httpService.GetAsync<List<SupplierDto>>($"api/Suppliers/search?term={searchTerm}");
        }

        public async Task<BaseResponse<SupplierDto>> UpdateSupplierAsync(SupplierDto supplierDto)
        {
          return  await _httpService.PutAsync<BaseResponse<SupplierDto>>($"api/Suppliers/{supplierDto.Id}", supplierDto);
        }
    }
}
