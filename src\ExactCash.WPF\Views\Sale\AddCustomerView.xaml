<Window x:Class="ExactCash.WPF.Views.Sale.AddCustomerView"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:ExactCash.WPF.Views.Sale"
        mc:Ignorable="d" x:Name="AddCustomerScreen"
        Title="{Binding WindowTitle}"
        Height="520"
        Width="400"
        WindowStartupLocation="CenterOwner"
        WindowStyle="None"
        ResizeMode="NoResize"
        Background="White"
        FlowDirection="RightToLeft">

        <Window.Resources>
                <Style x:Key="ModernButton"
                       TargetType="Button">
                        <Setter Property="Background"
                                Value="#0078D4"/>
                        <Setter Property="Foreground"
                                Value="White"/>
                        <Setter Property="BorderThickness"
                                Value="0"/>
                        <Setter Property="Padding"
                                Value="15,8"/>
                        <Setter Property="FontSize"
                                Value="14"/>
                        <Setter Property="Template">
                                <Setter.Value>
                                        <ControlTemplate TargetType="Button">
                                                <Border Background="{TemplateBinding Background}"
                                                        BorderThickness="{TemplateBinding BorderThickness}"
                                                        BorderBrush="{TemplateBinding BorderBrush}"
                                                        CornerRadius="4">
                                                        <ContentPresenter HorizontalAlignment="Center"
                                                                          VerticalAlignment="Center"/>
                                                </Border>
                                        </ControlTemplate>
                                </Setter.Value>
                        </Setter>
                        <Style.Triggers>
                                <Trigger Property="IsMouseOver"
                                         Value="True">
                                        <Setter Property="Background"
                                                Value="#106EBE"/>
                                </Trigger>
                        </Style.Triggers>
                </Style>

                <Style x:Key="ModernTextBox"
                       TargetType="TextBox">
                        <Setter Property="Height"
                                Value="35"/>
                        <Setter Property="Padding"
                                Value="10,5"/>
                        <Setter Property="BorderThickness"
                                Value="1"/>
                        <Setter Property="BorderBrush"
                                Value="#CCCCCC"/>
                        <Setter Property="Background"
                                Value="White"/>
                        <Setter Property="FontSize"
                                Value="14"/>
                </Style>
        </Window.Resources>

        <Border BorderBrush="#DEE2E6"
                BorderThickness="1"
                CornerRadius="8">
                <Grid>
                        <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="*"/>
                                <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>

                        <!-- Header -->
                        <Border Background="#0078D4"
                                Grid.Row="0"
                                CornerRadius="8,8,0,0">
                                <Grid Margin="20,15">
                                        <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="*"/>
                                                <ColumnDefinition Width="Auto"/>
                                        </Grid.ColumnDefinitions>
                                        <TextBlock Text="{Binding WindowTitle}"
                                                   Foreground="White"
                                                   FontSize="18"
                                                   FontWeight="SemiBold"/>
                                        <Button Grid.Column="1"
                                                Content="✕"
                                                Foreground="White"
                                                Background="Transparent"
                                                BorderThickness="0"
                                                FontSize="16"
                                                Click="CloseButton_Click"/>
                                </Grid>
                        </Border>

                        <!-- Content -->
                        <StackPanel Grid.Row="1"
                                    Margin="20">
                                <TextBlock Text="اسم العميل *"
                                           Margin="0,0,0,5"/>
                                <TextBox Text="{Binding NewCustomerName, UpdateSourceTrigger=PropertyChanged}"
                                         Height="35"
                                         Padding="10,0"
                                         VerticalContentAlignment="Center"
                                         Margin="0,0,0,15"/>

                                <TextBlock Text="رقم الهاتف *"
                                           Margin="0,0,0,5"/>
                                <TextBox Text="{Binding NewCustomerPhone, UpdateSourceTrigger=PropertyChanged}"
                                         Height="35"
                                         Padding="10,0"
                                         VerticalContentAlignment="Center"
                                         Margin="0,0,0,15"/>

                                <TextBlock Text="البريد الإلكتروني"
                                           Margin="0,0,0,5"/>
                                <TextBox Text="{Binding NewCustomerEmail, UpdateSourceTrigger=PropertyChanged}"
                                         Height="35"
                                         Padding="10,0"
                                         VerticalContentAlignment="Center"
                                         Margin="0,0,0,15"/>

                                <TextBlock Text="العنوان"
                                           Margin="0,0,0,5"/>
                                <TextBox Text="{Binding NewCustomerAddress, UpdateSourceTrigger=PropertyChanged}"
                                         Height="35"
                                         Padding="10,0"
                                         VerticalContentAlignment="Center"
                                         Margin="0,0,0,15"/>

                                <TextBlock Text="تصنيف العميل"
                                           Margin="0,0,0,5"/>
                                <ComboBox ItemsSource="{Binding Categories}"
                                          SelectedItem="{Binding SelectedCategory}"
                                          DisplayMemberPath="Name"
                                          Height="35"
                                          Margin="0,0,0,0"
                                          FlowDirection="RightToLeft"
                                          Background="White"
                                          BorderBrush="#CCCCCC"
                                          BorderThickness="1"
                                          FontSize="14"/>
                        </StackPanel>

                        <!-- Footer -->
                        <Border Grid.Row="2"
                                Background="#F8F9FA"
                                BorderThickness="0,1,0,0"
                                BorderBrush="#DEE2E6"
                                CornerRadius="0,0,8,8">
                                <StackPanel Orientation="Horizontal"
                                            HorizontalAlignment="Center"
                                            Margin="20,15">
                                        <Button Content="{Binding SaveButtonText}"
                                                Command="{Binding SaveCustomerCommand}"
                                                Style="{StaticResource ModernButton}"
                                                MinWidth="120"
                                                Height="35"
                                                Margin="0,0,10,0"/>
                                        <Button Content="إلغاء"
                                                Command="{Binding CancelCommand}"
                                                Style="{StaticResource ModernButton}"
                                                Background="#6C757D"
                                                MinWidth="120"
                                                Height="35"/>
                                </StackPanel>
                        </Border>
                </Grid>
        </Border>
</Window>