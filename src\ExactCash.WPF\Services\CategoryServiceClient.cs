﻿using ExactCash.Application.DTOs;
using ExactCash.Domain.Entities;

namespace ExactCash.WPF.Services
{
    public class CategoryServiceClient : ICategoryServiceClient
    {
        private readonly HttpService _httpService;

        public CategoryServiceClient(string baseUrl)
        {
            _httpService = new HttpService(baseUrl);
        }

        public async Task<Category> CreateAsync(Category category)
        {
           return await _httpService.PostAsync<Category>("api/Category", category);
        }

        public async Task<bool> DeleteAsync(int id)
        {
            return await _httpService.DeleteAsync($"api/Category/{id}");
        }

        public async Task<IEnumerable<CategoryDto>> GetAllAsync()
        {
            return await _httpService.GetAsync<IEnumerable<CategoryDto>>("api/Category");
        }

        public async Task<Category> GetByIdAsync(int id)
        {
            return await _httpService.GetAsync<Category>($"api/Category/{id}");
        }

        public async Task<Category> UpdateAsync(Category category)
        {
            return await _httpService.PutAsync<Category>("api/Category", category);
        }
    }
}
