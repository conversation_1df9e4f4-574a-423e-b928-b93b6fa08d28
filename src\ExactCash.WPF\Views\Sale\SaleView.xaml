<Window x:Name="SaleViewScreen"
        x:Class="ExactCash.WPF.Views.Sale.SaleView"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:ExactCash.WPF.Views.Sale"
        xmlns:common="clr-namespace:ExactCash.WPF.ViewModels.Common"
        xmlns:converters="clr-namespace:ExactCash.WPF.Converters"
        mc:Ignorable="d"
        Title="نافذة البيع - ExactCash"
        WindowState="Maximized"
        WindowStyle="SingleBorderWindow"
        ResizeMode="CanResize"
        WindowStartupLocation="CenterScreen"
        FlowDirection="RightToLeft"
        Background="#F5F5F5">

    <Window.Resources>
        <converters:CountToVisibilityConverter x:Key="CountToVisibilityConverter"/>

        <!-- Modern Button Style -->
        <Style x:Key="ModernButton"
               TargetType="Button">
            <Setter Property="Background"
                    Value="#0078D4"/>
            <Setter Property="Foreground"
                    Value="White"/>
            <Setter Property="BorderThickness"
                    Value="0"/>
            <Setter Property="Padding"
                    Value="15,8"/>
            <Setter Property="FontSize"
                    Value="14"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                CornerRadius="4">
                            <ContentPresenter HorizontalAlignment="Center"
                                              VerticalAlignment="Center"/>
                        </Border>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
            <Style.Triggers>
                <Trigger Property="IsMouseOver"
                         Value="True">
                    <Setter Property="Background"
                            Value="#106EBE"/>
                </Trigger>
            </Style.Triggers>
        </Style>

        <!-- Modern TextBox Style -->
        <Style x:Key="ModernTextBox"
               TargetType="TextBox">
            <Setter Property="Height"
                    Value="35"/>
            <Setter Property="Padding"
                    Value="10,5"/>
            <Setter Property="BorderThickness"
                    Value="1"/>
            <Setter Property="BorderBrush"
                    Value="#CCCCCC"/>
            <Setter Property="Background"
                    Value="White"/>
            <Setter Property="FontSize"
                    Value="14"/>
        </Style>

        <!-- Modern ComboBox Style -->
        <Style x:Key="ModernComboBox"
               TargetType="ComboBox">
            <Setter Property="Height"
                    Value="35"/>
            <Setter Property="BorderThickness"
                    Value="1"/>
            <Setter Property="BorderBrush"
                    Value="#CCCCCC"/>
            <Setter Property="Background"
                    Value="White"/>
            <Setter Property="FontSize"
                    Value="14"/>
        </Style>

        <!-- Modern Border Style -->
        <Style x:Key="ModernBorder"
               TargetType="Border">
            <Setter Property="BorderBrush"
                    Value="#E0E0E0"/>
            <Setter Property="BorderThickness"
                    Value="1"/>
            <Setter Property="Background"
                    Value="White"/>
            <Setter Property="CornerRadius"
                    Value="4"/>
            <Setter Property="Padding"
                    Value="15"/>
        </Style>
    </Window.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- Modern Header -->
        <Grid Grid.Row="0"
              Background="#0078D4"
              Height="60">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>

            <TextBlock Text="نافـذة البيـع"
                       Foreground="White"
                       FontSize="20"
                       FontWeight="SemiBold"
                       VerticalAlignment="Center"
                       Margin="20,0"/>

            <StackPanel Grid.Column="1"
                        Orientation="Horizontal"
                        Margin="20,0">
                <TextBlock Text="{Binding StoreName}"
                           Foreground="White"
                           VerticalAlignment="Center"
                           FontSize="14"
                           Margin="0,0,20,0"/>
                <TextBlock Text="{Binding UserName}"
                           Foreground="White"
                           VerticalAlignment="Center"
                           FontSize="14"
                           Margin="0,0,10,0"/>
                <TextBlock Text="{Binding UserRole}"
                           Foreground="White"
                           VerticalAlignment="Center"
                           FontSize="14"/>
            </StackPanel>

            <Button Grid.Column="2"
                    Content="تسجيل الخروج"
                    Command="{Binding LogoutCommand}"
                    Style="{StaticResource ModernButton}"
                    Margin="20,0"/>
        </Grid>

        <!-- Customer Information Section -->
        <Border Grid.Row="1"
                Style="{StaticResource ModernBorder}"
                Margin="10">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <!-- Customer Search -->
                <TextBlock Text="بحث عن العميل"
                           Grid.Column="0"
                           VerticalAlignment="Center"
                           FontSize="14"
                           Margin="0,0,10,0"/>
                <Grid Grid.Column="1">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>
                    <TextBox x:Name="CustomerSearchBox"
                             Style="{StaticResource ModernTextBox}"
                             Text="{Binding SearchTerm, UpdateSourceTrigger=PropertyChanged}"
                             Margin="0,0,10,0"/>
                    <Button Grid.Column="1"
                            Content="البحث عن عميل"
                            Command="{Binding FindCustomerCommand}"
                            Style="{StaticResource ModernButton}"
                            Width="120"
                            Height="35"
                            Margin="0,0,5,0">
                        <Button.ContentTemplate>
                            <DataTemplate>
                                <TextBlock Text="{Binding}"
                                           Margin="10,0"/>
                            </DataTemplate>
                        </Button.ContentTemplate>
                    </Button>
                    <Button Grid.Column="2"
                            Content="عميل جديد"
                            Command="{Binding AddNewCustomerCommand}"
                            Style="{StaticResource ModernButton}"
                            Width="120"
                            Height="35"
                            Margin="5,0,10,0">
                        <Button.ContentTemplate>
                            <DataTemplate>
                                <TextBlock Text="{Binding}"
                                           Margin="10,0"/>
                            </DataTemplate>
                        </Button.ContentTemplate>
                    </Button>
                </Grid>

                <!-- Customer Name -->
                <TextBlock Text="اسم العميل"
                           Grid.Column="2"
                           VerticalAlignment="Center"
                           FontSize="14"
                           Margin="0,0,10,0"/>
                <TextBox Grid.Column="3"
                         Style="{StaticResource ModernTextBox}"
                         Text="{Binding CustomerName, Mode=OneWay}"
                         IsReadOnly="True"/>

                <!-- Customer Phone -->
                <TextBlock Text="رقم الهاتف"
                           Grid.Column="4"
                           VerticalAlignment="Center"
                           FontSize="14"
                           Margin="10,0,5,0"/>
                <TextBox x:Name="CustomerPhoneBox"
                         Grid.Column="5"
                         Style="{StaticResource ModernTextBox}"
                         IsReadOnly="True"
                         Text="{Binding CustomerPhone, Mode=OneWay}"
                         Margin="0,0,10,0"/>

                <!-- Discount and Loyalty Points -->
                <TextBlock Text="خصم العميل"
                           Grid.Column="6"
                           VerticalAlignment="Center"
                           FontSize="14"
                           Margin="0,0,10,0"/>
                <TextBox x:Name="CustomerDiscountBox"
                         Grid.Column="7"
                         Style="{StaticResource ModernTextBox}"
                         Text="{Binding CustomerDiscount, Mode=OneWay}"
                         IsReadOnly="True"/>
            </Grid>
        </Border>

        <!-- Main Content Area -->
        <Grid Grid.Row="2">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>

            <!-- Product Selection Section -->
            <Border Grid.Column="0"
                    Style="{StaticResource ModernBorder}"
                    Margin="10">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <!-- Product Search -->
                    <Grid Grid.Row="0">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>

                        <TextBlock Text="بحث عن المنتج"
                                   VerticalAlignment="Center"
                                   FontSize="14"
                                   Margin="0,0,10,10"/>
                        <ComboBox x:Name="ProductSearchBox"
                                  Grid.Column="1"
                                  ItemsSource="{Binding ProductSearchResults}"
                                  SelectedItem="{Binding SelectedProduct}"
                                  Style="{StaticResource ModernComboBox}"
                                  Margin="0,0,10,10"
                                  IsDropDownOpen="{Binding HasSearchResults, Mode=OneWay}"
                                  SelectionChanged="ProductSearchBox_SelectionChanged"
                                  DropDownOpened="ProductSearchBox_DropDownOpened">
                            <ComboBox.Template>
                                <ControlTemplate TargetType="ComboBox">
                                    <Grid>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="20"/>
                                        </Grid.ColumnDefinitions>
                                        <TextBox x:Name="PART_EditableTextBox"
                                                 Text="{Binding DataContext.ProductSearchTerm, UpdateSourceTrigger=PropertyChanged, RelativeSource={RelativeSource AncestorType=Window}}"
                                                 Style="{StaticResource ModernTextBox}"
                                                 Margin="0"
                                                 GotFocus="ProductTextBox_GotFocus"/>
                                        <ToggleButton x:Name="ToggleButton"
                                                      Grid.Column="1"
                                                      Background="{TemplateBinding Background}"
                                                      BorderBrush="{TemplateBinding BorderBrush}"
                                                      BorderThickness="{TemplateBinding BorderThickness}"
                                                      IsChecked="{Binding IsDropDownOpen, Mode=TwoWay, RelativeSource={RelativeSource TemplatedParent}}">
                                            <Path Fill="Gray"
                                                  HorizontalAlignment="Center"
                                                  VerticalAlignment="Center"
                                                  Data="M 0 0 L 4 4 L 8 0 Z"/>
                                        </ToggleButton>
                                        <Popup x:Name="Popup"
                                               Placement="Bottom"
                                               IsOpen="{TemplateBinding IsDropDownOpen}"
                                               AllowsTransparency="True"
                                               Focusable="False"
                                               PopupAnimation="Slide">
                                            <Grid x:Name="DropDown"
                                                  SnapsToDevicePixels="True"
                                                  MinWidth="{TemplateBinding ActualWidth}"
                                                  MaxHeight="{TemplateBinding MaxDropDownHeight}">
                                                <Border x:Name="DropDownBorder"
                                                        Background="White"
                                                        BorderThickness="1"
                                                        BorderBrush="#DDDDDD">
                                                    <ScrollViewer>
                                                        <ItemsPresenter SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}"/>
                                                    </ScrollViewer>
                                                </Border>
                                            </Grid>
                                        </Popup>
                                    </Grid>
                                </ControlTemplate>
                            </ComboBox.Template>
                            <ComboBox.ItemTemplate>
                                <DataTemplate>
                                    <StackPanel Orientation="Horizontal">
                                        <Image Source="{Binding ImagePath}"
                                               Width="30"
                                               Height="30"
                                               Margin="0,0,5,0"/>
                                        <TextBlock Text="{Binding Name}"
                                                   VerticalAlignment="Center"/>
                                    </StackPanel>
                                </DataTemplate>
                            </ComboBox.ItemTemplate>
                        </ComboBox>

                        <TextBlock Text="بحث بالباركود"
                                   Grid.Column="2"
                                   VerticalAlignment="Center"
                                   FontSize="14"
                                   Margin="0,0,10,10"/>
                        <TextBox x:Name="BarcodeSearchBox"
                                 Grid.Column="3"
                                 Style="{StaticResource ModernTextBox}"
                                 Text="{Binding BarcodeSearchTerm, UpdateSourceTrigger=PropertyChanged}"
                                 Margin="0,0,10,10"
                                 FocusManager.FocusedElement="{Binding RelativeSource={RelativeSource Self}}"
                                 PreviewKeyDown="BarcodeSearchBox_PreviewKeyDown">
                            <TextBox.InputBindings>
                                <KeyBinding Key="Return"
                                            Command="{Binding SearchByBarcodeCommand}"/>
                            </TextBox.InputBindings>
                        </TextBox>

                        <Button Grid.Column="4"
                                Content="إضافة منتج جديد"
                                Command="{Binding AddNewProductCommand}"
                                Style="{StaticResource ModernButton}"
                                Width="120"
                                Height="35"
                                Margin="10,0,0,10">
                            <Button.ContentTemplate>
                                <DataTemplate>
                                    <TextBlock Text="{Binding}"
                                               Margin="10,0"/>
                                </DataTemplate>
                            </Button.ContentTemplate>
                        </Button>
                    </Grid>

                    <!-- Selected Products DataGrid -->
                    <DataGrid Grid.Row="1"
                              ItemsSource="{Binding SelectedProducts}"
                              AutoGenerateColumns="False"
                              CanUserAddRows="False"
                              CanUserDeleteRows="True"
                              IsReadOnly="False"
                              GridLinesVisibility="All"
                              BorderThickness="1"
                              BorderBrush="#E5E5E5"
                              Background="White"
                              RowHeaderWidth="0"
                              SelectionMode="Single"
                              SelectionUnit="FullRow"
                              HeadersVisibility="Column"
                              Margin="0,10,0,0"
                              HorizontalGridLinesBrush="#E5E5E5"
                              VerticalGridLinesBrush="#E5E5E5">
                        <DataGrid.Resources>
                            <Style TargetType="DataGridColumnHeader">
                                <Setter Property="Background"
                                        Value="#F8F9FA"/>
                                <Setter Property="Padding"
                                        Value="10,8"/>
                                <Setter Property="BorderBrush"
                                        Value="#E5E5E5"/>
                                <Setter Property="BorderThickness"
                                        Value="0,0,1,1"/>
                                <Setter Property="FontWeight"
                                        Value="SemiBold"/>
                            </Style>
                            <Style TargetType="DataGridCell">
                                <Setter Property="Background"
                                        Value="White"/>
                                <Setter Property="BorderBrush"
                                        Value="#E5E5E5"/>
                                <Setter Property="BorderThickness"
                                        Value="0,0,1,1"/>
                                <Setter Property="Padding"
                                        Value="10,5"/>
                                <Style.Triggers>
                                    <Trigger Property="IsSelected"
                                             Value="True">
                                        <Setter Property="Background"
                                                Value="#c3e6cb"/>
                                        <Setter Property="Foreground"
                                                Value="Black"/>
                                    </Trigger>
                                </Style.Triggers>
                            </Style>
                            <Style TargetType="DataGridRow">
                                <Setter Property="Background"
                                        Value="White"/>
                                <Setter Property="BorderBrush"
                                        Value="#E5E5E5"/>
                                <Setter Property="BorderThickness"
                                        Value="0"/>
                                <Style.Triggers>
                                    <Trigger Property="IsMouseOver"
                                             Value="True">
                                        <Setter Property="Background"
                                                Value="#F8F9FA"/>
                                    </Trigger>
                                    <Trigger Property="IsSelected"
                                             Value="True">
                                        <Setter Property="Background"
                                                Value="#c3e6cb"/>
                                        <Setter Property="Foreground"
                                                Value="Black"/>
                                    </Trigger>
                                </Style.Triggers>
                            </Style>
                        </DataGrid.Resources>

                        <DataGrid.Columns>
                            <DataGridTextColumn Header="اسم المنتج"
                                                Binding="{Binding Name}"
                                                Width="*"/>
                            <DataGridComboBoxColumn Header="الوحدة"
                                                    SelectedValueBinding="{Binding UnitId, UpdateSourceTrigger=PropertyChanged, Mode=TwoWay}"
                                                    DisplayMemberPath="Name"
                                                    SelectedValuePath="Id"
                                                    Width="100">
                                <DataGridComboBoxColumn.ElementStyle>
                                    <Style TargetType="ComboBox">
                                        <Setter Property="ItemsSource"
                                                Value="{Binding DataContext.AvailableUnits, RelativeSource={RelativeSource AncestorType=Window}}"/>
                                        <Setter Property="IsHitTestVisible"
                                                Value="False"/>
                                        <Setter Property="Focusable"
                                                Value="False"/>
                                    </Style>
                                </DataGridComboBoxColumn.ElementStyle>
                                <DataGridComboBoxColumn.EditingElementStyle>
                                    <Style TargetType="ComboBox">
                                        <Setter Property="ItemsSource"
                                                Value="{Binding DataContext.AvailableUnits, RelativeSource={RelativeSource AncestorType=Window}}"/>
                                    </Style>
                                </DataGridComboBoxColumn.EditingElementStyle>
                            </DataGridComboBoxColumn>

                            <DataGridTextColumn Header="الكمية"
                                                Binding="{Binding Quantity, UpdateSourceTrigger=PropertyChanged, StringFormat=N2}"
                                                Width="80"/>
                            <DataGridTextColumn Header="السعر الوحدة"
                                                Binding="{Binding SellingPrice, UpdateSourceTrigger=PropertyChanged, StringFormat=N2}"
                                                Width="100">
                            </DataGridTextColumn>
                            <DataGridTextColumn Header="إجمالي السعر"
                                                Binding="{Binding TotalPricePerProduct, Mode=OneWay, StringFormat='{}{0:N2} ج.م'}"
                                                Width="100"
                                                IsReadOnly="True">
                                <DataGridTextColumn.ElementStyle>
                                    <Style TargetType="TextBlock">
                                        <Setter Property="Background"
                                                Value="#F8F9FA"/>
                                        <Setter Property="Padding"
                                                Value="10,5"/>
                                        <Setter Property="Foreground"
                                                Value="Black"/>
                                    </Style>
                                </DataGridTextColumn.ElementStyle>
                            </DataGridTextColumn>
                            <DataGridTextColumn Header="الفئة"
                                                Binding="{Binding CategoryName}"
                                                Width="120"
                                                IsReadOnly="True">
                                <DataGridTextColumn.ElementStyle>
                                    <Style TargetType="TextBlock">
                                        <Setter Property="Background"
                                                Value="#F8F9FA"/>
                                        <Setter Property="Padding"
                                                Value="10,5"/>
                                        <Setter Property="Foreground"
                                                Value="Black"/>
                                    </Style>
                                </DataGridTextColumn.ElementStyle>
                            </DataGridTextColumn>
                            <DataGridTextColumn Header="العلامة التجارية"
                                                Binding="{Binding BrandName}"
                                                Width="120"
                                                IsReadOnly="True">
                                <DataGridTextColumn.ElementStyle>
                                    <Style TargetType="TextBlock">
                                        <Setter Property="Background"
                                                Value="#F8F9FA"/>
                                        <Setter Property="Padding"
                                                Value="10,5"/>
                                        <Setter Property="Foreground"
                                                Value="Black"/>
                                    </Style>
                                </DataGridTextColumn.ElementStyle>
                            </DataGridTextColumn>
                            <DataGridTextColumn Header="الخصم (%)"
                                                Binding="{Binding Discount, StringFormat=N2}"
                                                Width="100"/>
                            <DataGridTextColumn Header="مبلغ الخصم"
                                                Binding="{Binding DiscountAmount, UpdateSourceTrigger=PropertyChanged, StringFormat=N2}"
                                                Width="100"/>
                            <DataGridTextColumn Header="قيمة الخصم"
                                                Binding="{Binding DiscountValue, Mode=OneWay, StringFormat='{}{0:N2} ج.م'}"
                                                Width="100"
                                                IsReadOnly="True">
                                <DataGridTextColumn.ElementStyle>
                                    <Style TargetType="TextBlock">
                                        <Setter Property="Background"
                                                Value="#F8F9FA"/>
                                        <Setter Property="Padding"
                                                Value="10,5"/>
                                        <Setter Property="Foreground"
                                                Value="Black"/>
                                    </Style>
                                </DataGridTextColumn.ElementStyle>
                            </DataGridTextColumn>
                            <DataGridTextColumn Header="الإجمالي بعد الخصم"
                                                Binding="{Binding TotalPrice, Mode=OneWay, StringFormat='{}{0:N2} ج.م'}"
                                                Width="120"
                                                IsReadOnly="True">
                                <DataGridTextColumn.ElementStyle>
                                    <Style TargetType="TextBlock">
                                        <Setter Property="Background"
                                                Value="#F8F9FA"/>
                                        <Setter Property="Padding"
                                                Value="10,5"/>
                                    </Style>
                                </DataGridTextColumn.ElementStyle>
                            </DataGridTextColumn>
                            <DataGridTemplateColumn Width="50">
                                <DataGridTemplateColumn.CellTemplate>
                                    <DataTemplate>
                                        <Button Content="✕"
                                                Command="{Binding DataContext.RemoveProductCommand,
                                                                  RelativeSource={RelativeSource AncestorType=DataGrid}}"
                                                CommandParameter="{Binding}"
                                                Background="Transparent"
                                                BorderThickness="0"
                                                Foreground="#DC3545"
                                                FontSize="16"/>
                                    </DataTemplate>
                                </DataGridTemplateColumn.CellTemplate>
                            </DataGridTemplateColumn>
                        </DataGrid.Columns>
                    </DataGrid>
                </Grid>
            </Border>

            <!-- Cart Section -->
            <Border Grid.Column="1"
                    Style="{StaticResource ModernBorder}"
                    Margin="10"
                    Width="350">
                <StackPanel>
                    <!-- Invoice Header -->
                    <Grid Margin="0,0,0,15">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>

                        <!-- Invoice Number -->
                        <TextBlock Text="رقم الفاتورة"
                                   Grid.Column="0"
                                   VerticalAlignment="Center"
                                   FontSize="14"
                                   Margin="0,0,10,10"/>
                        <TextBox Grid.Column="1"
                                 Text="{Binding InvoiceNumber}"
                                 Style="{StaticResource ModernTextBox}"
                                 IsReadOnly="True"
                                 Margin="0,0,10,10"/>

                        <!-- Invoice Type -->
                        <TextBlock Text="نوع الفاتورة"
                                   Grid.Column="2"
                                   VerticalAlignment="Center"
                                   FontSize="14"
                                   Margin="0,0,10,10"/>
                        <ComboBox Grid.Column="3"
                                  Style="{StaticResource ModernComboBox}"
                                  SelectedValue="{Binding InvoiceType}"
                                  SelectedValuePath="Content"
                                  SelectedIndex="0"
                                  Margin="0,0,0,10">
                            <ComboBoxItem Content="نقدي"/>
                            <ComboBoxItem Content="آجل"/>
                        </ComboBox>

                        <!-- Notes -->
                        <TextBlock Text="ملاحظات"
                                   Grid.Row="1"
                                   Grid.Column="0"
                                   VerticalAlignment="Top"
                                   FontSize="14"
                                   Margin="0,0,10,0"/>
                        <TextBox Grid.Row="1"
                                 Grid.Column="1"
                                 Grid.ColumnSpan="3"
                                 Text="{Binding Notes}"
                                 Style="{StaticResource ModernTextBox}"
                                 Height="60"
                                 TextWrapping="Wrap"
                                 AcceptsReturn="True"
                                 VerticalContentAlignment="Top"/>
                    </Grid>

                    <!-- Amounts Section -->
                    <Grid Margin="0,15">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>

                        <!-- Total Amount -->
                        <TextBlock Text="إجمالي المبلغ"
                                   Grid.Row="0"
                                   FontSize="14"
                                   Margin="0,0,10,10"/>
                        <TextBox Grid.Row="0"
                                 Grid.Column="1"
                                 Text="{Binding TotalAmount, StringFormat='{}{0:N2} ج.م'}"
                                 Style="{StaticResource ModernTextBox}"
                                 IsReadOnly="True"
                                 Margin="0,0,0,10"/>

                        <!-- Discount Amount -->
                        <TextBlock Text="قيمة الخصم"
                                   Grid.Row="1"
                                   FontSize="14"
                                   Margin="0,0,10,10"/>
                        <TextBox Grid.Row="1"
                                 Grid.Column="1"
                                 Text="{Binding DiscountAmount, StringFormat=N2}"
                                 Style="{StaticResource ModernTextBox}"
                                 Margin="0,0,0,10"/>

                        <!-- Tax Amount -->
                        <TextBlock Text="قيمة الضريبة"
                                   Grid.Row="2"
                                   FontSize="14"
                                   Margin="0,0,10,10"/>
                        <TextBox Grid.Row="2"
                                 Grid.Column="1"
                                 Text="{Binding TaxAmount, StringFormat=N2}"
                                 Style="{StaticResource ModernTextBox}"
                                 IsReadOnly="True"
                                 Margin="0,0,0,10"/>

                        <!-- Net Amount -->
                        <TextBlock Text="الصافي"
                                   Grid.Row="3"
                                   FontSize="14"
                                   FontWeight="SemiBold"
                                   Margin="0,0,10,10"/>
                        <TextBox Grid.Row="3"
                                 Grid.Column="1"
                                 Text="{Binding NetAmount, StringFormat=N2}"
                                 Style="{StaticResource ModernTextBox}"
                                 IsReadOnly="True"
                                 FontWeight="SemiBold"
                                 Margin="0,0,0,10"/>

                        <!-- Paid Amount -->
                        <TextBlock Text="المبلغ المدفوع"
                                   Grid.Row="4"
                                   FontSize="14"
                                   Margin="0,0,10,10"/>
                        <TextBox Grid.Row="4"
                                 Grid.Column="1"
                                 Text="{Binding PaidAmount, StringFormat=N2, UpdateSourceTrigger=PropertyChanged}"
                                 Style="{StaticResource ModernTextBox}"
                                 Margin="0,0,0,10"/>

                        <!-- Remaining Amount -->
                        <TextBlock Text="المبلغ المتبقي"
                                   Grid.Row="5"
                                   FontSize="14"
                                   FontWeight="SemiBold"
                                   Margin="0,0,10,10"/>
                        <TextBox Grid.Row="5"
                                 Grid.Column="1"
                                 Text="{Binding RemainingAmount, StringFormat=N2}"
                                 Style="{StaticResource ModernTextBox}"
                                 IsReadOnly="True"
                                 FontWeight="SemiBold"
                                 Margin="0,0,0,10"/>
                    </Grid>

                    <!-- Action Buttons -->
                    <StackPanel Orientation="Horizontal"
                                HorizontalAlignment="Center"
                                Margin="0,15,0,0">
                        <Button Content="حفظ الفاتورة"
                                Command="{Binding SaveInvoiceCommand}"
                                Style="{StaticResource ModernButton}"
                                Width="120"
                                Height="30"
                                Margin="0,0,10,0"/>
                        <Button Content="حفظ وطباعة"
                                Command="{Binding PrintInvoiceCommand}"
                                Style="{StaticResource ModernButton}"
                                Width="120"/>
                    </StackPanel>
                </StackPanel>
            </Border>
        </Grid>
    </Grid>
</Window>