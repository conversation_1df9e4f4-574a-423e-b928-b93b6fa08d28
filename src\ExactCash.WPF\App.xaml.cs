﻿using ExactCash.WPF.Configuration;
using ExactCash.WPF.Services;
using ExactCash.WPF.ViewModels.Auth;
using ExactCash.WPF.ViewModels.Common;
using ExactCash.WPF.Views.Auth;
using ExactCash.WPF.Views.Sale;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using System.ComponentModel.Design;
using System.IO;
using System.Windows;
using AutoMapper;
using ExactCash.WPF.Services.Interfaces;
#nullable disable

namespace ExactCash.WPF
{
    /// <summary>
    /// Interaction logic for App.xaml
    /// </summary>
    public partial class App : System.Windows.Application
    {
        private readonly ServiceProvider _serviceProvider;
        private readonly IConfiguration _configuration;

        public IServiceProvider ServiceProvider => _serviceProvider;

        public App()
        {
            _configuration = new ConfigurationBuilder()
                .SetBasePath(Directory.GetCurrentDirectory())
                .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true)
                .AddJsonFile($"appsettings.{Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") ?? "Production"}.json", optional: true)
                .Build();

            var services = new ServiceCollection();
            ConfigureServices(services);
            _serviceProvider = services.BuildServiceProvider();
        }

        private void ConfigureServices(IServiceCollection services)
        {
            // Register Configuration
            services.AddSingleton(_configuration);

            // Add Logging
            services.AddLogging(builder =>
            {
                builder.AddConsole();
                builder.AddDebug();
                builder.SetMinimumLevel(LogLevel.Information);
            });

            // Configure API Settings
            services.Configure<ApiSettings>(_configuration.GetSection("ApiSettings"));
            var apiSettings = _configuration.GetSection("ApiSettings").Get<ApiSettings>();

            // Register HttpService
            services.AddSingleton<HttpService>();

            // Register ApiUserService with base URL
            services.AddSingleton<IApiUserService>(provider =>
            {
                return new ApiUserService(apiSettings.BaseUrl);
            });

            // Register CustomerService
            services.AddSingleton<ICustomerService>(provider =>
            {
                return new CustomerService(apiSettings.BaseUrl);
            });

            // Register ProductService
            services.AddSingleton<Services.Product.IProductService>(provider =>
            {
                return new Services.Product.ProductService(apiSettings.BaseUrl);
            });

            // Register SalesService
            services.AddSingleton<ISaleServiceClient>(provider =>
            {
                return new SaleServiceClient(apiSettings.BaseUrl);
            });

            services.AddSingleton<IUnitServiceClient>(provider =>
            {
                return new UnitServiceClient(apiSettings.BaseUrl);
            });

            services.AddSingleton<IBrandsServiceClient>(provider =>
            {
                return new BrandsServiceClient(apiSettings.BaseUrl);
            });

            services.AddSingleton<ICategoryServiceClient>(provider =>
            {
                return new CategoryServiceClient(apiSettings.BaseUrl);
            });

            services.AddSingleton<ISupplierServiceClient>(provider =>
            {
                return new SupplierServiceClient(apiSettings.BaseUrl);
            });

            services.AddSingleton<IPurchaseServiceClient>(provider =>
            {
                return new PurchaseServiceClient(apiSettings.BaseUrl);
            });

            services.AddSingleton<IUserServiceClient>(provider =>
            {
                return new UserServiceClient(apiSettings.BaseUrl);
            });

            services.AddSingleton<IRoleServiceClient>(provider =>
            {
                return new RoleServiceClient(apiSettings.BaseUrl);
            });

            services.AddSingleton<IExpenseCategoryServiceClient>(provider =>
            {
                return new ExpenseCategoryServiceClient(apiSettings.BaseUrl);
            });

            services.AddSingleton<IExpenseServiceClient>(provider =>
            {
                return new ExpenseServiceClient(apiSettings.BaseUrl);
            });

            services.AddSingleton<ISystemConfigurationServiceClient>(provider =>
            {
                return new SystemConfigurationServiceClient(apiSettings.BaseUrl);
            });

            // Register ReportServiceClient
            services.AddSingleton<IReportServiceClient>(provider =>
            {
                return new ReportServiceClient(apiSettings.BaseUrl);
            });

            // Register CustomerServiceClient
            services.AddSingleton<ICustomerServiceClient>(provider =>
            {
                return new CustomerServiceClient(apiSettings.BaseUrl);
            });

            // Register SupplierCategoryServiceClient
            services.AddSingleton<ISupplierCategoryServiceClient>(provider =>
            {
                return new SupplierCategoryServiceClient(apiSettings.BaseUrl);
            });
            // Register CustomerCategoryServiceClient
            services.AddSingleton<ICustomerCategoryServiceClient>(provider =>
            {
                return new CustomerCategoryServiceClient(apiSettings.BaseUrl);
            });
            
            services.AddSingleton<IPaymentServiceClient>(provider =>
            {
                return new PaymentServiceClient(apiSettings.BaseUrl);
            });
            
            // Register ViewModels
            services.AddTransient<LoginViewModel>();
            services.AddSingleton<LoadingViewModel>();
            services.AddSingleton<NotificationViewModel>();
            services.AddSingleton<FooterStatusViewModel>();
            services.AddSingleton<SidebarViewModel>();

            // Register Views
            services.AddTransient<LoginView>();
            services.AddSingleton<SaleView>();

            services.AddAutoMapper(typeof(Application.Mapping.MappingProfile));
        }

        protected override void OnStartup(StartupEventArgs e)
        {
            base.OnStartup(e);

            var loginView = _serviceProvider.GetRequiredService<LoginView>();
            loginView.Show();
        }
    }
}
