﻿<?xml version="1.0" encoding="utf-8"?>
<xs:schema id="DailySalesDataSet" targetNamespace="http://tempuri.org/DailySalesDataSet.xsd" xmlns:mstns="http://tempuri.org/DailySalesDataSet.xsd" xmlns="http://tempuri.org/DailySalesDataSet.xsd" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata" xmlns:msprop="urn:schemas-microsoft-com:xml-msprop" attributeFormDefault="qualified" elementFormDefault="qualified">
  <xs:annotation>
    <xs:appinfo source="urn:schemas-microsoft-com:xml-msdatasource">
      <DataSource DefaultConnectionIndex="0" FunctionsComponentName="QueriesTableAdapter" Modifier="AutoLayout, AnsiClass, Class, Public" SchemaSerializationMode="IncludeSchema" xmlns="urn:schemas-microsoft-com:xml-msdatasource">
        <Connections />
        <Tables />
        <Sources />
      </DataSource>
    </xs:appinfo>
  </xs:annotation>
  <xs:element name="DailySalesDataSet" msdata:IsDataSet="true" msdata:UseCurrentLocale="true" msprop:EnableTableAdapterManager="true" msprop:Generator_UserDSName="DailySalesDataSet" msprop:Generator_DataSetName="DailySalesDataSet">
    <xs:complexType>
      <xs:choice minOccurs="0" maxOccurs="unbounded">
        <xs:element name="DataTable1" msprop:Generator_RowEvHandlerName="DataTable1RowChangeEventHandler" msprop:Generator_RowDeletedName="DataTable1RowDeleted" msprop:Generator_RowDeletingName="DataTable1RowDeleting" msprop:Generator_RowEvArgName="DataTable1RowChangeEvent" msprop:Generator_TablePropName="DataTable1" msprop:Generator_RowChangedName="DataTable1RowChanged" msprop:Generator_RowChangingName="DataTable1RowChanging" msprop:Generator_TableClassName="DataTable1DataTable" msprop:Generator_RowClassName="DataTable1Row" msprop:Generator_TableVarName="tableDataTable1" msprop:Generator_UserTableName="DataTable1">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="SaleDate" msprop:Generator_ColumnPropNameInRow="SaleDate" msprop:Generator_ColumnPropNameInTable="SaleDateColumn" msprop:Generator_ColumnVarNameInTable="columnSaleDate" msprop:Generator_UserColumnName="SaleDate" type="xs:string" minOccurs="0" />
              <xs:element name="TotalSales" msprop:Generator_ColumnPropNameInRow="TotalSales" msprop:Generator_ColumnPropNameInTable="TotalSalesColumn" msprop:Generator_ColumnVarNameInTable="columnTotalSales" msprop:Generator_UserColumnName="TotalSales" type="xs:string" minOccurs="0" />
              <xs:element name="InvoiceCount" msprop:Generator_ColumnPropNameInRow="InvoiceCount" msprop:Generator_ColumnPropNameInTable="InvoiceCountColumn" msprop:Generator_ColumnVarNameInTable="columnInvoiceCount" msprop:Generator_UserColumnName="InvoiceCount" type="xs:string" minOccurs="0" />
              <xs:element name="CashierName" msprop:Generator_ColumnPropNameInRow="CashierName" msprop:Generator_ColumnPropNameInTable="CashierNameColumn" msprop:Generator_ColumnVarNameInTable="columnCashierName" msprop:Generator_UserColumnName="CashierName" type="xs:string" minOccurs="0" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
      </xs:choice>
    </xs:complexType>
  </xs:element>
</xs:schema>