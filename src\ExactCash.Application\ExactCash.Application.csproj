﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <Compile Remove="Contracts\ISystemConfigurationService.cs" />
  </ItemGroup>
	<ItemGroup>
		<PackageReference Include="AutoMapper.Extensions.Microsoft.DependencyInjection" Version="12.0.1" />
		<PackageReference Include="BarCode" Version="2025.4.2" />
		<PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="8.0.10" />
		<PackageReference Include="Microsoft.AspNetCore.Authentication.OpenIdConnect" Version="8.0.10" />
		<PackageReference Include="Microsoft.AspNetCore.Identity" Version="2.2.0" />
		<PackageReference Include="Microsoft.AspNetCore.Identity.EntityFrameworkCore" Version="8.0.10" />
		<PackageReference Include="Microsoft.AspNetCore.Mvc" Version="2.2.0" />
		<PackageReference Include="System.IdentityModel.Tokens.Jwt" Version="8.1.2" />
		<PackageReference Include="System.Drawing.Common" Version="9.0.4" />
		<PackageReference Include="ZXing.Net" Version="0.16.10" />
		<PackageReference Include="ZXing.Net.Bindings.Windows.Compatibility" Version="0.16.13" />
	</ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\ExactCash.Domain\ExactCash.Domain.csproj" />
    <ProjectReference Include="..\ExactCash.Infrastructure\ExactCash.Infrastructure.csproj" />
  </ItemGroup>
  <ItemGroup>
    <Folder Include="DTOs\Reports\" />
  </ItemGroup>

</Project>
