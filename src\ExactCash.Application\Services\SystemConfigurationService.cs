﻿using Microsoft.Extensions.Logging;
using Microsoft.EntityFrameworkCore;
using ExactCash.Infrastructure.Persistence;
using ExactCash.Domain.Entities;
using AutoMapper;
using ExactCash.Application.DTOs;
using ExactCash.Application.DTOs.Common;
using ExactCash.Application.Enums;
using ExactCash.Application.Helpers;
using ExactCash.Application.Interfaces;
using ExactCash.Application.Responses;

namespace ExactCash.Application.Services
{
    public class SystemConfigurationService : ISystemConfigurationService
    {
        private readonly AppPostgreSQLDbContext _context;
        private readonly ILogger<SystemConfigurationService> _logger;
        private readonly IMapper _mapper;

        // Constructor to inject DbContext, Logger, and AutoMapper
        public SystemConfigurationService(AppPostgreSQLDbContext context, ILogger<SystemConfigurationService> logger, IMapper mapper)
        {
            _context = context;
            _logger = logger;
            _mapper = mapper;
        }

        public async Task<SystemConfigurationDto> GetSystemConfigurationByIdAsync(int id)
        {
            var configuration = await _context.systemConfigurations.FindAsync(id);
            if (configuration == null)
                return null;

            return _mapper.Map<SystemConfigurationDto>(configuration);
        }

        public async Task<PagedResponse<SystemConfigurationDto>> GetAllSystemConfigurationsAsync(string settingName, string category, PaginationFilter pagination)
        {
            var query = _context.systemConfigurations.AsQueryable();

            if (!string.IsNullOrEmpty(settingName))
                query = query.Where(c => c.SettingName.Contains(settingName));

            var totalRecords = await query.CountAsync();

            query = pagination.SortField switch
            {
                nameof(SystemConfiguration.CreationDate) => pagination.SortOrder == SortOrder.Asc ?
                    query.OrderBy(c => c.CreationDate) : query.OrderByDescending(c => c.CreationDate),
                nameof(SystemConfiguration.SettingName) => pagination.SortOrder == SortOrder.Asc ?
                    query.OrderBy(c => c.SettingName) : query.OrderByDescending(c => c.SettingName),
                _ => pagination.SortOrder == SortOrder.Desc ?
                    query.OrderBy(c => c.CreationDate) : query.OrderByDescending(c => c.CreationDate)
            };

            var result = await query
                .Skip((pagination.PageNumber - 1) * pagination.PageSize)
                .Take(pagination.PageSize)
                .ToListAsync();

            return PaginationHelper.CreatePagedResponse(
                _mapper.Map<List<SystemConfigurationDto>>(result),
                pagination.PageNumber,
                pagination.PageSize,
                totalRecords);
        }

        public async Task<List<SystemConfigurationDto>> GetAllSystemConfigurationsAsync()
        {
            return _mapper.Map<List<SystemConfigurationDto>>(await _context.systemConfigurations.ToListAsync());
        }

        public async Task<SystemConfigurationDto> CreateSystemConfigurationAsync(SystemConfigurationDto configurationDto)
        {
            var configuration = new SystemConfiguration
            {
                SettingName = configurationDto.SettingName,
                SettingValue = configurationDto.SettingValue,
                Description = configurationDto.Description,
                IsActive = configurationDto.IsActive,
                CreationDate = DateTime.UtcNow
            };

            await _context.systemConfigurations.AddAsync(configuration);
            await _context.SaveChangesAsync();
            return _mapper.Map<SystemConfigurationDto>(configuration);
        }

        public async Task UpdateSystemConfigurationAsync(SystemConfigurationDto configurationDto)
        {
            var configuration = await _context.systemConfigurations.FindAsync(configurationDto.Id);
            if (configuration == null)
                throw new KeyNotFoundException($"SystemConfiguration with ID {configurationDto.Id} not found.");

            configuration.SettingName = configurationDto.SettingName;
            configuration.SettingValue = configurationDto.SettingValue;
            configuration.Description = configurationDto.Description;
            configuration.IsActive = configurationDto.IsActive;
            configuration.LastUpdatedDate = DateTime.UtcNow;

            _context.Entry(configuration).State = EntityState.Modified;
            await _context.SaveChangesAsync();
        }

        public async Task DeleteSystemConfigurationAsync(int id)
        {
            var configuration = await _context.systemConfigurations.FindAsync(id);
            if (configuration == null)
                throw new KeyNotFoundException($"SystemConfiguration with ID {id} not found.");

            _context.systemConfigurations.Remove(configuration);
            await _context.SaveChangesAsync();
        }

        /// <summary>
        /// Loads currency configuration and applies it to CurrencyHelper
        /// </summary>
        public async Task LoadCurrencyConfigurationAsync()
        {
            try
            {
                var currencySettings = await _context.systemConfigurations
                    .Where(s => s.IsActive && (s.SettingName == "CurrencySymbol" ||
                                               s.SettingName == "CurrencyPosition" ||
                                               s.SettingName == "CurrencyFormat"))
                    .ToDictionaryAsync(s => s.SettingName, s => s.SettingValue);

                var symbol = currencySettings.GetValueOrDefault("CurrencySymbol", "ج.م");
                var position = currencySettings.GetValueOrDefault("CurrencyPosition", "after");
                var cultureCode = currencySettings.GetValueOrDefault("CurrencyFormat", "ar-EG");

                CurrencyHelper.SetCurrencyConfiguration(symbol, position, cultureCode);

                _logger.LogInformation("Currency configuration loaded: Symbol={Symbol}, Position={Position}, Culture={Culture}",
                    symbol, position, cultureCode);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading currency configuration, using defaults");
                // Use default Egyptian Pound configuration
                CurrencyHelper.SetCurrencyConfiguration("ج.م", "after", "ar-EG");
            }
        }

        /// <summary>
        /// Gets currency configuration settings
        /// </summary>
        public async Task<Dictionary<string, string>> GetCurrencySettingsAsync()
        {
            var settings = await _context.systemConfigurations
                .Where(s => s.IsActive && (s.SettingName == "CurrencySymbol" ||
                                           s.SettingName == "CurrencyPosition" ||
                                           s.SettingName == "CurrencyFormat"))
                .ToDictionaryAsync(s => s.SettingName, s => s.SettingValue);

            // Ensure default values
            if (!settings.ContainsKey("CurrencySymbol"))
                settings["CurrencySymbol"] = "ج.م";
            if (!settings.ContainsKey("CurrencyPosition"))
                settings["CurrencyPosition"] = "after";
            if (!settings.ContainsKey("CurrencyFormat"))
                settings["CurrencyFormat"] = "ar-EG";

            return settings;
        }
    }
}
