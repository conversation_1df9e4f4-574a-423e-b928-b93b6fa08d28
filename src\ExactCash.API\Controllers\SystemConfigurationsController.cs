using ExactCash.Application.DTOs;
using ExactCash.Application.DTOs.Common;
using ExactCash.Application.Interfaces;
using ExactCash.Application.Responses;
using Microsoft.AspNetCore.Mvc;
#nullable disable

namespace ExactCash.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class SystemConfigurationsController : ControllerBase
    {
        private readonly ISystemConfigurationService _systemConfigurationService;

        public SystemConfigurationsController(ISystemConfigurationService systemConfigurationService)
        {
            _systemConfigurationService = systemConfigurationService;
        }

        [HttpGet]
        public async Task<ActionResult<PagedResponse<SystemConfigurationDto>>> GetAllSystemConfigurations(
            [FromQuery] string key,
            [FromQuery] string category,
            [FromQuery] PaginationFilter pagination)
        {
            var configurations = await _systemConfigurationService.GetAllSystemConfigurationsAsync(key, category, pagination);
            return Ok(configurations);
        }


        [HttpGet("get-all-configs")]
        public async Task<ActionResult<List<SystemConfigurationDto>>> GetAllSystemConfigurations()
        {
            var configurations = await _systemConfigurationService.GetAllSystemConfigurationsAsync();
            return Ok(configurations);
        }


        [HttpGet("{id}")]
        public async Task<ActionResult<SystemConfigurationDto>> GetSystemConfiguration(int id)
        {
            var configuration = await _systemConfigurationService.GetSystemConfigurationByIdAsync(id);
            if (configuration == null)
                return NotFound();

            return Ok(configuration);
        }

        [HttpPost]
        public async Task<ActionResult<SystemConfigurationDto>> CreateSystemConfiguration(SystemConfigurationDto configurationDto)
        {
            var createdConfiguration = await _systemConfigurationService.CreateSystemConfigurationAsync(configurationDto);
            return CreatedAtAction(nameof(GetSystemConfiguration), new { id = createdConfiguration.Id }, createdConfiguration);
        }

        [HttpPut("{id}")]
        public async Task<IActionResult> UpdateSystemConfiguration(int id, SystemConfigurationDto configurationDto)
        {
            if (id != configurationDto.Id)
                return BadRequest();

            try
            {
                await _systemConfigurationService.UpdateSystemConfigurationAsync(configurationDto);
                return NoContent();
            }
            catch (KeyNotFoundException)
            {
                return NotFound();
            }
        }

        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteSystemConfiguration(int id)
        {
            try
            {
                await _systemConfigurationService.DeleteSystemConfigurationAsync(id);
                return NoContent();
            }
            catch (KeyNotFoundException)
            {
                return NotFound();
            }
        }
    }
} 