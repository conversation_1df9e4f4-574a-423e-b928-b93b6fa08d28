using System;
using System.Collections.ObjectModel;
using System.Threading.Tasks;
using System.Windows;
using AutoMapper;
using ExactCash.Application.DTOs;
using ExactCash.Application.DTOs.Common;
using ExactCash.Application.Interfaces;
using ExactCash.WPF.Commands;
using ExactCash.WPF.Services;
using ExactCash.WPF.ViewModels.Common;
using ExactCash.WPF.Views.Sale;
using System.Linq;
using System.Windows.Media;
using ExactCash.WPF.Services.Interfaces;
using Microsoft.Extensions.Configuration;
using ExactCash.Application.Responses;
using System.IO;
using System.Text;
using Microsoft.Win32;

namespace ExactCash.WPF.ViewModels.Sale
{
    public class SaleListViewModel : ViewModelBase
    {
        private readonly ISaleServiceClient _saleService;
        private readonly IMapper _mapper;
        private DateTime? _dateFrom;
        private DateTime? _dateTo;
        private string _invoiceNumber;
        private int _currentPage = 1;
        private int _totalPages;
        private const int PageSize = 20;
        private ObservableCollection<SaleDto> _sales;
        private readonly IPrinterService _printerService;
        private readonly IApiUserService _apiService;
        private readonly ExactCash.WPF.Services.ICustomerService _customerService;
        private readonly Services.Product.IProductService _productService;
        private readonly Services.ISaleServiceClient _salesService;
        private readonly LoadingViewModel _loadingViewModel;
        private readonly IUnitServiceClient _unitServiceClient;
        private readonly ICategoryServiceClient _categoryServiceClient;
        private readonly IBrandsServiceClient _brandsServiceClient;
        private readonly ISupplierServiceClient _supplierServiceClient;
        private readonly NotificationViewModel _notificationViewModel;
        private readonly IPurchaseServiceClient _purchaseOrderService;
        private readonly IUserServiceClient _userServiceClient;
        private readonly IRoleServiceClient _roleServiceClient;
        private readonly IConfiguration _configuration;
        private readonly IExpenseCategoryServiceClient _expenseCategoryServiceClient;
        private readonly IExpenseServiceClient _expenseServiceClient;
        private readonly ISystemConfigurationServiceClient _systemConfigurationServiceClient;
        private readonly IReportServiceClient _reportServiceClient;
        private readonly ISupplierCategoryServiceClient _supplierCategoryServiceClient;
        private readonly ICustomerCategoryServiceClient _customerCategoryServiceClient;
        private int _totalInvoices;
        private decimal _totalAmount;
        private decimal _averageAmount;
        private int _currentPeriodInvoices;
        private decimal _currentPeriodTotalAmount;
        public SaleListViewModel(
            ISaleServiceClient saleService,
            IMapper mapper,
            IApiUserService apiService,
            ExactCash.WPF.Services.ICustomerService customerService,
            Services.Product.IProductService productService,
            Services.ISaleServiceClient salesService,
            LoadingViewModel loadingViewModel,
            IUnitServiceClient unitServiceClient,
            ICategoryServiceClient categoryServiceClient,
            IBrandsServiceClient brandsServiceClient,
            ISupplierServiceClient supplierServiceClient,
            NotificationViewModel notificationViewModel,
            IPurchaseServiceClient purchaseOrderService
,
            IUserServiceClient userServiceClient,
            IRoleServiceClient roleServiceClient,
            IConfiguration configuration,
            IExpenseCategoryServiceClient expenseCategoryServiceClient,
            IExpenseServiceClient expenseServiceClient,
            ISystemConfigurationServiceClient systemConfigurationServiceClient,
            IReportServiceClient reportServiceClient,
            ISupplierCategoryServiceClient supplierCategoryServiceClient,
            ICustomerCategoryServiceClient customerCategoryServiceClient)
        {
            _saleService = saleService;
            _mapper = mapper;
            _salesService = salesService;
            _apiService = apiService;
            _customerService = customerService;
            _productService = productService;
            _brandsServiceClient = brandsServiceClient;
            _supplierServiceClient = supplierServiceClient;
            _categoryServiceClient = categoryServiceClient;
            _unitServiceClient = unitServiceClient;
            _loadingViewModel = loadingViewModel;
            _notificationViewModel = notificationViewModel;
            _purchaseOrderService = purchaseOrderService;
            _configuration = configuration;
            _printerService = new XprinterService();
            Sales = new ObservableCollection<SaleDto>();
            SearchCommand = new RelayCommand(async () => await ExecuteSearch());
            ResetSearchCommand = new RelayCommand(async () => await ExecuteResetSearch());
            AddNewInvoiceCommand = new RelayCommand(() => ExecuteAddNewInvoice());
            ViewInvoiceCommand = new RelayCommand<SaleDto>(ExecuteViewInvoice);
            PrintInvoiceCommand = new RelayCommand<SaleDto>(ExecutePrintInvoice);
            DeleteSaleCommand = new RelayCommand<SaleDto>(ExecuteDeleteSale);
            CollectPaymentCommand = new RelayCommand<SaleDto>(ExecuteCollectPayment);
            RefreshCommand = new RelayCommand(LoadData);
            NextPageCommand = new RelayCommand(async () => await ExecuteNextPage());
            PreviousPageCommand = new RelayCommand(async () => await ExecutePreviousPage());
            ExportToExcelCommand = new RelayCommand(ExportToExcel);
            LoadData();
            _userServiceClient = userServiceClient;
            _roleServiceClient = roleServiceClient;
            _expenseCategoryServiceClient = expenseCategoryServiceClient;
            _expenseServiceClient = expenseServiceClient;
            _systemConfigurationServiceClient = systemConfigurationServiceClient;
            _reportServiceClient = reportServiceClient;
            _supplierCategoryServiceClient = supplierCategoryServiceClient;
            _customerCategoryServiceClient = customerCategoryServiceClient;
        }

        public ObservableCollection<SaleDto> Sales
        {
            get => _sales;
            set
            {
                _sales = value;
                OnPropertyChanged();
            }
        }

        public DateTime? DateFrom
        {
            get => _dateFrom;
            set
            {
                _dateFrom = value;
                OnPropertyChanged();
            }
        }

        public DateTime? DateTo
        {
            get => _dateTo;
            set
            {
                _dateTo = value;
                OnPropertyChanged();
            }
        }

        public string InvoiceNumber
        {
            get => _invoiceNumber;
            set
            {
                _invoiceNumber = value;
                OnPropertyChanged();
            }
        }

        public int CurrentPage
        {
            get => _currentPage;
            set
            {
                _currentPage = value;
                OnPropertyChanged();
            }
        }

        public int TotalPages
        {
            get => _totalPages;
            set
            {
                _totalPages = value;
                OnPropertyChanged();
            }
        }

        public int TotalInvoices
        {
            get => _totalInvoices;
            set
            {
                _totalInvoices = value;
                OnPropertyChanged();
            }
        }

        public decimal TotalAmount
        {
            get => _totalAmount;
            set
            {
                _totalAmount = value;
                OnPropertyChanged();
            }
        }

        public decimal AverageAmount
        {
            get => _averageAmount;
            set
            {
                _averageAmount = value;
                OnPropertyChanged();
            }
        }

        public int CurrentPeriodInvoices
        {
            get => _currentPeriodInvoices;
            set
            {
                _currentPeriodInvoices = value;
                OnPropertyChanged();
            }
        }

        public decimal CurrentPeriodTotalAmount
        {
            get => _currentPeriodTotalAmount;
            set
            {
                _currentPeriodTotalAmount = value;
                OnPropertyChanged();
            }
        }


        public RelayCommand SearchCommand { get; }
        public RelayCommand ResetSearchCommand { get; }
        public RelayCommand AddNewInvoiceCommand { get; }
        public RelayCommand<SaleDto> ViewInvoiceCommand { get; }
        public RelayCommand<SaleDto> PrintInvoiceCommand { get; }
        public RelayCommand<SaleDto> DeleteSaleCommand { get; }
        public RelayCommand<SaleDto> CollectPaymentCommand { get; }
        public RelayCommand RefreshCommand { get; }
        public RelayCommand NextPageCommand { get; }
        public RelayCommand PreviousPageCommand { get; }
        public System.Windows.Input.ICommand ExportToExcelCommand { get; }

        private async Task CalculateStatistics(PagedResponse<SaleDto> sales)
        {
            try
            {
                // Get all sales for statistics
                var allSales = await _saleService.GetAllAsync(null, null, null, null, new PaginationFilter { PageNumber = 1, PageSize = int.MaxValue });

                // Calculate total invoices
                TotalInvoices = allSales.Data.Count();

                // Calculate total amount
                TotalAmount = allSales.Data.Sum(s => s.TotalAmount);

                // Calculate average amount
                AverageAmount = TotalInvoices > 0 ? TotalAmount / TotalInvoices : 0;

                // Calculate current period invoices (last 30 days)
                CurrentPeriodInvoices = sales.Data.Count();

                CurrentPeriodTotalAmount = sales.Data.Sum(s => s.TotalAmount);
            }
            catch (Exception ex)
            {
                _notificationViewModel.ShowError($"حدث خطأ أثناء حساب الإحصائيات: {ex.Message}");
            }
        }

        private async void LoadData()
        {
            try
            {
                var filter = new PaginationFilter
                {
                    PageNumber = CurrentPage,
                    PageSize = PageSize
                };
                var result = await _saleService.GetAllAsync(null, InvoiceNumber, DateFrom, DateTo, filter);
                var sales = result.Data;
                TotalPages = result.TotalPages;
                Sales = new ObservableCollection<SaleDto>(sales);
                // Calculate statistics
                await CalculateStatistics(result);
            }
            catch (Exception ex)
            {
                _notificationViewModel.ShowError($"حدث خطأ أثناء تحميل البيانات: {ex.Message}");
            }
        }

        private async Task ExecuteSearch()
        {
            CurrentPage = 1;
            LoadData();
        }

        private async Task ExecuteResetSearch()
        {
            DateFrom = null;
            DateTo = null;
            InvoiceNumber = null;
            CurrentPage = 1;
            LoadData();
        }

        private void ExecuteAddNewInvoice()
        {
            var addInvoiceView = new SaleView(
                _apiService,
                _loadingViewModel,
                _customerService,
                _productService,
                _saleService,
                _unitServiceClient,
                _mapper,
                _categoryServiceClient,
                _brandsServiceClient,
                _supplierServiceClient,
                _notificationViewModel,
                _purchaseOrderService,
                _userServiceClient,
                _roleServiceClient,
                _configuration,
                _expenseCategoryServiceClient,
                _expenseServiceClient,
                _systemConfigurationServiceClient,
                _reportServiceClient,
                _supplierCategoryServiceClient,
                _customerCategoryServiceClient
            );

            addInvoiceView.Owner = System.Windows.Application.Current.MainWindow;
            if (addInvoiceView.ShowDialog() == true)
            {
                LoadData();
            }
        }

        private async void ExecuteViewInvoice(SaleDto sale)
        {
            if (sale == null) return;

            var saledb = await _saleService.GetByIdAsync(sale.Id);
            var saleView = new SaleView(
                _apiService,
                _loadingViewModel,
                _customerService,
                _productService,
                _salesService,
                _unitServiceClient,
                _mapper,
                _categoryServiceClient,
                _brandsServiceClient,
                _supplierServiceClient,
                _notificationViewModel,
                _purchaseOrderService,
                _userServiceClient,
                _roleServiceClient,
                _configuration,
                _expenseCategoryServiceClient,
                _expenseServiceClient,
                _systemConfigurationServiceClient,
                _reportServiceClient,
                _supplierCategoryServiceClient,
                _customerCategoryServiceClient
            );

            // Set the window owner to the current window
            var currentWindow = System.Windows.Application.Current.Windows.OfType<Window>().FirstOrDefault(w => w.IsActive);
            if (currentWindow != null)
            {
                saleView.Owner = currentWindow;
            }

            // Set the sale data in the view model
            if (saleView.DataContext is SaleViewModel viewModel)
            {
                SaleDto saleDto = _mapper.Map<SaleDto>(saledb);
                viewModel.LoadSaleData(saleDto);
            }

            saleView.ShowDialog();
        }

        private Window FindParentWindow()
        {
            // Find all windows
            foreach (Window window in System.Windows.Application.Current.Windows)
            {
                // Look for the window that contains a SaleView
                if (window.Content != null &&
                    VisualTreeHelper.GetChild(window.Content as DependencyObject, 0) is FrameworkElement content &&
                    window.Name == "SaleListViewScreen")
                {
                    return window;
                }
            }
            return System.Windows.Application.Current.MainWindow;
        }

        private async void ExecutePrintInvoice(SaleDto sale)
        {
            if (sale == null) return;

            try
            {
                var saleDetails = await _saleService.GetByIdAsync(sale.Id);
                var result = await _printerService.PrintInvoiceAsync(saleDetails);
                if (result)
                {
                    Helpers.BootstrapMessageBoxHelper.ShowSuccess($"تم طباعه الفاتوره بنجاح", owner: FindParentWindow());
                }
                else
                {
                    Helpers.BootstrapMessageBoxHelper.ShowError($"حدث خطأ أثناء طباعة الفاتورة", owner: FindParentWindow());
                }
            }
            catch (Exception ex)
            {
                Helpers.BootstrapMessageBoxHelper.ShowError($"حدث خطأ أثناء طباعة الفاتورة: {ex.Message}", owner: FindParentWindow());
            }
        }

        private async void ExecuteDeleteSale(SaleDto sale)
        {
            if (sale == null) return;

            try
            {
                var result = System.Windows.MessageBox.Show(
                    "هل أنت متأكد من حذف هذه الفاتورة؟",
                    "تأكيد الحذف",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Warning);

                if (result == MessageBoxResult.Yes)
                {
                    await _saleService.DeleteAsync(sale.Id);
                    Helpers.BootstrapMessageBoxHelper.ShowSuccess("تم حذف الفاتورة بنجاح", owner: FindParentWindow());
                    LoadData();
                }
            }
            catch (Exception ex)
            {
                Helpers.BootstrapMessageBoxHelper.ShowError($"حدث خطأ أثناء حذف الفاتورة: {ex.Message}", owner: FindParentWindow());
            }
        }

        private void ExecuteCollectPayment(SaleDto sale)
        {
            if (sale == null || sale.RemainingAmount <= 0) return;

            try
            {
                // Create OutstandingSaleDto from SaleDto
                var outstandingSale = new OutstandingSaleDto
                {
                    Id = sale.Id,
                    InvoiceNumber = sale.InvoiceNumber,
                    CustomerId = sale.CustomerId,
                    CustomerName = sale.CustomerName,
                    CustomerPhone = sale.Customer?.Phone ?? "غير محدد",
                    CreationDate = sale.CreationDate,
                    TotalAmount = sale.TotalAmount,
                    PaidAmount = sale.PaidAmount,
                    RemainingAmount = sale.RemainingAmount,
                    DaysOverdue = (DateTime.Now - sale.CreationDate).Days
                };

                // Open payment collection dialog for specific sale
                var paymentView = new Views.CreditSales.PaymentCollectionView(outstandingSale);
                paymentView.Owner = FindParentWindow();
                var result = paymentView.ShowDialog();

                if (result == true)
                {
                    // Refresh data after payment collection
                    LoadData();
                    Helpers.BootstrapMessageBoxHelper.ShowSuccess("تم تحصيل الدفعة بنجاح", owner: FindParentWindow());
                }
            }
            catch (Exception ex)
            {
                Helpers.BootstrapMessageBoxHelper.ShowError($"حدث خطأ أثناء تحصيل الدفعة: {ex.Message}", owner: FindParentWindow());
            }
        }

        private async Task ExecuteNextPage()
        {
            if (CurrentPage < TotalPages)
            {
                CurrentPage++;
                LoadData();
            }
        }

        private async Task ExecutePreviousPage()
        {
            if (CurrentPage > 1)
            {
                CurrentPage--;
                LoadData();
            }
        }

        private void ExportToExcel()
        {
            var saveFileDialog = new Microsoft.Win32.SaveFileDialog
            {
                Filter = "Excel Files (*.xlsx)|*.xlsx|CSV Files (*.csv)|*.csv",
                FileName = $"المبيعات_{DateTime.Now:yyyyMMdd_HHmmss}.csv"
            };

            if (saveFileDialog.ShowDialog() == true)
            {
                try
                {
                    using (var writer = new StreamWriter(saveFileDialog.FileName, false, System.Text.Encoding.UTF8))
                    {
                        // Write header
                        writer.WriteLine("التاريخ,العميل,المبلغ,الصافي,رقم الفاتورة");
                        foreach (var sale in Sales)
                        {
                            writer.WriteLine($"{sale.SaleDate:yyyy-MM-dd},{sale.CustomerName},{sale.TotalAmount},{sale.NetAmount},{sale.InvoiceNumber}");
                        }
                    }
                    System.Windows.MessageBox.Show("تم تصدير البيانات بنجاح!", "نجاح", System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Information);
                }
                catch (Exception ex)
                {
                    System.Windows.MessageBox.Show($"حدث خطأ أثناء التصدير: {ex.Message}", "خطأ", System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
                }
            }
        }
    }
}