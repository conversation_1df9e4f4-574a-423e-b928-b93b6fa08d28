﻿#nullable disable

using ExactCash;

namespace ExactCash.WASM.Application.DTOs
{
    public class DailySalesReportDto
    {
        public DateTime SaleDate { get; set; }
        public decimal TotalSales { get; set; }
        public int InvoiceCount { get; set; }
        public string CashierName { get; set; }
        public decimal AverageTransactionValue { get; set; }
        public int TotalItemsSold { get; set; }
        public decimal DiscountAmount { get; set; }
        public decimal TaxAmount { get; set; }
        public decimal NetAmount { get; set; }
        public decimal PaidAmount { get; set; }
        public decimal RemainingAmount { get; set; }
        public int UniqueCustomers { get; set; }
        public decimal CashPayments { get; set; }
        public decimal CreditPayments { get; set; }

        // Formatted properties for display
        public string FormattedTotalSales => $"{TotalSales:N2} ج.م";
        public string FormattedAverageTransactionValue => $"{AverageTransactionValue:N2} ج.م";
        public string FormattedDiscountAmount => $"{DiscountAmount:N2} ج.م";
        public string FormattedTaxAmount => $"{TaxAmount:N2} ج.م";
        public string FormattedNetAmount => $"{NetAmount:N2} ج.م";
        public string FormattedPaidAmount => $"{PaidAmount:N2} ج.م";
        public string FormattedRemainingAmount => $"{RemainingAmount:N2} ج.م";
        public string FormattedCashPayments => $"{CashPayments:N2} ج.م";
        public string FormattedCreditPayments => $"{CreditPayments:N2} ج.م";

        public DailySalesReportDto()
        {

        }
    }
}
