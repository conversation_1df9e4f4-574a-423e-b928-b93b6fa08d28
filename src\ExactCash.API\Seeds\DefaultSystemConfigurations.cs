using ExactCash.Domain.Entities;
using ExactCash.Infrastructure.Persistence;
using Microsoft.Extensions.Logging;

namespace ExactCash.API.Seeds
{
    public static class DefaultSystemConfigurations
    {
        public static async Task SeedAsync(AppPostgreSQLDbContext context, ILogger logger)
        {
            try
            {
                // Check if configurations already exist
                if (context.systemConfigurations.Any())
                {
                    logger.LogInformation("System configurations already exist, skipping seeding.");
                    return;
                }

                var configurations = new List<SystemConfiguration>
                {
                    // Currency Configuration
                    new SystemConfiguration
                    {
                        SettingName = "CurrencySymbol",
                        SettingValue = "ج.م",
                        SettingType = "string",
                        Description = "Default currency symbol for the system",
                        IsActive = true,
                        CreationDate = DateTime.UtcNow
                    },
                    new SystemConfiguration
                    {
                        SettingName = "CurrencyFormat",
                        SettingValue = "ar-EG",
                        SettingType = "string",
                        Description = "Currency format culture code",
                        IsActive = true,
                        CreationDate = DateTime.UtcNow
                    },
                    new SystemConfiguration
                    {
                        SettingName = "CurrencyPosition",
                        SettingValue = "after",
                        SettingType = "string",
                        Description = "Currency symbol position (before/after)",
                        IsActive = true,
                        CreationDate = DateTime.UtcNow
                    },
                    
                    // Barcode Settings
                    new SystemConfiguration
                    {
                        SettingName = "BarcodeWidth",
                        SettingValue = "300",
                        SettingType = "int",
                        Description = "Default barcode width in pixels",
                        IsActive = true,
                        CreationDate = DateTime.UtcNow
                    },
                    new SystemConfiguration
                    {
                        SettingName = "BarcodeHeight",
                        SettingValue = "150",
                        SettingType = "int",
                        Description = "Default barcode height in pixels",
                        IsActive = true,
                        CreationDate = DateTime.UtcNow
                    },
                    new SystemConfiguration
                    {
                        SettingName = "BarcodeMargin",
                        SettingValue = "10",
                        SettingType = "int",
                        Description = "Default barcode margin in pixels",
                        IsActive = true,
                        CreationDate = DateTime.UtcNow
                    },
                    new SystemConfiguration
                    {
                        SettingName = "BarcodeFontSize",
                        SettingValue = "12",
                        SettingType = "int",
                        Description = "Default barcode font size",
                        IsActive = true,
                        CreationDate = DateTime.UtcNow
                    },
                    new SystemConfiguration
                    {
                        SettingName = "BarcodePriceFontSize",
                        SettingValue = "14",
                        SettingType = "int",
                        Description = "Default barcode price font size",
                        IsActive = true,
                        CreationDate = DateTime.UtcNow
                    },
                    new SystemConfiguration
                    {
                        SettingName = "BarcodeTitleYOffset",
                        SettingValue = "20",
                        SettingType = "int",
                        Description = "Default barcode title Y offset",
                        IsActive = true,
                        CreationDate = DateTime.UtcNow
                    },
                    new SystemConfiguration
                    {
                        SettingName = "BarcodePriceYOffset",
                        SettingValue = "40",
                        SettingType = "int",
                        Description = "Default barcode price Y offset",
                        IsActive = true,
                        CreationDate = DateTime.UtcNow
                    },
                    new SystemConfiguration
                    {
                        SettingName = "BarcodeFontFamily",
                        SettingValue = "Arial",
                        SettingType = "string",
                        Description = "Default barcode font family",
                        IsActive = true,
                        CreationDate = DateTime.UtcNow
                    },
                    new SystemConfiguration
                    {
                        SettingName = "BarcodeTextAlign",
                        SettingValue = "center",
                        SettingType = "string",
                        Description = "Default barcode text alignment",
                        IsActive = true,
                        CreationDate = DateTime.UtcNow
                    },
                    new SystemConfiguration
                    {
                        SettingName = "BarcodeBackgroundColor",
                        SettingValue = "#FFFFFF",
                        SettingType = "string",
                        Description = "Default barcode background color",
                        IsActive = true,
                        CreationDate = DateTime.UtcNow
                    },

                    // Tax Configuration
                    new SystemConfiguration
                    {
                        SettingName = "DefaultTaxRate",
                        SettingValue = "14",
                        SettingType = "decimal",
                        Description = "Default tax rate percentage",
                        IsActive = true,
                        CreationDate = DateTime.UtcNow
                    },

                    // Company Information
                    new SystemConfiguration
                    {
                        SettingName = "CompanyName",
                        SettingValue = "شركة الحلول الدقيقة للنقد",
                        SettingType = "string",
                        Description = "Company name for reports and invoices",
                        IsActive = true,
                        CreationDate = DateTime.UtcNow
                    },
                    new SystemConfiguration
                    {
                        SettingName = "CompanyAddress",
                        SettingValue = "القاهرة، مصر",
                        SettingType = "string",
                        Description = "Company address",
                        IsActive = true,
                        CreationDate = DateTime.UtcNow
                    },
                    new SystemConfiguration
                    {
                        SettingName = "CompanyPhone",
                        SettingValue = "+20 ************",
                        SettingType = "string",
                        Description = "Company phone number",
                        IsActive = true,
                        CreationDate = DateTime.UtcNow
                    }
                };

                await context.systemConfigurations.AddRangeAsync(configurations);
                await context.SaveChangesAsync();

                logger.LogInformation("Default system configurations seeded successfully.");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "An error occurred while seeding system configurations: {Message}", ex.Message);
            }
        }
    }
}
