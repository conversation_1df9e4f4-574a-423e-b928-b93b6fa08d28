﻿using AutoMapper;
using ExactCash.Application.DTOs;
using Microsoft.AspNetCore.Identity;
using ExactCash.Domain.Entities;

namespace ExactCash.Application.Mapping
{
    public class MappingProfile : Profile
    {
        public MappingProfile()
        {
            CreateMap<Domain.Entities.Unit, DTOs.UnitDto>().ReverseMap();

            CreateMap<Domain.Entities.Customer, DTOs.CustomerDto>().ReverseMap();

            CreateMap<Domain.Entities.CustomerCategory, DTOs.CustomerCategoryDto>().ReverseMap();

            CreateMap<Domain.Entities.Discount, DTOs.DiscountDto>().ReverseMap();

            CreateMap<Domain.Entities.Supplier, DTOs.SupplierDto>().ReverseMap();

            CreateMap<Domain.Entities.SupplierCategory, DTOs.SupplierCategoryDto>().ReverseMap();

            CreateMap<Domain.Entities.SystemConfiguration, DTOs.SystemConfigurationDto>();

            CreateMap<Domain.Entities.SaleItem, DTOs.SaleItemDto>().ReverseMap();

            CreateMap<Domain.Entities.Sale, DTOs.SaleDto>().ReverseMap();

            CreateMap<Domain.Entities.Brand, DTOs.BrandDto>().ReverseMap();

            CreateMap<Category, CategoryDto>().ReverseMap();

            CreateMap<IdentityUser, UserDto>().ReverseMap();

            CreateMap<IdentityRole, RoleDto>().ReverseMap();

            CreateMap<Product, ProductDto>().ReverseMap();

            CreateMap<PurchaseItemDto, PurchaseItem>().ReverseMap();

            CreateMap<PurchaseDto, Purchase>().ReverseMap();

            // Role Claim Mappings
            CreateMap<IdentityRoleClaim<string>, RoleClaimDto>()
                .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.Id))
                .ForMember(dest => dest.RoleId, opt => opt.MapFrom(src => src.RoleId))
                .ForMember(dest => dest.ClaimType, opt => opt.MapFrom(src => src.ClaimType))
                .ForMember(dest => dest.ClaimValue, opt => opt.MapFrom(src => src.ClaimValue));

            CreateMap<CreateRoleClaimDto, IdentityRoleClaim<string>>()
                .ForMember(dest => dest.RoleId, opt => opt.MapFrom(src => src.RoleId))
                .ForMember(dest => dest.ClaimType, opt => opt.MapFrom(src => src.ClaimType))
                .ForMember(dest => dest.ClaimValue, opt => opt.MapFrom(src => src.ClaimValue));

            CreateMap<UpdateRoleClaimDto, IdentityRoleClaim<string>>()
                .ForMember(dest => dest.ClaimType, opt => opt.MapFrom(src => src.ClaimType))
                .ForMember(dest => dest.ClaimValue, opt => opt.MapFrom(src => src.ClaimValue));

            CreateMap<Sale, SaleDto>().ReverseMap();
            CreateMap<SaleItem, SaleItemDto>().ReverseMap();
            CreateMap<Payment, PaymentDto>().ReverseMap();
        }
    }
}
