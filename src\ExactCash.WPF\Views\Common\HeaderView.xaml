<UserControl x:Class="ExactCash.WPF.Views.Common.HeaderView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:local="clr-namespace:ExactCash.WPF.Views.Common"
             mc:Ignorable="d"
             d:DesignHeight="60"
             d:DesignWidth="800">
    <Grid Background="#0078D4">
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="Auto"/>
            <ColumnDefinition Width="*"/>
            <ColumnDefinition Width="Auto"/>
            <ColumnDefinition Width="Auto"/>
        </Grid.ColumnDefinitions>

        <!-- Store Name -->
        <TextBlock Grid.Column="0"
                   Text="{Binding StoreName}"
                   Foreground="White"
                   FontSize="18"
                   FontWeight="Bold"
                   FontFamily="Droid Arabic Kufi"
                   Margin="20,0,0,0"
                   VerticalAlignment="Center"/>

        <!-- Transaction ID -->
        <TextBlock Grid.Column="1"
                   Text="{Binding TransactionId}"
                   Foreground="White"
                   FontSize="14"
                   FontFamily="Droid Arabic Kufi"
                   HorizontalAlignment="Center"
                   VerticalAlignment="Center"/>

        <!-- User Info -->
        <StackPanel Grid.Column="2"
                    Orientation="Vertical"
                    Margin="0,0,20,0"
                    VerticalAlignment="Center">
            <TextBlock Text="{Binding UserName}"
                       Foreground="White"
                       FontSize="14"
                       FontFamily="Droid Arabic Kufi"
                       HorizontalAlignment="Right"/>
            <TextBlock Text="{Binding UserRole}"
                       Foreground="White"
                       FontSize="12"
                       FontFamily="Droid Arabic Kufi"
                       HorizontalAlignment="Right"/>
        </StackPanel>

        <!-- Logout Button -->
        <Button Grid.Column="3"
                Content="تسجيل الخروج"
                Command="{Binding LogoutCommand}"
                Style="{StaticResource HeaderButtonStyle}"
                Margin="0,0,20,0"
                VerticalAlignment="Center"/>
    </Grid>
</UserControl> 