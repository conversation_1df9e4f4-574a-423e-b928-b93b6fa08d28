﻿#nullable disable

namespace ExactCash.WASM.Application.DTOs
{
    public class CustomerAnalysisReportDto
    {
        public int CustomerId { get; set; }
        public string CustomerName { get; set; }
        public string Phone { get; set; }
        public string Email { get; set; }
        public int TotalPurchases { get; set; }
        public decimal TotalSpent { get; set; }
        public decimal AverageOrderValue { get; set; }
        public DateTime FirstPurchase { get; set; }
        public DateTime LastPurchase { get; set; }
        public int DaysSinceLastPurchase { get; set; }
        public string CustomerSegment { get; set; }
        public decimal OutstandingBalance { get; set; }
        public int TotalItems { get; set; }
        public decimal LifetimeValue { get; set; }

        // Formatted properties for display
        public string FormattedTotalSpent => $"{TotalSpent:N2} ج.م";
        public string FormattedAverageOrderValue => $"{AverageOrderValue:N2} ج.م";
        public string FormattedOutstandingBalance => $"{OutstandingBalance:N2} ج.م";
        public string FormattedLifetimeValue => $"{LifetimeValue:N2} ج.م";

        public string SegmentColor => CustomerSegment switch
        {
            "VIP" => "#FFD700", // Gold
            "عميل مميز" => "#28A745", // Green
            "عميل عادي" => "#17A2B8", // Blue
            "عميل جديد" => "#6C757D", // Gray
            _ => "#6C757D"
        };

        public CustomerAnalysisReportDto()
        {

        }
    }

    public class TopCustomersReportDto
    {
        public int CustomerId { get; set; }
        public string CustomerName { get; set; }
        public decimal TotalSpent { get; set; }
        public int TotalOrders { get; set; }
        public decimal AverageOrderValue { get; set; }
        public int Rank { get; set; }
        public decimal PercentageOfTotalSales { get; set; }
        public DateTime LastPurchase { get; set; }

        // Formatted properties for display
        public string FormattedTotalSpent => $"{TotalSpent:N2} ج.م";
        public string FormattedAverageOrderValue => $"{AverageOrderValue:N2} ج.م";
        public string FormattedPercentageOfTotalSales => $"{PercentageOfTotalSales:N1}%";

        public TopCustomersReportDto()
        {

        }
    }

    public class CustomerPurchaseHistoryDto
    {
        public int SaleId { get; set; }
        public DateTime PurchaseDate { get; set; }
        public string InvoiceNumber { get; set; }
        public decimal TotalAmount { get; set; }
        public decimal PaidAmount { get; set; }
        public decimal RemainingAmount { get; set; }
        public string PaymentStatus { get; set; }
        public int ItemCount { get; set; }
        public string CashierName { get; set; }

        // Formatted properties for display
        public string FormattedTotalAmount => $"{TotalAmount:N2} ج.م";
        public string FormattedPaidAmount => $"{PaidAmount:N2} ج.م";
        public string FormattedRemainingAmount => $"{RemainingAmount:N2} ج.م";

        public string PaymentStatusColor => PaymentStatus switch
        {
            "مدفوع بالكامل" => "#28A745", // Green
            "مستحق جزئياً" => "#FFC107", // Yellow
            "غير مدفوع" => "#DC3545", // Red
            _ => "#6C757D"
        };

        public CustomerPurchaseHistoryDto()
        {

        }
    }

    public class CustomerAccountStatementDto
    {
        public int CustomerId { get; set; }
        public string CustomerName { get; set; }
        public string Phone { get; set; }
        public string Email { get; set; }
        public string Address { get; set; }
        public DateTime StatementDate { get; set; }
        public DateTime FromDate { get; set; }
        public DateTime ToDate { get; set; }

        // Summary Information
        public decimal OpeningBalance { get; set; }
        public decimal TotalSales { get; set; }
        public decimal TotalPayments { get; set; }
        public decimal TotalReturns { get; set; }
        public decimal ClosingBalance { get; set; }
        public decimal OutstandingBalance { get; set; }

        // Formatted properties for display
        public string FormattedOpeningBalance => $"{OpeningBalance:N2} ج.م";
        public string FormattedTotalSales => $"{TotalSales:N2} ج.م";
        public string FormattedTotalPayments => $"{TotalPayments:N2} ج.م";
        public string FormattedTotalReturns => $"{TotalReturns:N2} ج.م";
        public string FormattedClosingBalance => $"{ClosingBalance:N2} ج.م";
        public string FormattedOutstandingBalance => $"{OutstandingBalance:N2} ج.م";

        public string FormattedStatementDate => StatementDate.ToString("dd/MM/yyyy", new System.Globalization.CultureInfo("ar-EG"));
        public string FormattedFromDate => FromDate.ToString("dd/MM/yyyy", new System.Globalization.CultureInfo("ar-EG"));
        public string FormattedToDate => ToDate.ToString("dd/MM/yyyy", new System.Globalization.CultureInfo("ar-EG"));

        // Transaction Details
        public List<CustomerAccountTransactionDto> Transactions { get; set; } = new List<CustomerAccountTransactionDto>();

        public CustomerAccountStatementDto()
        {

        }
    }

    public class CustomerAccountTransactionDto
    {
        public DateTime TransactionDate { get; set; }
        public string TransactionType { get; set; } // "مبيعات", "دفعة", "مرتجع"
        public string ReferenceNumber { get; set; } // Invoice number, payment reference, etc.
        public string Description { get; set; }
        public decimal DebitAmount { get; set; } // Sales, charges
        public decimal CreditAmount { get; set; } // Payments, returns
        public decimal RunningBalance { get; set; }

        // Formatted properties for display
        public string FormattedTransactionDate => TransactionDate.ToString("dd/MM/yyyy", new System.Globalization.CultureInfo("ar-EG"));
        public string FormattedDebitAmount => DebitAmount > 0 ? $"{DebitAmount:N2} ج.م" : "";
        public string FormattedCreditAmount => CreditAmount > 0 ? $"{CreditAmount:N2} ج.م" : "";
        public string FormattedRunningBalance => $"{RunningBalance:N2} ج.م";

        public string TransactionTypeColor => TransactionType switch
        {
            "مبيعات" => "#DC3545", // Red for sales (debit)
            "دفعة" => "#28A745", // Green for payments (credit)
            "مرتجع" => "#17A2B8", // Blue for returns (credit)
            _ => "#6C757D" // Gray for others
        };

        public CustomerAccountTransactionDto()
        {

        }
    }

    public class SupplierAccountStatementDto
    {
        public int SupplierId { get; set; }
        public string SupplierName { get; set; }
        public string Phone { get; set; }
        public string Email { get; set; }
        public string Address { get; set; }
        public DateTime StatementDate { get; set; }
        public DateTime FromDate { get; set; }
        public DateTime ToDate { get; set; }

        // Summary Information
        public decimal OpeningBalance { get; set; }
        public decimal TotalPurchases { get; set; }
        public decimal TotalPayments { get; set; }
        public decimal TotalReturns { get; set; }
        public decimal ClosingBalance { get; set; }
        public decimal OutstandingBalance { get; set; }

        // Formatted properties for display
        public string FormattedOpeningBalance => $"{OpeningBalance:N2} ج.م";
        public string FormattedTotalPurchases => $"{TotalPurchases:N2} ج.م";
        public string FormattedTotalPayments => $"{TotalPayments:N2} ج.م";
        public string FormattedTotalReturns => $"{TotalReturns:N2} ج.م";
        public string FormattedClosingBalance => $"{ClosingBalance:N2} ج.م";
        public string FormattedOutstandingBalance => $"{OutstandingBalance:N2} ج.م";

        public string FormattedStatementDate => StatementDate.ToString("dd/MM/yyyy", new System.Globalization.CultureInfo("ar-EG"));
        public string FormattedFromDate => FromDate.ToString("dd/MM/yyyy", new System.Globalization.CultureInfo("ar-EG"));
        public string FormattedToDate => ToDate.ToString("dd/MM/yyyy", new System.Globalization.CultureInfo("ar-EG"));

        // Additional formatted properties for compatibility with RDLC
        public string FormattedTotalDebits => $"{TotalPurchases:N2} ج.م";
        public string FormattedTotalCredits => $"{(TotalPayments + TotalReturns):N2} ج.م";

        // Transaction Details
        public List<SupplierAccountTransactionDto> Transactions { get; set; } = new List<SupplierAccountTransactionDto>();

        public SupplierAccountStatementDto()
        {

        }
    }

    public class SupplierAccountTransactionDto
    {
        public DateTime TransactionDate { get; set; }
        public string TransactionType { get; set; } // "مشتريات", "دفعة", "مرتجع"
        public string ReferenceNumber { get; set; } // Purchase order number, payment reference, etc.
        public string Description { get; set; }
        public decimal DebitAmount { get; set; } // Purchases, charges
        public decimal CreditAmount { get; set; } // Payments, returns
        public decimal Balance { get; set; }

        // Formatted properties for display
        public string FormattedTransactionDate => TransactionDate.ToString("dd/MM/yyyy", new System.Globalization.CultureInfo("ar-EG"));
        public string FormattedDebitAmount => DebitAmount > 0 ? $"{DebitAmount:N2} ج.م" : "";
        public string FormattedCreditAmount => CreditAmount > 0 ? $"{CreditAmount:N2} ج.م" : "";
        public string FormattedBalance => $"{Balance:N2} ج.م";

        public string TransactionTypeColor => TransactionType switch
        {
            "مشتريات" => "#DC3545", // Red for purchases (debit)
            "دفعة" => "#28A745", // Green for payments (credit)
            "مرتجع" => "#17A2B8", // Blue for returns (credit)
            _ => "#6C757D" // Gray for others
        };

        public SupplierAccountTransactionDto()
        {

        }
    }
}

namespace ExactCash.WASM.Application.DTOs
{
    public class HourlySalesReportDto
    {
        public int Hour { get; set; }
        public string TimeSlot { get; set; }
        public decimal TotalSales { get; set; }
        public int TransactionCount { get; set; }
        public decimal AverageTransactionValue { get; set; }
        public int ItemsSold { get; set; }
        public DateTime ReportDate { get; set; }

        // Formatted properties for display
        public string FormattedTotalSales => $"{TotalSales:N2} ج.م";
        public string FormattedAverageTransactionValue => $"{AverageTransactionValue:N2} ج.م";

        public HourlySalesReportDto()
        {

        }
    }

    public class CashierPerformanceReportDto
    {
        public string CashierName { get; set; }
        public decimal TotalSales { get; set; }
        public int TransactionCount { get; set; }
        public decimal AverageTransactionValue { get; set; }
        public int ItemsSold { get; set; }
        public decimal TotalDiscounts { get; set; }
        public int RefundCount { get; set; }
        public decimal RefundAmount { get; set; }
        public TimeSpan TotalWorkingHours { get; set; }
        public decimal SalesPerHour { get; set; }
        public DateTime ReportStartDate { get; set; }
        public DateTime ReportEndDate { get; set; }

        // Formatted properties for display
        public string FormattedTotalSales => $"{TotalSales:N2} ج.م";
        public string FormattedAverageTransactionValue => $"{AverageTransactionValue:N2} ج.م";
        public string FormattedTotalDiscounts => $"{TotalDiscounts:N2} ج.م";
        public string FormattedRefundAmount => $"{RefundAmount:N2} ج.م";
        public string FormattedSalesPerHour => $"{SalesPerHour:N2} ج.م/ساعة";
        public string FormattedWorkingHours => $"{TotalWorkingHours.Hours:D2}:{TotalWorkingHours.Minutes:D2}";

        public CashierPerformanceReportDto()
        {

        }
    }

    public class ProductPerformanceReportDto
    {
        public int ProductId { get; set; }
        public string ProductName { get; set; }
        public string CategoryName { get; set; }
        public decimal QuantitySold { get; set; }
        public decimal Revenue { get; set; }
        public decimal Profit { get; set; }
        public decimal ProfitMargin { get; set; }
        public int TransactionCount { get; set; }
        public decimal AverageSellingPrice { get; set; }
        public int Rank { get; set; }
        public decimal PercentageOfTotalSales { get; set; }
        public string PerformanceCategory { get; set; }

        // Formatted properties for display
        public string FormattedQuantitySold => $"{QuantitySold:N2}";
        public string FormattedRevenue => $"{Revenue:N2} ج.م";
        public string FormattedProfit => $"{Profit:N2} ج.م";
        public string FormattedProfitMargin => $"{ProfitMargin:N1}%";
        public string FormattedAverageSellingPrice => $"{AverageSellingPrice:N2} ج.م";
        public string FormattedPercentageOfTotalSales => $"{PercentageOfTotalSales:N1}%";

        public string PerformanceCategoryColor => PerformanceCategory switch
        {
            "أفضل المنتجات" => "#28A745", // Green
            "منتجات جيدة" => "#17A2B8", // Blue
            "منتجات متوسطة" => "#FFC107", // Yellow
            "منتجات ضعيفة" => "#DC3545", // Red
            _ => "#6C757D"
        };

        public ProductPerformanceReportDto()
        {

        }
    }
}
