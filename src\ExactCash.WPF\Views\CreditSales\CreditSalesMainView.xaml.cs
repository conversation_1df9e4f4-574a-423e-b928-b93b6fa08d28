using ExactCash.WPF.ViewModels.CreditSales;
using System.Windows.Controls;

namespace ExactCash.WPF.Views.CreditSales
{
    /// <summary>
    /// Interaction logic for CreditSalesMainView.xaml
    /// </summary>
    public partial class CreditSalesMainView : System.Windows.Controls.UserControl
    {
        public CreditSalesMainView()
        {
            InitializeComponent();
        }

        public CreditSalesMainView(CreditSalesMainViewModel viewModel)
        {
            InitializeComponent();
            DataContext = viewModel;
        }
    }
}
