<Window x:Class="ExactCash.WPF.Views.CreditSales.PaymentHistoryWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="تاريخ المدفوعات"
        Height="600"
        Width="1000"
        WindowStartupLocation="CenterScreen"
        FlowDirection="RightToLeft"
        FontFamily="Segoe UI"
        FontSize="14">
    
    <Window.Resources>
        <!-- Modern Button Style -->
        <Style x:Key="ModernButton" TargetType="Button">
            <Setter Property="Background" Value="#007ACC"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="10,5"/>
            <Setter Property="Margin" Value="5"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                                CornerRadius="3"
                                Padding="{TemplateBinding Padding}">
                            <ContentPresenter HorizontalAlignment="Center"
                                              VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#005A9E"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- Modern Border Style -->
        <Style x:Key="ModernBorder" TargetType="Border">
            <Setter Property="Background" Value="White"/>
            <Setter Property="BorderBrush" Value="#E5E5E5"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="CornerRadius" Value="5"/>
            <Setter Property="Padding" Value="15"/>
        </Style>
    </Window.Resources>

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header Section -->
        <Border Grid.Row="0" Style="{StaticResource ModernBorder}" Margin="0,0,0,15">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0">
                    <TextBlock Text="تاريخ المدفوعات"
                               FontSize="18"
                               FontWeight="Bold"
                               Margin="0,0,0,5"/>
                    <TextBlock Text="{Binding InvoiceInfo}"
                               FontSize="14"
                               Foreground="#666"/>
                </StackPanel>

                <Button Grid.Column="1"
                        Content="✕"
                        Click="CloseButton_Click"
                        Style="{StaticResource ModernButton}"
                        Background="#DC3545"
                        Width="30"
                        Height="30"
                        Padding="0"/>
            </Grid>
        </Border>

        <!-- Payment History Grid -->
        <Border Grid.Row="1" Style="{StaticResource ModernBorder}">
            <DataGrid ItemsSource="{Binding PaymentHistory}"
                      AutoGenerateColumns="False"
                      CanUserAddRows="False"
                      CanUserDeleteRows="False"
                      IsReadOnly="True"
                      SelectionMode="Single"
                      GridLinesVisibility="All"
                      BorderThickness="1"
                      BorderBrush="#E5E5E5"
                      Background="White"
                      RowHeaderWidth="0"
                      HeadersVisibility="Column"
                      HorizontalGridLinesBrush="#E5E5E5"
                      VerticalGridLinesBrush="#E5E5E5"
                      AlternatingRowBackground="#F8F9FA">
                <DataGrid.Columns>
                    <DataGridTextColumn Header="رقم الدفعة"
                                        Binding="{Binding PaymentId}"
                                        Width="100"/>
                    <DataGridTextColumn Header="المبلغ المدفوع"
                                        Binding="{Binding PaymentAmount, StringFormat='{}{0:N2} ج.م'}"
                                        Width="120"/>
                    <DataGridTextColumn Header="طريقة الدفع"
                                        Binding="{Binding PaymentMethod}"
                                        Width="120"/>
                    <DataGridTextColumn Header="تاريخ الدفع"
                                        Binding="{Binding PaymentDate, StringFormat=dd/MM/yyyy HH:mm}"
                                        Width="150"/>
                    <DataGridTextColumn Header="رقم المرجع"
                                        Binding="{Binding ReferenceNumber}"
                                        Width="120"/>
                    <DataGridTextColumn Header="المحصل بواسطة"
                                        Binding="{Binding CollectedBy}"
                                        Width="150"/>
                    <DataGridTextColumn Header="الرصيد المتبقي"
                                        Binding="{Binding RemainingAmountAfterPayment, StringFormat='{}{0:N2} ج.م'}"
                                        Width="120"/>
                    <DataGridTextColumn Header="ملاحظات"
                                        Binding="{Binding Notes}"
                                        Width="*"/>
                </DataGrid.Columns>
            </DataGrid>
        </Border>

        <!-- Footer Section -->
        <Border Grid.Row="2" Style="{StaticResource ModernBorder}" Margin="0,15,0,0">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0" HorizontalAlignment="Center">
                    <TextBlock Text="إجمالي المدفوعات"
                               FontWeight="Bold"
                               HorizontalAlignment="Center"/>
                    <TextBlock Text="{Binding TotalPaidAmount, StringFormat='{}{0:N2} ج.م'}"
                               FontSize="16"
                               FontWeight="Bold"
                               Foreground="#28A745"
                               HorizontalAlignment="Center"/>
                </StackPanel>

                <StackPanel Grid.Column="1" HorizontalAlignment="Center">
                    <TextBlock Text="عدد المدفوعات"
                               FontWeight="Bold"
                               HorizontalAlignment="Center"/>
                    <TextBlock Text="{Binding PaymentCount}"
                               FontSize="16"
                               FontWeight="Bold"
                               Foreground="#17A2B8"
                               HorizontalAlignment="Center"/>
                </StackPanel>

                <StackPanel Grid.Column="2" HorizontalAlignment="Center">
                    <TextBlock Text="الرصيد المتبقي"
                               FontWeight="Bold"
                               HorizontalAlignment="Center"/>
                    <TextBlock Text="{Binding RemainingAmount, StringFormat='{}{0:N2} ج.م'}"
                               FontSize="16"
                               FontWeight="Bold"
                               Foreground="#DC3545"
                               HorizontalAlignment="Center"/>
                </StackPanel>

                <Button Grid.Column="3"
                        Content="إغلاق"
                        Click="CloseButton_Click"
                        Style="{StaticResource ModernButton}"
                        Background="#6C757D"
                        Width="80"/>
            </Grid>
        </Border>
    </Grid>
</Window>
