using ExactCash.WPF.ViewModels;
#nullable disable

namespace ExactCash.WPF.ViewModels.Common
{
    public class LoadingViewModel : ViewModelBase
    {
        private string _message;
        private bool _isVisible;

        public LoadingViewModel()
        {
            Message = "جاري التحميل...";
            IsVisible = false;
        }

        public string Message
        {
            get => _message;
            set => SetProperty(ref _message, value);
        }

        public bool IsVisible
        {
            get => _isVisible;
            set => SetProperty(ref _isVisible, value);
        }

        public void Show(string message = null)
        {
            if (!string.IsNullOrEmpty(message))
            {
                Message = message;
            }
            IsVisible = true;
        }

        public void Hide()
        {
            IsVisible = false;
        }
    }
}