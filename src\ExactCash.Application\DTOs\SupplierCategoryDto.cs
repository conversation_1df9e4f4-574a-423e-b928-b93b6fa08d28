using ExactCash.Application.DTOs.Common;
#nullable disable

namespace ExactCash.Application.DTOs
{
    /// <summary>
    /// Data Transfer Object for SupplierCategory.
    /// </summary>
    public class SupplierCategoryDto : BaseEntityDto
    {
        /// <summary>
        /// The name of the supplier category.
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// The description of the supplier category.
        /// </summary>
        public string Description { get; set; }

        /// <summary>
        /// Indicates whether the supplier category is active.
        /// </summary>
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// Indicates whether the supplier category is deleted.
        /// </summary>
        public bool IsDeleted { get; set; } = false;
    }
}
