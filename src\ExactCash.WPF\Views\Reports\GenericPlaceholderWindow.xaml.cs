using System;
using System.Windows;
using System.Windows.Media;
using Color = System.Windows.Media.Color;
using ColorConverter = System.Windows.Media.ColorConverter;

namespace ExactCash.WPF.Views.Reports
{
    public partial class GenericPlaceholderWindow : Window
    {
        public GenericPlaceholderWindow(string title, string icon = "📊", string color = "#2196F3")
        {
            InitializeComponent();
            SetupWindow(title, icon, color);
        }

        private void SetupWindow(string title, string icon, string color)
        {
            Title = title;
            TitleText.Text = title;
            IconText.Text = icon;

            var brush = new SolidColorBrush((Color)ColorConverter.ConvertFromString(color));
            MainBorder.Background = brush;
        }

        private void Close_Click(object sender, RoutedEventArgs e)
        {
            this.Close();
        }
    }

    // Specific placeholder windows using the generic one
    public class CashFlowReportWindow : GenericPlaceholderWindow
    {
        public CashFlowReportWindow(DateTime? startDate, DateTime? endDate)
            : base("تقرير التدفق النقدي", "💸", "#9C27B0") { }
    }

    public class PaymentMethodReportWindow : GenericPlaceholderWindow
    {
        public PaymentMethodReportWindow(DateTime? startDate, DateTime? endDate)
            : base("تقرير طرق الدفع", "💳", "#009688") { }
    }

    public class TaxReportWindow : GenericPlaceholderWindow
    {
        public TaxReportWindow(DateTime? startDate, DateTime? endDate)
            : base("تقرير الضرائب", "📊", "#3F51B5") { }
    }

    public class ExpenseReportWindow : GenericPlaceholderWindow
    {
        public ExpenseReportWindow(DateTime? startDate, DateTime? endDate)
            : base("تقرير المصروفات", "💰", "#F44336") { }
    }

    public class CustomerAnalysisReportWindow : GenericPlaceholderWindow
    {
        public CustomerAnalysisReportWindow(DateTime? startDate, DateTime? endDate)
            : base("تحليل العملاء", "👥", "#673AB7") { }
    }

    public class TopCustomersReportWindow : GenericPlaceholderWindow
    {
        public TopCustomersReportWindow(DateTime? startDate, DateTime? endDate)
            : base("أفضل العملاء", "🏆", "#FF9800") { }
    }

    public class HourlySalesReportWindow : GenericPlaceholderWindow
    {
        public HourlySalesReportWindow(DateTime? date)
            : base("مبيعات كل ساعة", "⏰", "#4CAF50") { }
    }

    public class CashierPerformanceReportWindow : GenericPlaceholderWindow
    {
        public CashierPerformanceReportWindow(DateTime? startDate, DateTime? endDate)
            : base("أداء الكاشيرين", "👨‍💼", "#607D8B") { }
    }

    public class ProductPerformanceReportWindow : GenericPlaceholderWindow
    {
        public ProductPerformanceReportWindow(DateTime? startDate, DateTime? endDate)
            : base("أداء المنتجات", "📈", "#FF5722") { }
    }

    public class SupplierPerformanceReportWindow : GenericPlaceholderWindow
    {
        public SupplierPerformanceReportWindow(DateTime? startDate, DateTime? endDate)
            : base("أداء الموردين", "🏪", "#795548") { }
    }

    public class SupplierPaymentStatusReportWindow : GenericPlaceholderWindow
    {
        public SupplierPaymentStatusReportWindow()
            : base("حالة مدفوعات الموردين", "💳", "#E91E63") { }
    }

    public class TransactionLogReportWindow : GenericPlaceholderWindow
    {
        public TransactionLogReportWindow(DateTime? startDate, DateTime? endDate)
            : base("سجل المعاملات", "📝", "#424242") { }
    }

    public class SystemActivityReportWindow : GenericPlaceholderWindow
    {
        public SystemActivityReportWindow(DateTime? startDate, DateTime? endDate)
            : base("نشاط النظام", "🔍", "#37474F") { }
    }

    public class CustomerPurchaseHistoryWindow : GenericPlaceholderWindow
    {
        public CustomerPurchaseHistoryWindow(DateTime? startDate, DateTime? endDate)
            : base("تاريخ مشتريات العميل", "📋", "#8BC34A") { }
    }
}
