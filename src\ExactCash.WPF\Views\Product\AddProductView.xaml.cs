using System.Windows;
using System.Windows.Controls;
using ExactCash.WPF.ViewModels.Product;
using ExactCash.WPF.Services.Product;
using ExactCash.WPF.Services;
using AutoMapper;

namespace ExactCash.WPF.Views.Product
{
    public partial class AddProductView : Window
    {
        public AddProductView(
            IProductService productService,
            IMapper mapper,
            ICategoryServiceClient categoryServiceClient,
            IBrandsServiceClient brandsServiceClient,
            IUnitServiceClient unitServiceClient)
        {
            InitializeComponent();
            var viewModel = new AddProductViewModel(
                productService,
                mapper,
                categoryServiceClient,
                brandsServiceClient,
                unitServiceClient);
            viewModel.CurrentWindow = this;
            DataContext = viewModel;
            Loaded += AddProductView_Loaded;
        }

        private void AddProductView_Loaded(object sender, RoutedEventArgs e)
        {
            if (DataContext is AddProductViewModel viewModel)
            {
                viewModel.CloseWindow = result =>
                {
                    if (result.HasValue)
                    {
                        DialogResult = result.Value;
                    }
                    Close();
                };

                // Ensure barcode is generated after view is loaded
                if (string.IsNullOrEmpty(viewModel.Barcode))
                {
                    viewModel.Barcode = viewModel.GenerateBarcode();
                }
            }
            ProductNameTextBox.Focus();
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }
    }
}