using System.Windows;
using AutoMapper;
using ExactCash.WPF.Helpers;
using ExactCash.WPF.Services;
using ExactCash.WPF.Services.Interfaces;
using ExactCash.WPF.ViewModels.Common;
using ExactCash.WPF.Views.Sale;
using ExactCash.WPF.Views.Configuration;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;

namespace ExactCash.WPF.Views.Dashboard
{
    /// <summary>
    /// Interaction logic for DashboardView.xaml
    /// </summary>
    public partial class DashboardView : Window
    {
        private readonly IApiUserService _apiService;
        private readonly ICustomerService _customerService;
        private readonly Services.Product.IProductService _productService;
        private readonly Services.ISaleServiceClient _salesService;
        private readonly LoadingViewModel _loadingViewModel;
        private readonly IUnitServiceClient _unitServiceClient;
        private readonly IMapper _mapper;
        private readonly ICategoryServiceClient _categoryServiceClient;
        private readonly IBrandsServiceClient _brandsServiceClient;
        private readonly ISupplierServiceClient _supplierServiceClient;
        private readonly NotificationViewModel _notificationViewModel;
        private readonly IPurchaseServiceClient _purchaseOrderService;
        private readonly IUserServiceClient _userServiceClient;
        private readonly IRoleServiceClient _roleServiceClient;
        private readonly IConfiguration _configuration;
        private readonly IExpenseCategoryServiceClient _expenseCategoryServiceClient;
        private readonly IExpenseServiceClient _expenseServiceClient;
        private readonly ISystemConfigurationServiceClient _systemConfigurationService;
        private readonly IReportServiceClient _reportServiceClient;
        private readonly ISupplierCategoryServiceClient _supplierCategoryServiceClient;
        private readonly ICustomerCategoryServiceClient _customerCategoryServiceClient;

        public NotificationViewModel NotificationViewModel => _notificationViewModel;

        public DashboardView(
            IApiUserService apiService,
            LoadingViewModel loadingViewModel,
            ICustomerService customerService, Services.Product.IProductService productService,
            Services.ISaleServiceClient salesService, IUnitServiceClient unitServiceClient,
            IMapper mapper, ICategoryServiceClient categoryServiceClient,
            IBrandsServiceClient brandsServiceClient, ISupplierServiceClient supplierServiceClient,
            NotificationViewModel notificationViewModel, IPurchaseServiceClient purchaseOrderService,
            IUserServiceClient userServiceClient,
            IRoleServiceClient roleServiceClient,
            IConfiguration configuration,
            IExpenseCategoryServiceClient expenseCategoryServiceClient, IExpenseServiceClient expenseServiceClient,
            ISystemConfigurationServiceClient systemConfigurationService,
            IReportServiceClient reportServiceClient,
            ISupplierCategoryServiceClient supplierCategoryServiceClient,
            ICustomerCategoryServiceClient customerCategoryServiceClient
            )
        {
            _apiService = apiService;
            _customerService = customerService;
            _productService = productService;
            _salesService = salesService;
            _loadingViewModel = loadingViewModel;
            _unitServiceClient = unitServiceClient;
            _mapper = mapper;
            _categoryServiceClient = categoryServiceClient;
            _brandsServiceClient = brandsServiceClient;
            _supplierServiceClient = supplierServiceClient;
            _notificationViewModel = notificationViewModel;
            _roleServiceClient = roleServiceClient;
            _configuration = configuration;
            _systemConfigurationService = systemConfigurationService;
            InitializeComponent();
            _purchaseOrderService = purchaseOrderService;
            _userServiceClient = userServiceClient;
            _expenseCategoryServiceClient = expenseCategoryServiceClient;
            _expenseServiceClient = expenseServiceClient;
            _reportServiceClient = reportServiceClient;
            _supplierCategoryServiceClient = supplierCategoryServiceClient;
            _customerCategoryServiceClient = customerCategoryServiceClient;

            DataContext = this;
        }

        private void InventoryButton_Click(object sender, RoutedEventArgs e)
        {
            var inventoryStatsWindow = new Views.Inventory.InventoryStatsWindow();
            inventoryStatsWindow.Owner = this;
            inventoryStatsWindow.Show();
        }

        private void PurchasingButton_Click(object sender, RoutedEventArgs e)
        {
            var purchaseOrderView = new Views.PurchaseOrder.PurchaseListView(_purchaseOrderService, _productService, _supplierServiceClient, _unitServiceClient, _mapper, _notificationViewModel);
            purchaseOrderView.Show();
        }

        private void CustomersButton_Click(object sender, RoutedEventArgs e)
        {
            var customersView = new Views.Customer.CustomerListView(_customerService, _customerCategoryServiceClient);
            customersView.Show();
        }

        private void SuppliersButton_Click(object sender, RoutedEventArgs e)
        {
            var suppliers = new Views.Supplier.SupplierMainView(_supplierServiceClient, _supplierCategoryServiceClient, _mapper);
            suppliers.Show();
        }

        private void UserManagementButton_Click(object sender, RoutedEventArgs e)
        {
            var userListView = new Views.User.UserListView(_userServiceClient, _roleServiceClient);
            userListView.Owner = this;
            userListView.Show();
        }

        private void ReportingButton_Click(object sender, RoutedEventArgs e)
        {
            var reportView = new ExactCash.WPF.Views.ReportView(_salesService, _purchaseOrderService, _reportServiceClient, _customerService, _supplierServiceClient);
            reportView.Owner = this;
            reportView.Show();
        }

        private void ProductsButton_Click(object sender, RoutedEventArgs e)
        {
            var AddProductView = new Views.Product.ProductListView(_productService, _categoryServiceClient, _brandsServiceClient, _mapper, _unitServiceClient);
            AddProductView.Show();
        }

        private void SalesButton_Click(object sender, RoutedEventArgs e)
        {
            var saleView = new SaleView(
                _apiService,
                _loadingViewModel,
                _customerService,
                _productService,
                _salesService,
                _unitServiceClient,
                _mapper,
                _categoryServiceClient,
                _brandsServiceClient,
                _supplierServiceClient,
                _notificationViewModel,
                _purchaseOrderService,
                _userServiceClient,
                _roleServiceClient,
                _configuration,
                _expenseCategoryServiceClient,
                _expenseServiceClient,
                _systemConfigurationService,
                _reportServiceClient,
                _supplierCategoryServiceClient,
                _customerCategoryServiceClient
                );
            saleView.Show();
        }

        private void InvoicesButton_Click(object sender, RoutedEventArgs e)
        {
            var salesListView = new SaleListView(
                _salesService,
                _mapper,
                _apiService,
                _loadingViewModel,
                _customerService,
                _productService,
                _salesService,
                _unitServiceClient,
                _categoryServiceClient,
                _brandsServiceClient,
                _supplierServiceClient,
                _notificationViewModel,
                _purchaseOrderService,
                _userServiceClient,
                _roleServiceClient,
                _configuration,
                _expenseCategoryServiceClient,
                _expenseServiceClient,
                _systemConfigurationService,
                _reportServiceClient,
                _supplierCategoryServiceClient,
                _customerCategoryServiceClient
                );

            salesListView.Show();
        }
        private void ReturnsButton_Click(object sender, RoutedEventArgs e)
        {
            BootstrapMessageBoxHelper.Show("سيتم تنفيذ هذه الميزة قريباً");
        }

        private void SettingsButton_Click(object sender, RoutedEventArgs e)
        {
            var posConfigWindow = new POSConfigurationsWindow(_systemConfigurationService, _unitServiceClient, _brandsServiceClient, _categoryServiceClient);
            posConfigWindow.Owner = this;
            posConfigWindow.Show();
        }

        private void ExpensesButton_Click(object sender, RoutedEventArgs e)
        {
            var expenseMainView = new Views.Expense.ExpenseMainView(_expenseServiceClient, _expenseCategoryServiceClient);
            expenseMainView.Show();
        }

        private void CreditSalesButton_Click(object sender, RoutedEventArgs e)
        {
            // Get IPaymentServiceClient from DI container
            var app = (App)System.Windows.Application.Current;
            var paymentServiceClient = app.ServiceProvider.GetRequiredService<IPaymentServiceClient>();

            var creditSalesViewModel = new ViewModels.CreditSales.CreditSalesMainViewModel(
                _salesService,
                _customerService,
                paymentServiceClient);

            var creditSalesView = new Views.CreditSales.CreditSalesMainView(creditSalesViewModel);

            // Create a window to host the UserControl
            var window = new Window
            {
                Title = "إدارة المبيعات الآجلة",
                Content = creditSalesView,
                Width = 1200,
                Height = 800,
                WindowStartupLocation = WindowStartupLocation.CenterScreen,
                Owner = this
            };

            window.Show();
        }
    }
}