﻿<Application x:Class="ExactCash.WPF.App"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:local="clr-namespace:ExactCash.WPF">
        <Application.Resources>
                <ResourceDictionary>
                        <ResourceDictionary.MergedDictionaries>
                                <ResourceDictionary Source="/Resources/Styles.xaml"/>
                        </ResourceDictionary.MergedDictionaries>
                        <Style x:Key="HeaderButtonStyle"
                               TargetType="Button">
                                <Setter Property="Background"
                                        Value="Transparent"/>
                                <Setter Property="Foreground"
                                        Value="White"/>
                                <Setter Property="BorderThickness"
                                        Value="0"/>
                                <Setter Property="Padding"
                                        Value="15,8"/>
                                <Setter Property="FontSize"
                                        Value="14"/>
                                <Setter Property="FontWeight"
                                        Value="SemiBold"/>
                                <Setter Property="FontFamily"
                                        Value="Droid Arabic Kufi"/>
                                <Setter Property="Template">
                                        <Setter.Value>
                                                <ControlTemplate TargetType="Button">
                                                        <Border Background="{TemplateBinding Background}"
                                                                CornerRadius="5">
                                                                <ContentPresenter HorizontalAlignment="Center"
                                                                                  VerticalAlignment="Center"/>
                                                        </Border>
                                                </ControlTemplate>
                                        </Setter.Value>
                                </Setter>
                                <Style.Triggers>
                                        <Trigger Property="IsMouseOver"
                                                 Value="True">
                                                <Setter Property="Background"
                                                        Value="#106EBE"/>
                                        </Trigger>
                                </Style.Triggers>
                        </Style>

                        <Style x:Key="SidebarButtonStyle"
                               TargetType="Button">
                                <Setter Property="Background"
                                        Value="Transparent"/>
                                <Setter Property="Foreground"
                                        Value="#333333"/>
                                <Setter Property="BorderThickness"
                                        Value="0"/>
                                <Setter Property="Padding"
                                        Value="15,10"/>
                                <Setter Property="FontSize"
                                        Value="14"/>
                                <Setter Property="FontWeight"
                                        Value="Normal"/>
                                <Setter Property="FontFamily"
                                        Value="Droid Arabic Kufi"/>
                                <Setter Property="HorizontalContentAlignment"
                                        Value="Right"/>
                                <Setter Property="Template">
                                        <Setter.Value>
                                                <ControlTemplate TargetType="Button">
                                                        <Border Background="{TemplateBinding Background}"
                                                                CornerRadius="5">
                                                                <ContentPresenter HorizontalAlignment="Right"
                                                                                  VerticalAlignment="Center"/>
                                                        </Border>
                                                </ControlTemplate>
                                        </Setter.Value>
                                </Setter>
                                <Style.Triggers>
                                        <Trigger Property="IsMouseOver"
                                                 Value="True">
                                                <Setter Property="Background"
                                                        Value="#E5E5E5"/>
                                                <Setter Property="Foreground"
                                                        Value="#0078D4"/>
                                        </Trigger>
                                </Style.Triggers>
                        </Style>

                        <Style x:Key="NotificationCloseButtonStyle"
                               TargetType="Button">
                                <Setter Property="Background"
                                        Value="Transparent"/>
                                <Setter Property="Foreground"
                                        Value="White"/>
                                <Setter Property="BorderThickness"
                                        Value="0"/>
                                <Setter Property="Padding"
                                        Value="5"/>
                                <Setter Property="FontSize"
                                        Value="14"/>
                                <Setter Property="FontWeight"
                                        Value="Bold"/>
                                <Setter Property="Template">
                                        <Setter.Value>
                                                <ControlTemplate TargetType="Button">
                                                        <Border Background="{TemplateBinding Background}"
                                                                CornerRadius="10">
                                                                <ContentPresenter HorizontalAlignment="Center"
                                                                                  VerticalAlignment="Center"/>
                                                        </Border>
                                                </ControlTemplate>
                                        </Setter.Value>
                                </Setter>
                                <Style.Triggers>
                                        <Trigger Property="IsMouseOver"
                                                 Value="True">
                                                <Setter Property="Background"
                                                        Value="#33FFFFFF"/>
                                        </Trigger>
                                </Style.Triggers>
                        </Style>

                        <Style x:Key="LoadingSpinnerStyle"
                               TargetType="ProgressBar">
                                <Setter Property="Background"
                                        Value="Transparent"/>
                                <Setter Property="Foreground"
                                        Value="White"/>
                                <Setter Property="BorderThickness"
                                        Value="0"/>
                                <Setter Property="IsIndeterminate"
                                        Value="True"/>
                                <Setter Property="Template">
                                        <Setter.Value>
                                                <ControlTemplate TargetType="ProgressBar">
                                                        <Grid>
                                                                <Ellipse Stroke="{TemplateBinding Foreground}"
                                                                         StrokeThickness="3"
                                                                         Width="{TemplateBinding Width}"
                                                                         Height="{TemplateBinding Height}">
                                                                        <Ellipse.RenderTransform>
                                                                                <RotateTransform x:Name="SpinnerRotate"
                                                                                                 Angle="0"/>
                                                                        </Ellipse.RenderTransform>
                                                                        <Ellipse.Triggers>
                                                                                <EventTrigger RoutedEvent="Loaded">
                                                                                        <BeginStoryboard>
                                                                                                <Storyboard>
                                                                                                        <DoubleAnimation Storyboard.TargetName="SpinnerRotate"
                                                                                                                         Storyboard.TargetProperty="Angle"
                                                                                                                         From="0"
                                                                                                                         To="360"
                                                                                                                         Duration="0:0:1"
                                                                                                                         RepeatBehavior="Forever"/>
                                                                                                </Storyboard>
                                                                                        </BeginStoryboard>
                                                                                </EventTrigger>
                                                                        </Ellipse.Triggers>
                                                                </Ellipse>
                                                        </Grid>
                                                </ControlTemplate>
                                        </Setter.Value>
                                </Setter>
                        </Style>

                        <Style x:Key="ModernTextBox"
                               TargetType="TextBox">
                                <Setter Property="Height"
                                        Value="35"/>
                                <Setter Property="MinWidth"
                                        Value="120"/>
                                <Setter Property="Padding"
                                        Value="10,5"/>
                                <Setter Property="Background"
                                        Value="White"/>
                                <Setter Property="Foreground"
                                        Value="#333333"/>
                                <Setter Property="BorderBrush"
                                        Value="#ced4da"/>
                                <Setter Property="BorderThickness"
                                        Value="1"/>
                                <Setter Property="FontSize"
                                        Value="14"/>
                                <Setter Property="Template">
                                        <Setter.Value>
                                                <ControlTemplate TargetType="TextBox">
                                                        <Border Background="{TemplateBinding Background}"
                                                                BorderBrush="{TemplateBinding BorderBrush}"
                                                                BorderThickness="{TemplateBinding BorderThickness}"
                                                                CornerRadius="4">
                                                                <ScrollViewer x:Name="PART_ContentHost"
                                                                              Margin="{TemplateBinding Padding}"
                                                                              VerticalAlignment="Center"/>
                                                        </Border>
                                                        <ControlTemplate.Triggers>
                                                                <Trigger Property="IsMouseOver"
                                                                         Value="True">
                                                                        <Setter Property="BorderBrush"
                                                                                Value="#80bdff"/>
                                                                </Trigger>
                                                                <Trigger Property="IsFocused"
                                                                         Value="True">
                                                                        <Setter Property="BorderBrush"
                                                                                Value="#80bdff"/>
                                                                        <Setter Property="BorderThickness"
                                                                                Value="2"/>
                                                                </Trigger>
                                                                <Trigger Property="IsEnabled"
                                                                         Value="False">
                                                                        <Setter Property="Background"
                                                                                Value="#e9ecef"/>
                                                                        <Setter Property="BorderBrush"
                                                                                Value="#ced4da"/>
                                                                </Trigger>
                                                        </ControlTemplate.Triggers>
                                                </ControlTemplate>
                                        </Setter.Value>
                                </Setter>
                        </Style>

                        <Style x:Key="ModernComboBox"
                               TargetType="ComboBox">
                                <Setter Property="Height"
                                        Value="35"/>
                                <Setter Property="Padding"
                                        Value="8,5"/>
                                <Setter Property="Background"
                                        Value="White"/>
                                <Setter Property="BorderBrush"
                                        Value="#ced4da"/>
                                <Setter Property="BorderThickness"
                                        Value="1"/>
                                <Setter Property="FontSize"
                                        Value="14"/>
                                <Setter Property="ItemTemplate">
                                        <Setter.Value>
                                                <DataTemplate>
                                                        <TextBlock Text="{Binding Name}"/>
                                                </DataTemplate>
                                        </Setter.Value>
                                </Setter>
                                <Setter Property="Template">
                                        <Setter.Value>
                                                <ControlTemplate TargetType="ComboBox">
                                                        <Grid>
                                                                <ToggleButton x:Name="ToggleButton"
                                                                              Background="{TemplateBinding Background}"
                                                                              BorderBrush="{TemplateBinding BorderBrush}"
                                                                              BorderThickness="{TemplateBinding BorderThickness}"
                                                                              Focusable="False"
                                                                              IsChecked="{Binding Path=IsDropDownOpen, Mode=TwoWay, RelativeSource={RelativeSource TemplatedParent}}"
                                                                              ClickMode="Press">
                                                                        <ToggleButton.Template>
                                                                                <ControlTemplate TargetType="ToggleButton">
                                                                                        <Border x:Name="templateRoot"
                                                                                                Background="{TemplateBinding Background}"
                                                                                                BorderBrush="{TemplateBinding BorderBrush}"
                                                                                                BorderThickness="{TemplateBinding BorderThickness}"
                                                                                                CornerRadius="4">
                                                                                                <Grid>
                                                                                                        <Grid.ColumnDefinitions>
                                                                                                                <ColumnDefinition/>
                                                                                                                <ColumnDefinition Width="30"/>
                                                                                                        </Grid.ColumnDefinitions>
                                                                                                        <ContentPresenter Margin="{TemplateBinding Padding}"
                                                                                                                          Content="{TemplateBinding Content}"
                                                                                                                          ContentTemplate="{TemplateBinding ContentTemplate}"
                                                                                                                          HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                                                                                                          VerticalAlignment="{TemplateBinding VerticalContentAlignment}"/>
                                                                                                        <Path x:Name="arrow"
                                                                                                              Grid.Column="1"
                                                                                                              Data="M0,0 L8,0 L4,4 Z"
                                                                                                              Fill="#495057"
                                                                                                              HorizontalAlignment="Center"
                                                                                                              Margin="0,0,0,0"
                                                                                                              VerticalAlignment="Center"/>
                                                                                                </Grid>
                                                                                        </Border>
                                                                                </ControlTemplate>
                                                                        </ToggleButton.Template>
                                                                </ToggleButton>
                                                                <ContentPresenter x:Name="contentPresenter"
                                                                                  Content="{TemplateBinding SelectionBoxItem}"
                                                                                  ContentTemplate="{TemplateBinding SelectionBoxItemTemplate}"
                                                                                  ContentStringFormat="{TemplateBinding SelectionBoxItemStringFormat}"
                                                                                  HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                                                                  IsHitTestVisible="False"
                                                                                  Margin="{TemplateBinding Padding}"
                                                                                  SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}"
                                                                                  VerticalAlignment="{TemplateBinding VerticalContentAlignment}"/>
                                                                <Popup x:Name="PART_Popup"
                                                                       AllowsTransparency="True"
                                                                       IsOpen="{Binding IsDropDownOpen, RelativeSource={RelativeSource TemplatedParent}}"
                                                                       Placement="Bottom"
                                                                       PopupAnimation="Slide">
                                                                        <Border x:Name="dropDownBorder"
                                                                                Background="{TemplateBinding Background}"
                                                                                BorderBrush="{TemplateBinding BorderBrush}"
                                                                                BorderThickness="1"
                                                                                CornerRadius="0,0,4,4"
                                                                                MaxHeight="{TemplateBinding MaxDropDownHeight}"
                                                                                Width="{Binding ActualWidth, RelativeSource={RelativeSource AncestorType=ComboBox}}">
                                                                                <ScrollViewer>
                                                                                        <ItemsPresenter x:Name="ItemsPresenter"
                                                                                                        KeyboardNavigation.DirectionalNavigation="Contained"
                                                                                                        SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}"/>
                                                                                </ScrollViewer>
                                                                        </Border>
                                                                </Popup>
                                                        </Grid>
                                                        <ControlTemplate.Triggers>
                                                                <Trigger Property="IsMouseOver"
                                                                         Value="True">
                                                                        <Setter Property="BorderBrush"
                                                                                Value="#80bdff"/>
                                                                </Trigger>
                                                                <Trigger Property="IsEnabled"
                                                                         Value="False">
                                                                        <Setter Property="Background"
                                                                                Value="#e9ecef"/>
                                                                        <Setter Property="BorderBrush"
                                                                                Value="#ced4da"/>
                                                                </Trigger>
                                                                <Trigger Property="IsDropDownOpen"
                                                                         Value="True">
                                                                        <Setter Property="BorderBrush"
                                                                                Value="#80bdff"/>
                                                                        <Setter Property="BorderThickness"
                                                                                Value="2"/>
                                                                </Trigger>
                                                        </ControlTemplate.Triggers>
                                                </ControlTemplate>
                                        </Setter.Value>
                                </Setter>
                        </Style>

                        <!-- Success Button Style -->
                        <Style x:Key="SuccessButton"
                               TargetType="Button">
                                <Setter Property="Background"
                                        Value="#28a745"/>
                                <Setter Property="Foreground"
                                        Value="White"/>
                                <Setter Property="BorderBrush"
                                        Value="#28a745"/>
                                <Setter Property="FontSize"
                                        Value="14"/>
                                <Setter Property="Padding"
                                        Value="15,5"/>
                                <Setter Property="Template">
                                        <Setter.Value>
                                                <ControlTemplate TargetType="Button">
                                                        <Border Background="{TemplateBinding Background}"
                                                                BorderBrush="{TemplateBinding BorderBrush}"
                                                                BorderThickness="1"
                                                                CornerRadius="4">
                                                                <ContentPresenter HorizontalAlignment="Center"
                                                                                  VerticalAlignment="Center"/>
                                                        </Border>
                                                        <ControlTemplate.Triggers>
                                                                <Trigger Property="IsMouseOver"
                                                                         Value="True">
                                                                        <Setter Property="Background"
                                                                                Value="#218838"/>
                                                                        <Setter Property="BorderBrush"
                                                                                Value="#1e7e34"/>
                                                                </Trigger>
                                                                <Trigger Property="IsPressed"
                                                                         Value="True">
                                                                        <Setter Property="Background"
                                                                                Value="#1e7e34"/>
                                                                        <Setter Property="BorderBrush"
                                                                                Value="#1c7430"/>
                                                                </Trigger>
                                                                <Trigger Property="IsEnabled"
                                                                         Value="False">
                                                                        <Setter Property="Opacity"
                                                                                Value="0.65"/>
                                                                </Trigger>
                                                        </ControlTemplate.Triggers>
                                                </ControlTemplate>
                                        </Setter.Value>
                                </Setter>
                        </Style>

                        <!-- Danger Button Style -->
                        <Style x:Key="DangerButton"
                               TargetType="Button">
                                <Setter Property="Background"
                                        Value="#dc3545"/>
                                <Setter Property="Foreground"
                                        Value="White"/>
                                <Setter Property="BorderBrush"
                                        Value="#dc3545"/>
                                <Setter Property="FontSize"
                                        Value="14"/>
                                <Setter Property="Padding"
                                        Value="15,5"/>
                                <Setter Property="Template">
                                        <Setter.Value>
                                                <ControlTemplate TargetType="Button">
                                                        <Border Background="{TemplateBinding Background}"
                                                                BorderBrush="{TemplateBinding BorderBrush}"
                                                                BorderThickness="1"
                                                                CornerRadius="4">
                                                                <ContentPresenter HorizontalAlignment="Center"
                                                                                  VerticalAlignment="Center"/>
                                                        </Border>
                                                        <ControlTemplate.Triggers>
                                                                <Trigger Property="IsMouseOver"
                                                                         Value="True">
                                                                        <Setter Property="Background"
                                                                                Value="#c82333"/>
                                                                        <Setter Property="BorderBrush"
                                                                                Value="#bd2130"/>
                                                                </Trigger>
                                                                <Trigger Property="IsPressed"
                                                                         Value="True">
                                                                        <Setter Property="Background"
                                                                                Value="#bd2130"/>
                                                                        <Setter Property="BorderBrush"
                                                                                Value="#b21f2d"/>
                                                                </Trigger>
                                                                <Trigger Property="IsEnabled"
                                                                         Value="False">
                                                                        <Setter Property="Opacity"
                                                                                Value="0.65"/>
                                                                </Trigger>
                                                        </ControlTemplate.Triggers>
                                                </ControlTemplate>
                                        </Setter.Value>
                                </Setter>
                        </Style>

                        <!-- White Button Style -->
                        <Style x:Key="WhiteButton"
                               TargetType="Button">
                                <Setter Property="Background"
                                        Value="White"/>
                                <Setter Property="Foreground"
                                        Value="#0078D4"/>
                                <Setter Property="BorderThickness"
                                        Value="0"/>
                                <Setter Property="FontSize"
                                        Value="14"/>
                                <Setter Property="Padding"
                                        Value="15,5"/>
                                <Setter Property="Template">
                                        <Setter.Value>
                                                <ControlTemplate TargetType="Button">
                                                        <Border Background="{TemplateBinding Background}"
                                                                BorderBrush="{TemplateBinding BorderBrush}"
                                                                BorderThickness="{TemplateBinding BorderThickness}"
                                                                CornerRadius="4">
                                                                <ContentPresenter HorizontalAlignment="Center"
                                                                                  VerticalAlignment="Center"/>
                                                        </Border>
                                                        <ControlTemplate.Triggers>
                                                                <Trigger Property="IsMouseOver"
                                                                         Value="True">
                                                                        <Setter Property="Background"
                                                                                Value="#f0f0f0"/>
                                                                </Trigger>
                                                                <Trigger Property="IsPressed"
                                                                         Value="True">
                                                                        <Setter Property="Background"
                                                                                Value="#e0e0e0"/>
                                                                </Trigger>
                                                                <Trigger Property="IsEnabled"
                                                                         Value="False">
                                                                        <Setter Property="Opacity"
                                                                                Value="0.65"/>
                                                                </Trigger>
                                                        </ControlTemplate.Triggers>
                                                </ControlTemplate>
                                        </Setter.Value>
                                </Setter>
                        </Style>

                        <!-- Transparent Button Style -->
                        <Style x:Key="TransparentButton"
                               TargetType="Button">
                                <Setter Property="Background"
                                        Value="Transparent"/>
                                <Setter Property="Foreground"
                                        Value="White"/>
                                <Setter Property="BorderBrush"
                                        Value="White"/>
                                <Setter Property="BorderThickness"
                                        Value="1"/>
                                <Setter Property="FontSize"
                                        Value="14"/>
                                <Setter Property="Padding"
                                        Value="15,5"/>
                                <Setter Property="Template">
                                        <Setter.Value>
                                                <ControlTemplate TargetType="Button">
                                                        <Border Background="{TemplateBinding Background}"
                                                                BorderBrush="{TemplateBinding BorderBrush}"
                                                                BorderThickness="{TemplateBinding BorderThickness}"
                                                                CornerRadius="4">
                                                                <ContentPresenter HorizontalAlignment="Center"
                                                                                  VerticalAlignment="Center"/>
                                                        </Border>
                                                        <ControlTemplate.Triggers>
                                                                <Trigger Property="IsMouseOver"
                                                                         Value="True">
                                                                        <Setter Property="Background"
                                                                                Value="#ffffff20"/>
                                                                </Trigger>
                                                                <Trigger Property="IsPressed"
                                                                         Value="True">
                                                                        <Setter Property="Background"
                                                                                Value="#ffffff40"/>
                                                                </Trigger>
                                                                <Trigger Property="IsEnabled"
                                                                         Value="False">
                                                                        <Setter Property="Opacity"
                                                                                Value="0.65"/>
                                                                </Trigger>
                                                        </ControlTemplate.Triggers>
                                                </ControlTemplate>
                                        </Setter.Value>
                                </Setter>
                        </Style>
                </ResourceDictionary>
        </Application.Resources>
</Application>
