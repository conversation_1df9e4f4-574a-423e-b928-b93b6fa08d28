﻿#nullable disable
namespace ExactCash.WPF.Models.Fdin
{
    /// <summary>
    /// Represents an item in a sale transaction (product sold, quantity, price).
    /// </summary>
    public class SaleItem
    {
        /// <summary>
        /// The unique identifier for this sale item.
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// Foreign key to the sale this item belongs to.
        /// </summary>
        public int SaleId { get; set; }

        /// <summary>
        /// Navigation property to the parent sale.
        /// </summary>
        public Sale Sale { get; set; }

        /// <summary>
        /// Navigation property to the product being sold.
        /// </summary>
        public string ProductName{ get; set; }

        /// <summary>
        /// Navigation property to the untit of measure for this item.
        /// </summary>
        public string UnitName { get; set; }

        /// <summary>
        /// Quantity of the product sold.
        /// </summary>
        public decimal Quantity { get; set; }

        /// <summary>
        /// Selling price of a single unit of the product.
        /// </summary>
        public decimal Price { get; set; }

        /// <summary>
        /// TotalPrice
        /// </summary>
        public decimal TotalPrice => Quantity * Price;

        /// <summary>
        /// Discount applied to this item.
        /// </summary>
        public decimal Discount { get; set; }
    }
}
