using ExactCash.Application.DTOs;
using ExactCash.WPF.ViewModels.CreditSales;
using System.Windows;

namespace ExactCash.WPF.Views.CreditSales
{
    /// <summary>
    /// Interaction logic for PaymentCollectionView.xaml
    /// </summary>
    public partial class PaymentCollectionView : Window
    {
        public PaymentCollectionView()
        {
            InitializeComponent();
            DataContext = new PaymentCollectionViewModel();
        }

        public PaymentCollectionView(OutstandingSaleDto sale)
        {
            InitializeComponent();
            DataContext = new PaymentCollectionViewModel(sale);
        }

        public PaymentCollectionView(int customerId)
        {
            InitializeComponent();
            DataContext = new PaymentCollectionViewModel(customerId);
        }

        public PaymentCollectionView(CustomerDto customer)
        {
            InitializeComponent();
            DataContext = new PaymentCollectionViewModel(customer);
        }
    }
}
