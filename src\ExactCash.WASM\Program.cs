using ExactCash.WASM;
using ExactCash.WASM.Identity;
using ExactCash.WASM.Services;
using ExactCash.WASM.Services.Common;
using ExactCash.WASM.Services.Interfaces;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.AspNetCore.Components.Web;
using Microsoft.AspNetCore.Components.WebAssembly.Hosting;

var builder = WebAssemblyHostBuilder.CreateDefault(args);
builder.RootComponents.Add<App>("#app");
builder.RootComponents.Add<HeadOutlet>("head::after");
var apiBaseUrl = builder.Configuration["ServiceUrls:ExactCashAPI"] ?? "";

builder.Services.AddAuthorizationCore();
builder.Services.AddScoped<HttpService>();
builder.Services.AddScoped(sp => (IAccountManagement)sp.GetRequiredService<AuthenticationStateProvider>());
builder.Services.AddScoped<AuthenticationStateProvider, CookieAuthenticationStateProvider>();
builder.Services.AddScoped<IAuthServiceClient, AuthServiceClient>();
builder.Services.AddScoped(sp => new HttpClient { BaseAddress = new Uri(builder.HostEnvironment.BaseAddress) });
builder.Services.AddHttpClient("ExactCashAPI", options =>
    options.BaseAddress = new Uri(apiBaseUrl)
)
.AddHttpMessageHandler<CookieHandler>();

builder.Services.AddTransient<CookieHandler>();





await builder.Build().RunAsync();
