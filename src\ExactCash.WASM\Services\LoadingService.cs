using ExactCash.WASM.Services.Interfaces;

namespace ExactCash.WASM.Services;

public class LoadingService : ILoadingService
{
    private bool _isLoading;
    private string _loadingMessage = "جاري التحميل...";

    public bool IsLoading
    {
        get => _isLoading;
        private set
        {
            _isLoading = value;
            OnChange?.Invoke();
        }
    }

    public string LoadingMessage
    {
        get => _loadingMessage;
        private set
        {
            _loadingMessage = value;
            OnChange?.Invoke();
        }
    }

    public event Action? OnChange;

    public void Show(string message = "جاري التحميل...")
    {
        LoadingMessage = message;
        IsLoading = true;
    }

    public void Hide()
    {
        IsLoading = false;
        LoadingMessage = "جاري التحميل...";
    }

    public async Task ShowAsync(string message = "جاري التحميل...")
    {
        await Task.Run(() => Show(message));
    }

    public async Task HideAsync()
    {
        await Task.Run(() => Hide());
    }
}
