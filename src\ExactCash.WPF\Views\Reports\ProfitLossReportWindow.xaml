﻿<Window x:Class="ExactCash.WPF.Views.Reports.ProfitLossReportWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        mc:Ignorable="d"
        Title="تقرير الأرباح والخسائر - ExactCash POS"
        Height="600"
        Width="800"
        WindowStartupLocation="CenterScreen"
        FlowDirection="RightToLeft"
        FontFamily="Segoe UI">

        <Window.Resources>
                <Style x:Key="SectionHeaderStyle"
                       TargetType="TextBlock">
                        <Setter Property="FontSize"
                                Value="18"/>
                        <Setter Property="FontWeight"
                                Value="Bold"/>
                        <Setter Property="Foreground"
                                Value="#1976D2"/>
                        <Setter Property="Margin"
                                Value="0,15,0,10"/>
                </Style>

                <Style x:Key="LabelStyle"
                       TargetType="TextBlock">
                        <Setter Property="FontSize"
                                Value="14"/>
                        <Setter Property="FontWeight"
                                Value="SemiBold"/>
                        <Setter Property="Margin"
                                Value="0,5"/>
                        <Setter Property="VerticalAlignment"
                                Value="Center"/>
                </Style>

                <Style x:Key="ValueStyle"
                       TargetType="TextBlock">
                        <Setter Property="FontSize"
                                Value="14"/>
                        <Setter Property="FontWeight"
                                Value="Bold"/>
                        <Setter Property="Margin"
                                Value="0,5"/>
                        <Setter Property="VerticalAlignment"
                                Value="Center"/>
                        <Setter Property="HorizontalAlignment"
                                Value="Right"/>
                </Style>

                <Style x:Key="PositiveValueStyle"
                       TargetType="TextBlock"
                       BasedOn="{StaticResource ValueStyle}">
                        <Setter Property="Foreground"
                                Value="#4CAF50"/>
                </Style>

                <Style x:Key="NegativeValueStyle"
                       TargetType="TextBlock"
                       BasedOn="{StaticResource ValueStyle}">
                        <Setter Property="Foreground"
                                Value="#F44336"/>
                </Style>

                <Style x:Key="TotalValueStyle"
                       TargetType="TextBlock"
                       BasedOn="{StaticResource ValueStyle}">
                        <Setter Property="FontSize"
                                Value="16"/>
                        <Setter Property="Background"
                                Value="#F5F5F5"/>
                        <Setter Property="Padding"
                                Value="10,5"/>
                </Style>
        </Window.Resources>

        <Grid>
                <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <!-- Header -->
                <Border Grid.Row="0"
                        Background="#1976D2"
                        Padding="20">
                        <StackPanel Orientation="Horizontal">
                                <TextBlock Text="💰"
                                           FontSize="24"
                                           Margin="0,0,10,0"
                                           Foreground="White"/>
                                <TextBlock Text="تقرير الأرباح والخسائر"
                                           FontSize="24"
                                           FontWeight="Bold"
                                           Foreground="White"/>
                                <TextBlock x:Name="DateRangeText"
                                           Text=""
                                           FontSize="14"
                                           Foreground="White"
                                           Margin="20,0,0,0"
                                           VerticalAlignment="Center"/>
                        </StackPanel>
                </Border>

                <!-- Content -->
                <ScrollViewer Grid.Row="1"
                              VerticalScrollBarVisibility="Auto"
                              Padding="30">
                        <StackPanel>

                                <!-- Revenue Section -->
                                <TextBlock Text="الإيرادات"
                                           Style="{StaticResource SectionHeaderStyle}"/>
                                <Grid Margin="20,0,0,0">
                                        <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="*"/>
                                                <ColumnDefinition Width="200"/>
                                        </Grid.ColumnDefinitions>
                                        <Grid.RowDefinitions>
                                                <RowDefinition Height="Auto"/>
                                                <RowDefinition Height="Auto"/>
                                                <RowDefinition Height="Auto"/>
                                        </Grid.RowDefinitions>

                                        <TextBlock Grid.Row="0"
                                                   Grid.Column="0"
                                                   Text="إجمالي المبيعات"
                                                   Style="{StaticResource LabelStyle}"/>
                                        <TextBlock Grid.Row="0"
                                                   Grid.Column="1"
                                                   x:Name="TotalRevenueText"
                                                   Text="0.00 ج.م"
                                                   Style="{StaticResource PositiveValueStyle}"/>

                                        <TextBlock Grid.Row="1"
                                                   Grid.Column="0"
                                                   Text="إجمالي الخصومات"
                                                   Style="{StaticResource LabelStyle}"/>
                                        <TextBlock Grid.Row="1"
                                                   Grid.Column="1"
                                                   x:Name="TotalDiscountsText"
                                                   Text="0.00 ج.م"
                                                   Style="{StaticResource NegativeValueStyle}"/>

                                        <Separator Grid.Row="2"
                                                   Grid.ColumnSpan="2"
                                                   Margin="0,10"/>
                                </Grid>

                                <!-- Cost of Goods Sold Section -->
                                <TextBlock Text="تكلفة البضاعة المباعة"
                                           Style="{StaticResource SectionHeaderStyle}"/>
                                <Grid Margin="20,0,0,0">
                                        <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="*"/>
                                                <ColumnDefinition Width="200"/>
                                        </Grid.ColumnDefinitions>
                                        <Grid.RowDefinitions>
                                                <RowDefinition Height="Auto"/>
                                                <RowDefinition Height="Auto"/>
                                        </Grid.RowDefinitions>

                                        <TextBlock Grid.Row="0"
                                                   Grid.Column="0"
                                                   Text="تكلفة البضاعة المباعة"
                                                   Style="{StaticResource LabelStyle}"/>
                                        <TextBlock Grid.Row="0"
                                                   Grid.Column="1"
                                                   x:Name="TotalCOGSText"
                                                   Text="0.00 ج.م"
                                                   Style="{StaticResource NegativeValueStyle}"/>

                                        <Separator Grid.Row="1"
                                                   Grid.ColumnSpan="2"
                                                   Margin="0,10"/>
                                </Grid>

                                <!-- Gross Profit -->
                                <Grid Margin="20,0,0,0">
                                        <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="*"/>
                                                <ColumnDefinition Width="200"/>
                                        </Grid.ColumnDefinitions>

                                        <TextBlock Grid.Column="0"
                                                   Text="إجمالي الربح"
                                                   Style="{StaticResource SectionHeaderStyle}"/>
                                        <TextBlock Grid.Column="1"
                                                   x:Name="GrossProfitText"
                                                   Text="0.00 ج.م"
                                                   Style="{StaticResource TotalValueStyle}"/>
                                </Grid>

                                <!-- Operating Expenses Section -->
                                <TextBlock Text="المصروفات التشغيلية"
                                           Style="{StaticResource SectionHeaderStyle}"/>
                                <Grid Margin="20,0,0,0">
                                        <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="*"/>
                                                <ColumnDefinition Width="200"/>
                                        </Grid.ColumnDefinitions>
                                        <Grid.RowDefinitions>
                                                <RowDefinition Height="Auto"/>
                                                <RowDefinition Height="Auto"/>
                                                <RowDefinition Height="Auto"/>
                                        </Grid.RowDefinitions>

                                        <TextBlock Grid.Row="0"
                                                   Grid.Column="0"
                                                   Text="إجمالي المصروفات"
                                                   Style="{StaticResource LabelStyle}"/>
                                        <TextBlock Grid.Row="0"
                                                   Grid.Column="1"
                                                   x:Name="TotalExpensesText"
                                                   Text="0.00 ج.م"
                                                   Style="{StaticResource NegativeValueStyle}"/>

                                        <TextBlock Grid.Row="1"
                                                   Grid.Column="0"
                                                   Text="إجمالي الضرائب"
                                                   Style="{StaticResource LabelStyle}"/>
                                        <TextBlock Grid.Row="1"
                                                   Grid.Column="1"
                                                   x:Name="TotalTaxText"
                                                   Text="0.00 ج.م"
                                                   Style="{StaticResource NegativeValueStyle}"/>

                                        <Separator Grid.Row="2"
                                                   Grid.ColumnSpan="2"
                                                   Margin="0,10"/>
                                </Grid>

                                <!-- Net Profit -->
                                <Grid Margin="20,0,0,0">
                                        <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="*"/>
                                                <ColumnDefinition Width="200"/>
                                        </Grid.ColumnDefinitions>

                                        <TextBlock Grid.Column="0"
                                                   Text="صافي الربح"
                                                   Style="{StaticResource SectionHeaderStyle}"/>
                                        <TextBlock Grid.Column="1"
                                                   x:Name="NetProfitText"
                                                   Text="0.00 ج.م"
                                                   Style="{StaticResource TotalValueStyle}"/>
                                </Grid>

                                <!-- Key Metrics -->
                                <TextBlock Text="المؤشرات الرئيسية"
                                           Style="{StaticResource SectionHeaderStyle}"/>
                                <Grid Margin="20,0,0,0">
                                        <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="*"/>
                                                <ColumnDefinition Width="200"/>
                                        </Grid.ColumnDefinitions>
                                        <Grid.RowDefinitions>
                                                <RowDefinition Height="Auto"/>
                                                <RowDefinition Height="Auto"/>
                                                <RowDefinition Height="Auto"/>
                                                <RowDefinition Height="Auto"/>
                                        </Grid.RowDefinitions>

                                        <TextBlock Grid.Row="0"
                                                   Grid.Column="0"
                                                   Text="هامش الربح الإجمالي"
                                                   Style="{StaticResource LabelStyle}"/>
                                        <TextBlock Grid.Row="0"
                                                   Grid.Column="1"
                                                   x:Name="GrossProfitMarginText"
                                                   Text="0.0%"
                                                   Style="{StaticResource ValueStyle}"/>

                                        <TextBlock Grid.Row="1"
                                                   Grid.Column="0"
                                                   Text="هامش الربح الصافي"
                                                   Style="{StaticResource LabelStyle}"/>
                                        <TextBlock Grid.Row="1"
                                                   Grid.Column="1"
                                                   x:Name="NetProfitMarginText"
                                                   Text="0.0%"
                                                   Style="{StaticResource ValueStyle}"/>

                                        <TextBlock Grid.Row="2"
                                                   Grid.Column="0"
                                                   Text="عدد المعاملات"
                                                   Style="{StaticResource LabelStyle}"/>
                                        <TextBlock Grid.Row="2"
                                                   Grid.Column="1"
                                                   x:Name="TotalTransactionsText"
                                                   Text="0"
                                                   Style="{StaticResource ValueStyle}"/>

                                        <TextBlock Grid.Row="3"
                                                   Grid.Column="0"
                                                   Text="متوسط قيمة المعاملة"
                                                   Style="{StaticResource LabelStyle}"/>
                                        <TextBlock Grid.Row="3"
                                                   Grid.Column="1"
                                                   x:Name="AverageTransactionValueText"
                                                   Text="0.00 ج.م"
                                                   Style="{StaticResource ValueStyle}"/>
                                </Grid>

                        </StackPanel>
                </ScrollViewer>

                <!-- Footer -->
                <Border Grid.Row="2"
                        Background="#F5F5F5"
                        Padding="20">
                        <StackPanel Orientation="Horizontal"
                                    HorizontalAlignment="Center">
                                <Button Content="تحديث البيانات"
                                        Background="#2196F3"
                                        Foreground="White"
                                        Padding="15,8"
                                        Margin="5"
                                        BorderThickness="0"
                                        Click="RefreshData_Click"/>
                                <Button Content="طباعة التقرير"
                                        Background="#4CAF50"
                                        Foreground="White"
                                        Padding="15,8"
                                        Margin="5"
                                        BorderThickness="0"
                                        Click="PrintReport_Click"/>
                                <Button Content="إغلاق"
                                        Background="#757575"
                                        Foreground="White"
                                        Padding="15,8"
                                        Margin="5"
                                        BorderThickness="0"
                                        Click="Close_Click"/>
                        </StackPanel>
                </Border>
        </Grid>
</Window>
