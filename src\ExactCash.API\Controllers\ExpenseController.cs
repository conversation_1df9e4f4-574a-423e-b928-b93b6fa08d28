﻿using ExactCash.Application.DTOs;
using ExactCash.Application.Interfaces;
using Microsoft.AspNetCore.Mvc;

namespace ExactCash.API.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class ExpenseController : ControllerBase
    {
        private readonly IExpenseService _service;

        public ExpenseController(IExpenseService service)
        {
            _service = service;
        }

        [HttpGet]
        public async Task<ActionResult<IEnumerable<ExpenseDto>>> GetAll()
        {
            var categories = await _service.GetAllAsync(null);
            return Ok(categories);
        }

        [HttpGet("{id}")]
        public async Task<ActionResult<ExpenseDto>> GetById(int id)
        {
            var category = await _service.GetByIdAsync(id);
            if (category == null)
                return NotFound();
            return Ok(category);
        }

        [HttpPost]
        public async Task<ActionResult<ExpenseDto>> Create([FromBody] ExpenseDto dto)
        {
            if (dto == null)
                return BadRequest();

            var created = await _service.CreateAsync(dto);
            return CreatedAtAction(nameof(GetById), new { id = created.Id }, created);
        }

        [HttpPut("{id}")]
        public async Task<IActionResult> Update(int id, [FromBody] ExpenseDto dto)
        {
            if (dto == null || dto.Id != id)
                return BadRequest();

            var updated = await _service.UpdateAsync(dto);
            if (!updated)
                return NotFound();

            return Ok(updated);
        }

        [HttpDelete("{id}")]
        public async Task<IActionResult> Delete(int id)
        {
            var deleted = await _service.DeleteAsync(id);
            if (!deleted)
                return NotFound();

            return NoContent();
        }

        [HttpPost("search")]
        public async Task<ActionResult<IEnumerable<ExpenseDto>>> Search([FromBody] ExpenseSearchDto search)
        {
            var expenses = await _service.GetAllAsync(search);
            return Ok(expenses);
        }
    }
}
