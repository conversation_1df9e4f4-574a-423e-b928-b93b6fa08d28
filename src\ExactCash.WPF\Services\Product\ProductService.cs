using ExactCash.Application.DTOs;
using ExactCash.Application.Responses;
using Microsoft.AspNetCore.Http;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Globalization;
using ExactCash.Application.DTOs.Common;

namespace ExactCash.WPF.Services.Product
{
    public class ProductService : IProductService
    {
        private readonly HttpService _httpService;

        public ProductService(string baseUrl)
        {
            _httpService = new HttpService(baseUrl);
        }

        public async Task<List<ProductDto>> SearchProductsAsync(string searchTerm)
        {
            return await _httpService.GetAsync<List<ProductDto>>($"api/product/search?term={Uri.EscapeDataString(searchTerm)}");
        }

        public async Task<Domain.Entities.Product> GetProductByIdAsync(int id)
        {
            return await _httpService.GetAsync<Domain.Entities.Product>($"api/product/{id}");
        }

        public async Task<ExactCash.Application.DTOs.ProductDto> GetProductByBarcodeAsync(string barcode)
        {
            return await _httpService.GetAsync<ExactCash.Application.DTOs.ProductDto>($"api/product/get-product-by-barcode/{Uri.EscapeDataString(barcode)}");
        }

        public async Task<BaseResponse<Domain.Entities.Product>> CreateProductAsync(Domain.Entities.Product product, IFormFile imageFile)
        {
            using var content = new MultipartFormDataContent();

            // Add image file if provided
            if (imageFile != null && imageFile.Length > 0)
            {
                var streamContent = new StreamContent(imageFile.OpenReadStream());
                streamContent.Headers.ContentType = new MediaTypeHeaderValue(imageFile.ContentType);
                content.Add(streamContent, "ImageFile", imageFile.FileName);
            }

            // Add product properties as form fields
            content.Add(new StringContent(product.Name ?? ""), "Name");
            content.Add(new StringContent(product.SKU ?? ""), "SKU");
            content.Add(new StringContent(product.Barcode ?? ""), "Barcode");
            content.Add(new StringContent(product.Description ?? ""), "Description");
            content.Add(new StringContent(product.CostPrice.ToString("F2", CultureInfo.InvariantCulture)), "CostPrice");
            content.Add(new StringContent(product.SellingPrice.ToString("F2", CultureInfo.InvariantCulture)), "SellingPrice");
            content.Add(new StringContent(product.StockQuantity.ToString("F2", CultureInfo.InvariantCulture)), "StockQuantity");
            content.Add(new StringContent(product.Discount.ToString("F2", CultureInfo.InvariantCulture)), "Discount");

            if (product.CategoryId.HasValue)
            {
                content.Add(new StringContent(product.CategoryId.Value.ToString()), "CategoryId");
            }

            if (product.BrandId.HasValue)
            {
                content.Add(new StringContent(product.BrandId.Value.ToString()), "BrandId");
            }

            if (product.DefaultUnitId.HasValue)
            {
                content.Add(new StringContent(product.DefaultUnitId.Value.ToString()), "DefaultUnitId");
                content.Add(new StringContent(product.DefaultUnitId.Value.ToString()), "UnitId");
            }

            return await _httpService.PostAsync<BaseResponse<Domain.Entities.Product>>("api/product/create-product", content);
        }

        public async Task<BaseResponse<Domain.Entities.Product>> UpdateProductAsync(int Id, Domain.Entities.Product product, IFormFile imageFile)
        {
            using var content = new MultipartFormDataContent();

            // Add image file if provided
            if (imageFile != null && imageFile.Length > 0)
            {
                var streamContent = new StreamContent(imageFile.OpenReadStream());
                streamContent.Headers.ContentType = new MediaTypeHeaderValue(imageFile.ContentType);
                content.Add(streamContent, "ImageFile", imageFile.FileName);
            }

            // Add product properties as form fields
            content.Add(new StringContent(Convert.ToString(product.Id)), "Id");
            content.Add(new StringContent(product.Name ?? ""), "Name");
            content.Add(new StringContent(product.SKU ?? ""), "SKU");
            content.Add(new StringContent(product.Barcode ?? ""), "Barcode");
            content.Add(new StringContent(product.Description ?? ""), "Description");
            content.Add(new StringContent(product.CostPrice.ToString("F2", CultureInfo.InvariantCulture)), "CostPrice");
            content.Add(new StringContent(product.SellingPrice.ToString("F2", CultureInfo.InvariantCulture)), "SellingPrice");
            content.Add(new StringContent(product.StockQuantity.ToString("F2", CultureInfo.InvariantCulture)), "StockQuantity");
            content.Add(new StringContent(product.Discount.ToString("F2", CultureInfo.InvariantCulture)), "Discount");

            if (product.CategoryId.HasValue)
            {
                content.Add(new StringContent(product.CategoryId.Value.ToString()), "CategoryId");
            }

            if (product.BrandId.HasValue)
            {
                content.Add(new StringContent(product.BrandId.Value.ToString()), "BrandId");
            }

            if (product.DefaultUnitId.HasValue)
            {
                content.Add(new StringContent(product.DefaultUnitId.Value.ToString()), "DefaultUnitId");
                content.Add(new StringContent(product.DefaultUnitId.Value.ToString()), "UnitId");
            }

            return await _httpService.PostAsync<BaseResponse<Domain.Entities.Product>>($"api/product/update-product", content);
        }

        public async Task<bool> DeleteProductAsync(int id)
        {
            return await _httpService.DeleteAsync($"api/product/{id}");
        }

        public async Task<PagedResponse<ProductDto>> GetAllAsync(string name, string sku, int? categoryId, int? brandId, int? unitId, string barcode, PaginationFilter pagination)
        {
            var queryParams = new List<string>();

            if (!string.IsNullOrEmpty(name)) queryParams.Add($"name={Uri.EscapeDataString(name)}");
            if (!string.IsNullOrEmpty(sku)) queryParams.Add($"sku={Uri.EscapeDataString(sku)}");
            if (categoryId.HasValue) queryParams.Add($"categoryId={categoryId.Value}");
            if (unitId.HasValue) queryParams.Add($"unitId={unitId.Value}");
            if (brandId.HasValue) queryParams.Add($"brandId={brandId.Value}");
            if (!string.IsNullOrEmpty(barcode)) queryParams.Add($"barcode={Uri.EscapeDataString(barcode)}");

            if (pagination != null)
            {
                queryParams.Add($"pageNumber={pagination.PageNumber}");
                queryParams.Add($"pageSize={pagination.PageSize}");
            }

            var queryString = string.Join("&", queryParams);
            var url = $"api/product/get-all?{queryString}";

            return await _httpService.GetAsync<PagedResponse<ProductDto>>(url);
        }

        public async Task<byte[]> GenerateBarcodeImageAsync(int productId)
        {
            return await _httpService.GetAsync<byte[]>($"api/product/generate-barcode-image/{productId}");
        }
    }
}