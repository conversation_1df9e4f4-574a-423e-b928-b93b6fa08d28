@using Microsoft.AspNetCore.Components.Authorization

<CascadingAuthenticationState>
    <Router AppAssembly="@typeof(App).Assembly">
        <Found Context="routeData">
            <AuthorizeRouteView RouteData="@routeData" DefaultLayout="@typeof(MainLayout)">
                <NotAuthorized>
                    @if (context.User.Identity?.IsAuthenticated != true)
                    {
                        <RedirectToLogin />
                    }
                    else
                    {
                        <div class="unauthorized-container">
                            <div class="unauthorized-content">
                                <i class="fas fa-lock fa-3x mb-4"></i>
                                <h1>Access Denied</h1>
                                <p>You do not have permission to access this page.</p>
                                <a href="/" class="btn btn-primary mt-3">
                                    <i class="fas fa-home"></i> Return to Home
                                </a>
                            </div>
                        </div>
                    }
                </NotAuthorized>
                <Authorizing>
                    <div class="loading-container">
                        <div class="loading-spinner"></div>
                        <p>Authorizing...</p>
                    </div>
                </Authorizing>
            </AuthorizeRouteView>
            <FocusOnNavigate RouteData="@routeData" Selector="h1" />
        </Found>
        <NotFound>
            <PageTitle>Not Found</PageTitle>
            <LayoutView Layout="@typeof(MainLayout)">
                <div class="not-found-container">
                    <div class="not-found-content">
                        <i class="fas fa-exclamation-circle fa-3x mb-4"></i>
                        <h1>404 - Page Not Found</h1>
                        <p>The page you are looking for does not exist.</p>
                        <a href="/" class="btn btn-primary mt-3">
                            <i class="fas fa-home"></i> Return to Home
                        </a>
                    </div>
                </div>
            </LayoutView>
        </NotFound>
    </Router>
</CascadingAuthenticationState>

<style>
    .unauthorized-container,
    .not-found-container {
        display: flex;
        align-items: center;
        justify-content: center;
        min-height: 100vh;
        background: #f8f9fa;
    }

    .unauthorized-content,
    .not-found-content {
        text-align: center;
        padding: 2rem;
        background: white;
        border-radius: 12px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        max-width: 400px;
        width: 100%;
    }

    .unauthorized-content i,
    .not-found-content i {
        color: #e74c3c;
    }

    .unauthorized-content h1,
    .not-found-content h1 {
        color: #2c3e50;
        margin-bottom: 1rem;
    }

    .unauthorized-content p,
    .not-found-content p {
        color: #666;
        margin-bottom: 1.5rem;
    }

    .loading-container {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        min-height: 100vh;
        background: #f8f9fa;
    }

    .loading-spinner {
        width: 40px;
        height: 40px;
        border: 4px solid #f3f3f3;
        border-top: 4px solid #3498db;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin-bottom: 1rem;
    }

    @keyframes
    spin {
        0% {
            transform: rotate(0deg);
        }

        100% {
            transform: rotate(360deg);
        }
    }

    .loading-container p {
        color: #2c3e50;
        font-size: 1.1rem;
    }
</style>