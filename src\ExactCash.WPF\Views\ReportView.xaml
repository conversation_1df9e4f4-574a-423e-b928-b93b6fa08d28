<Window x:Class="ExactCash.WPF.Views.ReportView"
        x:Name="ReportViewScreen"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:wfi="clr-namespace:System.Windows.Forms.Integration;assembly=WindowsFormsIntegration"
        xmlns:rv="clr-namespace:Microsoft.Reporting.WinForms;assembly=Microsoft.ReportViewer.WinForms"
        Title="تقارير النظام"
        Height="700"
        Width="1000"
        Background="#F8F9FA"
        FlowDirection="RightToLeft"
        WindowState="Maximized"
        WindowStartupLocation="CenterScreen"
        ResizeMode="CanResize"
        FontFamily="Segoe UI">
        <Grid>
                <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                </Grid.RowDefinitions>

                <!-- Enhanced Toolbar for report selection and parameters -->
                <Border Grid.Row="0"
                        Background="#0078D4"
                        Padding="10">
                        <StackPanel Orientation="Vertical">
                                <!-- First Row: Report Type and Date Range -->
                                <StackPanel Orientation="Horizontal"
                                            Margin="0,0,0,10">
                                        <TextBlock Text="نوع التقرير:"
                                                   Foreground="White"
                                                   VerticalAlignment="Center"
                                                   Margin="0,0,10,0"
                                                   FontWeight="Bold"/>
                                        <ComboBox x:Name="ReportTypeComboBox"
                                                  Width="250"
                                                  Margin="0,0,20,0"
                                                  VerticalAlignment="Center"
                                                  FontSize="12"
                                                  SelectionChanged="ReportTypeComboBox_SelectionChanged"/>

                                        <!-- Customer Selection (visible only for Customer Account Statement) -->
                                        <TextBlock x:Name="CustomerLabel"
                                                   Text="العميل:"
                                                   Foreground="White"
                                                   VerticalAlignment="Center"
                                                   Margin="0,0,5,0"
                                                   FontWeight="Bold"
                                                   Visibility="Collapsed"/>
                                        <ComboBox x:Name="CustomerComboBox"
                                                  Width="200"
                                                  Margin="0,0,15,0"
                                                  VerticalAlignment="Center"
                                                  FontSize="12"
                                                  Visibility="Collapsed"
                                                  DisplayMemberPath="FullName"
                                                  SelectedValuePath="Id"/>

                                        <!-- Supplier Selection (visible only for Supplier Account Statement) -->
                                        <TextBlock x:Name="SupplierLabel"
                                                   Text="المورد:"
                                                   Foreground="White"
                                                   VerticalAlignment="Center"
                                                   Margin="0,0,5,0"
                                                   FontWeight="Bold"
                                                   Visibility="Collapsed"/>
                                        <ComboBox x:Name="SupplierComboBox"
                                                  Width="200"
                                                  Margin="0,0,15,0"
                                                  VerticalAlignment="Center"
                                                  FontSize="12"
                                                  Visibility="Collapsed"
                                                  DisplayMemberPath="Name"
                                                  SelectedValuePath="Id"/>

                                        <TextBlock Text="من:"
                                                   Foreground="White"
                                                   VerticalAlignment="Center"
                                                   Margin="0,0,5,0"
                                                   FontWeight="Bold"/>
                                        <DatePicker x:Name="StartDatePicker"
                                                    Width="130"
                                                    Margin="0,0,15,0"
                                                    VerticalAlignment="Center"/>
                                        <TextBlock Text="إلى:"
                                                   Foreground="White"
                                                   VerticalAlignment="Center"
                                                   Margin="0,0,5,0"
                                                   FontWeight="Bold"/>
                                        <DatePicker x:Name="EndDatePicker"
                                                    Width="130"
                                                    Margin="0,0,15,0"
                                                    VerticalAlignment="Center"/>
                                        <Button x:Name="GenerateReportButton"
                                                Content="عرض التقرير"
                                                Width="120"
                                                Height="30"
                                                Background="#28A745"
                                                Foreground="White"
                                                BorderThickness="0"
                                                VerticalAlignment="Center"
                                                FontWeight="Bold"
                                                Cursor="Hand"/>
                                </StackPanel>

                                <!-- Second Row: Export Options -->
                                <StackPanel Orientation="Horizontal">
                                        <TextBlock Text="تصدير:"
                                                   Foreground="White"
                                                   VerticalAlignment="Center"
                                                   Margin="0,0,10,0"
                                                   FontWeight="Bold"/>
                                        <Button x:Name="ExportPdfButton"
                                                Content="PDF"
                                                Width="60"
                                                Height="25"
                                                Background="#DC3545"
                                                Foreground="White"
                                                BorderThickness="0"
                                                Margin="0,0,5,0"
                                                VerticalAlignment="Center"
                                                FontSize="10"
                                                Cursor="Hand"/>
                                        <Button x:Name="ExportExcelButton"
                                                Content="Excel"
                                                Width="60"
                                                Height="25"
                                                Background="#17A2B8"
                                                Foreground="White"
                                                BorderThickness="0"
                                                Margin="0,0,5,0"
                                                VerticalAlignment="Center"
                                                FontSize="10"
                                                Cursor="Hand"/>
                                        <Button x:Name="PrintButton"
                                                Content="طباعة"
                                                Width="60"
                                                Height="25"
                                                Background="#6C757D"
                                                Foreground="White"
                                                BorderThickness="0"
                                                Margin="0,0,10,0"
                                                VerticalAlignment="Center"
                                                FontSize="10"
                                                Cursor="Hand"/>

                                        <!-- Loading Indicator -->
                                        <StackPanel x:Name="LoadingPanel"
                                                    Orientation="Horizontal"
                                                    Visibility="Collapsed"
                                                    Margin="20,0,0,0">
                                                <TextBlock Text="جاري تحميل التقرير..."
                                                           Foreground="White"
                                                           VerticalAlignment="Center"
                                                           Margin="0,0,10,0"/>
                                                <ProgressBar Width="100"
                                                             Height="15"
                                                             IsIndeterminate="True"
                                                             VerticalAlignment="Center"/>
                                        </StackPanel>
                                </StackPanel>
                        </StackPanel>
                </Border>

                <!-- ReportViewer Host -->
                <wfi:WindowsFormsHost x:Name="ReportHost"
                                      Grid.Row="1"
                                      Margin="0">
                        <rv:ReportViewer x:Name="ReportViewerControl"
                                         Dock="Fill"/>
                </wfi:WindowsFormsHost>
        </Grid>
</Window>