using ExactCash.Application.DTOs;
using ExactCash.WPF.ViewModels.CreditSales;
using System.Windows;

namespace ExactCash.WPF.Views.CreditSales
{
    /// <summary>
    /// Interaction logic for PaymentHistoryWindow.xaml
    /// </summary>
    public partial class PaymentHistoryWindow : Window
    {
        public PaymentHistoryWindow()
        {
            InitializeComponent();
        }

        public PaymentHistoryWindow(OutstandingSaleDto sale) : this()
        {
            if (sale != null)
            {
                var viewModel = new PaymentHistoryViewModel(sale);
                DataContext = viewModel;
            }
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            this.Close();
        }
    }
}
