﻿using ExactCash.WASM.Application.DTOs;
using ExactCash.WASM.Services.Common;
using ExactCash.WASM.Services.Interfaces;

namespace ExactCash.WASM.Services
{
    public class AuthServiceClient : IAuthServiceClient
    {
        private readonly HttpService _httpService;

        public AuthServiceClient(HttpService httpService)
        {
            _httpService = httpService;
        }

        public async Task<AuthResponseDto> LoginAsync(LoginDto loginDto)
        {
            return await _httpService.PostAsync<AuthResponseDto>("api/Auth/login", loginDto);
        }

        public async Task LogoutAsync(string userId)
        {
            await _httpService.PostAsync("api/Auth/logout", null);
        }

        public async Task<AuthResponseDto> RefreshTokenAsync(RefreshTokenDto refreshTokenDto)
        {
            return await _httpService.PostAsync<AuthResponseDto>("api/Auth/refresh-token", refreshTokenDto);
        }

        public async Task<AuthResponseDto> RegisterAsync(RegisterDto registerDto)
        {
            return await _httpService.PostAsync<AuthResponseDto>("api/Auth/register", registerDto);
        }

        public async Task<bool> ValidateTokenAsync(string token)
        {
            return await _httpService.GetAsync<bool>("api/Auth/validate-token");
        }
    }
}
