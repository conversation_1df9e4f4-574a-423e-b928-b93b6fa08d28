@inject AppState AppState
@inject IAuthenticationService AuthService
@inject NavigationManager Navigation

<div class="top-bar d-flex justify-content-between align-items-center">
    <div class="d-flex align-items-center">
        <h4 class="mb-0">@GetPageTitle()</h4>
    </div>

    <div class="d-flex align-items-center gap-3">
        <!-- Connection Status -->
        <div class="connection-status">
            @if (AppState.IsOnline)
            {
                <span class="badge bg-success">
                    <i class="bi bi-wifi"></i> متصل
                </span>
            }
            else
            {
                <span class="badge bg-danger">
                    <i class="bi bi-wifi-off"></i> غير متصل
                </span>
            }
        </div>

        <!-- Current Date & Time -->
        <div class="current-time">
            <small class="text-muted">
                @DateTime.Now.ToString("yyyy/MM/dd - HH:mm", new System.Globalization.CultureInfo("ar-EG"))
            </small>
        </div>

        <!-- User Info -->
        <div class="user-info dropdown">
            <button class="btn btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                <i class="bi bi-person-circle"></i>
                @AppState.CurrentUser?.Name
            </button>
            <ul class="dropdown-menu dropdown-menu-end">
                <li><a class="dropdown-item" href="/profile">الملف الشخصي</a></li>
                <li><a class="dropdown-item" href="/settings">الإعدادات</a></li>
                <li><hr class="dropdown-divider"></li>
                <li><button class="dropdown-item" @onclick="Logout">تسجيل الخروج</button></li>
            </ul>
        </div>
    </div>
</div>

@code {
    protected override void OnInitialized()
    {
        AppState.OnChange += StateHasChanged;
        
        // Update time every minute
        var timer = new System.Threading.Timer((_) => InvokeAsync(StateHasChanged), null, TimeSpan.Zero, TimeSpan.FromMinutes(1));
    }

    private string GetPageTitle()
    {
        var currentUrl = Navigation.ToBaseRelativePath(Navigation.Uri);
        
        return currentUrl switch
        {
            "" or "/" => "لوحة التحكم",
            "sales" => "المبيعات",
            "products" => "المنتجات", 
            "customers" => "العملاء",
            "suppliers" => "الموردين",
            "purchase-orders" => "أوامر الشراء",
            "inventory" => "المخزون",
            "expenses" => "المصروفات",
            "credit-sales" => "البيع بالآجل",
            "reports" => "التقارير",
            "users" => "المستخدمين",
            "settings" => "الإعدادات",
            _ => "ExactCash"
        };
    }

    private async Task Logout()
    {
        await AuthService.LogoutAsync();
        Navigation.NavigateTo("/login");
    }
}
