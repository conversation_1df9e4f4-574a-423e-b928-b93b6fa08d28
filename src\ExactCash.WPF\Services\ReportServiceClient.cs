using ExactCash.Application.DTOs;

namespace ExactCash.WPF.Services
{
    public class ReportServiceClient : IReportServiceClient
    {
        private readonly HttpService _httpService;

        public ReportServiceClient(string baseUrl)
        {
            _httpService = new HttpService(baseUrl);
        }

        public async Task<CustomerAccountStatementDto> GetCustomerAccountStatement(int customerId, DateTime? startDate, DateTime? endDate)
        {
            var queryParams = new List<string>();

            if (startDate.HasValue)
            {
                var utcStartDate = startDate.Value.Kind == DateTimeKind.Unspecified
                    ? DateTime.SpecifyKind(startDate.Value, DateTimeKind.Utc)
                    : startDate.Value.ToUniversalTime();
                queryParams.Add($"startDate={Uri.EscapeDataString(utcStartDate.ToString("yyyy-MM-ddTHH:mm:ss.fffZ"))}");
            }
            if (endDate.HasValue)
            {
                var utcEndDate = endDate.Value.Kind == DateTimeKind.Unspecified
                    ? DateTime.SpecifyKind(endDate.Value, DateTimeKind.Utc)
                    : endDate.Value.ToUniversalTime();
                queryParams.Add($"endDate={Uri.EscapeDataString(utcEndDate.ToString("yyyy-MM-ddTHH:mm:ss.fffZ"))}");
            }

            var queryString = queryParams.Count > 0 ? "?" + string.Join("&", queryParams) : "";
            var url = $"api/reports/customer-account-statement/{customerId}{queryString}";

            return await _httpService.GetAsync<CustomerAccountStatementDto>(url);
        }

        public async Task<SupplierAccountStatementDto> GetSupplierAccountStatement(int supplierId, DateTime? startDate, DateTime? endDate)
        {
            var queryParams = new List<string>();

            if (startDate.HasValue)
            {
                var utcStartDate = startDate.Value.Kind == DateTimeKind.Unspecified
                    ? DateTime.SpecifyKind(startDate.Value, DateTimeKind.Utc)
                    : startDate.Value.ToUniversalTime();
                queryParams.Add($"startDate={Uri.EscapeDataString(utcStartDate.ToString("yyyy-MM-ddTHH:mm:ss.fffZ"))}");
            }
            if (endDate.HasValue)
            {
                var utcEndDate = endDate.Value.Kind == DateTimeKind.Unspecified
                    ? DateTime.SpecifyKind(endDate.Value, DateTimeKind.Utc)
                    : endDate.Value.ToUniversalTime();
                queryParams.Add($"endDate={Uri.EscapeDataString(utcEndDate.ToString("yyyy-MM-ddTHH:mm:ss.fffZ"))}");
            }

            var queryString = queryParams.Count > 0 ? "?" + string.Join("&", queryParams) : "";
            var url = $"api/reports/supplier-account-statement/{supplierId}{queryString}";

            return await _httpService.GetAsync<SupplierAccountStatementDto>(url);
        }

        public async Task<CompanyInfoDto> GetCompanyInfoAsync()
        {
            return await _httpService.GetAsync<CompanyInfoDto>("api/reports/company-info");
        }

        public async Task<InventorySummaryDto> GetInventorySummaryAsync()
        {
            return await _httpService.GetAsync<InventorySummaryDto>("api/reports/inventory-summary");
        }

        public async Task<List<InventoryReportDto>> GetInventoryReportAsync()
        {
            return await _httpService.GetAsync<List<InventoryReportDto>>("api/reports/inventory");
        }

        public async Task<List<LowStockReportDto>> GetLowStockReportAsync()
        {
            return await _httpService.GetAsync<List<LowStockReportDto>>("api/reports/low-stock");
        }
    }
}
