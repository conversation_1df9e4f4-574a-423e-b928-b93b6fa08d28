using ExactCash.Application.DTOs;

namespace ExactCash.WPF.Services
{
    public class ApiUserService : IApiUserService
    {
        private readonly HttpService _httpService;

        public ApiUserService(string baseUrl)
        {
            _httpService = new HttpService(baseUrl);
        }

        public void SetToken(string token)
        {
            _httpService.SetBearerToken(token);
        }

        public async Task<AuthResponseDto> LoginAsync(string username, string password)
        {
            LoginDto loginDto = new LoginDto
            {
                Email = username,
                Password = password,
                RememberMe = true
            };
            return await _httpService.PostAsync<AuthResponseDto>("api/auth/login", loginDto);
        }

        public async Task<AuthResponseDto> RefreshTokenAsync(string token, string refreshToken)
        {
            var refreshDto = new { token, refreshToken };
            return await _httpService.PostAsync<AuthResponseDto>("api/auth/refresh-token", refreshDto);
        }

        public async Task<bool> LogoutAsync()
        {
            return await _httpService.DeleteAsync("api/auth/logout");
        }

        public async Task<UserDto> GetCurrentUserAsync()
        {
            return await _httpService.GetAsync<UserDto>("api/users/me");
        }

        public async Task<bool> UpdateUserProfileAsync(UpdateUserDto userDto)
        {
            return await _httpService.PutAsync<bool>("api/users/profile", userDto);
        }

        public async Task<bool> ChangePasswordAsync(string currentPassword, string newPassword)
        {
            var changePasswordDto = new { currentPassword, newPassword };
            return await _httpService.PostAsync<bool>("api/users/change-password", changePasswordDto);
        }

        public async Task<bool> ResetPasswordAsync(string email)
        {
            var resetPasswordDto = new { email };
            return await _httpService.PostAsync<bool>("api/users/reset-password", resetPasswordDto);
        }

        public async Task<bool> VerifyEmailAsync(string token)
        {
            var verifyEmailDto = new { token };
            return await _httpService.PostAsync<bool>("api/users/verify-email", verifyEmailDto);
        }
    }
}