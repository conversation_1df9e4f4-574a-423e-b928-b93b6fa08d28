@model List<FDIN.Web.ViewModels.StoreSalesGroup>

@{
    ViewData["Title"] = "My Invoices";
}




<meta name="viewport" content="width=device-width, initial-scale=1.0" />
<title>Stores</title>
<style>
    body {
        font-family: Arial, sans-serif;
        margin: 0;
        padding: 1rem;
        background: #f9f9f9;
    }

    h2 {
        font-size: 1.5rem;
        margin-bottom: 1rem;
    }

    .store-card {
        display: flex;
        align-items: center;
        justify-content: space-between;
        background: white;
        padding: 1rem;
        margin-bottom: 0.75rem;
        border-radius: 12px;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    }

    .store-info {
        display: flex;
        align-items: center;
    }

        .store-info img {
            width: 40px;
            height: 40px;
            border-radius: 8px;
            object-fit: contain;
            margin-right: 1rem;
        }

    .store-name {
        font-weight: 500;
        font-size: 1rem;
    }

    .receipts {
        font-weight: bold;
        color: #333;
    }

    .store-card-link {
        text-decoration: none;
        color: inherit;
    }

</style>




<div class="container-fluid">
    <div class="row">
        <div class="col-12">

            <h2>Stores</h2>

            @foreach (var invoice in Model)
            {
                <a href="@Url.Action("Invoices", "UserInvoices", new { storeName = invoice.StoreName, userId = 5 })" class="store-card-link">
                    <div class="store-card">
                        <div class="store-info">
                            <img src="~/Stores/@(invoice.StoreName).png" alt="@invoice.StoreName" />
                            <span class="store-name">@invoice.StoreName</span>
                        </div>
                        <span class="receipts">@invoice.Sales.Count Receipts</span>
                    </div>
                </a>
            }

            @* <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h4 class="mb-0">My Invoices</h4>
                    <div class="btn-toolbar">
                        <div class="btn-group me-2">
                            <button type="button" class="btn btn-sm btn-outline-secondary">
                                <i class="fas fa-share-alt"></i> Share
                            </button>
                            <button type="button" class="btn btn-sm btn-outline-secondary">
                                <i class="fas fa-file-export"></i> Export
                            </button>
                        </div>
                        <button type="button" class="btn btn-sm btn-outline-primary">
                            <i class="fas fa-filter"></i> Filter
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <!-- Search and Filter -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="input-group">
                                <input type="text" class="form-control" placeholder="Search invoices...">
                                <button class="btn btn-outline-secondary" type="button">
                                    <i class="fas fa-search"></i>
                                </button>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="d-flex justify-content-end">
                                <select class="form-select w-auto me-2">
                                    <option>All Status</option>
                                    <option>Paid</option>
                                    <option>Pending</option>
                                    <option>Overdue</option>
                                </select>
                                <select class="form-select w-auto">
                                    <option>Sort by Date</option>
                                    <option>Sort by Amount</option>
                                    <option>Sort by Status</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <!-- Invoices List -->
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Invoice #</th>
                                    <th>Date</th>
                                    <th>Store</th>
                                    <th>Amount</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach (var invoice in Model)
                                {
                                    <tr>
                                        <td>@invoice.InvoiceNumber</td>
                                        <td>@invoice.SaleDate.ToString("MMM dd, yyyy")</td>
                                        <td>@invoice.StoreName</td>
                                        <td>@invoice.TotalAmount.ToString("C")</td>
                                        <td>
                                            <span class="badge bg-@(invoice.PaidAmount >= invoice.TotalAmount ? "success" : invoice.PaidAmount > 0 ? "warning" : "danger")">
                                                @(invoice.PaidAmount >= invoice.TotalAmount ? "Paid" : invoice.PaidAmount > 0 ? "Partial" : "Pending")
                                            </span>
                                        </td>
                                        <td>
                                            <div class="btn-group">
                                                <a href="@Url.Action("Details", new { id = invoice.Id })" class="btn btn-sm btn-outline-primary">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <a href="@Url.Action("Download", new { id = invoice.Id })" class="btn btn-sm btn-outline-secondary">
                                                    <i class="fas fa-download"></i>
                                                </a>
                                                <a href="@Url.Action("Print", new { id = invoice.Id })" class="btn btn-sm btn-outline-info">
                                                    <i class="fas fa-print"></i>
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                }
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <nav aria-label="Page navigation" class="mt-4">
                        <ul class="pagination justify-content-center">
                            <li class="page-item disabled">
                                <a class="page-link" href="#" tabindex="-1">Previous</a>
                            </li>
                            <li class="page-item active"><a class="page-link" href="#">1</a></li>
                            <li class="page-item"><a class="page-link" href="#">2</a></li>
                            <li class="page-item"><a class="page-link" href="#">3</a></li>
                            <li class="page-item">
                                <a class="page-link" href="#">Next</a>
                            </li>
                        </ul>
                    </nav>
                </div>
            </div> *@
        </div>
    </div>
</div>

@section Scripts {
    <script>
        // Add any JavaScript functionality here
    </script>
}