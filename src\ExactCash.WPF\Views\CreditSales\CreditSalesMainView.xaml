<UserControl x:Class="ExactCash.WPF.Views.CreditSales.CreditSalesMainView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:local="clr-namespace:ExactCash.WPF.Views.CreditSales"
             mc:Ignorable="d"
             d:DesignHeight="800"
             d:DesignWidth="1200"
             FlowDirection="RightToLeft"
             FontFamily="Segoe UI">

        <UserControl.Resources>
                <Style x:Key="ModernBorder"
                       TargetType="Border">
                        <Setter Property="Background"
                                Value="White"/>
                        <Setter Property="BorderBrush"
                                Value="#DEE2E6"/>
                        <Setter Property="BorderThickness"
                                Value="1"/>
                        <Setter Property="CornerRadius"
                                Value="6"/>
                        <Setter Property="Padding"
                                Value="15"/>
                </Style>

                <Style x:Key="ModernButton"
                       TargetType="Button">
                        <Setter Property="Height"
                                Value="35"/>
                        <Setter Property="Padding"
                                Value="15,5"/>
                        <Setter Property="Background"
                                Value="#0078D4"/>
                        <Setter Property="Foreground"
                                Value="White"/>
                        <Setter Property="BorderThickness"
                                Value="0"/>
                        <Setter Property="Cursor"
                                Value="Hand"/>
                        <Setter Property="FontSize"
                                Value="14"/>
                </Style>

                <Style x:Key="ModernTextBox"
                       TargetType="TextBox">
                        <Setter Property="Height"
                                Value="35"/>
                        <Setter Property="Padding"
                                Value="10,5"/>
                        <Setter Property="BorderThickness"
                                Value="1"/>
                        <Setter Property="BorderBrush"
                                Value="#CCCCCC"/>
                        <Setter Property="Background"
                                Value="White"/>
                        <Setter Property="FontSize"
                                Value="14"/>
                        <Setter Property="TextAlignment"
                                Value="Right"/>
                </Style>

                <Style x:Key="ModernComboBox"
                       TargetType="ComboBox">
                        <Setter Property="Height"
                                Value="35"/>
                        <Setter Property="Padding"
                                Value="10,5"/>
                        <Setter Property="BorderThickness"
                                Value="1"/>
                        <Setter Property="BorderBrush"
                                Value="#CCCCCC"/>
                        <Setter Property="Background"
                                Value="White"/>
                        <Setter Property="FontSize"
                                Value="14"/>
                        <Setter Property="HorizontalContentAlignment"
                                Value="Right"/>
                </Style>

                <Style x:Key="ModernDatePicker"
                       TargetType="DatePicker">
                        <Setter Property="Height"
                                Value="35"/>
                        <Setter Property="Padding"
                                Value="10,5"/>
                        <Setter Property="BorderThickness"
                                Value="1"/>
                        <Setter Property="BorderBrush"
                                Value="#CCCCCC"/>
                        <Setter Property="Background"
                                Value="White"/>
                        <Setter Property="FontSize"
                                Value="14"/>
                </Style>

                <Style x:Key="TabItemStyle"
                       TargetType="TabItem">
                        <Setter Property="Template">
                                <Setter.Value>
                                        <ControlTemplate TargetType="TabItem">
                                                <Border Name="Border"
                                                        Background="#F8F9FA"
                                                        BorderBrush="#DEE2E6"
                                                        BorderThickness="1,1,1,0"
                                                        CornerRadius="6,6,0,0"
                                                        Margin="0,0,2,0"
                                                        Padding="20,10">
                                                        <ContentPresenter x:Name="ContentSite"
                                                                          VerticalAlignment="Center"
                                                                          HorizontalAlignment="Center"
                                                                          ContentSource="Header"
                                                                          Margin="0"/>
                                                </Border>
                                                <ControlTemplate.Triggers>
                                                        <Trigger Property="IsSelected"
                                                                 Value="True">
                                                                <Setter TargetName="Border"
                                                                        Property="Background"
                                                                        Value="White"/>
                                                                <Setter TargetName="Border"
                                                                        Property="BorderThickness"
                                                                        Value="1,1,1,0"/>
                                                        </Trigger>
                                                        <Trigger Property="IsMouseOver"
                                                                 Value="True">
                                                                <Setter TargetName="Border"
                                                                        Property="Background"
                                                                        Value="#E9ECEF"/>
                                                        </Trigger>
                                                </ControlTemplate.Triggers>
                                        </ControlTemplate>
                                </Setter.Value>
                        </Setter>
                </Style>
        </UserControl.Resources>

        <Grid Background="#F8F9FA">
                <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                </Grid.RowDefinitions>

                <!-- Header -->
                <Border Grid.Row="0"
                        Style="{StaticResource ModernBorder}"
                        Margin="10,10,10,5">
                        <Grid>
                                <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="Auto"/>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>

                                <StackPanel Grid.Column="0"
                                            Orientation="Horizontal">
                                        <TextBlock Text="💳"
                                                   FontSize="24"
                                                   VerticalAlignment="Center"
                                                   Margin="0,0,10,0"/>
                                        <TextBlock Text="إدارة المبيعات الآجلة"
                                                   FontSize="20"
                                                   FontWeight="Bold"
                                                   VerticalAlignment="Center"/>
                                </StackPanel>

                                <StackPanel Grid.Column="2"
                                            Orientation="Horizontal">
                                        <Button Content="تحصيل دفعة"
                                                Command="{Binding CollectPaymentCommand}"
                                                Style="{StaticResource ModernButton}"
                                                Background="#28A745"
                                                Margin="0,0,10,0"/>
                                        <Button Content="تقرير المديونية"
                                                Command="{Binding OutstandingReportCommand}"
                                                Style="{StaticResource ModernButton}"
                                                Background="#17A2B8"
                                                Margin="0,0,10,0"/>
                                        <Button Content="تحديث"
                                                Command="{Binding RefreshCommand}"
                                                Style="{StaticResource ModernButton}"
                                                Background="#6C757D"/>
                                </StackPanel>
                        </Grid>
                </Border>

                <!-- Main Content -->
                <TabControl Grid.Row="1"
                            Margin="10,5,10,10"
                            Background="Transparent"
                            BorderThickness="0">
                        <TabControl.Resources>
                                <Style TargetType="TabItem"
                                       BasedOn="{StaticResource TabItemStyle}"/>
                        </TabControl.Resources>

                        <!-- Outstanding Sales Tab -->
                        <TabItem Header="المبيعات المستحقة">
                                <Border Style="{StaticResource ModernBorder}"
                                        Margin="0,5,0,0">
                                        <Grid>
                                                <Grid.RowDefinitions>
                                                        <RowDefinition Height="Auto"/>
                                                        <RowDefinition Height="*"/>
                                                        <RowDefinition Height="Auto"/>
                                                </Grid.RowDefinitions>

                                                <!-- Search Section -->
                                                <Border Grid.Row="0"
                                                        Style="{StaticResource ModernBorder}"
                                                        Margin="0,0,0,10">
                                                        <Grid>
                                                                <Grid.ColumnDefinitions>
                                                                        <ColumnDefinition Width="Auto"/>
                                                                        <ColumnDefinition Width="*"/>
                                                                        <ColumnDefinition Width="Auto"/>
                                                                        <ColumnDefinition Width="*"/>
                                                                        <ColumnDefinition Width="Auto"/>
                                                                        <ColumnDefinition Width="*"/>
                                                                        <ColumnDefinition Width="Auto"/>
                                                                </Grid.ColumnDefinitions>

                                                                <TextBlock Text="رقم الفاتورة"
                                                                           VerticalAlignment="Center"
                                                                           Margin="0,0,10,0"/>
                                                                <TextBox Grid.Column="1"
                                                                         Text="{Binding InvoiceNumberSearch, UpdateSourceTrigger=PropertyChanged}"
                                                                         Style="{StaticResource ModernTextBox}"
                                                                         Margin="0,0,10,0"/>

                                                                <TextBlock Text="العميل"
                                                                           Grid.Column="2"
                                                                           VerticalAlignment="Center"
                                                                           Margin="0,0,10,0"/>
                                                                <TextBox Grid.Column="3"
                                                                         Text="{Binding CustomerNameSearch, UpdateSourceTrigger=PropertyChanged}"
                                                                         Style="{StaticResource ModernTextBox}"
                                                                         Margin="0,0,10,0"/>

                                                                <TextBlock Text="من تاريخ"
                                                                           Grid.Column="4"
                                                                           VerticalAlignment="Center"
                                                                           Margin="0,0,10,0"/>
                                                                <DatePicker Grid.Column="5"
                                                                            SelectedDate="{Binding FromDate}"
                                                                            Style="{StaticResource ModernDatePicker}"
                                                                            Margin="0,0,10,0"/>

                                                                <Button Grid.Column="6"
                                                                        Content="بحث"
                                                                        Command="{Binding SearchCommand}"
                                                                        Style="{StaticResource ModernButton}"
                                                                        Width="80"/>
                                                        </Grid>
                                                </Border>

                                                <!-- Outstanding Sales Grid -->
                                                <DataGrid Grid.Row="1"
                                                          ItemsSource="{Binding OutstandingSales}"
                                                          AutoGenerateColumns="False"
                                                          CanUserAddRows="False"
                                                          CanUserDeleteRows="False"
                                                          IsReadOnly="True"
                                                          SelectionMode="Single"
                                                          GridLinesVisibility="All"
                                                          BorderThickness="1"
                                                          BorderBrush="#E5E5E5"
                                                          Background="White"
                                                          RowHeaderWidth="0"
                                                          HeadersVisibility="Column"
                                                          HorizontalGridLinesBrush="#E5E5E5"
                                                          VerticalGridLinesBrush="#E5E5E5"
                                                          AlternatingRowBackground="#F8F9FA">
                                                        <DataGrid.Columns>
                                                                <DataGridTextColumn Header="رقم الفاتورة"
                                                                                    Binding="{Binding InvoiceNumber}"
                                                                                    Width="150"/>
                                                                <DataGridTextColumn Header="العميل"
                                                                                    Binding="{Binding CustomerName}"
                                                                                    Width="200"/>
                                                                <DataGridTextColumn Header="تاريخ الفاتورة"
                                                                                    Binding="{Binding CreationDate, StringFormat=dd/MM/yyyy}"
                                                                                    Width="120"/>
                                                                <DataGridTextColumn Header="إجمالي الفاتورة"
                                                                                    Binding="{Binding TotalAmount, StringFormat='{}{0:N2} ج.م'}"
                                                                                    Width="120"/>
                                                                <DataGridTextColumn Header="المبلغ المدفوع"
                                                                                    Binding="{Binding PaidAmount, StringFormat='{}{0:N2} ج.م'}"
                                                                                    Width="120"/>
                                                                <DataGridTextColumn Header="المبلغ المستحق"
                                                                                    Binding="{Binding RemainingAmount, StringFormat='{}{0:N2} ج.م'}"
                                                                                    Width="120"/>
                                                                <DataGridTextColumn Header="أيام التأخير"
                                                                                    Binding="{Binding DaysOverdue}"
                                                                                    Width="100"/>
                                                                <DataGridTemplateColumn Header="الإجراءات"
                                                                                        Width="150">
                                                                        <DataGridTemplateColumn.CellTemplate>
                                                                                <DataTemplate>
                                                                                        <StackPanel Orientation="Horizontal"
                                                                                                    HorizontalAlignment="Center">
                                                                                                <!-- Collect Payment Icon -->
                                                                                                <Button Command="{Binding DataContext.CollectPaymentForSaleCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                                                                                        CommandParameter="{Binding}"
                                                                                                        Style="{StaticResource ModernButton}"
                                                                                                        Background="#28A745"
                                                                                                        Width="32"
                                                                                                        Height="32"
                                                                                                        Margin="2"
                                                                                                        ToolTip="تحصيل دفعة"
                                                                                                        BorderThickness="0"
                                                                                                        Cursor="Hand">
                                                                                                        <TextBlock Text="💰"
                                                                                                                   FontSize="16"
                                                                                                                   HorizontalAlignment="Center"
                                                                                                                   VerticalAlignment="Center"/>
                                                                                                </Button>

                                                                                                <!-- Payment History Icon -->
                                                                                                <Button Command="{Binding DataContext.ViewPaymentHistoryCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                                                                                        CommandParameter="{Binding}"
                                                                                                        Style="{StaticResource ModernButton}"
                                                                                                        Background="#6F42C1"
                                                                                                        Width="32"
                                                                                                        Height="32"
                                                                                                        Margin="2"
                                                                                                        ToolTip="تاريخ المدفوعات"
                                                                                                        BorderThickness="0"
                                                                                                        Cursor="Hand">
                                                                                                        <TextBlock Text="📋"
                                                                                                                   FontSize="16"
                                                                                                                   HorizontalAlignment="Center"
                                                                                                                   VerticalAlignment="Center"/>
                                                                                                </Button>

                                                                                                <!-- View Details Icon -->
                                                                                                <Button Command="{Binding DataContext.ViewSaleDetailsCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                                                                                        CommandParameter="{Binding}"
                                                                                                        Style="{StaticResource ModernButton}"
                                                                                                        Background="#17A2B8"
                                                                                                        Width="32"
                                                                                                        Height="32"
                                                                                                        Margin="2"
                                                                                                        ToolTip="عرض تفاصيل الفاتورة"
                                                                                                        BorderThickness="0"
                                                                                                        Cursor="Hand">
                                                                                                        <TextBlock Text="👁️"
                                                                                                                   FontSize="16"
                                                                                                                   HorizontalAlignment="Center"
                                                                                                                   VerticalAlignment="Center"/>
                                                                                                </Button>
                                                                                        </StackPanel>
                                                                                </DataTemplate>
                                                                        </DataGridTemplateColumn.CellTemplate>
                                                                </DataGridTemplateColumn>
                                                        </DataGrid.Columns>
                                                </DataGrid>

                                                <!-- Summary Section -->
                                                <Border Grid.Row="2"
                                                        Style="{StaticResource ModernBorder}"
                                                        Margin="0,10,0,0">
                                                        <Grid>
                                                                <Grid.ColumnDefinitions>
                                                                        <ColumnDefinition Width="*"/>
                                                                        <ColumnDefinition Width="*"/>
                                                                        <ColumnDefinition Width="*"/>
                                                                        <ColumnDefinition Width="*"/>
                                                                </Grid.ColumnDefinitions>

                                                                <StackPanel Grid.Column="0"
                                                                            HorizontalAlignment="Center">
                                                                        <TextBlock Text="إجمالي المبيعات الآجلة"
                                                                                   FontWeight="Bold"
                                                                                   HorizontalAlignment="Center"/>
                                                                        <TextBlock Text="{Binding TotalOutstandingAmount, StringFormat='{}{0:N2} ج.م'}"
                                                                                   FontSize="18"
                                                                                   FontWeight="Bold"
                                                                                   Foreground="#DC3545"
                                                                                   HorizontalAlignment="Center"/>
                                                                </StackPanel>

                                                                <StackPanel Grid.Column="1"
                                                                            HorizontalAlignment="Center">
                                                                        <TextBlock Text="عدد الفواتير المستحقة"
                                                                                   FontWeight="Bold"
                                                                                   HorizontalAlignment="Center"/>
                                                                        <TextBlock Text="{Binding OutstandingSalesCount}"
                                                                                   FontSize="18"
                                                                                   FontWeight="Bold"
                                                                                   Foreground="#FD7E14"
                                                                                   HorizontalAlignment="Center"/>
                                                                </StackPanel>

                                                                <StackPanel Grid.Column="2"
                                                                            HorizontalAlignment="Center">
                                                                        <TextBlock Text="المبالغ المتأخرة"
                                                                                   FontWeight="Bold"
                                                                                   HorizontalAlignment="Center"/>
                                                                        <TextBlock Text="{Binding OverdueAmount, StringFormat='{}{0:N2} ج.م'}"
                                                                                   FontSize="18"
                                                                                   FontWeight="Bold"
                                                                                   Foreground="#DC3545"
                                                                                   HorizontalAlignment="Center"/>
                                                                </StackPanel>

                                                                <StackPanel Grid.Column="3"
                                                                            HorizontalAlignment="Center">
                                                                        <TextBlock Text="متوسط أيام التأخير"
                                                                                   FontWeight="Bold"
                                                                                   HorizontalAlignment="Center"/>
                                                                        <TextBlock Text="{Binding AverageDaysOverdue, StringFormat=N0}"
                                                                                   FontSize="18"
                                                                                   FontWeight="Bold"
                                                                                   Foreground="#6F42C1"
                                                                                   HorizontalAlignment="Center"/>
                                                                </StackPanel>
                                                        </Grid>
                                                </Border>
                                        </Grid>
                                </Border>
                        </TabItem>

                        <!-- Customer Credit Limits Tab -->
                        <TabItem Header="حدود الائتمان">
                                <Border Style="{StaticResource ModernBorder}"
                                        Margin="0,5,0,0">
                                        <Grid>
                                                <Grid.RowDefinitions>
                                                        <RowDefinition Height="Auto"/>
                                                        <RowDefinition Height="*"/>
                                                </Grid.RowDefinitions>

                                                <!-- Credit Limits Header -->
                                                <Border Grid.Row="0"
                                                        Style="{StaticResource ModernBorder}"
                                                        Margin="0,0,0,10">
                                                        <Grid>
                                                                <Grid.ColumnDefinitions>
                                                                        <ColumnDefinition Width="*"/>
                                                                        <ColumnDefinition Width="Auto"/>
                                                                </Grid.ColumnDefinitions>

                                                                <TextBlock Text="إدارة حدود الائتمان للعملاء"
                                                                           FontSize="16"
                                                                           FontWeight="Bold"
                                                                           VerticalAlignment="Center"/>

                                                                <Button Grid.Column="1"
                                                                        Content="تحديث حد ائتمان"
                                                                        Command="{Binding UpdateCreditLimitCommand}"
                                                                        Style="{StaticResource ModernButton}"
                                                                        Background="#28A745"/>
                                                        </Grid>
                                                </Border>

                                                <!-- Customer Credit Limits Grid -->
                                                <DataGrid Grid.Row="1"
                                                          ItemsSource="{Binding CustomerCreditLimits}"
                                                          AutoGenerateColumns="False"
                                                          CanUserAddRows="False"
                                                          CanUserDeleteRows="False"
                                                          IsReadOnly="True"
                                                          SelectionMode="Single"
                                                          GridLinesVisibility="All"
                                                          BorderThickness="1"
                                                          BorderBrush="#E5E5E5"
                                                          Background="White"
                                                          RowHeaderWidth="0"
                                                          HeadersVisibility="Column"
                                                          HorizontalGridLinesBrush="#E5E5E5"
                                                          VerticalGridLinesBrush="#E5E5E5"
                                                          AlternatingRowBackground="#F8F9FA">
                                                        <DataGrid.Columns>
                                                                <DataGridTextColumn Header="العميل"
                                                                                    Binding="{Binding CustomerName}"
                                                                                    Width="200"/>
                                                                <DataGridTextColumn Header="رقم الهاتف"
                                                                                    Binding="{Binding Phone}"
                                                                                    Width="150"/>
                                                                <DataGridTextColumn Header="حد الائتمان"
                                                                                    Binding="{Binding CreditLimit, StringFormat='{}{0:N2} ج.م'}"
                                                                                    Width="120"/>
                                                                <DataGridTextColumn Header="الرصيد المستحق"
                                                                                    Binding="{Binding OutstandingBalance, StringFormat='{}{0:N2} ج.م'}"
                                                                                    Width="120"/>
                                                                <DataGridTextColumn Header="الائتمان المتاح"
                                                                                    Binding="{Binding AvailableCredit, StringFormat='{}{0:N2} ج.م'}"
                                                                                    Width="120"/>
                                                                <DataGridTextColumn Header="نسبة الاستخدام"
                                                                                    Binding="{Binding CreditUtilization, StringFormat=P2}"
                                                                                    Width="100"/>
                                                                <DataGridTemplateColumn Header="الحالة"
                                                                                        Width="100">
                                                                        <DataGridTemplateColumn.CellTemplate>
                                                                                <DataTemplate>
                                                                                        <Border Background="{Binding StatusColor}"
                                                                                                CornerRadius="3"
                                                                                                Padding="5,2">
                                                                                                <TextBlock Text="{Binding Status}"
                                                                                                           Foreground="White"
                                                                                                           FontWeight="Bold"
                                                                                                           HorizontalAlignment="Center"/>
                                                                                        </Border>
                                                                                </DataTemplate>
                                                                        </DataGridTemplateColumn.CellTemplate>
                                                                </DataGridTemplateColumn>
                                                                <DataGridTemplateColumn Header="الإجراءات"
                                                                                        Width="80">
                                                                        <DataGridTemplateColumn.CellTemplate>
                                                                                <DataTemplate>
                                                                                        <Button Command="{Binding DataContext.EditCreditLimitCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                                                                                CommandParameter="{Binding}"
                                                                                                Style="{StaticResource ModernButton}"
                                                                                                Background="#FFC107"
                                                                                                Width="32"
                                                                                                Height="32"
                                                                                                ToolTip="تعديل حد الائتمان"
                                                                                                BorderThickness="0"
                                                                                                Cursor="Hand"
                                                                                                HorizontalAlignment="Center">
                                                                                                <TextBlock Text="✏️"
                                                                                                           FontSize="16"
                                                                                                           HorizontalAlignment="Center"
                                                                                                           VerticalAlignment="Center"/>
                                                                                        </Button>
                                                                                </DataTemplate>
                                                                        </DataGridTemplateColumn.CellTemplate>
                                                                </DataGridTemplateColumn>
                                                        </DataGrid.Columns>
                                                </DataGrid>
                                        </Grid>
                                </Border>
                        </TabItem>
                </TabControl>
        </Grid>
</UserControl>
