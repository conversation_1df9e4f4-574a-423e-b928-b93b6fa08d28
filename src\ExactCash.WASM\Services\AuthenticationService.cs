using ExactCash.WASM.Services.Interfaces;
using ExactCash.WASM.Authentication;
using ExactCash.WASM.Models;
using Microsoft.AspNetCore.Components.Authorization;
using System.Net.Http.Json;
using System.Text.Json;

namespace ExactCash.WASM.Services;

public class AuthenticationService : IAuthenticationService
{
    private readonly HttpClient _httpClient;
    private readonly CustomAuthenticationStateProvider _authStateProvider;
    private readonly IApiService _apiService;

    public AuthenticationService(
        IHttpClientFactory httpClientFactory,
        AuthenticationStateProvider authStateProvider,
        IApiService apiService)
    {
        _httpClient = httpClientFactory.CreateClient("ExactCashAPI");
        _authStateProvider = (CustomAuthenticationStateProvider)authStateProvider;
        _apiService = apiService;
    }

    public async Task<AuthResult> LoginAsync(LoginRequest request)
    {
        try
        {
            var response = await _httpClient.PostAsJsonAsync("api/auth/login", request);
            
            if (response.IsSuccessStatusCode)
            {
                var content = await response.Content.ReadAsStringAsync();
                var loginResponse = JsonSerializer.Deserialize<LoginResponse>(content, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                });

                if (loginResponse?.Success == true && !string.IsNullOrEmpty(loginResponse.Token))
                {
                    var userInfo = new UserInfo
                    {
                        Id = loginResponse.User.Id,
                        Name = loginResponse.User.Name,
                        Email = loginResponse.User.Email,
                        Username = loginResponse.User.Username,
                        Roles = loginResponse.User.Roles ?? new List<string>()
                    };

                    await _authStateProvider.MarkUserAsAuthenticated(loginResponse.Token, userInfo);

                    return new AuthResult
                    {
                        Success = true,
                        Token = loginResponse.Token,
                        User = userInfo,
                        Message = "تم تسجيل الدخول بنجاح"
                    };
                }
            }

            var errorContent = await response.Content.ReadAsStringAsync();
            var errorResponse = JsonSerializer.Deserialize<ErrorResponse>(errorContent, new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            });

            return new AuthResult
            {
                Success = false,
                Message = errorResponse?.Message ?? "فشل في تسجيل الدخول",
                Errors = errorResponse?.Errors ?? new List<string> { "خطأ غير معروف" }
            };
        }
        catch (Exception ex)
        {
            return new AuthResult
            {
                Success = false,
                Message = "حدث خطأ أثناء تسجيل الدخول",
                Errors = new List<string> { ex.Message }
            };
        }
    }

    public async Task LogoutAsync()
    {
        try
        {
            // Call logout endpoint if needed
            await _httpClient.PostAsync("api/auth/logout", null);
        }
        catch
        {
            // Ignore errors on logout
        }
        finally
        {
            await _authStateProvider.MarkUserAsLoggedOut();
        }
    }

    public async Task<UserInfo?> GetCurrentUserAsync()
    {
        var authState = await _authStateProvider.GetAuthenticationStateAsync();
        
        if (authState.User.Identity?.IsAuthenticated == true)
        {
            return new UserInfo
            {
                Id = int.Parse(authState.User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier)?.Value ?? "0"),
                Name = authState.User.FindFirst(System.Security.Claims.ClaimTypes.Name)?.Value ?? "",
                Email = authState.User.FindFirst(System.Security.Claims.ClaimTypes.Email)?.Value ?? "",
                Username = authState.User.Identity.Name ?? "",
                Roles = authState.User.FindAll(System.Security.Claims.ClaimTypes.Role).Select(c => c.Value).ToList()
            };
        }

        return null;
    }

    public async Task<bool> IsAuthenticatedAsync()
    {
        var authState = await _authStateProvider.GetAuthenticationStateAsync();
        return authState.User.Identity?.IsAuthenticated == true;
    }

    public async Task<bool> RefreshTokenAsync()
    {
        try
        {
            var response = await _httpClient.PostAsync("api/auth/refresh", null);
            
            if (response.IsSuccessStatusCode)
            {
                var content = await response.Content.ReadAsStringAsync();
                var refreshResponse = JsonSerializer.Deserialize<RefreshTokenResponse>(content, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                });

                if (refreshResponse?.Success == true && !string.IsNullOrEmpty(refreshResponse.Token))
                {
                    var currentUser = await GetCurrentUserAsync();
                    if (currentUser != null)
                    {
                        await _authStateProvider.MarkUserAsAuthenticated(refreshResponse.Token, currentUser);
                        return true;
                    }
                }
            }

            return false;
        }
        catch
        {
            return false;
        }
    }

    private class LoginResponse
    {
        public bool Success { get; set; }
        public string Token { get; set; } = string.Empty;
        public UserDto User { get; set; } = new();
    }

    private class UserDto
    {
        public int Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string Email { get; set; } = string.Empty;
        public string Username { get; set; } = string.Empty;
        public List<string>? Roles { get; set; }
    }

    private class ErrorResponse
    {
        public string Message { get; set; } = string.Empty;
        public List<string> Errors { get; set; } = new();
    }

    private class RefreshTokenResponse
    {
        public bool Success { get; set; }
        public string Token { get; set; } = string.Empty;
    }
}
