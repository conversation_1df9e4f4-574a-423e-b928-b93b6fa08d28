using System.ComponentModel;
using System.Runtime.CompilerServices;
using System.Collections.ObjectModel;
using ExactCash.Application.DTOs;
#nullable disable

namespace ExactCash.WPF.Models
{
    public class CartProduct : INotifyPropertyChanged
    {
        private string _unitName;
        private decimal _quantity;
        private decimal _sellingPrice;
        private decimal _discount;
        private decimal _discountAmount;

        public int Id { get; set; }

        public int ProductId { get; set; }

        public string Name { get; set; }
        public int? UnitId { get; set; }

        public string Category { get; set; }

        public int? CategoryId { get; set; }

        public string CategoryName { get; set; }
        public string Brand { get; set; }
        public string BrandName { get; set; }
        public string ImagePath { get; set; }

        public string UnitName
        {
            get => _unitName;
            set
            {
                if (_unitName != value)
                {
                    _unitName = value;
                    OnPropertyChanged();
                }
            }
        }

        public decimal Quantity
        {
            get => _quantity;
            set
            {
                if (_quantity != value)
                {
                    _quantity = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(TotalPricePerProduct));

                    // Update discount amount based on current percentage
                    _discountAmount = TotalPricePerProduct * (_discount / 100);
                    OnPropertyChanged(nameof(DiscountAmount));
                    OnPropertyChanged(nameof(DiscountValue));
                    OnPropertyChanged(nameof(TotalPrice));
                }
            }
        }

        public decimal SellingPrice
        {
            get => _sellingPrice;
            set
            {
                if (_sellingPrice != value)
                {
                    _sellingPrice = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(TotalPricePerProduct));

                    // Update discount amount based on current percentage
                    _discountAmount = TotalPricePerProduct * (_discount / 100);
                    OnPropertyChanged(nameof(DiscountAmount));
                    OnPropertyChanged(nameof(DiscountValue));
                    OnPropertyChanged(nameof(TotalPrice));
                }
            }
        }

        public decimal Discount
        {
            get => _discount;
            set
            {
                if (_discount != value)
                {
                    _discount = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(DiscountValue));
                    OnPropertyChanged(nameof(TotalPrice));

                    // Update discount amount when percentage changes
                    _discountAmount = TotalPricePerProduct * (value / 100);
                    OnPropertyChanged(nameof(DiscountAmount));
                }
            }
        }

        public decimal DiscountAmount
        {
            get => _discountAmount;
            set
            {
                if (_discountAmount != value)
                {
                    _discountAmount = value;
                    OnPropertyChanged();

                    // Update discount percentage when amount changes
                    if (TotalPricePerProduct > 0)
                    {
                        _discount = (value / TotalPricePerProduct) * 100;
                        OnPropertyChanged(nameof(Discount));
                    }

                    OnPropertyChanged(nameof(DiscountValue));
                    OnPropertyChanged(nameof(TotalPrice));
                }
            }
        }

        public decimal TotalPricePerProduct => Quantity * SellingPrice;

        public decimal DiscountValue => DiscountAmount;

        public decimal TotalPrice => TotalPricePerProduct - DiscountAmount;

        private ObservableCollection<UnitDto> _availableUnits = new ObservableCollection<UnitDto>();
        public ObservableCollection<UnitDto> AvailableUnits
        {
            get => _availableUnits;
            set
            {
                _availableUnits = value;
                OnPropertyChanged();
            }
        }

        public event PropertyChangedEventHandler PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }
}