using ExactCash.WASM.Models;

namespace ExactCash.WASM.Services.Interfaces;

public interface ICustomerService
{
    Task<PagedResult<Customer>> GetCustomersAsync(int pageNumber = 1, int pageSize = 10, string searchTerm = "");
    Task<Customer?> GetCustomerByIdAsync(int id);
    Task<Customer?> CreateCustomerAsync(Customer customer);
    Task<Customer?> UpdateCustomerAsync(Customer customer);
    Task<bool> DeleteCustomerAsync(int id);
    Task<List<CustomerCategory>> GetCustomerCategoriesAsync();
    Task<CustomerAccountStatement> GetCustomerAccountStatementAsync(int customerId, DateTime? fromDate = null, DateTime? toDate = null);
}

public class Customer
{
    public int Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Phone { get; set; } = string.Empty;
    public string Email { get; set; } = string.Empty;
    public string Address { get; set; } = string.Empty;
    public int? CategoryId { get; set; }
    public string? CategoryName { get; set; }
    public decimal Balance { get; set; }
    public bool IsActive { get; set; } = true;
    public DateTime CreatedDate { get; set; }
}

public class CustomerCategory
{
    public int Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string? Description { get; set; }
    public bool IsActive { get; set; } = true;
}

public class CustomerAccountStatement
{
    public Customer Customer { get; set; } = new();
    public List<CustomerTransaction> Transactions { get; set; } = new();
    public decimal OpeningBalance { get; set; }
    public decimal ClosingBalance { get; set; }
    public decimal TotalDebits { get; set; }
    public decimal TotalCredits { get; set; }
}

public class CustomerTransaction
{
    public int Id { get; set; }
    public DateTime Date { get; set; }
    public string Description { get; set; } = string.Empty;
    public string ReferenceNumber { get; set; } = string.Empty;
    public decimal DebitAmount { get; set; }
    public decimal CreditAmount { get; set; }
    public decimal Balance { get; set; }
    public string TransactionType { get; set; } = string.Empty;
}
