using System;
using System.Globalization;
using System.Windows;
using System.Windows.Data;

namespace ExactCash.WPF.Converters
{
    public class CountToVisibilityConverter : IValueConverter
    {
        private int _count;
        public int Count
        {
            get => _count;
            set
            {
                if (_count != value)
                {
                    _count = value;
                    OnCountChanged();
                }
            }
        }

        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is int count)
            {
                Count = count;
                return count > 0 ? Visibility.Visible : Visibility.Collapsed;
            }
            return Visibility.Collapsed;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }

        private void OnCountChanged()
        {
            // This method is called when Count changes
            // You can add any additional logic here if needed
        }
    }
}