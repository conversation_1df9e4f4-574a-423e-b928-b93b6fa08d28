﻿using ExactCash.Domain.Common;
#nullable disable

namespace ExactCash.Domain.Entities
{
    /// <summary>
    /// Expense
    /// </summary>
    public class Expense : BaseEntity
    {
        /// <summary>
        /// ExpenseDate
        /// </summary>
        public DateTime ExpenseDate { get; set; }
        /// <summary>
        /// Amount
        /// </summary>
        public decimal Amount { get; set; }
        /// <summary>
        /// Description
        /// </summary>
        public string Description { get; set; }
        /// <summary>
        /// CategoryId
        /// </summary>
        public int? CategoryId { get; set; }
        /// <summary>
        /// Category
        /// </summary>
        public ExpenseCategory Category { get; set; }
        /// <summary>
        /// PaymentMethodId
        /// </summary>
        public int? PaymentMethodId { get; set; }
        /// <summary>
        /// PaymentMethod
        /// </summary>
        public PaymentMethod PaymentMethod { get; set; }
        /// <summary>
        /// ReferenceNumber
        /// </summary>
        public string ReferenceNumber { get; set; }
        /// <summary>
        /// IsDeleted
        /// </summary>
        public bool IsDeleted { get; set; }
    }
}
