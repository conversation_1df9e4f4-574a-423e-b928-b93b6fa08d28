using ExactCash.Application.DTOs;
using ExactCash.WPF.Services.Interfaces;
using ExactCash.WPF.ViewModels.Customer;
using ExactCash.WPF.ViewModels.Expense;
using ExactCash.WPF.ViewModels.Sale;
using Microsoft.Extensions.DependencyInjection;
using System.Windows;

namespace ExactCash.WPF.Views.Expense
{
    public partial class ExpenseMainView : Window
    {
        public ExpenseMainView(
            IExpenseServiceClient expenseServiceClient,
            IExpenseCategoryServiceClient expenseCategoryServiceClient)
        {
            InitializeComponent();

            // Create a container for both ViewModels
            var container = new ExpenseViewModelContainer
            {
                ExpenseListViewModel = new ExpenseListViewModel(expenseServiceClient, expenseCategoryServiceClient),
                ExpenseCategoryListViewModel = new ExpenseCategoryListViewModel(expenseCategoryServiceClient)
            };

            DataContext = container;
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            this.Close();
        }

        private void MinimizeButton_Click(object sender, RoutedEventArgs e)
        {
            this.WindowState = WindowState.Minimized;
        }
    }

    public class ExpenseViewModelContainer
    {
        public ExpenseListViewModel ExpenseListViewModel { get; set; }
        public ExpenseCategoryListViewModel ExpenseCategoryListViewModel { get; set; }
    }
}