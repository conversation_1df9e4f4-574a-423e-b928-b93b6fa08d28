# ExactCash WASM Migration

This document outlines the migration from WPF to Blazor WebAssembly (WASM) for the ExactCash POS system.

## Migration Overview

### Technology Stack
- **From**: WPF with XAML, MVVM pattern
- **To**: Blazor WebAssembly with Razor components, MudBlazor UI framework

### Key Benefits
1. **Cross-Platform**: Runs in any modern web browser
2. **No Installation**: No client-side installation required
3. **Automatic Updates**: Updates deployed centrally
4. **Mobile Support**: Responsive design for tablets and mobile devices
5. **Cloud Ready**: Easy deployment to cloud platforms

## Architecture

### Project Structure
```
src/ExactCash.WASM/
├── Pages/                  # Razor pages (equivalent to WPF Views)
├── Shared/                 # Shared components and layouts
├── Services/               # Business logic services
├── Models/                 # Data models
├── Authentication/         # Authentication providers
├── wwwroot/               # Static files (CSS, JS, images)
└── Program.cs             # Application entry point
```

### Key Components

#### 1. Authentication
- `CustomAuthenticationStateProvider`: Manages user authentication state
- `IAuthenticationService`: Handles login/logout operations
- JWT token-based authentication with local storage

#### 2. Services
- `IApiService`: HTTP client wrapper for API communication
- `ILoadingService`: Global loading state management
- `INotificationService`: Toast notifications using Blazored.Toast
- Business services: Customer, Product, Sale, etc.

#### 3. UI Framework
- **MudBlazor**: Material Design components for Blazor
- **Bootstrap 5**: CSS framework for responsive design
- **RTL Support**: Full Arabic language support

#### 4. State Management
- `AppState`: Global application state
- Component-level state management
- Event-driven state updates

## Migration Mapping

### WPF → Blazor Equivalents

| WPF Concept | Blazor Equivalent | Notes |
|-------------|-------------------|-------|
| UserControl | Razor Component | `.razor` files |
| XAML | Razor Syntax | HTML with C# |
| ViewModel | Component Code | `@code` blocks |
| Binding | Data Binding | `@bind` directive |
| Commands | Event Handlers | `@onclick` etc. |
| Converters | C# Methods | Direct method calls |
| Styles | CSS Classes | CSS/SCSS files |
| Resources | wwwroot | Static files |

### Service Layer
The existing service layer from the WPF application can be largely reused:
- `ICustomerService` → Adapted for HTTP calls
- `IProductService` → Adapted for HTTP calls
- `IReportService` → Adapted for web reports
- Business logic remains the same

## Features Implemented

### ✅ Completed
1. **Project Setup**
   - Blazor WASM project structure
   - NuGet packages configuration
   - Solution file integration

2. **Authentication System**
   - Login page with Arabic UI
   - JWT token management
   - Authentication state provider
   - Route protection

3. **Core Infrastructure**
   - API service for HTTP communication
   - Loading service for UI feedback
   - Notification service for user messages
   - RTL CSS support for Arabic

4. **Layout & Navigation**
   - Main layout with sidebar navigation
   - Top bar with user info and status
   - Responsive design for mobile

5. **Dashboard**
   - Statistics cards
   - Recent sales display
   - Low stock alerts
   - Quick action buttons

### 🚧 In Progress
1. **Business Modules**
   - Customer management
   - Product management
   - Sales processing
   - Inventory tracking
   - Reports generation

2. **Advanced Features**
   - Offline support (PWA)
   - Print functionality
   - File upload/download
   - Real-time updates

## Development Guidelines

### Component Development
1. Use Razor components for reusable UI elements
2. Implement proper data binding with `@bind`
3. Handle async operations with proper loading states
4. Use MudBlazor components for consistent UI

### Service Integration
1. All API calls should go through `IApiService`
2. Use dependency injection for service registration
3. Implement proper error handling and user feedback
4. Cache data when appropriate for performance

### Styling
1. Use Bootstrap classes for layout and spacing
2. Custom CSS for brand-specific styling
3. Ensure RTL support for all custom styles
4. Mobile-first responsive design

## Deployment

### Development
```bash
dotnet run --project src/ExactCash.WASM
```

### Production Build
```bash
dotnet publish src/ExactCash.WASM -c Release -o publish/
```

### Hosting Options
1. **IIS**: Traditional web server hosting
2. **Azure Static Web Apps**: Cloud hosting with CI/CD
3. **GitHub Pages**: Free hosting for static sites
4. **Docker**: Containerized deployment

## Migration Checklist

### Phase 1: Core Infrastructure ✅
- [x] Project setup and configuration
- [x] Authentication system
- [x] Basic layout and navigation
- [x] API service integration

### Phase 2: Business Logic 🚧
- [ ] Customer management module
- [ ] Product management module
- [ ] Sales processing module
- [ ] Inventory management module
- [ ] Purchase orders module
- [ ] Expense tracking module

### Phase 3: Advanced Features
- [ ] Reporting system (RDLC → PDF/Excel)
- [ ] Print functionality
- [ ] Offline support (PWA)
- [ ] Real-time notifications
- [ ] Mobile optimization

### Phase 4: Testing & Deployment
- [ ] Unit tests for components
- [ ] Integration tests for services
- [ ] Performance optimization
- [ ] Production deployment
- [ ] User training and documentation

## Notes

### Key Differences from WPF
1. **Stateless**: Web applications are stateless by nature
2. **Async**: All operations should be async for better UX
3. **Security**: Client-side code is visible to users
4. **Performance**: Consider bundle size and loading times
5. **Browser Compatibility**: Test across different browsers

### Best Practices
1. Keep components small and focused
2. Use proper error boundaries
3. Implement loading states for all async operations
4. Cache data appropriately
5. Optimize for mobile devices
6. Follow accessibility guidelines

## Support

For questions or issues related to the WASM migration, please refer to:
- Blazor documentation: https://docs.microsoft.com/en-us/aspnet/core/blazor/
- MudBlazor documentation: https://mudblazor.com/
- Project-specific documentation in this repository
