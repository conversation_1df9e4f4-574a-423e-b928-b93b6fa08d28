using System.Windows;
using ExactCash.Application.DTOs;
using ExactCash.WPF.Services.Interfaces;

namespace ExactCash.WPF.Views.Expense
{
    public partial class EditExpenseCategoryView : Window
    {
        private readonly IExpenseCategoryServiceClient _service;
        private readonly ExpenseCategoryDto _category;

        public EditExpenseCategoryView(IExpenseCategoryServiceClient service, ExpenseCategoryDto category)
        {
            InitializeComponent();
            _service = service;
            _category = category;

            // Initialize the text boxes with the category data
            NameTextBox.Text = category.Name;
            DescriptionTextBox.Text = category.Description;
        }

        private async void SaveButton_Click(object sender, RoutedEventArgs e)
        {
            if (string.IsNullOrWhiteSpace(NameTextBox.Text))
            {
                System.Windows.MessageBox.Show("يرجى إدخال اسم التصنيف.", "خطأ في التحقق", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            try
            {
                _category.Name = NameTextBox.Text.Trim();
                _category.Description = DescriptionTextBox.Text.Trim();

                var success = await _service.UpdateAsync(_category);
                if (success)
                {
                    DialogResult = true;
                    Close();
                }
                else
                {
                    System.Windows.MessageBox.Show("فشل في تحديث التصنيف.", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            catch (Exception ex)
            {
                System.Windows.MessageBox.Show($"خطأ في تحديث التصنيف: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }
    }
}