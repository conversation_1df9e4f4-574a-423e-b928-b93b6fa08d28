using ExactCash.Domain.Common;
using System.Text.Json.Serialization;
#nullable disable

namespace ExactCash.Domain.Entities
{
    public class Brand : BaseEntity
    {
        /// <summary>
        /// The name of the brand.
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// A description of the brand.
        /// </summary>
        public string Description { get; set; }

        /// <summary>
        /// The products associated with this brand.
        /// </summary>
        [JsonIgnore]
        public ICollection<Product> Products { get; set; } = new List<Product>();
    }
}