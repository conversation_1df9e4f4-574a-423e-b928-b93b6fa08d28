﻿using ExactCash.Application.Contracts;
using ExactCash.Domain.Entities;
using ExactCash.Infrastructure.Persistence;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Microsoft.AspNetCore.Http;
using ExactCash.Application.Responses;
using ExactCash.Application.DTOs.Common;
using ExactCash.Application.Enums;
using System.Drawing;
using ZXing;
using ZXing.Common;
using ZXing.Windows.Compatibility;
using System.Globalization;
using ExactCash.Application.DTOs;
#nullable disable warnings

namespace ExactCash.Application.Services
{
    public class ProductService : IProductService
    {
        private readonly AppPostgreSQLDbContext _context;
        private readonly ILogger<ProductService> _logger;

        public ProductService(AppPostgreSQLDbContext context, ILogger<ProductService> logger)
        {
            _context = context;
            _logger = logger;
        }

        // Create a new product
        public async Task<BaseResponse<Product>> CreateAsync(Product product, IFormFile imageFile, string createdBy)
        {
            try
            {
                _logger.LogInformation("Creating product with name: {ProductName}", product.Name);

                if (!string.IsNullOrEmpty(product.SKU))
                {
                    var existingProduct = await _context.Products
                    .FirstOrDefaultAsync(p => p.SKU == product.SKU);
                    if (existingProduct != null)
                    {
                        _logger.LogWarning("Product with SKU '{ProductSKU}' already exists.", product.SKU);
                        return ResponseHelper.Failure<Product>(StatusCodes.Status400BadRequest, null, "Product with the same SKU already exists.");
                    }
                }
                else if (!string.IsNullOrEmpty(product.Barcode) && await _context.Products.AnyAsync(x => x.Barcode == product.Barcode))
                {
                    _logger.LogWarning("Product with Barcode '{Barcode}' already exists.", product.Barcode);
                    return ResponseHelper.Failure<Product>(StatusCodes.Status400BadRequest, null, "Product with the same Barcode already exists.");
                }
                // Save image to disk (if imageFile is provided)
                if (imageFile != null && imageFile.Length > 0)
                {
                    // Get image upload path from the SystemConfigurations table
                    var config = await _context.systemConfigurations
                        .FirstOrDefaultAsync(c => c.SettingName == "ProductImagePath" && c.IsActive);

                    if (config == null || string.IsNullOrEmpty(config.SettingValue))
                    {
                        _logger.LogError("Product image path configuration is missing or inactive.");
                        return ResponseHelper.Failure<Product>(StatusCodes.Status400BadRequest, null, "Product image path is not configured.");
                    }

                    var imageUploadPath = config.SettingValue;  // Path from SystemConfigurations

                    var fileName = $"{Guid.NewGuid()}_{imageFile.FileName}";
                    var filePath = Path.Combine(imageUploadPath, fileName);

                    // Ensure the directory exists
                    Directory.CreateDirectory(imageUploadPath);

                    using (var stream = new FileStream(filePath, FileMode.Create))
                    {
                        await imageFile.CopyToAsync(stream);  // Save image to the file system
                    }

                    // Set the image path in the product entity
                    product.ImagePath = filePath;
                }

                if (string.IsNullOrWhiteSpace(product.Barcode))
                {
                    string generatedBarcode = GenerateBarcode();
                    while (await _context.Products.AnyAsync(p => p.Barcode == generatedBarcode))
                    {
                        generatedBarcode = GenerateBarcode();
                    }
                    product.Barcode = generatedBarcode;
                }
                product.CreatedBy = createdBy;
                _context.Products.Add(product);
                await _context.SaveChangesAsync();

                _logger.LogInformation("Product created successfully with ID: {ProductId}", product.Id);
                var resultToBeReturn = await _context.Products
                        .Include(x => x.Unit)
                        .Include(x => x.Category)
                        .Include(x => x.Brand)
                        .FirstAsync(x => x.Id == product.Id);
                return ResponseHelper.Success(
                    StatusCodes.Status201Created, resultToBeReturn, $"Product created successfully with ID: {product.Id}"
                );
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while creating product with name: {ProductName}", product.Name);
                return ResponseHelper.Failure<Product>(StatusCodes.Status500InternalServerError, null, $"Error occurred while creating product with name: {product.Name}");
            }
        }

        // Get a product by ID
        public async Task<Product> GetByIdAsync(int id)
        {
            try
            {
                _logger.LogInformation("Fetching product with ID: {ProductId}", id);
                var product = await _context.Products.FindAsync(id);

                if (product == null)
                {
                    _logger.LogWarning("Product with ID {ProductId} not found.", id);
                    return null;
                }

                return product;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while fetching product with ID: {ProductId}", id);
                return null;
            }
        }

        // Get all products
        public async Task<PagedResponse<ProductDto>> GetAllAsync(string name, string sku, int? categoryId, int? brandId, int? unitId, string barcode, PaginationFilter pagination)
        {
            try
            {
                IQueryable<Product> query = _context.Products.Where(p => !p.IsDeleted).AsQueryable();
                query = query.Include(p => p.Category).Include(p => p.Unit);

                if (!string.IsNullOrEmpty(name))
                    query = query.Where(x => EF.Functions.ILike(x.Name, $"%{name}%"));

                if (!string.IsNullOrEmpty(sku))
                    query = query.Where(x => EF.Functions.ILike(x.SKU, $"%{sku}%"));

                if (categoryId.HasValue)
                    query = query.Where(x => x.CategoryId == categoryId.Value);

                if (unitId.HasValue)
                    query = query.Where(x => x.UnitId == unitId.Value);

                if (brandId.HasValue)
                    query = query.Where(x => x.BrandId == brandId.Value);

                if (!string.IsNullOrEmpty(barcode))
                    query = query.Where(x => x.Barcode.ToLower().Contains(barcode.ToLower()));

                var totalRecords = await query.CountAsync();

                query = pagination.SortField switch
                {
                    nameof(Product.CreationDate) => pagination.SortOrder == SortOrder.Asc ? query.OrderBy(a => a.CreationDate) : query.OrderByDescending(a => a.CreationDate),
                    nameof(Product.Name) => pagination.SortOrder == SortOrder.Asc ? query.OrderBy(a => a.Name) : query.OrderByDescending(a => a.Name),
                    nameof(Product.SKU) => pagination.SortOrder == SortOrder.Asc ? query.OrderBy(a => a.SKU) : query.OrderByDescending(a => a.SKU),
                    nameof(Product.Barcode) => pagination.SortOrder == SortOrder.Asc ? query.OrderBy(a => a.Barcode) : query.OrderByDescending(a => a.Barcode),
                    _ => pagination.SortOrder == SortOrder.Desc ? query.OrderBy(a => a.CreationDate) : query.OrderByDescending(a => a.CreationDate)
                };

                var result = await query
               .Select(x => new ProductDto
               {
                   Id = x.Id,
                   Name = x.Name,
                   SKU = x.SKU,
                   Barcode = x.Barcode,
                   CategoryId = x.CategoryId,
                   CategoryName = x.Category.Name,
                   BrandId = x.BrandId,
                   BrandName = x.Brand.Name,
                   UnitId = x.UnitId,
                   UnitName = x.Unit.Name,
                   StockQuantity = x.StockQuantity,
                   CostPrice = x.CostPrice,
                   SellingPrice = x.SellingPrice,
                   Discount = x.Discount,
                   Description = x.Description,
                   ImagePath = x.ImagePath,
                   CreationDate = x.CreationDate,
                   CreatedBy = x.CreatedBy
               })
               .Skip((pagination.PageNumber - 1) * pagination.PageSize)
               .Take(pagination.PageSize)
               .ToListAsync();

                return PaginationHelper.CreatePagedResponse(result, pagination.PageNumber, pagination.PageSize, totalRecords);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error fetching products list.");
                return null;
            }
        }

        public async Task<List<ProductDto>> SearchProductsAsync(string searchTerm)
        {
            IQueryable<Product> query = _context.Products
                .Where(p => !p.IsDeleted)
                .Include(p => p.Unit)
                .Include(p => p.Category)
                .Include(p => p.Brand);

            // If search term is provided, filter by it
            if (!string.IsNullOrEmpty(searchTerm))
            {
                query = query.Where(p => p.Name.ToLower().Contains(searchTerm.ToLower()) ||
                                        p.SKU.ToLower().Contains(searchTerm.ToLower()) ||
                                        p.Barcode.ToLower().Contains(searchTerm.ToLower()));
            }

            var result = await query
                .Select(p => new ProductDto
                {
                    Id = p.Id,
                    Name = p.Name,
                    SKU = p.SKU,
                    Barcode = p.Barcode,
                    SellingPrice = p.SellingPrice,
                    CostPrice = p.CostPrice,
                    UnitName = p.Unit.Name,
                    CategoryName = p.Category.Name,
                    BrandName = p.Brand.Name,
                    UnitId = p.UnitId,
                    DefaultUnitId = (int)p.DefaultUnitId,
                    Discount = p.Discount,
                    ImagePath = p.ImagePath
                })
                .Take(50) // Limit results for performance
                .ToListAsync();

            return result;
        }


        public async Task<ProductDto> GetProductByBarcodeAsync(string barcode)
        {
            if (string.IsNullOrWhiteSpace(barcode))
                return new ProductDto();

            var result = await _context.Products
                .Where(p => p.Barcode.ToLower().Trim() == barcode.ToLower().Trim())
                .Select(p => new ProductDto
                {
                    Id = p.Id,
                    Name = p.Name,
                    SKU = p.SKU,
                    Barcode = p.Barcode,
                    SellingPrice = p.SellingPrice,
                    CostPrice = p.CostPrice,
                    UnitName = p.Unit.Name,
                    CategoryName = p.Category.Name,
                    BrandName = p.Brand.Name,
                    UnitId = p.UnitId,
                    DefaultUnitId = (int)p.DefaultUnitId,
                    Discount = p.Discount
                })
                .FirstOrDefaultAsync();

            return result ?? new ProductDto();
        }


        // Update an existing product
        public async Task<BaseResponse<Product>> UpdateAsync(int id, Product product, IFormFile imageFile)
        {
            try
            {
                _logger.LogInformation("Attempting to update product with ID: {ProductId}", product.Id);

                var existingProduct = await _context.Products.FindAsync(product.Id);
                if (existingProduct == null)
                {
                    _logger.LogWarning("Product with ID {ProductId} not found for update.", product.Id);
                    return null;
                }

                // Manually update only the properties that need to be changed
                if (imageFile != null && imageFile.Length > 0)
                {
                    // Check if the product has an associated image and delete the image from the file system
                    if (!string.IsNullOrEmpty(product.ImagePath) && File.Exists(product.ImagePath))
                    {
                        try
                        {
                            _logger.LogInformation("Deleting image for product with ID: {ProductId} at path: {ImagePath}", id, product.ImagePath);
                            File.Delete(product.ImagePath);
                            _logger.LogInformation("Image for product with ID: {ProductId} deleted successfully.", id);
                        }
                        catch (Exception ex)
                        {
                            _logger.LogError(ex, "Error occurred while deleting image for product with ID: {ProductId}", id);
                        }
                    }

                    // Get image upload path from the SystemConfigurations table
                    var config = await _context.systemConfigurations
                        .FirstOrDefaultAsync(c => c.SettingName == "ProductImagePath" && c.IsActive);

                    if (config == null || string.IsNullOrEmpty(config.SettingValue))
                    {
                        _logger.LogError("Product image path configuration is missing or inactive.");
                        return ResponseHelper.Failure<Product>(StatusCodes.Status400BadRequest, null, "Product image path is not configured.");
                    }

                    var imageUploadPath = config.SettingValue;  // Path from SystemConfigurations

                    var fileName = $"{Guid.NewGuid()}_{imageFile.FileName}";
                    var filePath = Path.Combine(imageUploadPath, fileName);

                    // Ensure the directory exists
                    Directory.CreateDirectory(imageUploadPath);

                    using (var stream = new FileStream(filePath, FileMode.Create))
                    {
                        await imageFile.CopyToAsync(stream);  // Save image to the file system
                    }

                    // Set the image path in the product entity
                    product.ImagePath = filePath;
                }
                _context.Entry(existingProduct).CurrentValues.SetValues(product);
                await _context.SaveChangesAsync();

                _logger.LogInformation("Product with ID {ProductId} updated successfully.", product.Id);
                var resultToBeReturn = await _context.Products
                        .Include(x => x.Unit)
                        .Include(x => x.Category)
                        .Include(x => x.Brand)
                        .FirstAsync(x => x.Id == product.Id);
                return ResponseHelper.Success(
                    StatusCodes.Status201Created, resultToBeReturn, $"Product created successfully with ID: {product.Id}"
                );
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while updating product with ID: {ProductId}", product.Id);
                return ResponseHelper.Failure<Product>(StatusCodes.Status500InternalServerError, null, $"Error occurred while updating product with name: {product.Name}");
            }
        }

        // Delete a product by ID
        public async Task<bool> DeleteAsync(int id)
        {
            try
            {
                _logger.LogInformation("Attempting to delete product with ID: {ProductId}", id);

                var product = await _context.Products.FindAsync(id);
                if (product == null)
                {
                    _logger.LogWarning("Product with ID {ProductId} not found for deletion.", id);
                    return false;
                }

                // Check if the product has an associated image and delete the image from the file system
                if (!string.IsNullOrEmpty(product.ImagePath) && File.Exists(product.ImagePath))
                {
                    try
                    {
                        _logger.LogInformation("Deleting image for product with ID: {ProductId} at path: {ImagePath}", id, product.ImagePath);
                        File.Delete(product.ImagePath);
                        _logger.LogInformation("Image for product with ID: {ProductId} deleted successfully.", id);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Error occurred while deleting image for product with ID: {ProductId}", id);
                    }
                }

                // Remove the product from the database
                product.SoftDelete();
                await _context.SaveChangesAsync();

                _logger.LogInformation("Product with ID {ProductId} deleted successfully.", id);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while deleting product with ID: {ProductId}", id);
                return false;
            }
        }

        // Update product stock (quantity)
        public async Task<bool> UpdateStockAsync(int productId, int quantityChange)
        {
            try
            {
                _logger.LogInformation("Updating stock for product with ID: {ProductId} by {QuantityChange}", productId, quantityChange);

                var product = await _context.Products.FindAsync(productId);
                if (product == null)
                {
                    _logger.LogWarning("Product with ID {ProductId} not found for stock update.", productId);
                    return false;  // Return false if the product was not found
                }

                product.StockQuantity += quantityChange;
                product.UpdateStock(product.StockQuantity);
                await _context.SaveChangesAsync();

                _logger.LogInformation("Stock updated successfully for product with ID: {ProductId}. New stock: {NewStock}", productId, product.StockQuantity);
                return true;  // Return true if the stock update was successful
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while updating stock for product with ID: {ProductId}", productId);
                return false;
            }
        }

        private string GenerateBarcode()
        {
            // Use timestamp and a random 4-digit number
            var timestamp = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds();
            var random = new Random().Next(1000, 9999);
            return $"PRD{timestamp}{random}";
        }

        // Implement barcode printing logic here
        public async Task<byte[]> GenerateBarcodeImageAsync(int productId)
        {
            try
            {
                Product product = _context.Products.Find(productId);
                if (product == null)
                {
                    _logger.LogWarning("Product with ID {ProductId} not found.", productId);
                    return null;
                }

                // Ensure the product has a barcode
                if (string.IsNullOrEmpty(product.Barcode))
                {
                    _logger.LogWarning("Product with ID {ProductId} does not have a barcode.", productId);
                    return null;
                }

#pragma warning disable
                // Fetch barcode settings
                var settings = await GetBarcodeSettingsAsync();
                int width = int.Parse(settings["BarcodeWidth"]);
                int height = int.Parse(settings["BarcodeHeight"]);
                int margin = int.Parse(settings["BarcodeMargin"]);
                int fontSize = int.Parse(settings["BarcodeFontSize"]);
                int priceFontSize = int.Parse(settings["BarcodePriceFontSize"]);
                int titleYOffset = int.Parse(settings["BarcodeTitleYOffset"]);
                int priceYOffset = int.Parse(settings["BarcodePriceYOffset"]);
                string fontFamily = settings["BarcodeFontFamily"];
                string textAlign = settings["BarcodeTextAlign"];
                string backgroundColorHex = settings["BarcodeBackgroundColor"];
                string currencyFormatCode = settings["CurrencyFormat"];

                var writer = new BarcodeWriter<Bitmap>
                {
                    Format = BarcodeFormat.CODE_128,
                    Options = new EncodingOptions
                    {
                        Height = height,
                        Width = width,
                        Margin = margin
                    },
                    Renderer = new BitmapRenderer()
                };
                Bitmap barcodeBitmap = writer.Write(product.Barcode);

                // Create new bitmap to fit title and price
                Bitmap finalImage = new Bitmap(barcodeBitmap.Width, barcodeBitmap.Height + 60);
                using (Graphics graphics = Graphics.FromImage(finalImage))
                {
                    // Fonts
                    var priceFont = new Font(fontFamily, priceFontSize, FontStyle.Regular);

                    graphics.Clear(Color.White);

                    // Draw product name
                    using var fontTitle = new Font(fontFamily, fontSize, FontStyle.Bold);
                    var titleRect = new RectangleF(0, 0, finalImage.Width, 30);
                    graphics.DrawString(product.Name, fontTitle, Brushes.Black, titleRect, new StringFormat { Alignment = StringAlignment.Center });

                    // Draw barcode
                    graphics.DrawImage(barcodeBitmap, 0, 30);

                    // Draw price (bottom center)
                    // Get culture setting from configuration
                    // Fallback to "ar-EG" if not configured
                    var cultureCode = currencyFormatCode ?? "ar-EG";
                    CultureInfo culture;

                    try
                    {
                        culture = new CultureInfo(cultureCode);
                    }
                    catch
                    {
                        culture = new CultureInfo("ar-EG");
                        cultureCode = "ar-EG";
                    }

                    // Map localized "Price" labels
                    var priceLabels = new Dictionary<string, string>(StringComparer.OrdinalIgnoreCase)
                    {
                        { "ar-EG", "السعر" },
                        { "en-US", "Price" },
                        { "fr-FR", "Prix" },
                        { "de-DE", "Preis" },
                        { "it-IT", "Prezzo" }
                    };

                    // Fallback to English if not found
                    var priceLabel = priceLabels.TryGetValue(cultureCode, out var label) ? label : "Price";

                    // Now format the final price text with exactly 2 decimal places and Egyptian Pound symbol
                    var priceText = $"{priceLabel}: {product.SellingPrice:N2} ج.م";


                    var priceSize = graphics.MeasureString(priceText, priceFont);
                    graphics.DrawString(priceText, priceFont, Brushes.Black, (finalImage.Width - priceSize.Width) / 2, barcodeBitmap.Height + 35);
                }

                // Save to a MemoryStream
                using (var ms = new MemoryStream())
                {
                    finalImage.Save(ms, System.Drawing.Imaging.ImageFormat.Png);
                    return ms.ToArray();
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating barcode image for: {productId}", productId);
                return null;
            }
        }

        public async Task<Dictionary<string, string>> GetBarcodeSettingsAsync()
        {
            var settings = await _context.systemConfigurations
                .Where(s => s.IsActive && s.SettingName.StartsWith("Barcode") || s.SettingName == "CurrencyFormat")
                .ToDictionaryAsync(s => s.SettingName, s => s.SettingValue);

            return settings;
        }

    }
}
