using AutoMapper;
using ExactCash.Application.DTOs;
using ExactCash.Application.DTOs.Common;
using ExactCash.Application.Enums;
using ExactCash.Application.Interfaces;
using ExactCash.Application.Responses;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
#nullable disable

namespace ExactCash.Application.Services
{
    public class UserService : IUserService
    {
        private readonly UserManager<IdentityUser> _userManager;
        private readonly RoleManager<IdentityRole> _roleManager;
        private readonly IMapper _mapper;
        private readonly ILogger<UserService> _logger;

        public UserService(
            UserManager<IdentityUser> userManager,
            RoleManager<IdentityRole> roleManager,
            IMapper mapper,
            ILogger<UserService> logger)
        {
            _userManager = userManager;
            _roleManager = roleManager;
            _mapper = mapper;
            _logger = logger;
        }

        public async Task<UserDto> GetUserByIdAsync(string id)
        {
            var user = await _userManager.FindByIdAsync(id);
            if (user == null)
                return null;

            var userDto = _mapper.Map<UserDto>(user);
            userDto.Roles = (await _userManager.GetRolesAsync(user)).ToList();
            return userDto;
        }

        public async Task<PagedResponse<UserDto>> GetAllUsersAsync(string email, string username, string phoneNumber, PaginationFilter pagination)
        {
            var query = _userManager.Users.AsQueryable();

            if(!string.IsNullOrEmpty(email))
                query = query.Where(x => x.Email.ToLower().Contains(email.ToLower()));

            if(!string.IsNullOrEmpty(username))
                query = query.Where(x => x.UserName.ToLower().Contains(username.ToLower()));

            if (!string.IsNullOrEmpty(phoneNumber))
                query = query.Where(x => x.PhoneNumber.Contains(phoneNumber.ToLower()));

            var totalRecords = await query.CountAsync();

            query = pagination.SortField switch
            {
                nameof(IdentityUser.UserName) => pagination.SortOrder == SortOrder.Asc ?
                    query.OrderBy(u => u.UserName) : query.OrderByDescending(u => u.UserName),
                nameof(IdentityUser.Email) => pagination.SortOrder == SortOrder.Asc ?
                    query.OrderBy(u => u.Email) : query.OrderByDescending(u => u.Email),
                _ => pagination.SortOrder == SortOrder.Desc ?
                    query.OrderBy(u => u.UserName) : query.OrderByDescending(u => u.UserName)
            };

            var users = await query
                .Skip((pagination.PageNumber - 1) * pagination.PageSize)
                .Take(pagination.PageSize)
                .ToListAsync();

            var userDtos = new List<UserDto>();
            foreach (var user in users)
            {
                var userDto = _mapper.Map<UserDto>(user);
                userDto.Roles = (await _userManager.GetRolesAsync(user)).ToList();
                userDtos.Add(userDto);
            }

            return PaginationHelper.CreatePagedResponse(
                userDtos,
                pagination.PageNumber,
                pagination.PageSize,
                totalRecords);
        }

        public async Task<IdentityResult> CreateUserAsync(CreateUserDto userDto)
        {
            var user = new IdentityUser
            {
                UserName = userDto.UserName,
                Email = userDto.Email,
                PhoneNumber = userDto.PhoneNumber
            };

            var result = await _userManager.CreateAsync(user, userDto.Password);

            if (result.Succeeded)
            {
                _logger.LogInformation("User created successfully: {UserId}", user.Id);

                // Assign roles
                if (userDto.Roles != null && userDto.Roles.Any())
                {
                    var roleResult = await _userManager.AddToRolesAsync(user, userDto.Roles);
                    if (!roleResult.Succeeded)
                    {
                        _logger.LogError("Failed to assign roles to user: {Errors}",
                            string.Join(", ", roleResult.Errors.Select(e => e.Description)));
                        return roleResult;
                    }
                }
            }
            else
            {
                _logger.LogError("Failed to create user: {Errors}",
                    string.Join(", ", result.Errors.Select(e => e.Description)));
            }

            return result;
        }

        public async Task<IdentityResult> UpdateUserAsync(string id, UpdateUserDto userDto)
        {
            var user = await _userManager.FindByIdAsync(id);
            if (user == null)
                return IdentityResult.Failed(new IdentityError { Description = "User not found." });

            user.UserName = userDto.UserName;
            user.Email = userDto.Email;
            user.PhoneNumber = userDto.PhoneNumber;

            var result = await _userManager.UpdateAsync(user);

            if (result.Succeeded)
            {
                _logger.LogInformation("User updated successfully: {UserId}", user.Id);

                // Update roles
                if (userDto.Roles != null)
                {
                    var currentRoles = await _userManager.GetRolesAsync(user);
                    var rolesToRemove = currentRoles.Except(userDto.Roles).ToList();
                    var rolesToAdd = userDto.Roles.Except(currentRoles).ToList();

                    if (rolesToRemove.Any())
                    {
                        var removeResult = await _userManager.RemoveFromRolesAsync(user, rolesToRemove);
                        if (!removeResult.Succeeded)
                        {
                            _logger.LogError("Failed to remove roles from user: {Errors}",
                                string.Join(", ", removeResult.Errors.Select(e => e.Description)));
                            return removeResult;
                        }
                    }

                    if (rolesToAdd.Any())
                    {
                        var addResult = await _userManager.AddToRolesAsync(user, rolesToAdd);
                        if (!addResult.Succeeded)
                        {
                            _logger.LogError("Failed to add roles to user: {Errors}",
                                string.Join(", ", addResult.Errors.Select(e => e.Description)));
                            return addResult;
                        }
                    }
                }
            }
            else
            {
                _logger.LogError("Failed to update user: {Errors}",
                    string.Join(", ", result.Errors.Select(e => e.Description)));
            }

            return result;
        }

        public async Task<IdentityResult> DeleteUserAsync(string id)
        {
            var user = await _userManager.FindByIdAsync(id);
            if (user == null)
                return IdentityResult.Failed(new IdentityError { Description = "User not found." });

            var result = await _userManager.DeleteAsync(user);

            if (result.Succeeded)
            {
                _logger.LogInformation("User deleted successfully: {UserId}", user.Id);
            }
            else
            {
                _logger.LogError("Failed to delete user: {Errors}", string.Join(", ", result.Errors.Select(e => e.Description)));
            }

            return result;
        }

        public async Task<IdentityResult> ChangePasswordAsync(string id, ChangePasswordDto passwordDto)
        {
            var user = await _userManager.FindByIdAsync(id);
            if (user == null)
                return IdentityResult.Failed(new IdentityError { Description = "User not found." });

            var result = await _userManager.ChangePasswordAsync(user, passwordDto.CurrentPassword, passwordDto.NewPassword);

            if (result.Succeeded)
            {
                _logger.LogInformation("Password changed successfully for user: {UserId}", user.Id);
            }
            else
            {
                _logger.LogError("Failed to change password: {Errors}", string.Join(", ", result.Errors.Select(e => e.Description)));
            }

            return result;
        }

        public async Task<IdentityResult> ResetPasswordAsync(ResetPasswordDto resetPasswordDto)
        {
            var user = await _userManager.FindByEmailAsync(resetPasswordDto.Email);
            if (user == null)
                return IdentityResult.Failed(new IdentityError { Description = "User not found." });

            var result = await _userManager.ResetPasswordAsync(user, resetPasswordDto.Token, resetPasswordDto.NewPassword);

            if (result.Succeeded)
            {
                _logger.LogInformation("Password reset successfully for user: {UserId}", user.Id);
            }
            else
            {
                _logger.LogError("Failed to reset password: {Errors}", string.Join(", ", result.Errors.Select(e => e.Description)));
            }

            return result;
        }

        public async Task<string> GeneratePasswordResetTokenAsync(string email)
        {
            var user = await _userManager.FindByEmailAsync(email);
            if (user == null)
                return null;

            return await _userManager.GeneratePasswordResetTokenAsync(user);
        }

        public async Task<IdentityResult> LockUserAsync(string id)
        {
            var user = await _userManager.FindByIdAsync(id);
            if (user == null)
                return IdentityResult.Failed(new IdentityError { Description = "User not found." });

            var result = await _userManager.SetLockoutEndDateAsync(user, DateTimeOffset.MaxValue);

            if (result.Succeeded)
            {
                _logger.LogInformation("User locked successfully: {UserId}", user.Id);
            }
            else
            {
                _logger.LogError("Failed to lock user: {Errors}", string.Join(", ", result.Errors.Select(e => e.Description)));
            }

            return result;
        }

        public async Task<IdentityResult> UnlockUserAsync(string id)
        {
            var user = await _userManager.FindByIdAsync(id);
            if (user == null)
                return IdentityResult.Failed(new IdentityError { Description = "User not found." });

            var result = await _userManager.SetLockoutEndDateAsync(user, null);

            if (result.Succeeded)
            {
                _logger.LogInformation("User unlocked successfully: {UserId}", user.Id);
            }
            else
            {
                _logger.LogError("Failed to unlock user: {Errors}", string.Join(", ", result.Errors.Select(e => e.Description)));
            }

            return result;
        }

        public async Task<IdentityResult> ConfirmEmailAsync(string id, string token)
        {
            var user = await _userManager.FindByIdAsync(id);
            if (user == null)
                return IdentityResult.Failed(new IdentityError { Description = "User not found." });

            var result = await _userManager.ConfirmEmailAsync(user, token);

            if (result.Succeeded)
            {
                _logger.LogInformation("Email confirmed successfully for user: {UserId}", user.Id);
            }
            else
            {
                _logger.LogError("Failed to confirm email: {Errors}", string.Join(", ", result.Errors.Select(e => e.Description)));
            }

            return result;
        }

        public async Task<string> GenerateEmailConfirmationTokenAsync(string id)
        {
            var user = await _userManager.FindByIdAsync(id);
            if (user == null)
                return null;

            return await _userManager.GenerateEmailConfirmationTokenAsync(user);
        }
    }
}