﻿using ExactCash.Domain.Common;
#nullable disable

namespace ExactCash.Domain.Entities
{
    public class Promotion : BaseEntity
    {
        /// <summary>
        /// The name of the promotion (e.g., "Buy One Get One Free", "Summer Discount").
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// A description of the promotion, explaining the details (e.g., "Buy one product and get another for free").
        /// </summary>
        public string Description { get; set; }

        /// <summary>
        /// Flag indicating whether the promotion is currently active.
        /// </summary>
        public bool IsActive { get; set; }

        /// <summary>
        /// The start date of the promotion. Can be null if the promotion does not have a start date.
        /// </summary>
        public DateTime? StartDate { get; set; }

        /// <summary>
        /// The end date of the promotion. Can be null if the promotion does not have an end date.
        /// </summary>
        public DateTime? EndDate { get; set; }

        /// <summary>
        /// A collection of discounts associated with this promotion. A promotion can have multiple discounts.
        /// </summary>
        public ICollection<Discount> Discounts { get; set; }
    }

}
