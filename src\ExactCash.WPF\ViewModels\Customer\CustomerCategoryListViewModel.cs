using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using System.Windows.Input;
using ExactCash.Application.DTOs;
using ExactCash.WPF.Commands;
using ExactCash.WPF.Services;
using ExactCash.WPF.Views.Customer;
using System.Windows;
using Microsoft.Win32;
using System.IO;
using System.Text;

namespace ExactCash.WPF.ViewModels.Customer
{
    public class CustomerCategoryListViewModel : INotifyPropertyChanged
    {
        private readonly ICustomerCategoryServiceClient _service;
        private ObservableCollection<CustomerCategoryDto> _categories;

        public CustomerCategoryListViewModel(ICustomerCategoryServiceClient service)
        {
            _service = service;
            Categories = new ObservableCollection<CustomerCategoryDto>();
            
            AddCategoryCommand = new RelayCommand(OpenAddCategoryDialog);
            LoadCategoriesCommand = new RelayCommand(async () => await LoadCategoriesAsync());
            EditCommand = new RelayCommand<CustomerCategoryDto>(EditCategory);
            DeleteCommand = new RelayCommand<CustomerCategoryDto>(DeleteCategory);
            ExportToExcelCommand = new RelayCommand(ExportToExcel);
            RefreshCommand = new RelayCommand(async () => await LoadCategoriesAsync());
            
            // Load categories on construction
            LoadCategoriesCommand.Execute(null);
        }

        public ObservableCollection<CustomerCategoryDto> Categories
        {
            get => _categories;
            set
            {
                _categories = value;
                OnPropertyChanged();
            }
        }

        public ICommand AddCategoryCommand { get; }
        public ICommand LoadCategoriesCommand { get; }
        public ICommand EditCommand { get; }
        public ICommand DeleteCommand { get; }
        public ICommand ExportToExcelCommand { get; }
        public ICommand RefreshCommand { get; }

        private void OpenAddCategoryDialog()
        {
            var addCategoryView = new AddCustomerCategoryView(_service);
            addCategoryView.Owner = FindParentWindow();
            if (addCategoryView.ShowDialog() == true)
            {
                LoadCategoriesCommand.Execute(null);
            }
        }

        private void EditCategory(CustomerCategoryDto category)
        {
            if (category == null) return;

            var editCategoryView = new AddCustomerCategoryView(_service, category);
            editCategoryView.Owner = FindParentWindow();
            if (editCategoryView.ShowDialog() == true)
            {
                LoadCategoriesCommand.Execute(null);
            }
        }

        private async void DeleteCategory(CustomerCategoryDto category)
        {
            if (category == null) return;

            var result = Helpers.BootstrapMessageBoxHelper.Show(
                $"هل أنت متأكد من حذف التصنيف '{category.Name}'؟",
                "تأكيد الحذف", 
                owner: FindParentWindow());

            if (result == true)
            {
                try
                {
                    var deleteResult = await _service.DeleteAsync(category.Id);
                    if (deleteResult.Success)
                    {
                        Helpers.BootstrapMessageBoxHelper.ShowSuccess(deleteResult.Message, owner: FindParentWindow());
                        await LoadCategoriesAsync();
                    }
                    else
                    {
                        Helpers.BootstrapMessageBoxHelper.ShowError(deleteResult.Message, "خطأ", owner: FindParentWindow());
                    }
                }
                catch (Exception ex)
                {
                    Helpers.BootstrapMessageBoxHelper.ShowError($"حدث خطأ أثناء حذف التصنيف: {ex.Message}", "خطأ", owner: FindParentWindow());
                }
            }
        }

        private void ExportToExcel()
        {
            var saveFileDialog = new Microsoft.Win32.SaveFileDialog
            {
                Filter = "CSV Files (*.csv)|*.csv",
                FileName = $"تصنيفات_العملاء_{DateTime.Now:yyyyMMdd_HHmmss}.csv"
            };

            if (saveFileDialog.ShowDialog() == true)
            {
                try
                {
                    using (var writer = new StreamWriter(saveFileDialog.FileName, false, Encoding.UTF8))
                    {
                        writer.WriteLine("الاسم,الوصف,تاريخ الإنشاء,تم الإنشاء بواسطة");
                        foreach (var category in Categories)
                        {
                            writer.WriteLine($"{category.Name},{category.Description},{category.CreationDate:yyyy-MM-dd},{category.CreatedBy ?? "-"}");
                        }
                    }

                    Helpers.BootstrapMessageBoxHelper.ShowSuccess("تم تصدير البيانات بنجاح!", owner: FindParentWindow());
                }
                catch (Exception ex)
                {
                    Helpers.BootstrapMessageBoxHelper.ShowError($"حدث خطأ أثناء التصدير: {ex.Message}", "خطأ", owner: FindParentWindow());
                }
            }
        }

        private Window FindParentWindow()
        {
            foreach (Window window in System.Windows.Application.Current.Windows)
            {
                if (window.IsActive || window.Name == "CustomerListView1")
                {
                    return window;
                }
            }
            return System.Windows.Application.Current.MainWindow;
        }

        public async Task LoadCategoriesAsync()
        {
            try
            {
                Categories.Clear();
                var categories = await _service.GetAllAsync();
                foreach (var category in categories)
                {
                    Categories.Add(category);
                }
            }
            catch (Exception ex)
            {
                Helpers.BootstrapMessageBoxHelper.ShowError($"حدث خطأ أثناء تحميل التصنيفات: {ex.Message}", "خطأ", owner: FindParentWindow());
            }
        }

        public event PropertyChangedEventHandler PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }
}
