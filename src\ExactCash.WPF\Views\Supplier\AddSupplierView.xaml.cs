using System.Windows;
using ExactCash.WPF.ViewModels.Supplier;

namespace ExactCash.WPF.Views.Supplier
{
    public partial class AddSupplierView : Window
    {
        public AddSupplierView()
        {
            InitializeComponent();
            Loaded += AddSupplierView_Loaded;
        }

        private void AddSupplierView_Loaded(object sender, RoutedEventArgs e)
        {
            if (DataContext is AddSupplierViewModel viewModel)
            {
                viewModel.RequestClose += ViewModel_RequestClose;
            }
        }

        private void ViewModel_RequestClose(object sender, bool dialogResult)
        {
            DialogResult = dialogResult;
            Close();
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }
    }
}