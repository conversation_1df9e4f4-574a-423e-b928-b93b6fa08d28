using System.ComponentModel.DataAnnotations;
#nullable disable

namespace ExactCash.Application.DTOs
{
    /// <summary>
    /// DTO for outstanding sales (credit sales with remaining amounts)
    /// </summary>
    public class OutstandingSaleDto
    {
        public int Id { get; set; }
        public string InvoiceNumber { get; set; }
        public int CustomerId { get; set; }
        public string CustomerName { get; set; }
        public string CustomerPhone { get; set; }
        public DateTime CreationDate { get; set; }
        public DateTime DueDate { get; set; }
        public decimal TotalAmount { get; set; }
        public decimal PaidAmount { get; set; }
        public decimal RemainingAmount { get; set; }
        public int DaysOverdue { get; set; }
        public string Status { get; set; }
        public string Notes { get; set; }

        // Calculated properties
        public bool IsOverdue => DaysOverdue > 0;
        public string StatusColor => IsOverdue ? "#DC3545" : "#28A745";
        public string FormattedDueDate => DueDate.ToString("dd/MM/yyyy");
        public string FormattedCreationDate => CreationDate.ToString("dd/MM/yyyy");
    }

    /// <summary>
    /// DTO for customer credit limits and utilization
    /// </summary>
    public class CustomerCreditLimitDto
    {
        public int CustomerId { get; set; }
        public string CustomerName { get; set; }
        public string Phone { get; set; }
        public string Email { get; set; }
        public decimal CreditLimit { get; set; }
        public decimal OutstandingBalance { get; set; }
        public decimal AvailableCredit { get; set; }
        public double CreditUtilization { get; set; }
        public DateTime LastUpdated { get; set; }
        public string UpdatedBy { get; set; }

        // Calculated properties
        public string Status
        {
            get
            {
                if (CreditUtilization >= 1.0) return "تجاوز الحد";
                if (CreditUtilization >= 0.8) return "قريب من الحد";
                if (CreditUtilization >= 0.5) return "متوسط";
                return "آمن";
            }
        }

        public string StatusColor
        {
            get
            {
                if (CreditUtilization >= 1.0) return "#DC3545"; // Red
                if (CreditUtilization >= 0.8) return "#FD7E14"; // Orange
                if (CreditUtilization >= 0.5) return "#FFC107"; // Yellow
                return "#28A745"; // Green
            }
        }
    }

    /// <summary>
    /// DTO for payment collection
    /// </summary>
    public class PaymentCollectionDto
    {
        public int SaleId { get; set; }
        public string InvoiceNumber { get; set; }
        public int CustomerId { get; set; }
        public string CustomerName { get; set; }
        public decimal RemainingAmount { get; set; }
        public decimal PaymentAmount { get; set; }
        public string PaymentMethod { get; set; }
        public DateTime PaymentDate { get; set; }
        public string Notes { get; set; }
        public string CollectedBy { get; set; }

        // Validation
        public bool IsValidPayment => PaymentAmount > 0 && PaymentAmount <= RemainingAmount;
    }

    /// <summary>
    /// DTO for credit limit update
    /// </summary>
    public class CreditLimitUpdateDto
    {
        [Required]
        public int CustomerId { get; set; }

        [Required]
        public string CustomerName { get; set; }

        [Required]
        [Range(0, double.MaxValue, ErrorMessage = "حد الائتمان يجب أن يكون أكبر من أو يساوي صفر")]
        public decimal NewCreditLimit { get; set; }

        public decimal CurrentCreditLimit { get; set; }
        public decimal OutstandingBalance { get; set; }
        public string Reason { get; set; }
        public string UpdatedBy { get; set; }
        public DateTime UpdateDate { get; set; }

        // Validation
        public bool IsValidUpdate => NewCreditLimit >= 0 && NewCreditLimit >= OutstandingBalance;
        public string ValidationMessage
        {
            get
            {
                if (NewCreditLimit < 0) return "حد الائتمان لا يمكن أن يكون سالباً";
                if (NewCreditLimit < OutstandingBalance) return "حد الائتمان لا يمكن أن يكون أقل من الرصيد المستحق";
                return string.Empty;
            }
        }
    }

    /// <summary>
    /// DTO for outstanding payments report
    /// </summary>
    public class OutstandingPaymentsReportDto
    {
        public DateTime ReportDate { get; set; }
        public DateTime FromDate { get; set; }
        public DateTime ToDate { get; set; }

        // Summary totals
        public decimal TotalOutstandingAmount { get; set; }
        public int TotalOutstandingInvoices { get; set; }
        public decimal OverdueAmount { get; set; }
        public int OverdueInvoices { get; set; }
        public double AverageDaysOverdue { get; set; }

        // Aging analysis
        public decimal Current { get; set; } // 0-30 days
        public decimal Days31To60 { get; set; }
        public decimal Days61To90 { get; set; }
        public decimal Over90Days { get; set; }

        // Customer breakdown
        public List<CustomerOutstandingDto> CustomerBreakdown { get; set; } = new List<CustomerOutstandingDto>();

        // Detailed transactions
        public List<OutstandingSaleDto> OutstandingSales { get; set; } = new List<OutstandingSaleDto>();
    }

    /// <summary>
    /// DTO for customer outstanding breakdown
    /// </summary>
    public class CustomerOutstandingDto
    {
        public int CustomerId { get; set; }
        public string CustomerName { get; set; }
        public string Phone { get; set; }
        public decimal OutstandingAmount { get; set; }
        public int OutstandingInvoices { get; set; }
        public decimal CreditLimit { get; set; }
        public double CreditUtilization { get; set; }
        public int DaysOverdue { get; set; }
        public string RiskLevel { get; set; }
    }

    /// <summary>
    /// DTO for credit sales search filters
    /// </summary>
    public class CreditSalesSearchDto
    {
        public string InvoiceNumber { get; set; }
        public string CustomerName { get; set; }
        public int? CustomerId { get; set; }
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public bool? IsOverdue { get; set; }
        public decimal? MinAmount { get; set; }
        public decimal? MaxAmount { get; set; }
        public int PageNumber { get; set; } = 1;
        public int PageSize { get; set; } = 50;
    }

    /// <summary>
    /// DTO for payment history
    /// </summary>
    public class PaymentHistoryDto
    {
        public int PaymentId { get; set; }
        public int SaleId { get; set; }
        public string InvoiceNumber { get; set; }
        public decimal PaymentAmount { get; set; }
        public string PaymentMethod { get; set; }
        public DateTime PaymentDate { get; set; }
        public string CollectedBy { get; set; }
        public string Notes { get; set; }
        public decimal RemainingAmountAfterPayment { get; set; }
    }

    /// <summary>
    /// DTO for credit payment history from CreditPayment table
    /// </summary>
    public class CreditPaymentHistoryDto
    {
        public int PaymentId { get; set; }
        public int CreditSaleTransactionId { get; set; }
        public string CustomerName { get; set; }
        public decimal PaymentAmount { get; set; }
        public string PaymentMethod { get; set; }
        public DateTime PaymentDate { get; set; }
        public string ReferenceNumber { get; set; }
        public string CollectedBy { get; set; }
        public string Notes { get; set; }
        public decimal RemainingAmountAfterPayment { get; set; }
    }

    /// <summary>
    /// DTO for searching credit payment history
    /// </summary>
    public class CreditPaymentSearchDto
    {
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public string? CustomerName { get; set; }
        public string? PaymentMethod { get; set; }
        public string? ReferenceNumber { get; set; }
    }

    /// <summary>
    /// DTO for credit validation response
    /// </summary>
    public class CreditValidationDto
    {
        public bool IsValid { get; set; }
        public string Message { get; set; }
        public decimal CreditLimit { get; set; }
        public decimal OutstandingBalance { get; set; }
        public decimal AvailableCredit { get; set; }
        public decimal RequestedAmount { get; set; }
        public decimal RemainingCreditAfterPurchase { get; set; }
    }

    /// <summary>
    /// DTO for payment receipt
    /// </summary>
    public class PaymentReceiptDto
    {
        public int PaymentId { get; set; }
        public string ReceiptNumber { get; set; }
        public DateTime PaymentDate { get; set; }
        public string CustomerName { get; set; }
        public string CustomerPhone { get; set; }
        public decimal PaymentAmount { get; set; }
        public string PaymentMethod { get; set; }
        public string CollectedBy { get; set; }
        public string Notes { get; set; }
        public List<PaymentReceiptItemDto> PaidInvoices { get; set; } = new List<PaymentReceiptItemDto>();

        // Company information
        public string CompanyName { get; set; }
        public string CompanyAddress { get; set; }
        public string CompanyPhone { get; set; }
    }

    /// <summary>
    /// DTO for payment receipt items
    /// </summary>
    public class PaymentReceiptItemDto
    {
        public string InvoiceNumber { get; set; }
        public DateTime InvoiceDate { get; set; }
        public decimal InvoiceAmount { get; set; }
        public decimal PreviouslyPaid { get; set; }
        public decimal PaymentAmount { get; set; }
        public decimal RemainingAmount { get; set; }
    }
}
