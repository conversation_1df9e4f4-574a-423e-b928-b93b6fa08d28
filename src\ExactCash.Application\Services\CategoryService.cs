﻿using AutoMapper;
using ExactCash.Application.Contracts;
using ExactCash.Application.DTOs;
using ExactCash.Domain.Entities;
using ExactCash.Infrastructure.Persistence;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
#nullable disable

namespace ExactCash.Application.Services
{
    public class CategoryService : ICategoryService
    {
        private readonly AppPostgreSQLDbContext _context;
        private readonly ILogger<CategoryService> _logger;
        private readonly IMapper _mapper;
        public CategoryService(AppPostgreSQLDbContext context, ILogger<CategoryService> logger,IMapper mapper)
        {
            _context = context;
            _logger = logger;
            _mapper = mapper;
        }

        // Create a new category
        public async Task<Category> CreateAsync(Category category)
        {
            try
            {
                _logger.LogInformation("Attempting to create category with name: {CategoryName}", category.Name);

                // Check if category already exists
                var existingCategory = await _context.Categories
                    .FirstOrDefaultAsync(c => c.Name == category.Name);
                if (existingCategory != null)
                {
                    _logger.LogWarning("Category with name '{CategoryName}' already exists.", category.Name);
                    return new Category();
                }

                _context.Categories.Add(category);
                await _context.SaveChangesAsync();

                _logger.LogInformation("Category created successfully with ID: {CategoryId}", category.Id);
                return category;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "An error occurred while creating the category with name: {CategoryName}", category.Name);
                return new Category();
            }
        }

        // Get a category by ID
        public async Task<Category> GetByIdAsync(int id)
        {
            try
            {
                _logger.LogInformation("Fetching category with ID: {CategoryId}", id);
                var category = await _context.Categories.FindAsync(id);

                if (category == null)
                {
                    _logger.LogWarning("Category with ID {CategoryId} not found.", id);
                    return null;
                }

                return category;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "An error occurred while fetching the category with ID: {CategoryId}", id);
                return null;
            }
        }

        // Get all categories
        public async Task<IEnumerable<CategoryDto>> GetAllAsync()
        {
            try
            {
                _logger.LogInformation("Fetching all categories from the database.");
                var categories = await _context.Categories.ToListAsync();
                return _mapper.Map<IEnumerable<CategoryDto>>(categories);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "An error occurred while fetching all categories.");
                return null;
            }
        }

        // Update an existing category
        public async Task<Category> UpdateAsync(Category category)
        {
            try
            {
                _logger.LogInformation("Attempting to update category with ID: {CategoryId}", category.Id);
                var existingCategory = await _context.Categories.FindAsync(category.Id);

                if (existingCategory == null)
                {
                    _logger.LogWarning("Category with ID {CategoryId} not found for update.", category.Id);
                    return null;
                }

                _context.Categories.Update(category);
                await _context.SaveChangesAsync();

                _logger.LogInformation("Category with ID {CategoryId} updated successfully.", category.Id);
                return category;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "An error occurred while updating the category with ID: {CategoryId}", category.Id);
                return null;
            }
        }

        // Delete a category by ID
        public async Task<bool> DeleteAsync(int id)
        {
            try
            {
                _logger.LogInformation("Attempting to delete category with ID: {CategoryId}", id);
                var category = await _context.Categories.FindAsync(id);

                if (category == null)
                {
                    _logger.LogWarning("Category with ID {CategoryId} not found for deletion.", id);
                    return false;
                }

                _context.Categories.Remove(category);
                await _context.SaveChangesAsync();

                _logger.LogInformation("Category with ID {CategoryId} deleted successfully.", id);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "An error occurred while deleting the category with ID: {CategoryId}", id);
                return false;
            }
        }
    }
}
