<Project Sdk="Microsoft.NET.Sdk.Web">

	<PropertyGroup>
		<TargetFramework>net8.0</TargetFramework>
		<Nullable>enable</Nullable>
		<ImplicitUsings>enable</ImplicitUsings>
	</PropertyGroup>

	<ItemGroup>
	  <Compile Remove="Controllers\SystemConfigurationController.cs" />
	</ItemGroup>

	<ItemGroup>
		<PackageReference Include="ClosedXML" Version="0.102.2" />
		<PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="8.0.10">
			<PrivateAssets>all</PrivateAssets>
			<IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
		</PackageReference>
		<PackageReference Include="Swashbuckle.AspNetCore" Version="6.6.2" />
		<PackageReference Include="System.Drawing.Common" Version="9.0.4" />
	</ItemGroup>

	<ItemGroup>
	  <ProjectReference Include="..\ExactCash.Application\ExactCash.Application.csproj" />
	  <ProjectReference Include="..\ExactCash.Domain\ExactCash.Domain.csproj" />
	  <ProjectReference Include="..\ExactCash.Infrastructure\ExactCash.Infrastructure.csproj" />
	</ItemGroup>

</Project>
