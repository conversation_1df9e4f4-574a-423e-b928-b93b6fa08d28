﻿#nullable disable

namespace ExactCash.Application.DTOs
{
    public class PurchaseOrderReportDto
    {
        public int PurchaseId { get; set; }
        public string PONumber { get; set; }
        public string SupplierName { get; set; }
        public DateTime PurchaseDate { get; set; }
        public decimal TotalAmount { get; set; }
        public decimal AmountPaid { get; set; }
        public decimal RemainingAmount { get; set; }
        public string PaymentStatus { get; set; }
        public int ItemCount { get; set; }
        public string CreatedBy { get; set; }
        public DateTime ReportStartDate { get; set; }
        public DateTime ReportEndDate { get; set; }

        // Formatted properties for display
        public string FormattedTotalAmount => $"{TotalAmount:N2} ج.م";
        public string FormattedAmountPaid => $"{AmountPaid:N2} ج.م";
        public string FormattedRemainingAmount => $"{RemainingAmount:N2} ج.م";

        public PurchaseOrderReportDto()
        {

        }
    }
}
