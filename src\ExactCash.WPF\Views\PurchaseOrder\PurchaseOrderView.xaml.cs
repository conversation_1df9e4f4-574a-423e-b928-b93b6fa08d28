using AutoMapper;
using ExactCash.Application.DTOs;
using ExactCash.WPF.Services;
using ExactCash.WPF.Services.Product;
using ExactCash.WPF.ViewModels.Common;
using ExactCash.WPF.ViewModels.PurchaseOrder;
using System.Windows;
using System.Windows.Controls;

namespace ExactCash.WPF.Views.PurchaseOrder
{
    public partial class PurchaseOrderView : Window
    {
        public PurchaseOrderView(IPurchaseServiceClient purchaseOrderService,
            IProductService productService,
            ISupplierServiceClient supplierService,
            IUnitServiceClient unitService,
            IMapper mapper,
            NotificationViewModel notificationViewModel)
        {
            InitializeComponent();
            var viewModel = new PurchaseOrderViewModel(purchaseOrderService,
                productService,
                supplierService,
                unitService,
                mapper,
                notificationViewModel,
                new LoadingViewModel());
            viewModel.CloseWindowAction = () => this.Dispatcher.Invoke(Close);
            DataContext = viewModel;
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            Close();
        }

        private void MinimizeButton_Click(object sender, RoutedEventArgs e)
        {
            WindowState = WindowState.Minimized;
        }

        private void ProductSearchBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (DataContext is PurchaseOrderViewModel viewModel)
            {
                viewModel.SelectedProduct = ProductSearchBox.SelectedItem as ProductDto;
            }
        }

        private void SupplierSearchBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (DataContext is PurchaseOrderViewModel viewModel)
            {
                viewModel.SelectedSupplier = SupplierSearchBox.SelectedItem as SupplierDto;
            }
        }

        private void SupplierSearchBox_DropDownOpened(object sender, EventArgs e)
        {
            if (DataContext is PurchaseOrderViewModel viewModel)
            {
                viewModel.LoadSuppliersOnDropdownOpen();
            }
        }

        private void SupplierTextBox_GotFocus(object sender, RoutedEventArgs e)
        {
            if (DataContext is PurchaseOrderViewModel viewModel)
            {
                viewModel.LoadSuppliersOnDropdownOpen();
            }
        }

        private void ProductSearchBox_DropDownOpened(object sender, EventArgs e)
        {
            if (DataContext is PurchaseOrderViewModel viewModel)
            {
                viewModel.LoadProductsOnDropdownOpen();
            }
        }

        private void ProductTextBox_GotFocus(object sender, RoutedEventArgs e)
        {
            if (DataContext is PurchaseOrderViewModel viewModel)
            {
                viewModel.LoadProductsOnDropdownOpen();
            }
        }
    }
}