﻿using ExactCash.Application.Interfaces;
using ExactCash.Domain.Entities;
using ExactCash.Infrastructure.Persistence;
using Microsoft.EntityFrameworkCore;

namespace ExactCash.API.Seeds
{
    public static class DefaultBrands
    {
        public static async Task SeedAsync(IBrandService _brandseService, AppPostgreSQLDbContext _db, ILogger logger)
        {
            try
            {
                if (await _db.Brands.AnyAsync())
                    return;

                var brands = new List<Brand>
                {
                   new Brand
                   {
                       Name = "Nestlé",
                       Description = "Nestlé is a Swiss multinational food and drink processing conglomerate corporation.",
                       CreationDate = DateTime.UtcNow
                   },
                   new Brand
                   {
                       Name = "PepsiCo",
                       Description = "PepsiCo is an American multinational food, snack, and beverage corporation.",
                       CreationDate = DateTime.UtcNow
                   },
                   new Brand
                   {
                       Name = "Unilever",
                       Description = "Unilever is a British-Dutch multinational consumer goods company.",
                       CreationDate = DateTime.UtcNow
                   },
                   new Brand
                   {
                       Name = "Procter & Gamble",
                       Description = "Procter & Gamble is an American multinational consumer goods corporation.",
                       CreationDate = DateTime.UtcNow
                   },
                   new Brand
                   {
                       Name = "Coca-Cola",
                       Description = "The Coca-Cola Company is an American multinational beverage corporation.",
                       CreationDate = DateTime.UtcNow
                   },
                   new Brand
                   {
                       Name = "Kellogg's",
                       Description = "Kellogg's is an American multinational food manufacturing company.",
                       CreationDate = DateTime.UtcNow
                   },
                   new Brand
                   {
                       Name = "General Mills",
                       Description = "General Mills is an American multinational manufacturer and marketer of branded consumer foods.",
                       CreationDate = DateTime.UtcNow
                   },
                   new Brand
                   {
                       Name = "Mars",
                       Description = "Mars is an American multinational manufacturer of confectionery, pet food, and other food products.",
                       CreationDate = DateTime.UtcNow
                   },
                   new Brand
                   {
                       Name = "Danone",
                       Description = "Danone is a French multinational food-products corporation.",
                       CreationDate = DateTime.UtcNow
                   },
                   new Brand
                   {
                       Name = "Kraft Heinz",
                       Description = "The Kraft Heinz Company is an American food company formed by the merger of Kraft Foods and Heinz.",
                       CreationDate = DateTime.UtcNow
                   }
                };

                await _db.Brands.AddRangeAsync(brands);
                await _db.SaveChangesAsync();
            }
            catch (Exception ex)
            {
                logger.LogError($"An error occurred while sedding brands {ex?.InnerException?.Message}");
            }
        }
    }
}
