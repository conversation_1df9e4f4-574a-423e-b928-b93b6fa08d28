<Window x:Class="ExactCash.WPF.Views.CreditSales.CreditLimitUpdateView"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        mc:Ignorable="d"
        Title="تحديث حد الائتمان"
        Height="500"
        Width="600"
        FlowDirection="RightToLeft"
        FontFamily="Segoe UI"
        WindowStartupLocation="CenterScreen"
        ResizeMode="NoResize">

    <Window.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="../../Resources/Styles.xaml"/>
            </ResourceDictionary.MergedDictionaries>

            <Style x:Key="ModernBorder"
                   TargetType="Border">
                <Setter Property="Background"
                        Value="White"/>
                <Setter Property="BorderBrush"
                        Value="#DEE2E6"/>
                <Setter Property="BorderThickness"
                        Value="1"/>
                <Setter Property="CornerRadius"
                        Value="6"/>
                <Setter Property="Padding"
                        Value="15"/>
            </Style>

            <Style x:Key="ModernButton"
                   TargetType="Button">
                <Setter Property="Height"
                        Value="40"/>
                <Setter Property="Padding"
                        Value="20,5"/>
                <Setter Property="Background"
                        Value="#0078D4"/>
                <Setter Property="Foreground"
                        Value="White"/>
                <Setter Property="BorderThickness"
                        Value="0"/>
                <Setter Property="Cursor"
                        Value="Hand"/>
                <Setter Property="FontSize"
                        Value="14"/>
                <Setter Property="FontWeight"
                        Value="SemiBold"/>
            </Style>

            <Style x:Key="ModernTextBox"
                   TargetType="TextBox">
                <Setter Property="Height"
                        Value="40"/>
                <Setter Property="Padding"
                        Value="10,5"/>
                <Setter Property="BorderThickness"
                        Value="1"/>
                <Setter Property="BorderBrush"
                        Value="#CCCCCC"/>
                <Setter Property="Background"
                        Value="White"/>
                <Setter Property="FontSize"
                        Value="14"/>
                <Setter Property="TextAlignment"
                        Value="Right"/>
            </Style>

            <Style x:Key="CustomerSearchBox"
                   TargetType="TextBox"
                   BasedOn="{StaticResource ModernTextBox}">
                <Setter Property="Background"
                        Value="#F8F9FA"/>
            </Style>
        </ResourceDictionary>
    </Window.Resources>

    <Grid Background="#F8F9FA">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0"
                Style="{StaticResource ModernBorder}"
                Margin="20,20,20,10">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0"
                            Orientation="Horizontal">
                    <TextBlock Text="💳"
                               FontSize="24"
                               VerticalAlignment="Center"
                               Margin="0,0,10,0"/>
                    <TextBlock Text="تحديث حد الائتمان"
                               FontSize="20"
                               FontWeight="Bold"
                               VerticalAlignment="Center"/>
                </StackPanel>
            </Grid>
        </Border>

        <!-- Main Content -->
        <Border Grid.Row="1"
                Style="{StaticResource ModernBorder}"
                Margin="20,10,20,10">
            <ScrollViewer VerticalScrollBarVisibility="Auto">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <!-- Customer Selection -->
                    <GroupBox Grid.Row="0"
                              Header="اختيار العميل"
                              Margin="0,0,0,20">
                        <Grid Margin="10">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>

                            <TextBlock Text="البحث عن عميل"
                                       VerticalAlignment="Center"
                                       Margin="0,0,10,10"/>
                            <TextBox Grid.Column="1"
                                     Text="{Binding CustomerSearchTerm, UpdateSourceTrigger=PropertyChanged}"
                                     Style="{StaticResource CustomerSearchBox}"
                                     Margin="0,0,10,10"
                                     ToolTip="ابحث بالاسم أو رقم الهاتف"/>
                            <Button Grid.Column="2"
                                    Content="بحث"
                                    Command="{Binding SearchCustomerCommand}"
                                    Style="{StaticResource ModernButton}"
                                    Width="80"
                                    Margin="0,0,0,10"/>

                            <!-- Customer Search Results -->
                            <ListBox Grid.Row="1"
                                     Grid.ColumnSpan="3"
                                     ItemsSource="{Binding CustomerSearchResults}"
                                     SelectedItem="{Binding SelectedCustomer}"
                                     MaxHeight="120"
                                     Visibility="{Binding HasSearchResults, Converter={StaticResource BooleanToVisibilityConverter}}"
                                     Background="White"
                                     BorderBrush="#CCCCCC"
                                     BorderThickness="1">
                                <ListBox.ItemTemplate>
                                    <DataTemplate>
                                        <Grid Margin="5">
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="*"/>
                                                <ColumnDefinition Width="Auto"/>
                                            </Grid.ColumnDefinitions>
                                            <TextBlock Text="{Binding FullName}"
                                                       FontWeight="Bold"/>
                                            <TextBlock Grid.Column="1"
                                                       Text="{Binding Phone}"
                                                       Foreground="Gray"/>
                                        </Grid>
                                    </DataTemplate>
                                </ListBox.ItemTemplate>
                            </ListBox>
                        </Grid>
                    </GroupBox>

                    <!-- Current Customer Information -->
                    <GroupBox Grid.Row="1"
                              Header="معلومات العميل الحالية"
                              Margin="0,0,0,20"
                              Visibility="{Binding HasSelectedCustomer, Converter={StaticResource BooleanToVisibilityConverter}}">
                        <Grid Margin="10">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>

                            <TextBlock Text="اسم العميل"
                                       VerticalAlignment="Center"
                                       Margin="0,0,10,10"/>
                            <TextBox Grid.Column="1"
                                     Text="{Binding CustomerName, Mode=OneWay}"
                                     Style="{StaticResource ModernTextBox}"
                                     IsReadOnly="True"
                                     Background="#F8F9FA"
                                     Margin="0,0,10,10"/>

                            <TextBlock Text="رقم الهاتف"
                                       Grid.Column="2"
                                       VerticalAlignment="Center"
                                       Margin="0,0,10,10"/>
                            <TextBox Grid.Column="3"
                                     Text="{Binding CustomerPhone, Mode=OneWay}"
                                     Style="{StaticResource ModernTextBox}"
                                     IsReadOnly="True"
                                     Background="#F8F9FA"
                                     Margin="0,0,0,10"/>

                            <TextBlock Text="حد الائتمان الحالي"
                                       Grid.Row="1"
                                       VerticalAlignment="Center"
                                       Margin="0,0,10,0"/>
                            <TextBox Grid.Row="1"
                                     Grid.Column="1"
                                     Text="{Binding CurrentCreditLimit, StringFormat='{}{0:N2} ج.م', Mode=OneWay}"
                                     Style="{StaticResource ModernTextBox}"
                                     IsReadOnly="True"
                                     Background="#F8F9FA"
                                     Margin="0,0,10,0"/>

                            <TextBlock Text="الرصيد المستحق"
                                       Grid.Row="1"
                                       Grid.Column="2"
                                       VerticalAlignment="Center"
                                       Margin="0,0,10,0"/>
                            <TextBox Grid.Row="1"
                                     Grid.Column="3"
                                     Text="{Binding OutstandingBalance, StringFormat='{}{0:N2} ج.م', Mode=OneWay}"
                                     Style="{StaticResource ModernTextBox}"
                                     IsReadOnly="True"
                                     Background="#FFF3CD"
                                     Foreground="#856404"/>
                        </Grid>
                    </GroupBox>

                    <!-- Credit Limit Update -->
                    <GroupBox Grid.Row="2"
                              Header="تحديث حد الائتمان"
                              Margin="0,0,0,20"
                              Visibility="{Binding HasSelectedCustomer, Converter={StaticResource BooleanToVisibilityConverter}}">
                        <Grid Margin="10">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>

                            <TextBlock Text="حد الائتمان الجديد"
                                       VerticalAlignment="Center"
                                       Margin="0,0,10,10"/>
                            <TextBox Grid.Column="1"
                                     Text="{Binding NewCreditLimit, UpdateSourceTrigger=PropertyChanged, StringFormat=N2}"
                                     Style="{StaticResource ModernTextBox}"
                                     Margin="0,0,10,10"/>

                            <TextBlock Text="تاريخ التحديث"
                                       Grid.Column="2"
                                       VerticalAlignment="Center"
                                       Margin="0,0,10,10"/>
                            <DatePicker Grid.Column="3"
                                        SelectedDate="{Binding UpdateDate}"
                                        Style="{StaticResource ModernTextBox}"
                                        Margin="0,0,0,10"/>

                            <TextBlock Text="سبب التحديث"
                                       Grid.Row="1"
                                       VerticalAlignment="Top"
                                       Margin="0,5,10,0"/>
                            <TextBox Grid.Row="1"
                                     Grid.Column="1"
                                     Grid.ColumnSpan="3"
                                     Text="{Binding Reason}"
                                     Style="{StaticResource ModernTextBox}"
                                     Height="60"
                                     TextWrapping="Wrap"
                                     AcceptsReturn="True"
                                     VerticalScrollBarVisibility="Auto"/>
                        </Grid>
                    </GroupBox>

                    <!-- Update Summary -->
                    <GroupBox Grid.Row="3"
                              Header="ملخص التحديث"
                              Visibility="{Binding HasSelectedCustomer, Converter={StaticResource BooleanToVisibilityConverter}}">
                        <Grid Margin="10">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <StackPanel Grid.Column="0"
                                        HorizontalAlignment="Center">
                                <TextBlock Text="التغيير في الحد"
                                           FontWeight="Bold"
                                           HorizontalAlignment="Center"/>
                                <TextBlock Text="{Binding CreditLimitChange, StringFormat='{}{0:N2} ج.م'}"
                                           FontSize="18"
                                           FontWeight="Bold"
                                           Foreground="{Binding ChangeColor}"
                                           HorizontalAlignment="Center"/>
                            </StackPanel>

                            <StackPanel Grid.Column="1"
                                        HorizontalAlignment="Center">
                                <TextBlock Text="الائتمان المتاح الجديد"
                                           FontWeight="Bold"
                                           HorizontalAlignment="Center"/>
                                <TextBlock Text="{Binding NewAvailableCredit, StringFormat='{}{0:N2} ج.م'}"
                                           FontSize="18"
                                           FontWeight="Bold"
                                           Foreground="#28A745"
                                           HorizontalAlignment="Center"/>
                            </StackPanel>

                            <StackPanel Grid.Column="2"
                                        HorizontalAlignment="Center">
                                <TextBlock Text="حالة التحديث"
                                           FontWeight="Bold"
                                           HorizontalAlignment="Center"/>
                                <TextBlock Text="{Binding ValidationStatus}"
                                           FontSize="18"
                                           FontWeight="Bold"
                                           Foreground="{Binding ValidationStatusColor}"
                                           HorizontalAlignment="Center"/>
                            </StackPanel>
                        </Grid>
                    </GroupBox>
                </Grid>
            </ScrollViewer>
        </Border>

        <!-- Footer Buttons -->
        <Border Grid.Row="2"
                Style="{StaticResource ModernBorder}"
                Margin="20,10,20,20">
            <StackPanel Orientation="Horizontal"
                        HorizontalAlignment="Center">
                <Button Content="تحديث حد الائتمان"
                        Command="{Binding UpdateCreditLimitCommand}"
                        Style="{StaticResource ModernButton}"
                        Background="#28A745"
                        Margin="0,0,10,0"
                        Width="150"/>
                <Button Content="إلغاء"
                        Command="{Binding CancelCommand}"
                        Style="{StaticResource ModernButton}"
                        Background="#6C757D"
                        Width="120"/>
            </StackPanel>
        </Border>
    </Grid>
</Window>
