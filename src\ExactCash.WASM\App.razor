<Router AppAssembly="@typeof(App).Assembly">
    <Found Context="routeData">
        <AuthorizeRouteView RouteData="@routeData" DefaultLayout="@typeof(MainLayout)">
            <NotAuthorized>
                @if (context.User.Identity?.IsAuthenticated != true)
                {
                    <RedirectToLogin />
                }
                else
                {
                    <div class="alert alert-danger" role="alert">
                        <h4 class="alert-heading">غير مصرح</h4>
                        <p>ليس لديك صلاحية للوصول إلى هذه الصفحة.</p>
                    </div>
                }
            </NotAuthorized>
            <Authorizing>
                <div class="d-flex justify-content-center align-items-center" style="height: 100vh;">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">جاري التحقق من الصلاحيات...</span>
                    </div>
                </div>
            </Authorizing>
        </AuthorizeRouteView>
    </Found>
    <NotFound>
        <PageTitle>غير موجود</PageTitle>
        <LayoutView Layout="@typeof(MainLayout)">
            <div class="alert alert-warning" role="alert">
                <h4 class="alert-heading">الصفحة غير موجودة</h4>
                <p>عذراً، الصفحة التي تبحث عنها غير موجودة.</p>
                <hr>
                <a href="/" class="btn btn-primary">العودة للرئيسية</a>
            </div>
        </LayoutView>
    </NotFound>
</Router>



@code {

}
