<!DOCTYPE html>
<html lang="ar" dir="rtl">

<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>ExactCash - نظام نقاط البيع</title>
    <base href="/" />
    <link href="css/bootstrap/bootstrap.min.css" rel="stylesheet" />
    <link href="_content/MudBlazor/MudBlazor.min.css" rel="stylesheet" />
    <link href="_content/Blazored.Modal/blazored-modal.css" rel="stylesheet" />
    <link href="_content/Blazored.Toast/blazored-toast.min.css" rel="stylesheet" />
    <link href="css/app.css" rel="stylesheet" />
    <link href="css/rtl.css" rel="stylesheet" />
    <link rel="icon" type="image/png" href="favicon.png" />
    <link href="ExactCash.WASM.styles.css" rel="stylesheet" />
    <link href="manifest.json" rel="manifest" />
    <link rel="apple-touch-icon" sizes="512x512" href="icon-512.png" />
    <link rel="apple-touch-icon" sizes="192x192" href="icon-192.png" />
</head>

<body>
    <div id="app">
        <div class="loading-container">
            <div class="loading-spinner"></div>
            <div class="loading-text">جاري تحميل ExactCash...</div>
        </div>
    </div>

    <div id="blazor-error-ui">
        حدث خطأ غير متوقع. يرجى إعادة تحميل الصفحة.
        <a href="" class="reload">إعادة تحميل</a>
        <a class="dismiss">🗙</a>
    </div>

    <script src="_framework/blazor.webassembly.js"></script>
    <script src="_content/MudBlazor/MudBlazor.min.js"></script>
    <script src="_content/Blazored.Modal/blazored.modal.js"></script>
    <script src="js/app.js"></script>
    <script>navigator.serviceWorker.register('service-worker.js');</script>
</body>

</html>

<Router AppAssembly="@typeof(App).Assembly">
    <Found Context="routeData">
        <AuthorizeRouteView RouteData="@routeData" DefaultLayout="@typeof(MainLayout)">
            <NotAuthorized>
                @if (context.User.Identity?.IsAuthenticated != true)
                {
                    <RedirectToLogin />
                }
                else
                {
                    <div class="alert alert-danger" role="alert">
                        <h4 class="alert-heading">غير مصرح</h4>
                        <p>ليس لديك صلاحية للوصول إلى هذه الصفحة.</p>
                    </div>
                }
            </NotAuthorized>
            <Authorizing>
                <div class="d-flex justify-content-center align-items-center" style="height: 100vh;">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">جاري التحقق من الصلاحيات...</span>
                    </div>
                </div>
            </Authorizing>
        </AuthorizeRouteView>
    </Found>
    <NotFound>
        <PageTitle>غير موجود</PageTitle>
        <LayoutView Layout="@typeof(MainLayout)">
            <div class="alert alert-warning" role="alert">
                <h4 class="alert-heading">الصفحة غير موجودة</h4>
                <p>عذراً، الصفحة التي تبحث عنها غير موجودة.</p>
                <hr>
                <a href="/" class="btn btn-primary">العودة للرئيسية</a>
            </div>
        </LayoutView>
    </NotFound>
</Router>

<BlazoredModal />
<BlazoredToasts Position="ToastPosition.TopLeft" 
                Timeout="5" 
                IconType="IconType.FontAwesome" 
                SuccessClass="success-toast" 
                SuccessIcon="fas fa-check-circle" 
                ErrorClass="error-toast" 
                ErrorIcon="fas fa-times-circle" />

@code {
    
}
