﻿@using Microsoft.AspNetCore.Components

<BlazorBootstrap.Modal @ref="ModalRef" Title="@Title">
    <BodyTemplate>
        <div class="delete-modal-body">
            <div class="alert alert-warning">
                <i class="bi bi-exclamation-triangle-fill me-2"></i>
                @Message <strong>@Highlight</strong>?
            </div>
        </div>
    </BodyTemplate>
    <FooterTemplate>
        <div class="d-flex justify-content-end gap-2">
            <BlazorBootstrap.Button Color="BlazorBootstrap.ButtonColor.Secondary" @onclick="OnCancel">
                Cancel
            </BlazorBootstrap.Button>
            <BlazorBootstrap.Button Color="BlazorBootstrap.ButtonColor.Danger" @onclick="OnConfirm">
                @ConfirmButtonText
            </BlazorBootstrap.Button>
        </div>
    </FooterTemplate>
</BlazorBootstrap.Modal>

@code {
    private BlazorBootstrap.Modal ModalRef = new BlazorBootstrap.Modal();

    [Parameter] public string Title { get; set; } = "Confirm";
    [Parameter] public string Message { get; set; } = string.Empty;
    [Parameter] public string Highlight { get; set; } = string.Empty;
    [Parameter] public string Icon { get; set; } = "fas fa-exclamation-triangle";
    [Parameter] public string ConfirmButtonText { get; set; } = "Delete";
    [Parameter] public EventCallback OnConfirmed { get; set; }
    [Parameter] public EventCallback OnCancelled { get; set; }

    public async Task ShowAsync() => await ModalRef.ShowAsync();
    public async Task HideAsync() => await ModalRef.HideAsync();

    private async Task OnConfirm()
    {
        await HideAsync();
        if (OnConfirmed.HasDelegate)
            await OnConfirmed.InvokeAsync();
    }

    private async Task OnCancel()
    {
        await HideAsync();
        if (OnCancelled.HasDelegate)
            await OnCancelled.InvokeAsync();
    }
}