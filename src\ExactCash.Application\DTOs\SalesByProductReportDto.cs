﻿#nullable disable

namespace ExactCash.Application.DTOs
{
    public class SalesByProductReportDto
    {
        public int ProductId { get; set; }
        public string ProductName { get; set; }
        public string ProductSKU { get; set; }
        public string CategoryName { get; set; }
        public string BrandName { get; set; }
        public decimal QuantitySold { get; set; }
        public decimal TotalSales { get; set; }
        public decimal AveragePrice { get; set; }
        public int TransactionCount { get; set; }
        public DateTime ReportStartDate { get; set; }
        public DateTime ReportEndDate { get; set; }

        // Formatted properties for display
        public string FormattedQuantitySold => $"{QuantitySold:N2}";
        public string FormattedTotalSales => $"{TotalSales:N2} ج.م";
        public string FormattedAveragePrice => $"{AveragePrice:N2} ج.م";

        public SalesByProductReportDto()
        {

        }
    }
}
