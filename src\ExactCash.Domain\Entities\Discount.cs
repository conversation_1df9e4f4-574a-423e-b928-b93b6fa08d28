﻿#nullable disable

using ExactCash.Domain.Common;

namespace ExactCash.Domain.Entities
{
    public class Discount : BaseEntity
    {
        /// <summary>
        /// The name of the discount (e.g., "Spring Sale", "Black Friday").
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// The discount amount. If IsPercentage is true, this value is treated as a percentage.
        /// Otherwise, it is a fixed amount.
        /// </summary>
        public decimal Amount { get; set; }

        /// <summary>
        /// Flag indicating whether the discount is a percentage (true) or a fixed amount (false).
        /// </summary>
        public bool IsPercentage { get; set; }

        /// <summary>
        /// Foreign key for the product that this discount applies to. Nullable if the discount applies to categories or order total.
        /// </summary>
        public int? ProductId { get; set; }

        /// <summary>
        /// Navigation property to the Product entity, representing the product the discount applies to.
        /// </summary>
        public Product Product { get; set; }

        /// <summary>
        /// Foreign key for the product category that this discount applies to. Nullable if the discount applies to a single product or the total order.
        /// </summary>
        public int? CategoryId { get; set; }

        /// <summary>
        /// Navigation property to the Category entity, representing the category the discount applies to.
        /// </summary>
        public Category Category { get; set; }

        /// <summary>
        /// Flag indicating whether the discount is currently active.
        /// </summary>
        public bool IsActive { get; set; }

        /// <summary>
        /// The date and time when the discount becomes active. Can be null if no start date is specified.
        /// </summary>
        public DateTime? StartDate { get; set; }

        /// <summary>
        /// The date and time when the discount expires. Can be null if no end date is specified.
        /// </summary>
        public DateTime? EndDate { get; set; }

        /// <summary>
        /// Flag indicating whether the discount applies to the total order value.
        /// </summary>
        public bool ApplyToTotal { get; set; }
    }
}
