using ExactCash.Application.DTOs;
#nullable disable

namespace ExactCash.Application.Interfaces
{
    public interface IAuthService
    {
        Task<AuthResponseDto> LoginAsync(LoginDto loginDto);
        Task<AuthResponseDto> RegisterAsync(RegisterDto registerDto);
        Task<AuthResponseDto> RefreshTokenAsync(RefreshTokenDto refreshTokenDto);
        Task LogoutAsync(string userId);
        Task<bool> ValidateTokenAsync(string token);
    }
}