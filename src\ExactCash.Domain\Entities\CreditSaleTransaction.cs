using ExactCash.Domain.Common;
using System.Text.Json.Serialization;

namespace ExactCash.Domain.Entities
{
    /// <summary>
    /// Represents a credit sale transaction for tracking outstanding payments.
    /// </summary>
    public class CreditSaleTransaction : BaseEntity
    {
        /// <summary>
        /// Foreign key to the original sale.
        /// </summary>
        public int SaleId { get; set; }

        /// <summary>
        /// Navigation property to the original sale.
        /// </summary>
        [JsonIgnore]
        public Sale Sale { get; set; }

        /// <summary>
        /// Foreign key to the customer.
        /// </summary>
        public int CustomerId { get; set; }

        /// <summary>
        /// Navigation property to the customer.
        /// </summary>
        [JsonIgnore]
        public Customer Customer { get; set; }

        /// <summary>
        /// The original credit amount (total amount that was on credit).
        /// </summary>
        public decimal OriginalCreditAmount { get; set; }

        /// <summary>
        /// The remaining amount to be paid.
        /// </summary>
        public decimal RemainingAmount { get; set; }

        /// <summary>
        /// The total amount paid so far.
        /// </summary>
        public decimal PaidAmount { get; set; }

        /// <summary>
        /// The due date for this credit payment.
        /// </summary>
        public DateTime? DueDate { get; set; }

        /// <summary>
        /// Status of the credit transaction (Outstanding, PartiallyPaid, FullyPaid, Overdue).
        /// </summary>
        public string Status { get; set; }

        /// <summary>
        /// Number of days overdue (calculated field).
        /// </summary>
        public int DaysOverdue => DueDate.HasValue && DueDate < DateTime.UtcNow
            ? (DateTime.UtcNow - DueDate.Value).Days
            : 0;

        /// <summary>
        /// Notes or comments about this credit transaction.
        /// </summary>
        public string? Notes { get; set; }

        /// <summary>
        /// Navigation property for payments made against this credit transaction.
        /// </summary>
        [JsonIgnore]
        public ICollection<CreditPayment> CreditPayments { get; set; } = new List<CreditPayment>();

        #region Constructors
        public CreditSaleTransaction()
        {
            Status = "Outstanding";
        }

        public CreditSaleTransaction(int saleId, int customerId, decimal originalCreditAmount, DateTime? dueDate = null)
        {
            SaleId = saleId;
            CustomerId = customerId;
            OriginalCreditAmount = originalCreditAmount;
            RemainingAmount = originalCreditAmount;
            PaidAmount = 0;
            DueDate = dueDate;
            Status = "Outstanding";
            CreationDate = DateTime.UtcNow;
        }
        #endregion

        #region Methods
        /// <summary>
        /// Records a payment against this credit transaction.
        /// </summary>
        /// <param name="amount">The payment amount</param>
        /// <param name="paymentMethod">The payment method used</param>
        /// <param name="notes">Optional notes about the payment</param>
        public void RecordPayment(decimal amount, string paymentMethod, string notes = null)
        {
            if (amount <= 0 || amount > RemainingAmount)
                throw new ArgumentException("Invalid payment amount");

            PaidAmount += amount;
            RemainingAmount -= amount;

            UpdateStatus();
            LastUpdatedDate = DateTime.UtcNow;

            // Add to credit payments collection
            CreditPayments.Add(new CreditPayment(Id, amount, paymentMethod, notes));
        }

        /// <summary>
        /// Updates the status based on current payment state.
        /// </summary>
        private void UpdateStatus()
        {
            if (RemainingAmount <= 0)
            {
                Status = "FullyPaid";
            }
            else if (PaidAmount > 0)
            {
                Status = "PartiallyPaid";
            }
            else if (DueDate.HasValue && DueDate < DateTime.UtcNow)
            {
                Status = "Overdue";
            }
            else
            {
                Status = "Outstanding";
            }
        }

        /// <summary>
        /// Checks if this credit transaction is overdue.
        /// </summary>
        /// <returns>True if overdue, false otherwise</returns>
        public bool IsOverdue()
        {
            return DueDate.HasValue && DueDate < DateTime.UtcNow && RemainingAmount > 0;
        }

        /// <summary>
        /// Gets the payment completion percentage.
        /// </summary>
        /// <returns>Percentage of payment completed (0-100)</returns>
        public decimal GetPaymentPercentage()
        {
            if (OriginalCreditAmount == 0) return 100;
            return (PaidAmount / OriginalCreditAmount) * 100;
        }
        #endregion
    }
}
