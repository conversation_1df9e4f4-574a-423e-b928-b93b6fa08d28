using ExactCash.Application.DTOs;
using ExactCash.WPF.Services;
using ExactCash.WPF.ViewModels.Common;
using ExactCash.WPF.Models;
using Microsoft.Extensions.DependencyInjection;
using System;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using System.Threading.Tasks;
using System.Windows.Input;

namespace ExactCash.WPF.ViewModels.Inventory
{
    public class InventoryStatsViewModel : INotifyPropertyChanged
    {
        #region Private Fields
        private readonly IReportServiceClient _reportServiceClient;
        private InventorySummaryDto _inventorySummary;
        private ObservableCollection<InventoryReportDto> _inventoryItems;
        private ObservableCollection<LowStockReportDto> _lowStockItems;
        private bool _isLoading;
        private string _searchText;
        private string _selectedStockFilter;
        #endregion

        #region Public Properties

        /// <summary>
        /// Inventory summary statistics
        /// </summary>
        public InventorySummaryDto InventorySummary
        {
            get => _inventorySummary;
            set
            {
                _inventorySummary = value;
                OnPropertyChanged();
            }
        }

        /// <summary>
        /// Collection of all inventory items
        /// </summary>
        public ObservableCollection<InventoryReportDto> InventoryItems
        {
            get => _inventoryItems;
            set
            {
                _inventoryItems = value;
                OnPropertyChanged();
            }
        }

        /// <summary>
        /// Collection of low stock items
        /// </summary>
        public ObservableCollection<LowStockReportDto> LowStockItems
        {
            get => _lowStockItems;
            set
            {
                _lowStockItems = value;
                OnPropertyChanged();
            }
        }

        /// <summary>
        /// Indicates if data is being loaded
        /// </summary>
        public bool IsLoading
        {
            get => _isLoading;
            set
            {
                _isLoading = value;
                OnPropertyChanged();
            }
        }

        /// <summary>
        /// Search text for filtering inventory items
        /// </summary>
        public string SearchText
        {
            get => _searchText;
            set
            {
                _searchText = value;
                OnPropertyChanged();
                FilterInventoryItems();
            }
        }

        /// <summary>
        /// Selected stock status filter
        /// </summary>
        public string SelectedStockFilter
        {
            get => _selectedStockFilter;
            set
            {
                _selectedStockFilter = value;
                OnPropertyChanged();
                FilterInventoryItems();
            }
        }

        /// <summary>
        /// Available stock filter options
        /// </summary>
        public ObservableCollection<string> StockFilterOptions { get; }

        #endregion

        #region Commands
        public ICommand LoadDataCommand { get; }
        public ICommand RefreshCommand { get; }
        public ICommand ExportCommand { get; }
        public ICommand ViewProductDetailsCommand { get; }
        #endregion

        #region Constructor
        public InventoryStatsViewModel()
        {
            // Get services from DI container
            var app = (App)System.Windows.Application.Current;
            _reportServiceClient = app.ServiceProvider.GetRequiredService<IReportServiceClient>();

            // Initialize collections
            InventoryItems = new ObservableCollection<InventoryReportDto>();
            LowStockItems = new ObservableCollection<LowStockReportDto>();
            StockFilterOptions = new ObservableCollection<string>
            {
                "الكل",
                "مخزون جيد",
                "مخزون منخفض",
                "نفد المخزون"
            };

            // Initialize commands
            LoadDataCommand = new RelayCommand(async () => await LoadDataAsync());
            RefreshCommand = new RelayCommand(async () => await RefreshDataAsync());
            ExportCommand = new RelayCommand(async () => await ExportDataAsync());
            ViewProductDetailsCommand = new RelayCommand<InventoryReportDto>(ViewProductDetails);

            // Initialize with empty data
            InventorySummary = new InventorySummaryDto();
            SelectedStockFilter = "الكل";

            // Load data on initialization
            _ = LoadDataAsync();
        }
        #endregion

        #region Public Methods

        /// <summary>
        /// Loads all inventory data
        /// </summary>
        public async Task LoadDataAsync()
        {
            try
            {
                IsLoading = true;

                // Load summary statistics
                InventorySummary = await _reportServiceClient.GetInventorySummaryAsync();

                // Load inventory items
                var inventoryReport = await _reportServiceClient.GetInventoryReportAsync();
                InventoryItems.Clear();
                foreach (var item in inventoryReport)
                {
                    InventoryItems.Add(item);
                }

                // Load low stock items
                var lowStockReport = await _reportServiceClient.GetLowStockReportAsync();
                LowStockItems.Clear();
                foreach (var item in lowStockReport)
                {
                    LowStockItems.Add(item);
                }

                // Apply current filters
                FilterInventoryItems();
            }
            catch (Exception ex)
            {
                // Handle error - could show notification
                System.Diagnostics.Debug.WriteLine($"Error loading inventory data: {ex.Message}");
                InventorySummary = new InventorySummaryDto();
            }
            finally
            {
                IsLoading = false;
            }
        }

        /// <summary>
        /// Refreshes all data
        /// </summary>
        public async Task RefreshDataAsync()
        {
            await LoadDataAsync();
        }

        /// <summary>
        /// Exports inventory data to Excel
        /// </summary>
        public async Task ExportDataAsync()
        {
            try
            {
                // TODO: Implement Excel export functionality
                System.Windows.MessageBox.Show("سيتم تنفيذ ميزة التصدير قريباً", "تصدير البيانات",
                    System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                System.Windows.MessageBox.Show($"حدث خطأ أثناء التصدير: {ex.Message}", "خطأ",
                    System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// Views product details
        /// </summary>
        public void ViewProductDetails(InventoryReportDto product)
        {
            if (product != null)
            {
                // TODO: Open product details window
                System.Windows.MessageBox.Show($"عرض تفاصيل المنتج: {product.ProductName}", "تفاصيل المنتج",
                    System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Information);
            }
        }

        #endregion

        #region Private Methods

        /// <summary>
        /// Filters inventory items based on search text and stock filter
        /// </summary>
        private void FilterInventoryItems()
        {
            // TODO: Implement filtering logic
            // This would filter the InventoryItems collection based on SearchText and SelectedStockFilter
        }

        #endregion

        #region INotifyPropertyChanged Implementation
        public event PropertyChangedEventHandler PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
        #endregion
    }
}
