using ExactCash.Application.DTOs;
using ExactCash.Application.DTOs.Common;
using ExactCash.Application.Responses;
using Microsoft.AspNetCore.Http;

namespace ExactCash.WPF.Services.Product
{
    public interface IProductService
    {
        Task<List<ProductDto>> SearchProductsAsync(string searchTerm);
        Task<Domain.Entities.Product> GetProductByIdAsync(int id);
        Task<Application.DTOs.ProductDto> GetProductByBarcodeAsync(string barcode);
        Task<BaseResponse<Domain.Entities.Product>> CreateProductAsync(Domain.Entities.Product product, IFormFile imageFile);
        Task<BaseResponse<Domain.Entities.Product>> UpdateProductAsync(int Id, Domain.Entities.Product product, IFormFile imageFile);
        Task<bool> DeleteProductAsync(int id);
        Task<PagedResponse<ProductDto>> GetAllAsync(string name, string sku, int? categoryId,int? brandId, int? unitId, string barcode, PaginationFilter pagination);

        Task<byte[]> GenerateBarcodeImageAsync(int productId);
    }
}