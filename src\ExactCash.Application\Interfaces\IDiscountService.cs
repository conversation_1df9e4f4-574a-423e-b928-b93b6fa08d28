using ExactCash.Application.DTOs;
using ExactCash.Application.DTOs.Common;
using ExactCash.Application.Responses;

namespace ExactCash.Application.Interfaces
{
    public interface IDiscountService
    {
        Task<DiscountDto> GetDiscountByIdAsync(int id);
        Task<PagedResponse<DiscountDto>> GetAllDiscountsAsync(string name, PaginationFilter pagination);
        Task<DiscountDto> CreateDiscountAsync(DiscountDto discountDto);
        Task UpdateDiscountAsync(DiscountDto discountDto);
        Task DeleteDiscountAsync(int id);
    }
}