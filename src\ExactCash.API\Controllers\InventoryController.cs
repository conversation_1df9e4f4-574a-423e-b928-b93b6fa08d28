using Microsoft.AspNetCore.Mvc;
using ExactCash.Application.Interfaces;
using ExactCash.Application.DTOs.Inventory;
using ExactCash.Application.Responses;

namespace ExactCash.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class InventoryController : ControllerBase
    {
        private readonly IInventoryService _inventoryService;

        public InventoryController(IInventoryService inventoryService)
        {
            _inventoryService = inventoryService;
        }

        [HttpGet("stats")]
        public async Task<ActionResult<BaseResponse<InventoryStatsDto>>> GetInventoryStats()
        {
            try
            {
                var result = await _inventoryService.GetInventoryStatsAsync();
                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

        [HttpGet("low-stock")]
        public async Task<ActionResult<BaseResponse<List<LowStockItemDto>>>> GetLowStockItems([FromQuery] int threshold = 10)
        {
            try
            {
                var result = await _inventoryService.GetLowStockItemsAsync(threshold);
                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }
    }
}
