<Window x:Class="ExactCash.WPF.Views.Inventory.InventoryStatsWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        mc:Ignorable="d"
        Title="إحصائيات المخزون"
        Height="900"
        Width="1400"
        WindowStartupLocation="CenterScreen"
        Background="#F5F5F5"
        FlowDirection="RightToLeft">

    <Window.Resources>
        <!-- Converters -->
        <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>
        
        <!-- Modern Card Style -->
        <Style x:Key="ModernCard" TargetType="Border">
            <Setter Property="Background" Value="White"/>
            <Setter Property="CornerRadius" Value="10"/>
            <Setter Property="Padding" Value="20"/>
            <Setter Property="Margin" Value="10"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect BlurRadius="10" ShadowDepth="2" Opacity="0.1"/>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- Modern Button Style -->
        <Style x:Key="ModernButton" TargetType="Button">
            <Setter Property="Background" Value="#0078D4"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="15,8"/>
            <Setter Property="Margin" Value="5"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                                CornerRadius="5"
                                Padding="{TemplateBinding Padding}">
                            <ContentPresenter HorizontalAlignment="Center"
                                              VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#106EBE"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </Window.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Background="#0078D4" Height="60">
            <Grid Margin="20,0">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center">
                    <Path Data="M4 5a2 2 0 012-2h6a2 2 0 012 2v2h2a2 2 0 012 2v12a2 2 0 01-2 2H4a2 2 0 01-2-2V7a2 2 0 012-2h2V5zm8 0v2H8V5h4zM4 7v12h16V7H4zm4 3a1 1 0 011-1h6a1 1 0 110 2h-6a1 1 0 01-1-1zm0 4a1 1 0 011-1h6a1 1 0 110 2h-6a1 1 0 01-1-1z"
                          Fill="White"
                          Width="24"
                          Height="24"
                          Stretch="Uniform"
                          VerticalAlignment="Center"
                          Margin="0,0,15,0"/>
                    <TextBlock Text="إحصائيات المخزون"
                               Foreground="White"
                               FontSize="24"
                               FontWeight="SemiBold"
                               VerticalAlignment="Center"/>
                </StackPanel>

                <StackPanel Grid.Column="1" Orientation="Horizontal" VerticalAlignment="Center">
                    <Button Content="🔄 تحديث"
                            Command="{Binding RefreshCommand}"
                            Style="{StaticResource ModernButton}"
                            Background="#28A745"/>
                    <Button Content="📊 تصدير"
                            Command="{Binding ExportCommand}"
                            Style="{StaticResource ModernButton}"
                            Background="#17A2B8"/>
                </StackPanel>
            </Grid>
        </Border>

        <!-- Summary Statistics Cards -->
        <Grid Grid.Row="1" Margin="10,20,10,0">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- Total Products -->
            <Border Grid.Column="0" Style="{StaticResource ModernCard}" Background="#E3F2FD">
                <StackPanel HorizontalAlignment="Center">
                    <TextBlock Text="📦" FontSize="32" HorizontalAlignment="Center" Margin="0,0,0,10"/>
                    <TextBlock Text="إجمالي المنتجات" FontWeight="Bold" HorizontalAlignment="Center" FontSize="14"/>
                    <TextBlock Text="{Binding InventorySummary.TotalProducts}" 
                               FontSize="24" FontWeight="Bold" 
                               Foreground="#1976D2" HorizontalAlignment="Center"/>
                </StackPanel>
            </Border>

            <!-- Active Products -->
            <Border Grid.Column="1" Style="{StaticResource ModernCard}" Background="#E8F5E8">
                <StackPanel HorizontalAlignment="Center">
                    <TextBlock Text="✅" FontSize="32" HorizontalAlignment="Center" Margin="0,0,0,10"/>
                    <TextBlock Text="المنتجات النشطة" FontWeight="Bold" HorizontalAlignment="Center" FontSize="14"/>
                    <TextBlock Text="{Binding InventorySummary.ActiveProducts}" 
                               FontSize="24" FontWeight="Bold" 
                               Foreground="#388E3C" HorizontalAlignment="Center"/>
                </StackPanel>
            </Border>

            <!-- Low Stock Products -->
            <Border Grid.Column="2" Style="{StaticResource ModernCard}" Background="#FFF3E0">
                <StackPanel HorizontalAlignment="Center">
                    <TextBlock Text="⚠️" FontSize="32" HorizontalAlignment="Center" Margin="0,0,0,10"/>
                    <TextBlock Text="مخزون منخفض" FontWeight="Bold" HorizontalAlignment="Center" FontSize="14"/>
                    <TextBlock Text="{Binding InventorySummary.LowStockProducts}" 
                               FontSize="24" FontWeight="Bold" 
                               Foreground="#F57C00" HorizontalAlignment="Center"/>
                </StackPanel>
            </Border>

            <!-- Out of Stock -->
            <Border Grid.Column="3" Style="{StaticResource ModernCard}" Background="#FFEBEE">
                <StackPanel HorizontalAlignment="Center">
                    <TextBlock Text="❌" FontSize="32" HorizontalAlignment="Center" Margin="0,0,0,10"/>
                    <TextBlock Text="نفد المخزون" FontWeight="Bold" HorizontalAlignment="Center" FontSize="14"/>
                    <TextBlock Text="{Binding InventorySummary.OutOfStockProducts}" 
                               FontSize="24" FontWeight="Bold" 
                               Foreground="#D32F2F" HorizontalAlignment="Center"/>
                </StackPanel>
            </Border>

            <!-- Total Inventory Value -->
            <Border Grid.Column="4" Style="{StaticResource ModernCard}" Background="#F3E5F5">
                <StackPanel HorizontalAlignment="Center">
                    <TextBlock Text="💰" FontSize="32" HorizontalAlignment="Center" Margin="0,0,0,10"/>
                    <TextBlock Text="قيمة المخزون" FontWeight="Bold" HorizontalAlignment="Center" FontSize="14"/>
                    <TextBlock Text="{Binding InventorySummary.FormattedTotalInventoryValue}" 
                               FontSize="18" FontWeight="Bold" 
                               Foreground="#7B1FA2" HorizontalAlignment="Center"/>
                </StackPanel>
            </Border>

            <!-- Total Categories -->
            <Border Grid.Column="5" Style="{StaticResource ModernCard}" Background="#E0F2F1">
                <StackPanel HorizontalAlignment="Center">
                    <TextBlock Text="📂" FontSize="32" HorizontalAlignment="Center" Margin="0,0,0,10"/>
                    <TextBlock Text="الفئات" FontWeight="Bold" HorizontalAlignment="Center" FontSize="14"/>
                    <TextBlock Text="{Binding InventorySummary.TotalCategories}" 
                               FontSize="24" FontWeight="Bold" 
                               Foreground="#00695C" HorizontalAlignment="Center"/>
                </StackPanel>
            </Border>
        </Grid>

        <!-- Main Content Area -->
        <TabControl Grid.Row="2" Margin="10,20,10,10" Background="Transparent" BorderThickness="0">
            <TabItem Header="📋 تفاصيل المخزون">
                <Border Style="{StaticResource ModernCard}" Margin="0">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="*"/>
                        </Grid.RowDefinitions>

                        <!-- Search and Filter Controls -->
                        <Grid Grid.Row="0" Margin="0,0,0,15">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="200"/>
                            </Grid.ColumnDefinitions>

                            <TextBox Grid.Column="0"
                                     Text="{Binding SearchText, UpdateSourceTrigger=PropertyChanged}"
                                     FontSize="14"
                                     Padding="10"
                                     Margin="0,0,10,0"
                                     BorderBrush="#E0E0E0"
                                     BorderThickness="1">
                                <TextBox.Style>
                                    <Style TargetType="TextBox">
                                        <Style.Triggers>
                                            <Trigger Property="Text" Value="">
                                                <Setter Property="Background">
                                                    <Setter.Value>
                                                        <VisualBrush AlignmentX="Left" AlignmentY="Center" Stretch="None">
                                                            <VisualBrush.Visual>
                                                                <TextBlock Text="🔍 البحث في المنتجات..." 
                                                                           Foreground="Gray" 
                                                                           Margin="5,0"/>
                                                            </VisualBrush.Visual>
                                                        </VisualBrush>
                                                    </Setter.Value>
                                                </Setter>
                                            </Trigger>
                                        </Style.Triggers>
                                    </Style>
                                </TextBox.Style>
                            </TextBox>

                            <ComboBox Grid.Column="1"
                                      SelectedItem="{Binding SelectedStockFilter}"
                                      ItemsSource="{Binding StockFilterOptions}"
                                      FontSize="14"
                                      Padding="10"
                                      BorderBrush="#E0E0E0"
                                      BorderThickness="1"/>
                        </Grid>

                        <!-- Inventory Items DataGrid -->
                        <DataGrid Grid.Row="1"
                                  ItemsSource="{Binding InventoryItems}"
                                  AutoGenerateColumns="False"
                                  CanUserAddRows="False"
                                  CanUserDeleteRows="False"
                                  IsReadOnly="True"
                                  SelectionMode="Single"
                                  GridLinesVisibility="All"
                                  BorderThickness="1"
                                  BorderBrush="#E5E5E5"
                                  Background="White"
                                  RowHeaderWidth="0"
                                  HeadersVisibility="Column"
                                  HorizontalGridLinesBrush="#E5E5E5"
                                  VerticalGridLinesBrush="#E5E5E5"
                                  AlternatingRowBackground="#F8F9FA">
                            <DataGrid.Columns>
                                <DataGridTextColumn Header="اسم المنتج" Binding="{Binding ProductName}" Width="200"/>
                                <DataGridTextColumn Header="الباركود" Binding="{Binding Barcode}" Width="120"/>
                                <DataGridTextColumn Header="الفئة" Binding="{Binding CategoryName}" Width="120"/>
                                <DataGridTextColumn Header="الماركة" Binding="{Binding BrandName}" Width="120"/>
                                <DataGridTextColumn Header="المخزون الحالي" Binding="{Binding CurrentStock}" Width="100"/>
                                <DataGridTextColumn Header="الحد الأدنى" Binding="{Binding MinStock}" Width="100"/>
                                <DataGridTextColumn Header="سعر التكلفة" Binding="{Binding FormattedCostPrice}" Width="120"/>
                                <DataGridTextColumn Header="سعر البيع" Binding="{Binding FormattedSellingPrice}" Width="120"/>
                                <DataGridTextColumn Header="قيمة المخزون" Binding="{Binding FormattedStockValue}" Width="120"/>
                                <DataGridTemplateColumn Header="حالة المخزون" Width="120">
                                    <DataGridTemplateColumn.CellTemplate>
                                        <DataTemplate>
                                            <Border Background="{Binding StockStatusColor}"
                                                    CornerRadius="3"
                                                    Padding="5,2">
                                                <TextBlock Text="{Binding StockStatus}"
                                                           Foreground="White"
                                                           FontWeight="Bold"
                                                           HorizontalAlignment="Center"/>
                                            </Border>
                                        </DataTemplate>
                                    </DataGridTemplateColumn.CellTemplate>
                                </DataGridTemplateColumn>
                                <DataGridTemplateColumn Header="الإجراءات" Width="80">
                                    <DataGridTemplateColumn.CellTemplate>
                                        <DataTemplate>
                                            <Button Command="{Binding DataContext.ViewProductDetailsCommand, RelativeSource={RelativeSource AncestorType=Window}}"
                                                    CommandParameter="{Binding}"
                                                    Background="#17A2B8"
                                                    Width="32"
                                                    Height="32"
                                                    ToolTip="عرض التفاصيل"
                                                    BorderThickness="0"
                                                    Cursor="Hand"
                                                    HorizontalAlignment="Center">
                                                <TextBlock Text="👁️"
                                                           FontSize="16"
                                                           HorizontalAlignment="Center"
                                                           VerticalAlignment="Center"/>
                                            </Button>
                                        </DataTemplate>
                                    </DataGridTemplateColumn.CellTemplate>
                                </DataGridTemplateColumn>
                            </DataGrid.Columns>
                        </DataGrid>
                    </Grid>
                </Border>
            </TabItem>

            <TabItem Header="⚠️ مخزون منخفض">
                <Border Style="{StaticResource ModernCard}" Margin="0">
                    <DataGrid ItemsSource="{Binding LowStockItems}"
                              AutoGenerateColumns="False"
                              CanUserAddRows="False"
                              CanUserDeleteRows="False"
                              IsReadOnly="True"
                              SelectionMode="Single"
                              GridLinesVisibility="All"
                              BorderThickness="1"
                              BorderBrush="#E5E5E5"
                              Background="White"
                              RowHeaderWidth="0"
                              HeadersVisibility="Column"
                              HorizontalGridLinesBrush="#E5E5E5"
                              VerticalGridLinesBrush="#E5E5E5"
                              AlternatingRowBackground="#FFF3E0">
                        <DataGrid.Columns>
                            <DataGridTextColumn Header="اسم المنتج" Binding="{Binding ProductName}" Width="200"/>
                            <DataGridTextColumn Header="الفئة" Binding="{Binding CategoryName}" Width="150"/>
                            <DataGridTextColumn Header="المخزون الحالي" Binding="{Binding CurrentStock}" Width="120"/>
                            <DataGridTextColumn Header="الحد الأدنى" Binding="{Binding MinStock}" Width="120"/>
                            <DataGridTextColumn Header="النقص" Binding="{Binding StockDeficit}" Width="100"/>
                            <DataGridTextColumn Header="قيمة إعادة الطلب" Binding="{Binding FormattedReorderValue}" Width="150"/>
                            <DataGridTextColumn Header="المورد" Binding="{Binding SupplierName}" Width="150"/>
                            <DataGridTextColumn Header="أيام النفاد" Binding="{Binding DaysOutOfStock}" Width="100"/>
                        </DataGrid.Columns>
                    </DataGrid>
                </Border>
            </TabItem>
        </TabControl>

        <!-- Loading Overlay -->
        <Border Grid.RowSpan="3"
                Background="#80000000"
                Visibility="{Binding IsLoading, Converter={StaticResource BooleanToVisibilityConverter}}">
            <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                <TextBlock Text="⏳"
                           FontSize="48"
                           HorizontalAlignment="Center"
                           Margin="0,0,0,20"/>
                <TextBlock Text="جاري تحميل بيانات المخزون..."
                           FontSize="18"
                           Foreground="White"
                           HorizontalAlignment="Center"/>
            </StackPanel>
        </Border>
    </Grid>
</Window>
