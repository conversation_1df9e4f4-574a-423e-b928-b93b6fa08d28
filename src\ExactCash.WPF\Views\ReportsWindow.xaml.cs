﻿using System;
using System.Windows;
using ExactCash.WPF.Views.Reports;
using MessageBox = System.Windows.MessageBox;

namespace ExactCash.WPF.Views
{
    public partial class ReportsWindow : Window
    {
        private DateTime? _startDate;
        private DateTime? _endDate;

        public ReportsWindow()
        {
            InitializeComponent();

            // Set default date range to current month
            var now = DateTime.Now;
            _startDate = new DateTime(now.Year, now.Month, 1);
            _endDate = now;

            StartDatePicker.SelectedDate = _startDate;
            EndDatePicker.SelectedDate = _endDate;
        }

        private void ApplyDateRange_Click(object sender, RoutedEventArgs e)
        {
            _startDate = StartDatePicker.SelectedDate;
            _endDate = EndDatePicker.SelectedDate;

            MessageBox.Show($"تم تطبيق الفترة من {_startDate?.ToString("yyyy-MM-dd")} إلى {_endDate?.ToString("yyyy-MM-dd")}",
                          "تطبيق الفترة",
                          MessageBoxButton.OK,
                          MessageBoxImage.Information);
        }

        #region Inventory Reports

        private void InventoryReport_Click(object sender, RoutedEventArgs e)
        {
            var window = new InventoryReportWindow();
            window.ShowDialog();
        }

        private void LowStockReport_Click(object sender, RoutedEventArgs e)
        {
            var window = new LowStockReportWindow();
            window.ShowDialog();
        }

        private void StockMovementReport_Click(object sender, RoutedEventArgs e)
        {
            var window = new StockMovementReportWindow(_startDate, _endDate);
            window.ShowDialog();
        }

        private void InventorySummary_Click(object sender, RoutedEventArgs e)
        {
            var window = new InventorySummaryWindow();
            window.ShowDialog();
        }

        #endregion

        #region Financial Reports

        private void ProfitLossReport_Click(object sender, RoutedEventArgs e)
        {
            var window = new ProfitLossReportWindow(_startDate, _endDate);
            window.ShowDialog();
        }

        private void CashFlowReport_Click(object sender, RoutedEventArgs e)
        {
            var window = new CashFlowReportWindow(_startDate, _endDate);
            window.ShowDialog();
        }

        private void PaymentMethodReport_Click(object sender, RoutedEventArgs e)
        {
            var window = new PaymentMethodReportWindow(_startDate, _endDate);
            window.ShowDialog();
        }

        private void TaxReport_Click(object sender, RoutedEventArgs e)
        {
            var window = new TaxReportWindow(_startDate, _endDate);
            window.ShowDialog();
        }

        private void ExpenseReport_Click(object sender, RoutedEventArgs e)
        {
            var window = new ExpenseReportWindow(_startDate, _endDate);
            window.ShowDialog();
        }

        #endregion

        #region Customer Reports

        private void CustomerAnalysisReport_Click(object sender, RoutedEventArgs e)
        {
            var window = new CustomerAnalysisReportWindow(_startDate, _endDate);
            window.ShowDialog();
        }

        private void TopCustomersReport_Click(object sender, RoutedEventArgs e)
        {
            var window = new TopCustomersReportWindow(_startDate, _endDate);
            window.ShowDialog();
        }

        private void CustomerPurchaseHistory_Click(object sender, RoutedEventArgs e)
        {
            // This would typically show a customer selection dialog first
            MessageBox.Show("يرجى تحديد العميل أولاً من قائمة العملاء",
                          "تحديد العميل",
                          MessageBoxButton.OK,
                          MessageBoxImage.Information);
        }

        #endregion

        #region Performance Reports

        private void HourlySalesReport_Click(object sender, RoutedEventArgs e)
        {
            var window = new HourlySalesReportWindow(_startDate);
            window.ShowDialog();
        }

        private void CashierPerformanceReport_Click(object sender, RoutedEventArgs e)
        {
            var window = new CashierPerformanceReportWindow(_startDate, _endDate);
            window.ShowDialog();
        }

        private void ProductPerformanceReport_Click(object sender, RoutedEventArgs e)
        {
            var window = new ProductPerformanceReportWindow(_startDate, _endDate);
            window.ShowDialog();
        }

        #endregion

        #region Supplier Reports

        private void SupplierPerformanceReport_Click(object sender, RoutedEventArgs e)
        {
            var window = new SupplierPerformanceReportWindow(_startDate, _endDate);
            window.ShowDialog();
        }

        private void SupplierPaymentStatusReport_Click(object sender, RoutedEventArgs e)
        {
            var window = new SupplierPaymentStatusReportWindow();
            window.ShowDialog();
        }

        #endregion

        #region Audit Reports

        private void TransactionLogReport_Click(object sender, RoutedEventArgs e)
        {
            var window = new TransactionLogReportWindow(_startDate, _endDate);
            window.ShowDialog();
        }

        private void SystemActivityReport_Click(object sender, RoutedEventArgs e)
        {
            var window = new SystemActivityReportWindow(_startDate, _endDate);
            window.ShowDialog();
        }

        #endregion
    }
}
