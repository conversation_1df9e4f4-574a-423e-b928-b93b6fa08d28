using ExactCash.Application.DTOs;
using ExactCash.Application.DTOs.Common;
using ExactCash.Application.Interfaces;
using ExactCash.Application.Responses;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using System.Security.Claims;
#nullable disable

namespace ExactCash.API.Controllers
{
    [Authorize]
    [ApiController]
    [Route("api/[controller]")]
    public class CustomersController : ControllerBase
    {
        private readonly ICustomerService _customerService;

        public CustomersController(ICustomerService customerService)
        {
            _customerService = customerService;
        }

        [HttpGet]
        public async Task<ActionResult<PagedResponse<CustomerDto>>> GetAllCustomers([FromQuery] string name, [FromQuery] string phone, [FromQuery] int? categoryId, [FromQuery] PaginationFilter pagination)
        {
            var customers = await _customerService.GetAllCustomersAsync(name, phone, categoryId, pagination);
            return Ok(customers);
        }


        [HttpGet("get-all-customers")]
        public async Task<ActionResult<List<CustomerDto>>> GetAllCustomers()
        {
            var customers = await _customerService.GetAllCustomersAsync();
            return Ok(customers);
        }

        [HttpGet("{id}")]
        public async Task<ActionResult<CustomerDto>> GetCustomer(int id)
        {
            var customer = await _customerService.GetCustomerByIdAsync(id);
            if (customer == null)
                return NotFound();

            return Ok(customer);
        }

        [HttpPost]
        public async Task<ActionResult<CustomerDto>> CreateCustomer(CustomerDto customerDto)
        {
            var userEmail = User.FindFirst(ClaimTypes.Email)?.Value;
            var createdCustomer = await _customerService.CreateCustomerAsync(customerDto, userEmail);
            return CreatedAtAction(nameof(GetCustomer), new { id = createdCustomer.Id }, createdCustomer);
        }

        [HttpPut("{id}")]
        public async Task<IActionResult> UpdateCustomer(int id, CustomerDto customerDto)
        {
            var result = await _customerService.UpdateCustomerAsync(customerDto);
            return StatusCode(result.StatusCode, result);
        }

        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteCustomer(int id)
        {
            try
            {
                await _customerService.DeleteCustomerAsync(id);
                return NoContent();
            }
            catch (KeyNotFoundException)
            {
                return NotFound();
            }
        }

        [HttpGet("search")]
        public async Task<IActionResult> SearchCustomers([FromQuery] string term)
        {
            var customers = await _customerService.SearchCustomersAsync(term);
            return Ok(customers);
        }
    }
}