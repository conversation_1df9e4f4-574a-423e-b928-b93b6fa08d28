using System;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using System.Windows.Input;
using ExactCash.Application.DTOs;
using ExactCash.Application.DTOs.Common;
using ExactCash.WPF.Commands;
using ExactCash.WPF.Services;
using System.Windows;
using ExactCash.WPF.Views.Sale;
using System.Windows.Media;
using Microsoft.Win32;
using System.IO;
using System.Text;

namespace ExactCash.WPF.ViewModels.Customer
{
    public class CustomerListViewModel : INotifyPropertyChanged
    {
        private readonly ICustomerService _customerService;
        private readonly ICustomerCategoryServiceClient _customerCategoryService;
        private ObservableCollection<CustomerDto> _customers;
        private ObservableCollection<CustomerCategoryDto> _searchCategories;
        private CustomerCategoryDto _searchCategory;
        private string _nameSearch;
        private string _phoneSearch;
        private string _emailSearch;
        private string _addressSearch;
        private int _currentPage = 1;
        private int _pageSize = 20;
        private int _totalPages;
        private int _totalItems;
        private CustomerDto _selectedCustomer;

        public Action CloseWindow { get; set; }

        public ObservableCollection<CustomerDto> Customers
        {
            get => _customers;
            set
            {
                _customers = value;
                OnPropertyChanged();
            }
        }

        public ObservableCollection<CustomerCategoryDto> SearchCategories
        {
            get => _searchCategories;
            set
            {
                _searchCategories = value;
                OnPropertyChanged();
            }
        }

        public CustomerCategoryDto SearchCategory
        {
            get => _searchCategory;
            set
            {
                _searchCategory = value;
                OnPropertyChanged();
            }
        }

        public string NameSearch
        {
            get => _nameSearch;
            set
            {
                _nameSearch = value;
                OnPropertyChanged();
            }
        }

        public string PhoneSearch
        {
            get => _phoneSearch;
            set
            {
                _phoneSearch = value;
                OnPropertyChanged();
            }
        }

        public string EmailSearch
        {
            get => _emailSearch;
            set
            {
                _emailSearch = value;
                OnPropertyChanged();
            }
        }

        public string AddressSearch
        {
            get => _addressSearch;
            set
            {
                _addressSearch = value;
                OnPropertyChanged();
            }
        }

        public int CurrentPage
        {
            get => _currentPage;
            set
            {
                _currentPage = value;
                OnPropertyChanged();
                OnPropertyChanged(nameof(PaginationInfo));
            }
        }

        public int PageSize
        {
            get => _pageSize;
            set
            {
                _pageSize = value;
                OnPropertyChanged();
            }
        }

        public int TotalPages
        {
            get => _totalPages;
            set
            {
                _totalPages = value;
                OnPropertyChanged();
                OnPropertyChanged(nameof(PaginationInfo));
            }
        }

        public int TotalItems
        {
            get => _totalItems;
            set
            {
                _totalItems = value;
                OnPropertyChanged();
                OnPropertyChanged(nameof(PaginationInfo));
            }
        }

        public string PaginationInfo => $"صفحة {CurrentPage} من {TotalPages} (إجمالي العناصر: {TotalItems})";

        public CustomerDto SelectedCustomer
        {
            get => _selectedCustomer;
            set
            {
                _selectedCustomer = value;
                OnPropertyChanged();
            }
        }

        public ICommand SearchCommand { get; }
        public ICommand AddNewCustomerCommand { get; }
        public ICommand RefreshCommand { get; }
        public ICommand PreviousPageCommand { get; }
        public ICommand NextPageCommand { get; }
        public ICommand ResetSearchCommand { get; }
        public ICommand EditCustomerCommand { get; }
        public ICommand DeleteCustomerCommand { get; }
        public ICommand CollectPaymentCommand { get; }
        public ICommand ExportToExcelCommand { get; }

        public CustomerListViewModel(ICustomerService customerService, ICustomerCategoryServiceClient customerCategoryService)
        {
            _customerService = customerService;
            _customerCategoryService = customerCategoryService;
            Customers = new ObservableCollection<CustomerDto>();
            SearchCategories = new ObservableCollection<CustomerCategoryDto>();

            SearchCommand = new RelayCommand(ExecuteSearch);
            AddNewCustomerCommand = new RelayCommand(ExecuteAddNewCustomer);
            RefreshCommand = new RelayCommand(ExecuteRefresh);
            PreviousPageCommand = new RelayCommand(ExecutePreviousPage, CanExecutePreviousPage);
            NextPageCommand = new RelayCommand(ExecuteNextPage, CanExecuteNextPage);
            ResetSearchCommand = new RelayCommand(ExecuteResetSearch);
            EditCustomerCommand = new RelayCommand<CustomerDto>(ExecuteEditCustomer);
            DeleteCustomerCommand = new RelayCommand<CustomerDto>(ExecuteDeleteCustomer);
            CollectPaymentCommand = new RelayCommand<CustomerDto>(ExecuteCollectPayment);
            ExportToExcelCommand = new RelayCommand(ExportToExcel);

            LoadSearchCategories();
            LoadData();
        }

        private async void LoadData()
        {
            try
            {
                ExecuteSearch();
            }
            catch (Exception ex)
            {
                Helpers.BootstrapMessageBoxHelper.ShowError($"حدث خطأ أثناء تحميل البيانات: {ex.Message}", "خطأ");
            }
        }

        private async void ExecuteSearch()
        {
            try
            {
                var filter = new PaginationFilter
                {
                    PageNumber = CurrentPage,
                    PageSize = PageSize
                };

                var categoryId = SearchCategory?.Id > 0 ? SearchCategory.Id : (int?)null;
                var result = await _customerService.GetAllCustomersAsync(NameSearch, PhoneSearch, categoryId, filter);

                Customers.Clear();
                foreach (var customer in result.Data)
                {
                    Customers.Add(customer);
                }

                TotalPages = result.TotalPages;
                TotalItems = result.TotalItems;
            }
            catch (Exception ex)
            {
                Helpers.BootstrapMessageBoxHelper.ShowError($"حدث خطأ أثناء البحث: {ex.Message}", "خطأ");
            }
        }

        private Window FindParentWindow()
        {
            foreach (Window window in System.Windows.Application.Current.Windows)
            {
                if (window.Content != null &&
                    VisualTreeHelper.GetChild(window.Content as DependencyObject, 0) is FrameworkElement content &&
                    window.Name == "CustomerListView1")
                {
                    return window;
                }
            }
            return System.Windows.Application.Current.MainWindow;
        }

        private void ExecuteAddNewCustomer()
        {
            var addCustomerView = new AddCustomerView();
            addCustomerView.Owner = FindParentWindow();
            if (addCustomerView.ShowDialog() == true)
            {
                ExecuteRefresh();
            }
        }

        private void ExecuteRefresh()
        {
            LoadData();
        }

        private bool CanExecutePreviousPage()
        {
            return CurrentPage > 1;
        }

        private void ExecutePreviousPage()
        {
            if (CurrentPage > 1)
            {
                CurrentPage--;
                ExecuteSearch();
            }
        }

        private bool CanExecuteNextPage()
        {
            return CurrentPage < TotalPages;
        }

        private void ExecuteNextPage()
        {
            if (CurrentPage < TotalPages)
            {
                CurrentPage++;
                ExecuteSearch();
            }
        }

        private void ExecuteResetSearch()
        {
            NameSearch = string.Empty;
            PhoneSearch = string.Empty;
            EmailSearch = string.Empty;
            AddressSearch = string.Empty;
            SearchCategory = null;
            CurrentPage = 1;
            ExecuteSearch();
        }

        private async void LoadSearchCategories()
        {
            try
            {
                var categories = await _customerCategoryService.GetActiveAsync();

                SearchCategories.Clear();
                SearchCategories.Add(new CustomerCategoryDto { Id = 0, Name = "-- جميع التصنيفات --" });

                foreach (var category in categories)
                {
                    SearchCategories.Add(category);
                }
            }
            catch (Exception ex)
            {
                // If categories fail to load, just show the "All Categories" option
                SearchCategories.Clear();
                SearchCategories.Add(new CustomerCategoryDto { Id = 0, Name = "-- جميع التصنيفات --" });
            }
        }

        private void ExecuteEditCustomer(CustomerDto customer)
        {
            if (customer == null) return;

            var addCustomerView = new AddCustomerView(customer);
            addCustomerView.Owner = FindParentWindow();
            if (addCustomerView.ShowDialog() == true)
            {
                ExecuteRefresh();
            }
        }

        private async void ExecuteDeleteCustomer(CustomerDto customer)
        {
            if (customer == null) return;

            var result = Helpers.BootstrapMessageBoxHelper.Show(
                $"هل أنت متأكد من حذف العميل '{customer.FullName}'؟",
                "تأكيد الحذف", owner: FindParentWindow());

            if (result == true)
            {
                try
                {
                    await _customerService.DeleteCustomerAsync(customer.Id);
                    ExecuteRefresh();
                }
                catch (Exception ex)
                {
                    Helpers.BootstrapMessageBoxHelper.ShowError($"حدث خطأ أثناء حذف العميل: {ex.Message}", "خطأ", owner: FindParentWindow());
                }
            }
        }

        private void ExecuteCollectPayment(CustomerDto customer)
        {
            if (customer == null) return;

            try
            {
                // Open payment collection dialog for specific customer
                var paymentView = new Views.CreditSales.PaymentCollectionView(customer);
                paymentView.Owner = FindParentWindow();
                var result = paymentView.ShowDialog();

                if (result == true)
                {
                    // Refresh data after payment collection
                    ExecuteRefresh();
                    Helpers.BootstrapMessageBoxHelper.ShowSuccess("تم تحصيل الدفعة بنجاح", owner: FindParentWindow());
                }
            }
            catch (Exception ex)
            {
                Helpers.BootstrapMessageBoxHelper.ShowError($"حدث خطأ أثناء تحصيل الدفعة: {ex.Message}", owner: FindParentWindow());
            }
        }

        private void ExportToExcel()
        {
            var saveFileDialog = new Microsoft.Win32.SaveFileDialog
            {
                Filter = "Excel Files (*.xlsx)|*.xlsx|CSV Files (*.csv)|*.csv",
                FileName = $"العملاء_{DateTime.Now:yyyyMMdd_HHmmss}.csv"
            };

            if (saveFileDialog.ShowDialog() == true)
            {
                try
                {
                    using (var writer = new StreamWriter(saveFileDialog.FileName, false, Encoding.UTF8))
                    {
                        // Write header (adjust columns as per your DataGrid)
                        writer.WriteLine("الاسم,رقم الهاتف,البريد الإلكتروني,العنوان,تم الإنشاء بواسطة");
                        foreach (var customer in Customers)
                        {
                            writer.WriteLine($"{customer.FullName},{customer.Phone},{customer.Email},{customer.Address},{customer.CreatedBy ?? "-"}");
                        }
                    }

                    Helpers.BootstrapMessageBoxHelper.Show(owner: FindParentWindow(), message: "تم تصدير البيانات بنجاح!");
                }
                catch (Exception ex)
                {
                    Helpers.BootstrapMessageBoxHelper.Show(owner: FindParentWindow(), message: $"حدث خطأ أثناء التصدير: {ex.Message}");
                }
            }
        }

        public event PropertyChangedEventHandler PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }
}