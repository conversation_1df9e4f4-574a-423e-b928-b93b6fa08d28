using System;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Input;
using AutoMapper;
using ExactCash.Application.DTOs;
using ExactCash.WPF.Commands;
using ExactCash.WPF.Services.Product;
using Microsoft.Win32;
using System.IO;
using Microsoft.AspNetCore.Http;
using System.Text;
using System.Threading;
using ExactCash.WPF.Services;
using System.Linq;

namespace ExactCash.WPF.ViewModels.Product
{
    public class AddProductViewModel : INotifyPropertyChanged
    {
        private readonly IProductService _productService;
        private int _id;
        private string _name;
        private string _barcode;
        private string _sku;
        private int? _categoryId;
        private string _categoryName;
        private int? _brandId;
        private int? _defaultUnitId;
        private string _imagePath;
        private decimal _costPrice;
        private decimal _sellingPrice;
        private decimal _stockQuantity;
        private decimal _discount;
        private string _description;
        private ObservableCollection<CategoryDto> _categories;
        private ObservableCollection<BrandDto> _brands;
        private ObservableCollection<UnitDto> _units;
        private ProductDto _newProduct;
        private readonly IMapper _mapper;
        private readonly ICategoryServiceClient _categoryServiceClient;
        private readonly IBrandsServiceClient _brandsServiceClient;
        private readonly IUnitServiceClient _unitServiceClient;
        public Window CurrentWindow { get; set; }

        public Action<bool?> CloseWindow { get; set; }

        private string _windowTitle = "إضافة منتج جديد";
        public string WindowTitle
        {
            get => _windowTitle;
            set
            {
                _windowTitle = value;
                OnPropertyChanged();
            }
        }

        private string _saveButtonText = "إضافة";
        public string SaveButtonText
        {
            get => _saveButtonText;
            set
            {
                _saveButtonText = value;
                OnPropertyChanged();
            }
        }

        public int Id
        {
            get => _id;
            set
            {
                _id = value;
                OnPropertyChanged();
            }
        }

        public AddProductViewModel(IProductService productService, IMapper mapper, ICategoryServiceClient categoryServiceClient, IBrandsServiceClient brandsServiceClient, IUnitServiceClient unitServiceClient)
        {
            _productService = productService;
            _mapper = mapper;
            _categoryServiceClient = categoryServiceClient;
            _brandsServiceClient = brandsServiceClient;
            SaveCommand = new RelayCommand(ExecuteSave, CanExecuteSave);
            SelectImageCommand = new RelayCommand(ExecuteSelectImage);
            LoadData();
            _unitServiceClient = unitServiceClient;
            Barcode = GenerateBarcode();
        }

        public string GenerateBarcode()
        {
            // Use timestamp and a random 4-digit number
            var timestamp = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds();
            var random = new Random().Next(1000, 9999);
            return $"PRD{timestamp}{random}";
        }

        private async void LoadData()
        {
            try
            {
                var categories = await _categoryServiceClient.GetAllAsync();
                var brands = await _brandsServiceClient.GetAllBrandsAsync();
                var units = await _unitServiceClient.GetAllUnitsAsync();

                Categories = new ObservableCollection<CategoryDto>(categories);
                Brands = new ObservableCollection<BrandDto>(brands);
                Units = new ObservableCollection<UnitDto>(units);
            }
            catch (Exception ex)
            {
                System.Windows.MessageBox.Show($"Error loading data: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        public string Name
        {
            get => _name;
            set
            {
                _name = value;
                OnPropertyChanged();
                (SaveCommand as RelayCommand)?.RaiseCanExecuteChanged();
            }
        }

        public string Barcode
        {
            get => _barcode;
            set
            {
                _barcode = value;
                OnPropertyChanged();
            }
        }

        public string SKU
        {
            get => _sku;
            set
            {
                _sku = value;
                OnPropertyChanged();
            }
        }

        public int? CategoryId
        {
            get => _categoryId;
            set
            {
                _categoryId = value;
                if (value.HasValue)
                {
                    var selectedCategory = Categories?.FirstOrDefault(c => c.Id == value);
                    CategoryName = selectedCategory?.Name;
                }
                OnPropertyChanged();
            }
        }

        public string CategoryName
        {
            get => _categoryName;
            set
            {
                _categoryName = value;
                OnPropertyChanged();
            }
        }

        public int? BrandId
        {
            get => _brandId;
            set
            {
                _brandId = value;
                OnPropertyChanged();
            }
        }

        public int? DefaultUnitId
        {
            get => _defaultUnitId;
            set
            {
                _defaultUnitId = value;
                OnPropertyChanged();
            }
        }

        public string ImagePath
        {
            get => _imagePath;
            set
            {
                _imagePath = value;
                OnPropertyChanged();
            }
        }

        public decimal CostPrice
        {
            get => _costPrice;
            set
            {
                _costPrice = value;
                OnPropertyChanged();
            }
        }

        public decimal SellingPrice
        {
            get => _sellingPrice;
            set
            {
                _sellingPrice = value;
                OnPropertyChanged();
                (SaveCommand as RelayCommand)?.RaiseCanExecuteChanged();
            }
        }


        public decimal StockQuantity
        {
            get => _stockQuantity;
            set
            {
                _stockQuantity = value;
                OnPropertyChanged();
                (SaveCommand as RelayCommand)?.RaiseCanExecuteChanged();
            }
        }

        public decimal Discount
        {
            get => _discount;
            set
            {
                _discount = value;
                OnPropertyChanged();
            }
        }

        public string Description
        {
            get => _description;
            set
            {
                _description = value;
                OnPropertyChanged();
            }
        }

        public ObservableCollection<CategoryDto> Categories
        {
            get => _categories;
            set
            {
                _categories = value;
                OnPropertyChanged();
            }
        }

        public ObservableCollection<BrandDto> Brands
        {
            get => _brands;
            set
            {
                _brands = value;
                OnPropertyChanged();
            }
        }

        public ObservableCollection<UnitDto> Units
        {
            get => _units;
            set
            {
                _units = value;
                OnPropertyChanged();
            }
        }

        public ProductDto NewProduct => _newProduct;

        public ICommand SaveCommand { get; }
        public ICommand SelectImageCommand { get; }

        private bool CanExecuteSave()
        {
            return !string.IsNullOrWhiteSpace(Name) && SellingPrice > 0;
        }

        private async void ExecuteSave()
        {
            try
            {
                if (string.IsNullOrEmpty(Name) || string.IsNullOrEmpty(Barcode) || DefaultUnitId == 0 || DefaultUnitId == 0 || CostPrice == 0 || SellingPrice == 0)
                {
                    Helpers.BootstrapMessageBoxHelper.ShowError("الرجاء ادخال البيانات المطلوبه", "خطأ", CurrentWindow);
                    return;
                }
                var productDto = new ProductDto
                {
                    Id = Id,
                    Name = Name,
                    Barcode = Barcode,
                    SKU = SKU,
                    CategoryId = CategoryId,
                    CategoryName = CategoryName,
                    BrandId = BrandId,
                    DefaultUnitId = DefaultUnitId,
                    ImagePath = ImagePath,
                    CostPrice = CostPrice,
                    SellingPrice = SellingPrice,
                    Discount = Discount,
                    Description = Description,
                    StockQuantity = StockQuantity
                };

                // Create IFormFile from the selected image path
                IFormFile imageFile = null;
                if (!string.IsNullOrEmpty(ImagePath) && File.Exists(ImagePath))
                {
                    var fileInfo = new FileInfo(ImagePath);
                    var fileStream = new FileStream(ImagePath, FileMode.Open, FileAccess.Read);
                    imageFile = new FormFile(
                        fileStream,
                        0,
                        fileStream.Length,
                        null,
                        Path.GetFileName(ImagePath)
                    )
                    {
                        Headers = new HeaderDictionary(),
                        ContentType = GetContentType(Path.GetExtension(ImagePath))
                    };
                }

                var result = Id > 0
                    ? await _productService.UpdateProductAsync(Id, _mapper.Map<Domain.Entities.Product>(productDto), imageFile)
                    : await _productService.CreateProductAsync(_mapper.Map<Domain.Entities.Product>(productDto), imageFile);

                if (result?.Data != null)
                {
                    _newProduct = _mapper.Map<ProductDto>(result.Data);
                    _newProduct.CategoryName = CategoryName;
                    var message = Id > 0 ? "تم تحديث المنتج بنجاح" : "تم إضافة المنتج بنجاح";
                    Helpers.BootstrapMessageBoxHelper.ShowSuccess(message, "نجاح", CurrentWindow);
                    System.Windows.Application.Current.Dispatcher.Invoke(() =>
                    {
                        CloseWindow?.Invoke(true);
                    });
                }
                else
                {
                    var errorMessage = Id > 0 ? "حدث خطأ أثناء تحديث المنتج" : "حدث خطأ أثناء إضافة المنتج";
                    Helpers.BootstrapMessageBoxHelper.ShowError(errorMessage, "خطأ", CurrentWindow);
                }
            }
            catch (Exception ex)
            {
                Helpers.BootstrapMessageBoxHelper.ShowError($"حدث خطأ: {ex.Message}", "خطأ", CurrentWindow);
            }
        }

        private string GetContentType(string fileExtension)
        {
            switch (fileExtension.ToLower())
            {
                case ".jpg":
                case ".jpeg":
                    return "image/jpeg";
                case ".png":
                    return "image/png";
                default:
                    return "application/octet-stream";
            }
        }

        // Custom FormFile implementation for WPF
        private class FormFile : IFormFile
        {
            private readonly Stream _stream;
            private readonly long _length;

            public FormFile(Stream stream, long baseStreamOffset, long length, string name, string fileName)
            {
                _stream = stream;
                _length = length;
                Name = name;
                FileName = fileName;
            }

            public string ContentType { get; set; }
            public string ContentDisposition { get; set; }
            public IHeaderDictionary Headers { get; set; }
            public long Length => _length;
            public string Name { get; }
            public string FileName { get; }

            public void CopyTo(Stream target)
            {
                if (_stream.CanSeek)
                {
                    _stream.Seek(0, SeekOrigin.Begin);
                }
                _stream.CopyTo(target);
            }

            public async Task CopyToAsync(Stream target, CancellationToken cancellationToken = default)
            {
                if (_stream.CanSeek)
                {
                    _stream.Seek(0, SeekOrigin.Begin);
                }
                await _stream.CopyToAsync(target, cancellationToken);
            }

            public Stream OpenReadStream()
            {
                if (_stream.CanSeek)
                {
                    _stream.Seek(0, SeekOrigin.Begin);
                }
                return _stream;
            }
        }

        private void ExecuteSelectImage()
        {
            var openFileDialog = new Microsoft.Win32.OpenFileDialog
            {
                Filter = "Image files (*.jpg, *.jpeg, *.png) | *.jpg; *.jpeg; *.png",
                Title = "Select Product Image"
            };

            if (openFileDialog.ShowDialog() == true)
            {
                ImagePath = openFileDialog.FileName;
            }
        }

        public void LoadProduct(ProductDto product)
        {
            if (product == null) return;

            // Update window title and save button text
            WindowTitle = "تحديث المنتج";
            SaveButtonText = "حفظ";

            // Load the product data into the view model
            Id = product.Id;
            Name = product.Name;
            SKU = product.SKU;
            Barcode = product.Barcode;
            Description = product.Description;
            CategoryId = product.CategoryId;
            BrandId = product.BrandId;
            DefaultUnitId = (product.DefaultUnitId == null ? product.UnitId : product.DefaultUnitId);
            CostPrice = product.CostPrice;
            SellingPrice = product.SellingPrice;
            Discount = product.Discount;
            ImagePath = product.ImagePath;
            StockQuantity = product.StockQuantity;
        }

        public event PropertyChangedEventHandler PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }
}