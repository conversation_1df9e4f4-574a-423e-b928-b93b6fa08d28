﻿using ExactCash.Application.DTOs;
using ExactCash.Application.Interfaces;
using ExactCash.WPF.Commands;
using ExactCash.WPF.Services.Interfaces;
using ExactCash.WPF.Views.Expense;
using System.Collections.ObjectModel;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Input;
using System.Windows.Media;
using Microsoft.Win32;
using System.IO;
using System.Text;

namespace ExactCash.WPF.ViewModels.Expense
{
    public class ExpenseCategoryListViewModel : ViewModelBase
    {
        private readonly IExpenseCategoryServiceClient _service;

        public ObservableCollection<ExpenseCategoryDto> Categories { get; set; } = new ObservableCollection<ExpenseCategoryDto>();

        public ExpenseCategoryListViewModel(IExpenseCategoryServiceClient service)
        {
            _service = service;
            AddCategoryCommand = new RelayCommand(OpenAddCategoryDialog);
            LoadCategoriesCommand = new RelayCommand(async () => await LoadCategoriesAsync());
            EditCommand = new RelayCommand<ExpenseCategoryDto>(EditCategory);
            DeleteCommand = new RelayCommand<ExpenseCategoryDto>(DeleteCategory);
            ExportToExcelCommand = new RelayCommand(ExportToExcel);
            // Optionally load on construction
            LoadCategoriesCommand.Execute(null);
        }

        private Window FindParentWindow()
        {
            // Find all windows
            foreach (Window window in System.Windows.Application.Current.Windows)
            {
                // Look for the window that contains a SaleView
                if (window.Content != null &&
                    VisualTreeHelper.GetChild(window.Content as DependencyObject, 0) is FrameworkElement content &&
                    window.Name == "ExpenseMainViewScreen")
                {
                    return window;
                }
            }
            return System.Windows.Application.Current.MainWindow;
        }

        public ICommand AddCategoryCommand { get; }
        public ICommand LoadCategoriesCommand { get; }
        public ICommand EditCommand { get; }
        public ICommand DeleteCommand { get; }
        public ICommand ExportToExcelCommand { get; }

        private void OpenAddCategoryDialog()
        {
            var addCategoryView = new AddExpenseCategoryView(_service); // Pass the service to the dialog
            addCategoryView.Owner = FindParentWindow();
            if (addCategoryView.ShowDialog() == true)
            {
                LoadCategoriesCommand.Execute(null);
            }
        }

        private async void EditCategory(ExpenseCategoryDto category)
        {
            try
            {
                var editCategoryView = new EditExpenseCategoryView(_service, category);
                editCategoryView.Owner = FindParentWindow();
                if (editCategoryView.ShowDialog() == true)
                {
                    await LoadCategoriesAsync();
                    Helpers.BootstrapMessageBoxHelper.Show("تم تحديث التصنيف بنجاح!", owner: FindParentWindow());
                }
            }
            catch (Exception ex)
            {
                Helpers.BootstrapMessageBoxHelper.Show($"خطأ في تحديث التصنيف: {ex.Message}", owner: FindParentWindow());
            }
        }

        private async void DeleteCategory(ExpenseCategoryDto category)
        {
            var result = System.Windows.MessageBox.Show(
                $"هل أنت متأكد من حذف التصنيف '{category.Name}'؟",
                "تأكيد الحذف",
                MessageBoxButton.YesNo,
                MessageBoxImage.Warning);

            if (result == MessageBoxResult.Yes)
            {
                try
                {
                    await _service.DeleteAsync(category.Id);
                    await LoadCategoriesAsync();
                    Helpers.BootstrapMessageBoxHelper.Show("تم حذف التصنيف بنجاح!", owner: FindParentWindow());
                }
                catch (Exception ex)
                {
                    Helpers.BootstrapMessageBoxHelper.Show($"خطأ في حذف التصنيف: {ex.Message}", owner: FindParentWindow());
                }
            }
        }

        private void ExportToExcel()
        {
            var saveFileDialog = new Microsoft.Win32.SaveFileDialog
            {
                Filter = "Excel Files (*.xlsx)|*.xlsx|CSV Files (*.csv)|*.csv",
                FileName = $"تصنيفات_المصروفات_{DateTime.Now:yyyyMMdd_HHmmss}.csv"
            };

            if (saveFileDialog.ShowDialog() == true)
            {
                try
                {
                    using (var writer = new StreamWriter(saveFileDialog.FileName, false, Encoding.UTF8))
                    {
                        // Write header (adjust columns as per your DataGrid)
                        writer.WriteLine("الاسم,الوصف");
                        foreach (var category in Categories)
                        {
                            writer.WriteLine($"{category.Name},{category.Description}");
                        }
                    }
                    Helpers.BootstrapMessageBoxHelper.Show(owner: FindParentWindow(), message: "تم تصدير البيانات بنجاح!");
                }
                catch (Exception ex)
                {
                    Helpers.BootstrapMessageBoxHelper.Show(owner: FindParentWindow(), message: $"حدث خطأ أثناء التصدير: {ex.Message}");
                }
            }
        }

        public async Task LoadExpenseCategories()
        {
            try
            {
                var categories = await _service.GetAllAsync();
                Categories.Clear(); // Clear existing items
                foreach (var category in categories)
                {
                    Categories.Add(category); // Add new items
                }
            }
            catch (Exception ex)
            {
                Helpers.BootstrapMessageBoxHelper.Show($"Error loading categories: {ex.Message}", owner: FindParentWindow());
            }
        }

        public async Task LoadCategoriesAsync()
        {
            Categories.Clear();
            var categories = await _service.GetAllAsync();
            foreach (var cat in categories)
                Categories.Add(cat);
        }
    }
}
