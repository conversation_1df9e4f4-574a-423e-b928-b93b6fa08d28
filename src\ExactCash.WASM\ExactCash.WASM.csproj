﻿<Project Sdk="Microsoft.NET.Sdk.BlazorWebAssembly">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.AspNetCore.Components.WebAssembly" Version="8.0.15" />
    <PackageReference Include="Microsoft.AspNetCore.Components.WebAssembly.DevServer" Version="8.0.15" PrivateAssets="all" />

	  <PackageReference Include="Microsoft.AspNetCore.Components.Authorization" Version="8.0.0" />
	  <PackageReference Include="Microsoft.Extensions.Http" Version="8.0.0" />

	  <PackageReference Include="Blazor.Bootstrap" Version="3.1.0" />
	  <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
	  <PackageReference Include="Radzen.Blazor" Version="5.3.5" />

	  <PackageReference Include="Microsoft.AspNetCore.Identity.EntityFrameworkCore" Version="8.0.10" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\ExactCash.Domain\ExactCash.Domain.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="DTOs\Reports\" />
  </ItemGroup>
</Project>
