using System.Windows.Media;
#nullable disable

namespace ExactCash.WPF.ViewModels.Common
{
    public class FooterStatusViewModel : ViewModelBase
    {
        private string _statusMessage;
        private string _connectionStatusText;
        private System.Windows.Media.Brush _connectionStatusColor;

        public FooterStatusViewModel()
        {
            // Initialize with default values
            StatusMessage = "مرحباً بك في نظام ExactCash";
            ConnectionStatusText = "متصل";
            ConnectionStatusColor = System.Windows.Media.Brushes.Green;
        }

        public string StatusMessage
        {
            get => _statusMessage;
            set => SetProperty(ref _statusMessage, value);
        }

        public string ConnectionStatusText
        {
            get => _connectionStatusText;
            set => SetProperty(ref _connectionStatusText, value);
        }

        public System.Windows.Media.Brush ConnectionStatusColor
        {
            get => _connectionStatusColor;
            set => SetProperty(ref _connectionStatusColor, value);
        }

        public void UpdateConnectionStatus(bool isConnected)
        {
            if (isConnected)
            {
                ConnectionStatusText = "متصل";
                ConnectionStatusColor = System.Windows.Media.Brushes.Green;
            }
            else
            {
                ConnectionStatusText = "غير متصل";
                ConnectionStatusColor = System.Windows.Media.Brushes.Red;
            }
        }

        public void SetStatusMessage(string message)
        {
            StatusMessage = message;
        }
    }
}