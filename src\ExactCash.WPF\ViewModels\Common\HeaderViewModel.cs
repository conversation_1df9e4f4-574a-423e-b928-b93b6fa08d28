using ExactCash.WPF.Commands;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using System.Windows;
using System.Windows.Input;
using System.Windows.Threading;
using ExactCash.WPF.ViewModels;
using ExactCash.WPF.Views.Auth;
using ExactCash.WPF.Services;
using ExactCash.WPF.ViewModels.Auth;
using AutoMapper;
using ExactCash.WPF.Services.Interfaces;
using Microsoft.Extensions.Configuration;

namespace ExactCash.WPF.ViewModels.Common
{
    public class HeaderViewModel : ViewModelBase
    {
        private readonly DispatcherTimer _timer;
        private readonly IApiUserService _apiService;
        private readonly LoadingViewModel _loadingViewModel;
        private readonly ICustomerService _customerService;
        private readonly Services.Product.IProductService _productService;
        private readonly ISaleServiceClient _saleServiceClient;
        private readonly IUnitServiceClient _unitServiceClient;
        private string _storeName;
        private string _transactionId;
        private string _userName;
        private string _userRole;
        private string _currentDateTime;
        private readonly IMapper _mapper;
        private readonly IBrandsServiceClient _brandsServiceClient;
        private readonly ICategoryServiceClient _categoryServiceClient;
        private readonly ISupplierServiceClient _supplierServiceClient;
        private readonly NotificationViewModel _notificationViewModel;
        private readonly IPurchaseServiceClient  _purchaseServiceClient;
        private readonly IUserServiceClient _userServiceClient;
        private readonly IRoleServiceClient _roleServiceClient;
        private readonly IConfiguration _configuration;
        private readonly IExpenseCategoryServiceClient _expenseCategoryServiceClient;
        private readonly IExpenseServiceClient _expenseServiceClient;
        private readonly ISystemConfigurationServiceClient _systemConfigurationServiceClient;
        private readonly IReportServiceClient _reportServiceClient;
        private readonly ISupplierCategoryServiceClient _supplierCategoryServiceClient;
        private readonly ICustomerCategoryServiceClient _customerCategoryServiceClient;
        public HeaderViewModel(
            IApiUserService apiService,
            LoadingViewModel loadingViewModel,
            ICustomerService customerService,
            Services.Product.IProductService productService,
            ISaleServiceClient saleServiceClient,
            IUnitServiceClient unitServiceClient, IMapper mapper,
            IBrandsServiceClient brandsServiceClient, ICategoryServiceClient categoryServiceClient,
            ISupplierServiceClient supplierServiceClient, NotificationViewModel notificationViewModel,
            IPurchaseServiceClient purchaseServiceClient, IUserServiceClient userServiceClient,
            IRoleServiceClient roleServiceClient,
            IConfiguration configuration,
            IExpenseCategoryServiceClient expenseCategoryServiceClient,
            IExpenseServiceClient expenseServiceClient, ISystemConfigurationServiceClient systemConfigurationServiceClient, IReportServiceClient reportServiceClient,
            ISupplierCategoryServiceClient supplierCategoryServiceClient, ICustomerCategoryServiceClient customerCategoryServiceClient)
        {
            _apiService = apiService;
            _loadingViewModel = loadingViewModel;
            _customerService = customerService;
            _saleServiceClient = saleServiceClient;
            LogoutCommand = new RelayCommand(ExecuteLogout);

            // Initialize with default values
            UserName = "مستخدم";
            UserRole = "مدير";
            StoreName = "متجر ExactCash";

            // Setup timer for date/time updates
            _timer = new DispatcherTimer();
            _timer.Interval = TimeSpan.FromSeconds(1);
            _timer.Tick += Timer_Tick;
            _timer.Start();
            _productService = productService;
            _unitServiceClient = unitServiceClient;
            _mapper = mapper;
            _brandsServiceClient = brandsServiceClient;
            _categoryServiceClient = categoryServiceClient;
            _supplierServiceClient = supplierServiceClient;
            _notificationViewModel = notificationViewModel;
            _purchaseServiceClient = purchaseServiceClient;
            _userServiceClient = userServiceClient;
            _roleServiceClient = roleServiceClient;
            _configuration = configuration;
            _expenseCategoryServiceClient = expenseCategoryServiceClient;
            _expenseServiceClient = expenseServiceClient;
            _systemConfigurationServiceClient = systemConfigurationServiceClient;
            _reportServiceClient = reportServiceClient;
            _supplierCategoryServiceClient = supplierCategoryServiceClient;
            _customerCategoryServiceClient = customerCategoryServiceClient;
        }

        public string StoreName
        {
            get => _storeName;
            set => SetProperty(ref _storeName, value);
        }

        public string TransactionId
        {
            get => _transactionId;
            set
            {
                _transactionId = value;
                OnPropertyChanged();
            }
        }

        public string UserName
        {
            get => _userName;
            set => SetProperty(ref _userName, value);
        }

        public string UserRole
        {
            get => _userRole;
            set => SetProperty(ref _userRole, value);
        }

        public string CurrentDateTime
        {
            get => _currentDateTime;
            set
            {
                _currentDateTime = value;
                OnPropertyChanged();
            }
        }

        public ICommand LogoutCommand { get; }

        private void Timer_Tick(object sender, EventArgs e)
        {
            CurrentDateTime = DateTime.Now.ToString("dd/MM/yyyy HH:mm:ss");
        }

        private void ExecuteLogout()
        {
            var result = System.Windows.MessageBox.Show("هل أنت متأكد من تسجيل الخروج؟", "تأكيد تسجيل الخروج",
                MessageBoxButton.YesNo, MessageBoxImage.Question);

            if (result == MessageBoxResult.Yes)
            {
                // Close current window
                System.Windows.Application.Current.MainWindow.Close();

                // Create LoginViewModel with required services
                var loginViewModel = new LoginViewModel(
                    _apiService, _loadingViewModel,
                    _customerService, _productService,
                    _saleServiceClient, _unitServiceClient,
                    _mapper, _categoryServiceClient, _brandsServiceClient,
                    _supplierServiceClient, _notificationViewModel,
                    _purchaseServiceClient, _userServiceClient, _roleServiceClient, _configuration,
                    _expenseCategoryServiceClient,
                    _expenseServiceClient,
                    _systemConfigurationServiceClient,
                    _reportServiceClient,
                    _supplierCategoryServiceClient,
                    _customerCategoryServiceClient
                    );

                // Create and show login window with the view model
                var loginWindow = new LoginView(loginViewModel);
                loginWindow.Show();
                System.Windows.Application.Current.MainWindow = loginWindow;
            }
        }

        public event PropertyChangedEventHandler PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }
}