﻿using ExactCash.Application.DTOs;
using ExactCash.WPF.Services.Interfaces;

namespace ExactCash.WPF.Services
{
    public class ExpenseCategoryServiceClient : IExpenseCategoryServiceClient
    {
        private readonly HttpService _httpService;
        private const string BaseUrl = "api/ExpenseCategory";

        public ExpenseCategoryServiceClient(string baseUrl)
        {
            _httpService = new HttpService(baseUrl);
        }

        public async Task<ExpenseCategoryDto> CreateAsync(ExpenseCategoryDto dto)
        {
            return await _httpService.PostAsync<ExpenseCategoryDto>($"{BaseUrl}", dto);
        }

        public async Task<bool> DeleteAsync(int id)
        {
            return await _httpService.DeleteAsync($"{BaseUrl}/{id}");
        }

        public async Task<IEnumerable<ExpenseCategoryDto>> GetAllAsync()
        {
            return await _httpService.GetAsync<IEnumerable<ExpenseCategoryDto>>($"{BaseUrl}");
        }

        public async Task<ExpenseCategoryDto> GetByIdAsync(int id)
        {
            return await _httpService.GetAsync<ExpenseCategoryDto>($"{BaseUrl}/{id}");
        }

        public async Task<bool> UpdateAsync(ExpenseCategoryDto dto)
        {
            // Usually PUT returns no content, so just check for success
            return await _httpService.PutAsync<bool>($"{BaseUrl}/{dto.Id}", dto);
        }

    }
}
