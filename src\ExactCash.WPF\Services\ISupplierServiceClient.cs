﻿using ExactCash.Application.DTOs.Common;
using ExactCash.Application.DTOs;
using ExactCash.Application.Responses;

namespace ExactCash.WPF.Services
{
    public interface ISupplierServiceClient
    {
        Task<SupplierDto> GetSupplierByIdAsync(int id);
        Task<PagedResponse<SupplierDto>> GetAllSuppliersAsync(string name, string phoneNumber, PaginationFilter pagination);
        Task<IEnumerable<SupplierDto>> GetAllSuppliersAsync(string name, string phoneNumber);
        Task<BaseResponse<SupplierDto>> CreateSupplierAsync(SupplierDto supplierDto);
        Task<BaseResponse<SupplierDto>> UpdateSupplierAsync(SupplierDto supplierDto);
        Task DeleteSupplierAsync(int id);

        Task<List<SupplierDto>> SearchSuppliersAsync(string searchTerm);
    }
}
