#nullable disable
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using System.Windows.Input;
using AutoMapper;
using ExactCash.Application.DTOs;
using ExactCash.Application.DTOs.Common;
using ExactCash.Application.Interfaces;
using ExactCash.WPF.Commands;
using ExactCash.WPF.Services;
using ExactCash.WPF.Services.Product;
using ExactCash.WPF.Views.Product;
using System.Windows;
using System.Threading.Tasks;
using System.Linq;

namespace ExactCash.WPF.ViewModels.Product
{
    public class ProductListViewModel : INotifyPropertyChanged
    {
        private readonly IProductService _productService;
        private readonly ICategoryServiceClient _categoryServiceClient;
        private readonly IBrandsServiceClient _brandsServiceClient;
        private readonly IMapper _mapper;
        private readonly IUnitServiceClient _unitServiceClient;
        private readonly IProductService _categoryService;
        private readonly IBrandService _brandService;

        private ObservableCollection<ProductDto> _products;
        private ObservableCollection<CategoryDto> _categories;
        private ObservableCollection<BrandDto> _brands;

        private string _nameSearch;
        private string _skuSearch;
        private string _barcodeSearch;
        private int? _categoryIdSearch;
        private int? _brandIdSearch;

        private int _currentPage = 1;
        private int _pageSize = 20;
        private int _totalPages;
        private int _totalItems;

        public ObservableCollection<ProductDto> Products
        {
            get => _products;
            set
            {
                _products = value;
                OnPropertyChanged();
            }
        }

        public ObservableCollection<CategoryDto> Categories
        {
            get => _categories;
            set
            {
                _categories = value;
                OnPropertyChanged();
            }
        }

        public ObservableCollection<BrandDto> Brands
        {
            get => _brands;
            set
            {
                _brands = value;
                OnPropertyChanged();
            }
        }

        public string NameSearch
        {
            get => _nameSearch;
            set
            {
                _nameSearch = value;
                OnPropertyChanged();
            }
        }

        public string SkuSearch
        {
            get => _skuSearch;
            set
            {
                _skuSearch = value;
                OnPropertyChanged();
            }
        }

        public string BarcodeSearch
        {
            get => _barcodeSearch;
            set
            {
                _barcodeSearch = value;
                OnPropertyChanged();
            }
        }

        public int? CategoryIdSearch
        {
            get => _categoryIdSearch;
            set
            {
                _categoryIdSearch = value;
                OnPropertyChanged();
            }
        }

        public int? BrandIdSearch
        {
            get => _brandIdSearch;
            set
            {
                _brandIdSearch = value;
                OnPropertyChanged();
            }
        }

        public int CurrentPage
        {
            get => _currentPage;
            set
            {
                _currentPage = value;
                OnPropertyChanged();
                OnPropertyChanged(nameof(PaginationInfo));
            }
        }

        public int PageSize
        {
            get => _pageSize;
            set
            {
                _pageSize = value;
                OnPropertyChanged();
            }
        }

        public int TotalPages
        {
            get => _totalPages;
            set
            {
                _totalPages = value;
                OnPropertyChanged();
                OnPropertyChanged(nameof(PaginationInfo));
            }
        }

        public int TotalItems
        {
            get => _totalItems;
            set
            {
                _totalItems = value;
                OnPropertyChanged();
                OnPropertyChanged(nameof(PaginationInfo));
            }
        }

        public string PaginationInfo => $"صفحة {CurrentPage} من {TotalPages} (إجمالي العناصر: {TotalItems})";

        public ICommand SearchCommand { get; }
        public ICommand AddNewProductCommand { get; }
        public ICommand RefreshCommand { get; }
        public ICommand PreviousPageCommand { get; }
        public ICommand NextPageCommand { get; }
        public ICommand ResetSearchCommand { get; }
        public ICommand PrintBarcodeCommand { get; }
        public ICommand EditProductCommand { get; }
        public ICommand DeleteProductCommand { get; }

        public ProductListViewModel(
            IProductService productService,
            ICategoryServiceClient categoryServiceClient,
            IBrandsServiceClient brandsServiceClient,
            IMapper mapper,
            IUnitServiceClient unitServiceClient)
        {
            _productService = productService;
            _categoryServiceClient = categoryServiceClient;
            _brandsServiceClient = brandsServiceClient;
            _mapper = mapper;
            _unitServiceClient = unitServiceClient;

            Products = new ObservableCollection<ProductDto>();
            Categories = new ObservableCollection<CategoryDto>();
            Brands = new ObservableCollection<BrandDto>();

            SearchCommand = new RelayCommand(ExecuteSearch);
            AddNewProductCommand = new RelayCommand(ExecuteAddNewProduct);
            RefreshCommand = new RelayCommand(ExecuteRefresh);
            PreviousPageCommand = new RelayCommand(ExecutePreviousPage, CanExecutePreviousPage);
            NextPageCommand = new RelayCommand(ExecuteNextPage, CanExecuteNextPage);
            ResetSearchCommand = new RelayCommand(ExecuteResetSearch);
            PrintBarcodeCommand = new RelayCommand<ProductDto>(ExecutePrintBarcode);
            EditProductCommand = new RelayCommand<ProductDto>(ExecuteEditProduct);
            DeleteProductCommand = new RelayCommand<ProductDto>(ExecuteDeleteProduct);

            LoadData();
        }

        private async void LoadData()
        {
            try
            {
                var categories = await _categoryServiceClient.GetAllAsync();
                var brands = await _brandsServiceClient.GetAllBrandsAsync();

                Categories.Clear();
                foreach (var category in categories)
                {
                    Categories.Add(category);
                }

                Brands.Clear();
                foreach (var brand in brands)
                {
                    Brands.Add(brand);
                }

                ExecuteSearch();
            }
            catch (Exception ex)
            {
                Helpers.BootstrapMessageBoxHelper.ShowError($"حدث خطأ أثناء تحميل البيانات: {ex.Message}", "خطأ");
            }
        }

        private async void ExecuteSearch()
        {
            try
            {
                var filter = new PaginationFilter
                {
                    PageNumber = CurrentPage,
                    PageSize = PageSize
                };

                var result = await _productService.GetAllAsync(NameSearch, SkuSearch, CategoryIdSearch, BrandIdSearch, null, BarcodeSearch, filter);

                Products.Clear();
                foreach (var product in result.Data)
                {
                    Products.Add(product);
                }

                TotalPages = result.TotalPages;
                TotalItems = result.TotalItems;
            }
            catch (Exception ex)
            {
                Helpers.BootstrapMessageBoxHelper.ShowError($"حدث خطأ أثناء البحث: {ex.Message}", "خطأ");
            }
        }

        private void ExecuteAddNewProduct()
        {
            var addProductWindow = new AddProductView(_productService, _mapper, _categoryServiceClient, _brandsServiceClient, _unitServiceClient);
            if (addProductWindow.ShowDialog() == true)
            {
                ExecuteSearch();
            }
        }

        private void ExecuteRefresh()
        {
            LoadData();
        }

        private bool CanExecutePreviousPage()
        {
            return CurrentPage > 1;
        }

        private void ExecutePreviousPage()
        {
            if (CurrentPage > 1)
            {
                CurrentPage--;
                ExecuteSearch();
            }
        }

        private bool CanExecuteNextPage()
        {
            return CurrentPage < TotalPages;
        }

        private void ExecuteNextPage()
        {
            if (CurrentPage < TotalPages)
            {
                CurrentPage++;
                ExecuteSearch();
            }
        }

        private void ExecuteResetSearch()
        {
            // Reset all search criteria
            NameSearch = string.Empty;
            SkuSearch = string.Empty;
            BarcodeSearch = string.Empty;
            CategoryIdSearch = null;
            BrandIdSearch = null;

            // Reset to first page
            CurrentPage = 1;

            // Execute search with cleared criteria
            ExecuteSearch();
        }

        private async void ExecutePrintBarcode(ProductDto product)
        {
            if (product == null) return;

            try
            {
                // Generate barcode image
                var barcodeImageBytes = await _productService.GenerateBarcodeImageAsync(product.Id);
                if (barcodeImageBytes == null || barcodeImageBytes.Length == 0)
                {
                    Helpers.BootstrapMessageBoxHelper.ShowError("فشل في إنشاء صورة الباركود", "خطأ");
                    return;
                }

                // Print the barcode
                await Helpers.BarcodeHelper.PrintBarcode(product, barcodeImageBytes);
            }
            catch (Exception ex)
            {
                Helpers.BootstrapMessageBoxHelper.ShowError($"خطأ: {ex.Message}", "خطأ");
            }
        }

        private void ExecuteEditProduct(ProductDto product)
        {
            if (product == null) return;

            try
            {
                var editProductView = new Views.Product.AddProductView(
                    _productService,
                    _mapper,
                    _categoryServiceClient,
                    _brandsServiceClient,
                    _unitServiceClient)
                {
                    Owner = System.Windows.Application.Current.Windows.OfType<Window>().FirstOrDefault(w => w.IsActive)
                };

                // Set the product to edit
                if (editProductView.DataContext is AddProductViewModel viewModel)
                {
                    viewModel.LoadProduct(product);
                }

                var result = editProductView.ShowDialog();
                if (result == true)
                {
                    // Refresh the product list
                    ExecuteRefresh();
                }
            }
            catch (Exception ex)
            {
                System.Windows.MessageBox.Show($"Error editing product: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void ExecuteDeleteProduct(ProductDto product)
        {
            if (product == null) return;

            var result = Helpers.BootstrapMessageBoxHelper.Show(
                $"هل أنت متأكد من حذف المنتج '{product.Name}'؟",
                "تأكيد الحذف");

            if (result == true)
            {
                try
                {
                    await _productService.DeleteProductAsync(product.Id);
                    ExecuteRefresh();
                }
                catch (Exception ex)
                {
                    System.Windows.MessageBox.Show($"Error deleting product: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        public event PropertyChangedEventHandler PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }
}