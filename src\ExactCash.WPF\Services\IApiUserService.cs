using ExactCash.Application.DTOs;

namespace ExactCash.WPF.Services
{
    public interface IApiUserService
    {
        Task<AuthResponseDto> LoginAsync(string username, string password);
        Task<AuthResponseDto> RefreshTokenAsync(string token, string refreshToken);
        Task<bool> LogoutAsync();
        Task<UserDto> GetCurrentUserAsync();
        Task<bool> UpdateUserProfileAsync(UpdateUserDto userDto);
        Task<bool> ChangePasswordAsync(string currentPassword, string newPassword);
        Task<bool> ResetPasswordAsync(string email);
        Task<bool> VerifyEmailAsync(string token);
        void SetToken(string token);
    }
}