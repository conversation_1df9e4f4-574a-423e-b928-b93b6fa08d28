using FDIN.Web.Data;
using FDIN.Web.Models;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System.Diagnostics;
using System.Threading.Tasks;

namespace FDIN.Web.Controllers
{
    public class HomeController : Controller
    {
        private readonly ILogger<HomeController> _logger;
        private readonly FDINDbContext _dbContext;
        public HomeController(ILogger<HomeController> logger, FDINDbContext dbContext)
        {
            _logger = logger;
            _dbContext = dbContext;
        }


        [HttpPost]
        public async Task<IActionResult> Login(string MobileNumber)
        {
            // Basic validation
            if (string.IsNullOrWhiteSpace(MobileNumber) || MobileNumber.Length < 10 || MobileNumber.Length > 15)
            {
                ViewBag.Error = "Please enter a valid mobile number (10-15 digits).";
                return View("Index");
            }

            // Try to find the user by mobile number
            var user = await _dbContext.Users
                .FirstOrDefaultAsync(x => x.Phone == MobileNumber);

            if (user != null)
            {
                // Redirect to UserInvoices/Index with userId as a route parameter
                return RedirectToAction("Index", "UserInvoices", new { userId = user.Id });
            }
            else
            {
                ViewBag.Error = "Mobile number not found or not authorized.";
                return View("Index");
            }
        }

        // Example dashboard action
        public IActionResult Dashboard()
        {
            return View();
        }


        public IActionResult Index()
        {
            return View();
        }

        public IActionResult Privacy()
        {
            return View();
        }

        [ResponseCache(Duration = 0, Location = ResponseCacheLocation.None, NoStore = true)]
        public IActionResult Error()
        {
            return View(new ErrorViewModel { RequestId = Activity.Current?.Id ?? HttpContext.TraceIdentifier });
        }
    }
}
