﻿using ExactCash.Application.DTOs;
using ExactCash.Application.Interfaces;
using ExactCash.Infrastructure.Persistence;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace ExactCash.Application.Services
{
    public class ReportService : IReportService
    {
        private readonly AppPostgreSQLDbContext _context;
        private readonly ILogger<ReportService> _logger;

        public ReportService(AppPostgreSQLDbContext context, ILogger<ReportService> logger)
        {
            _context = context;
            _logger = logger;
        }

        #region Inventory Reports

        public async Task<List<InventoryReportDto>> GetInventoryReport()
        {
            try
            {
                var query = from product in _context.Products
                            join category in _context.Categories on product.CategoryId equals category.Id into categoryGroup
                            from category in categoryGroup.DefaultIfEmpty()
                            join brand in _context.Brands on product.BrandId equals brand.Id into brandGroup
                            from brand in brandGroup.DefaultIfEmpty()
                            join unit in _context.Units on product.UnitId equals unit.Id into unitGroup
                            from unit in unitGroup.DefaultIfEmpty()
                            select new InventoryReportDto
                            {
                                ProductId = product.Id,
                                ProductName = product.Name,
                                ProductSKU = product.SKU,
                                Barcode = product.Barcode,
                                CategoryName = category != null ? category.Name : "غير محدد",
                                BrandName = brand != null ? brand.Name : "غير محدد",
                                UnitName = unit != null ? unit.Name : "غير محدد",
                                CurrentStock = (int)product.StockQuantity,
                                MinStock = product.MinStock,
                                CostPrice = product.CostPrice,
                                SellingPrice = product.SellingPrice,
                                StockValue = product.StockQuantity * product.CostPrice,
                                StockStatus = product.StockQuantity == 0 ? "نفد المخزون" :
                                            product.StockQuantity <= product.MinStock ? "مخزون منخفض" : "مخزون جيد",
                                LastStockUpdate = product.LastUpdatedDate,
                                DaysWithoutMovement = (DateTime.UtcNow - product.LastUpdatedDate).Days,
                                ProfitMargin = product.CostPrice > 0 ? ((product.SellingPrice - product.CostPrice) / product.CostPrice) * 100 : 0,
                                IsActive = product.IsActive
                            };

                return await query.OrderBy(x => x.ProductName).ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error getting inventory report: {ex.Message}");
                return new List<InventoryReportDto>();
            }
        }

        public async Task<List<LowStockReportDto>> GetLowStockReport()
        {
            try
            {
                var query = from product in _context.Products
                            join category in _context.Categories on product.CategoryId equals category.Id into categoryGroup
                            from category in categoryGroup.DefaultIfEmpty()
                            where product.StockQuantity <= product.MinStock && product.IsActive
                            select new LowStockReportDto
                            {
                                ProductId = product.Id,
                                ProductName = product.Name,
                                ProductSKU = product.SKU,
                                CategoryName = category != null ? category.Name : "غير محدد",
                                CurrentStock = (int)product.StockQuantity,
                                MinStock = product.MinStock,
                                StockDeficit = product.MinStock - (int)product.StockQuantity,
                                ReorderValue = (product.MinStock - product.StockQuantity) * product.CostPrice,
                                SupplierName = "غير محدد", // You can join with supplier if you have that relationship
                                DaysOutOfStock = product.StockQuantity == 0 ? (DateTime.UtcNow - product.LastUpdatedDate).Days : 0,
                                LostSalesEstimate = product.StockQuantity == 0 ? (DateTime.UtcNow - product.LastUpdatedDate).Days * product.SellingPrice : 0
                            };

                return await query.OrderBy(x => x.CurrentStock).ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error getting low stock report: {ex.Message}");
                return new List<LowStockReportDto>();
            }
        }

        public async Task<List<StockMovementReportDto>> GetStockMovementReport(DateTime? startDate, DateTime? endDate)
        {
            try
            {
                var query = from movement in _context.StockMovements
                            join product in _context.Products on movement.ProductId equals product.Id
                            where movement.CreationDate >= startDate && movement.CreationDate <= endDate
                            select new StockMovementReportDto
                            {
                                MovementId = movement.Id,
                                MovementDate = movement.CreationDate,
                                ProductName = product.Name,
                                ProductSKU = product.SKU,
                                Quantity = movement.Quantity,
                                MovementType = movement.Type,
                                Reference = movement.Reference,
                                CreatedBy = movement.CreatedBy ?? "النظام",
                                StockBefore = 0, // You might need to calculate this
                                StockAfter = (int)product.StockQuantity,
                                Reason = movement.Reference
                            };

                return await query.OrderByDescending(x => x.MovementDate).ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error getting stock movement report: {ex.Message}");
                return new List<StockMovementReportDto>();
            }
        }

        public async Task<InventorySummaryDto> GetInventorySummary()
        {
            try
            {
                var products = await _context.Products.ToListAsync();
                var categories = await _context.Categories.CountAsync();
                var brands = await _context.Brands.CountAsync();

                return new InventorySummaryDto
                {
                    TotalProducts = products.Count,
                    ActiveProducts = products.Count(p => p.IsActive),
                    InactiveProducts = products.Count(p => !p.IsActive),
                    LowStockProducts = products.Count(p => p.StockQuantity <= p.MinStock && p.IsActive),
                    OutOfStockProducts = products.Count(p => p.StockQuantity == 0 && p.IsActive),
                    TotalInventoryValue = products.Sum(p => p.StockQuantity * p.SellingPrice),
                    TotalCostValue = products.Sum(p => p.StockQuantity * p.CostPrice),
                    TotalProfitPotential = products.Sum(p => p.StockQuantity * (p.SellingPrice - p.CostPrice)),
                    TotalCategories = categories,
                    TotalBrands = brands,
                    AverageStockLevel = products.Any() ? products.Average(p => p.StockQuantity) : 0,
                    InventoryTurnoverRatio = 0, // Calculate based on COGS and average inventory
                    DaysOfInventoryOnHand = 0 // Calculate based on average daily sales
                };
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error getting inventory summary: {ex.Message}");
                return new InventorySummaryDto();
            }
        }

        public async Task<List<PaymentMethodReportDto>> GetPaymentMethodReport(DateTime? startDate, DateTime? endDate)
        {
            try
            {
                var payments = await _context.Payments
                    .Include(p => p.Sale)
                    .Where(p => p.Sale.CreationDate >= startDate && p.Sale.CreationDate <= endDate)
                    .GroupBy(p => p.PaymentMethod)
                    .Select(g => new PaymentMethodReportDto
                    {
                        PaymentMethod = g.Key,
                        TransactionCount = g.Count(),
                        TotalAmount = g.Sum(p => p.Amount),
                        AverageTransactionValue = g.Average(p => p.Amount),
                        ReportStartDate = startDate ?? DateTime.MinValue,
                        ReportEndDate = endDate ?? DateTime.MaxValue
                    })
                    .ToListAsync();

                var totalAmount = payments.Sum(p => p.TotalAmount);
                foreach (var payment in payments)
                {
                    payment.Percentage = totalAmount > 0 ? (payment.TotalAmount / totalAmount) * 100 : 0;
                }

                return payments.OrderByDescending(p => p.TotalAmount).ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error getting payment method report: {ex.Message}");
                return new List<PaymentMethodReportDto>();
            }
        }

        public async Task<List<TaxReportDto>> GetTaxReport(DateTime? startDate, DateTime? endDate)
        {
            try
            {
                var sales = await _context.Sales
                    .Where(s => s.CreationDate >= startDate && s.CreationDate <= endDate)
                    .ToListAsync();

                var taxableSales = sales.Where(s => s.TaxAmount > 0).ToList();
                var nonTaxableSales = sales.Where(s => s.TaxAmount == 0).ToList();

                return new List<TaxReportDto>
                {
                    new TaxReportDto
                    {
                        ReportDate = DateTime.UtcNow,
                        TaxableAmount = taxableSales.Sum(s => s.NetAmount),
                        TaxAmount = taxableSales.Sum(s => s.TaxAmount),
                        TaxRate = taxableSales.Any() ? (taxableSales.Sum(s => s.TaxAmount) / taxableSales.Sum(s => s.NetAmount)) * 100 : 0,
                        TaxableTransactions = taxableSales.Count,
                        NonTaxableAmount = nonTaxableSales.Sum(s => s.TotalAmount),
                        NonTaxableTransactions = nonTaxableSales.Count,
                        TotalSales = sales.Sum(s => s.TotalAmount),
                        TaxPercentageOfSales = sales.Sum(s => s.TotalAmount) > 0 ? (sales.Sum(s => s.TaxAmount) / sales.Sum(s => s.TotalAmount)) * 100 : 0
                    }
                };
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error getting tax report: {ex.Message}");
                return new List<TaxReportDto>();
            }
        }

        public async Task<List<ExpenseReportDto>> GetExpenseReport(DateTime? startDate, DateTime? endDate)
        {
            try
            {
                var query = from expense in _context.Expenses
                            join category in _context.ExpenseCategories on expense.CategoryId equals category.Id into categoryGroup
                            from category in categoryGroup.DefaultIfEmpty()
                            where expense.CreationDate >= startDate && expense.CreationDate <= endDate
                            select new ExpenseReportDto
                            {
                                ExpenseId = expense.Id,
                                ExpenseDate = expense.CreationDate,
                                CategoryName = category != null ? category.Name : "غير محدد",
                                Description = expense.Description,
                                Amount = expense.Amount,
                                PaymentMethod = "غير محدد", // You can add payment method lookup if needed
                                CreatedBy = expense.CreatedBy ?? "غير محدد",
                                Reference = expense.ReferenceNumber ?? ""
                            };

                return await query.OrderByDescending(x => x.ExpenseDate).ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error getting expense report: {ex.Message}");
                return new List<ExpenseReportDto>();
            }
        }

        public async Task<List<ExpenseSummaryDto>> GetExpenseSummaryReport(DateTime? startDate, DateTime? endDate)
        {
            try
            {
                var query = from expense in _context.Expenses
                            join category in _context.ExpenseCategories on expense.CategoryId equals category.Id into categoryGroup
                            from category in categoryGroup.DefaultIfEmpty()
                            where expense.CreationDate >= startDate && expense.CreationDate <= endDate
                            group expense by category.Name into grouped
                            select new ExpenseSummaryDto
                            {
                                CategoryName = grouped.Key ?? "غير محدد",
                                TotalAmount = grouped.Sum(e => e.Amount),
                                TransactionCount = grouped.Count(),
                                AverageExpense = grouped.Average(e => e.Amount),
                                ReportStartDate = startDate ?? DateTime.MinValue,
                                ReportEndDate = endDate ?? DateTime.MaxValue
                            };

                var result = await query.ToListAsync();
                var totalAmount = result.Sum(r => r.TotalAmount);

                foreach (var item in result)
                {
                    item.Percentage = totalAmount > 0 ? (item.TotalAmount / totalAmount) * 100 : 0;
                }

                return result.OrderByDescending(x => x.TotalAmount).ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error getting expense summary report: {ex.Message}");
                return new List<ExpenseSummaryDto>();
            }
        }

        #endregion

        #region Financial Reports

        public async Task<ProfitLossReportDto> GetProfitLossReport(DateTime? startDate, DateTime? endDate)
        {
            try
            {
                var sales = await _context.Sales
                    .Where(s => s.CreationDate >= startDate && s.CreationDate <= endDate)
                    .Include(s => s.SaleItems)
                    .ToListAsync();

                var expenses = await _context.Expenses
                    .Where(e => e.CreationDate >= startDate && e.CreationDate <= endDate)
                    .ToListAsync();

                var totalRevenue = sales.Sum(s => s.TotalAmount);
                var totalCOGS = sales.SelectMany(s => s.SaleItems)
                    .Join(_context.Products, si => si.ProductId, p => p.Id, (si, p) => si.Quantity * p.CostPrice)
                    .Sum();
                var totalExpenses = expenses.Sum(e => e.Amount);
                var grossProfit = totalRevenue - totalCOGS;
                var netProfit = grossProfit - totalExpenses;

                return new ProfitLossReportDto
                {
                    ReportDate = DateTime.UtcNow,
                    TotalRevenue = totalRevenue,
                    TotalCostOfGoodsSold = totalCOGS,
                    GrossProfit = grossProfit,
                    TotalExpenses = totalExpenses,
                    NetProfit = netProfit,
                    GrossProfitMargin = totalRevenue > 0 ? (grossProfit / totalRevenue) * 100 : 0,
                    NetProfitMargin = totalRevenue > 0 ? (netProfit / totalRevenue) * 100 : 0,
                    TotalTax = sales.Sum(s => s.TaxAmount),
                    TotalDiscounts = sales.Sum(s => s.DiscountAmount),
                    TotalTransactions = sales.Count,
                    AverageTransactionValue = sales.Any() ? sales.Average(s => s.TotalAmount) : 0
                };
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error getting profit loss report: {ex.Message}");
                return new ProfitLossReportDto();
            }
        }

        public async Task<CashFlowReportDto> GetCashFlowReport(DateTime? startDate, DateTime? endDate)
        {
            try
            {
                var cashSales = await _context.Sales
                    .Where(s => s.CreationDate >= startDate && s.CreationDate <= endDate)
                    .Include(s => s.Payments)
                    .Where(s => s.Payments.Any(p => p.PaymentMethod == "نقدي"))
                    .SumAsync(s => s.PaidAmount);

                var creditSales = await _context.Sales
                    .Where(s => s.CreationDate >= startDate && s.CreationDate <= endDate)
                    .Include(s => s.Payments)
                    .Where(s => s.Payments.Any(p => p.PaymentMethod == "آجل"))
                    .SumAsync(s => s.TotalAmount);

                var cashExpenses = await _context.Expenses
                    .Where(e => e.CreationDate >= startDate && e.CreationDate <= endDate)
                    .SumAsync(e => e.Amount);

                return new CashFlowReportDto
                {
                    ReportDate = DateTime.UtcNow,
                    OpeningBalance = 0, // You might want to track this separately
                    CashSales = cashSales,
                    CashReceipts = cashSales,
                    CashExpenses = cashExpenses,
                    CashWithdrawals = 0, // Track separately if needed
                    ClosingBalance = cashSales - cashExpenses,
                    NetCashFlow = cashSales - cashExpenses,
                    CashTransactions = await _context.Sales
                        .Where(s => s.CreationDate >= startDate && s.CreationDate <= endDate)
                        .Include(s => s.Payments)
                        .CountAsync(s => s.Payments.Any(p => p.PaymentMethod == "نقدي")),
                    CreditTransactions = await _context.Sales
                        .Where(s => s.CreationDate >= startDate && s.CreationDate <= endDate)
                        .Include(s => s.Payments)
                        .CountAsync(s => s.Payments.Any(p => p.PaymentMethod == "آجل")),
                    CreditSales = creditSales,
                    AccountsReceivable = await _context.Sales
                        .Where(s => s.RemainingAmount > 0)
                        .SumAsync(s => s.RemainingAmount)
                };
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error getting cash flow report: {ex.Message}");
                return new CashFlowReportDto();
            }
        }

        #endregion

        #region Customer Reports

        public async Task<List<CustomerAnalysisReportDto>> GetCustomerAnalysisReport(DateTime? startDate, DateTime? endDate)
        {
            try
            {
                var query = from customer in _context.Customers
                            join sale in _context.Sales on customer.Id equals sale.CustomerId into salesGroup
                            from sales in salesGroup.DefaultIfEmpty()
                            where sales == null || (sales.CreationDate >= startDate && sales.CreationDate <= endDate)
                            group sales by new { customer.Id, customer.FullName, customer.Phone, customer.Email } into grouped
                            let totalSpent = grouped.Where(s => s != null).Sum(s => s.TotalAmount)
                            let totalPurchases = grouped.Count(s => s != null)
                            let firstPurchase = grouped.Where(s => s != null).Min(s => (DateTime?)s.CreationDate)
                            let lastPurchase = grouped.Where(s => s != null).Max(s => (DateTime?)s.CreationDate)
                            select new CustomerAnalysisReportDto
                            {
                                CustomerId = grouped.Key.Id,
                                CustomerName = grouped.Key.FullName,
                                Phone = grouped.Key.Phone,
                                Email = grouped.Key.Email,
                                TotalPurchases = totalPurchases,
                                TotalSpent = totalSpent,
                                AverageOrderValue = totalPurchases > 0 ? totalSpent / totalPurchases : 0,
                                FirstPurchase = firstPurchase ?? DateTime.MinValue,
                                LastPurchase = lastPurchase ?? DateTime.MinValue,
                                DaysSinceLastPurchase = lastPurchase.HasValue ? (DateTime.UtcNow - lastPurchase.Value).Days : 0,
                                CustomerSegment = totalSpent > 10000 ? "VIP" : totalSpent > 5000 ? "عميل مميز" : totalSpent > 1000 ? "عميل عادي" : "عميل جديد",
                                OutstandingBalance = grouped.Where(s => s != null).Sum(s => s.RemainingAmount),
                                TotalItems = grouped.Where(s => s != null).Sum(s => s.SaleItems.Sum(si => (int)si.Quantity)),
                                LifetimeValue = totalSpent
                            };

                return await query.Where(c => c.TotalPurchases > 0).OrderByDescending(c => c.TotalSpent).ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error getting customer analysis report: {ex.Message}");
                return new List<CustomerAnalysisReportDto>();
            }
        }

        public async Task<List<TopCustomersReportDto>> GetTopCustomersReport(DateTime? startDate, DateTime? endDate, int topCount = 10)
        {
            try
            {
                var query = from customer in _context.Customers
                            join sale in _context.Sales on customer.Id equals sale.CustomerId
                            where sale.CreationDate >= startDate && sale.CreationDate <= endDate
                            group sale by new { customer.Id, customer.FullName } into grouped
                            let totalSpent = grouped.Sum(s => s.TotalAmount)
                            select new TopCustomersReportDto
                            {
                                CustomerId = grouped.Key.Id,
                                CustomerName = grouped.Key.FullName,
                                TotalSpent = totalSpent,
                                TotalOrders = grouped.Count(),
                                AverageOrderValue = grouped.Average(s => s.TotalAmount),
                                LastPurchase = grouped.Max(s => s.CreationDate)
                            };

                var result = await query.OrderByDescending(c => c.TotalSpent).Take(topCount).ToListAsync();
                var totalSales = await _context.Sales
                    .Where(s => s.CreationDate >= startDate && s.CreationDate <= endDate)
                    .SumAsync(s => s.TotalAmount);

                for (int i = 0; i < result.Count(); i++)
                {
                    result[i].Rank = i + 1;
                    result[i].PercentageOfTotalSales = totalSales > 0 ? (result[i].TotalSpent / totalSales) * 100 : 0;
                }

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error getting top customers report: {ex.Message}");
                return new List<TopCustomersReportDto>();
            }
        }

        public async Task<List<CustomerPurchaseHistoryDto>> GetCustomerPurchaseHistory(int customerId, DateTime? startDate, DateTime? endDate)
        {
            try
            {
                var query = from sale in _context.Sales
                            where sale.CustomerId == customerId &&
                                  sale.CreationDate >= startDate && sale.CreationDate <= endDate
                            select new CustomerPurchaseHistoryDto
                            {
                                SaleId = sale.Id,
                                PurchaseDate = sale.CreationDate,
                                InvoiceNumber = sale.InvoiceNumber,
                                TotalAmount = sale.TotalAmount,
                                PaidAmount = sale.PaidAmount,
                                RemainingAmount = sale.RemainingAmount,
                                PaymentStatus = sale.RemainingAmount > 0 ? "مستحق جزئياً" : "مدفوع بالكامل",
                                ItemCount = sale.SaleItems.Count,
                                CashierName = sale.CreatedBy ?? "غير محدد"
                            };

                return await query.OrderByDescending(s => s.PurchaseDate).ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error getting customer purchase history: {ex.Message}");
                return new List<CustomerPurchaseHistoryDto>();
            }
        }

        public async Task<CustomerAccountStatementDto> GetCustomerAccountStatement(int customerId, DateTime? startDate, DateTime? endDate)
        {
            try
            {
                // Get customer information
                var customer = await _context.Customers.FindAsync(customerId);
                if (customer == null)
                {
                    return null;
                }

                var statement = new CustomerAccountStatementDto
                {
                    CustomerId = customerId,
                    CustomerName = customer.FullName,
                    Phone = customer.Phone,
                    Email = customer.Email,
                    Address = customer.Address,
                    StatementDate = DateTime.Now,
                    FromDate = startDate ?? DateTime.Today.AddMonths(-1),
                    ToDate = endDate ?? DateTime.Today
                };

                // Calculate opening balance (transactions before start date)
                var openingBalance = await CalculateCustomerBalance(customerId, null, startDate?.AddDays(-1));
                statement.OpeningBalance = openingBalance;

                // Get all transactions in the date range
                var transactions = new List<CustomerAccountTransactionDto>();
                var runningBalance = openingBalance;

                // Get sales transactions
                var sales = await _context.Sales
                    .Where(s => s.CustomerId == customerId &&
                               s.CreationDate >= startDate &&
                               s.CreationDate <= endDate)
                    .OrderBy(s => s.CreationDate)
                    .ToListAsync();

                foreach (var sale in sales)
                {
                    runningBalance += sale.TotalAmount;
                    transactions.Add(new CustomerAccountTransactionDto
                    {
                        TransactionDate = sale.CreationDate,
                        TransactionType = "مبيعات",
                        ReferenceNumber = sale.InvoiceNumber,
                        Description = $"فاتورة مبيعات - {sale.SaleItems?.Count ?? 0} عنصر",
                        DebitAmount = sale.TotalAmount,
                        CreditAmount = 0,
                        RunningBalance = runningBalance
                    });
                }

                // Get payment transactions
                var payments = await _context.Payments
                    .Where(p => p.Sale.CustomerId == customerId &&
                               p.CreationDate >= startDate &&
                               p.CreationDate <= endDate)
                    .OrderBy(p => p.CreationDate)
                    .ToListAsync();

                foreach (var payment in payments)
                {
                    runningBalance -= payment.Amount;
                    transactions.Add(new CustomerAccountTransactionDto
                    {
                        TransactionDate = payment.CreationDate,
                        TransactionType = "دفعة",
                        ReferenceNumber = $"PAY-{payment.Id}",
                        Description = $"دفعة - {payment.PaymentMethod ?? "نقدي"}",
                        DebitAmount = 0,
                        CreditAmount = payment.Amount,
                        RunningBalance = runningBalance
                    });
                }

                // Sort all transactions by date
                statement.Transactions = transactions.OrderBy(t => t.TransactionDate).ToList();

                // Calculate summary totals
                statement.TotalSales = transactions.Where(t => t.TransactionType == "مبيعات").Sum(t => t.DebitAmount);
                statement.TotalPayments = transactions.Where(t => t.TransactionType == "دفعة").Sum(t => t.CreditAmount);
                statement.TotalReturns = transactions.Where(t => t.TransactionType == "مرتجع").Sum(t => t.CreditAmount);
                statement.ClosingBalance = runningBalance;
                statement.OutstandingBalance = Math.Max(0, runningBalance); // Only positive balances are outstanding

                return statement;
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error getting customer account statement: {ex.Message}");
                return null;
            }
        }

        private async Task<decimal> CalculateCustomerBalance(int customerId, DateTime? fromDate, DateTime? toDate)
        {
            try
            {
                var salesQuery = _context.Sales.Where(s => s.CustomerId == customerId);
                var paymentsQuery = _context.Payments.Where(p => p.Sale.CustomerId == customerId);

                if (fromDate.HasValue)
                {
                    salesQuery = salesQuery.Where(s => s.CreationDate >= fromDate);
                    paymentsQuery = paymentsQuery.Where(p => p.CreationDate >= fromDate);
                }

                if (toDate.HasValue)
                {
                    salesQuery = salesQuery.Where(s => s.CreationDate <= toDate);
                    paymentsQuery = paymentsQuery.Where(p => p.CreationDate <= toDate);
                }

                var totalSales = await salesQuery.SumAsync(s => s.TotalAmount);
                var totalPayments = await paymentsQuery.SumAsync(p => p.Amount);

                return totalSales - totalPayments;
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error calculating customer balance: {ex.Message}");
                return 0;
            }
        }

        #endregion

        #region Performance Reports

        public async Task<List<HourlySalesReportDto>> GetHourlySalesReport(DateTime? date)
        {
            try
            {
                var targetDate = date ?? DateTime.Today;
                var sales = await _context.Sales
                    .Where(s => s.CreationDate.Date == targetDate.Date)
                    .ToListAsync();

                var hourlySales = sales
                    .GroupBy(s => s.CreationDate.Hour)
                    .Select(g => new HourlySalesReportDto
                    {
                        Hour = g.Key,
                        TimeSlot = $"{g.Key:D2}:00 - {g.Key + 1:D2}:00",
                        TotalSales = g.Sum(s => s.TotalAmount),
                        TransactionCount = g.Count(),
                        AverageTransactionValue = g.Average(s => s.TotalAmount),
                        ItemsSold = g.Sum(s => s.SaleItems.Sum(si => (int)si.Quantity)),
                        ReportDate = targetDate
                    })
                    .OrderBy(h => h.Hour)
                    .ToList();

                return hourlySales;
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error getting hourly sales report: {ex.Message}");
                return new List<HourlySalesReportDto>();
            }
        }

        public async Task<List<CashierPerformanceReportDto>> GetCashierPerformanceReport(DateTime? startDate, DateTime? endDate)
        {
            try
            {
                var query = from sale in _context.Sales
                            where sale.CreationDate >= startDate && sale.CreationDate <= endDate
                            group sale by sale.CreatedBy into grouped
                            select new CashierPerformanceReportDto
                            {
                                CashierName = grouped.Key ?? "غير محدد",
                                TotalSales = grouped.Sum(s => s.TotalAmount),
                                TransactionCount = grouped.Count(),
                                AverageTransactionValue = grouped.Average(s => s.TotalAmount),
                                ItemsSold = grouped.Sum(s => s.SaleItems.Sum(si => (int)si.Quantity)),
                                TotalDiscounts = grouped.Sum(s => s.DiscountAmount),
                                RefundCount = 0, // You might need to track refunds separately
                                RefundAmount = 0,
                                TotalWorkingHours = TimeSpan.FromHours(8), // Default, you might calculate this differently
                                SalesPerHour = grouped.Sum(s => s.TotalAmount) / 8, // Assuming 8-hour workday
                                ReportStartDate = startDate ?? DateTime.MinValue,
                                ReportEndDate = endDate ?? DateTime.MaxValue
                            };

                return await query.OrderByDescending(c => c.TotalSales).ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error getting cashier performance report: {ex.Message}");
                return new List<CashierPerformanceReportDto>();
            }
        }

        public async Task<List<ProductPerformanceReportDto>> GetProductPerformanceReport(DateTime? startDate, DateTime? endDate)
        {
            try
            {
                var query = from saleItem in _context.SaleItems
                            join sale in _context.Sales on saleItem.SaleId equals sale.Id
                            join product in _context.Products on saleItem.ProductId equals product.Id
                            join category in _context.Categories on product.CategoryId equals category.Id into categoryGroup
                            from category in categoryGroup.DefaultIfEmpty()
                            where sale.CreationDate >= startDate && sale.CreationDate <= endDate
                            group new { saleItem, product, category } by new { product.Id, product.Name, CategoryName = category.Name } into grouped
                            let revenue = grouped.Sum(x => x.saleItem.Quantity * x.saleItem.Price)
                            let profit = grouped.Sum(x => x.saleItem.Quantity * (x.saleItem.Price - x.product.CostPrice))
                            select new ProductPerformanceReportDto
                            {
                                ProductId = grouped.Key.Id,
                                ProductName = grouped.Key.Name,
                                CategoryName = grouped.Key.CategoryName ?? "غير محدد",
                                QuantitySold = grouped.Sum(x => x.saleItem.Quantity),
                                Revenue = revenue,
                                Profit = profit,
                                ProfitMargin = revenue > 0 ? (profit / revenue) * 100 : 0,
                                TransactionCount = grouped.Count(),
                                AverageSellingPrice = grouped.Average(x => x.saleItem.Price)
                            };

                var result = await query.OrderByDescending(p => p.Revenue).ToListAsync();
                var totalRevenue = result.Sum(p => p.Revenue);

                for (int i = 0; i < result.Count(); i++)
                {
                    result[i].Rank = i + 1;
                    result[i].PercentageOfTotalSales = totalRevenue > 0 ? (result[i].Revenue / totalRevenue) * 100 : 0;
                    result[i].PerformanceCategory = i < result.Count() * 0.2 ? "أفضل المنتجات" :
                                                   i < result.Count() * 0.5 ? "منتجات جيدة" :
                                                   i < result.Count() * 0.8 ? "منتجات متوسطة" : "منتجات ضعيفة";
                }

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error getting product performance report: {ex.Message}");
                return new List<ProductPerformanceReportDto>();
            }
        }

        #endregion

        #region Supplier Reports

        public async Task<SupplierAccountStatementDto> GetSupplierAccountStatement(int supplierId, DateTime? startDate, DateTime? endDate)
        {
            try
            {
                // Get supplier information
                var supplier = await _context.Suppliers.FindAsync(supplierId);
                if (supplier == null)
                {
                    return null;
                }

                var statement = new SupplierAccountStatementDto
                {
                    SupplierId = supplierId,
                    SupplierName = supplier.Name,
                    Phone = supplier.Phone,
                    Email = supplier.Email,
                    Address = supplier.Address,
                    StatementDate = DateTime.Now,
                    FromDate = startDate ?? DateTime.Today.AddMonths(-1),
                    ToDate = endDate ?? DateTime.Today
                };

                // Calculate opening balance (all purchases minus payments before start date)
                var openingPurchases = await _context.Purchases
                    .Where(p => p.SupplierId == supplierId && p.CreationDate < statement.FromDate)
                    .SumAsync(p => p.TotalAmount);

                var openingPayments = await _context.Purchases
                    .Where(p => p.SupplierId == supplierId && p.CreationDate < statement.FromDate)
                    .SumAsync(p => p.AmountPaid);

                statement.OpeningBalance = openingPurchases - openingPayments;

                // Get all transactions in the period
                var transactions = new List<SupplierAccountTransactionDto>();

                // Get purchases in the period
                var purchases = await _context.Purchases
                    .Where(p => p.SupplierId == supplierId &&
                               p.CreationDate >= statement.FromDate &&
                               p.CreationDate <= statement.ToDate)
                    .OrderBy(p => p.CreationDate)
                    .ToListAsync();

                // Add purchase transactions
                foreach (var purchase in purchases)
                {
                    // Add purchase transaction (debit)
                    transactions.Add(new SupplierAccountTransactionDto
                    {
                        TransactionDate = purchase.CreationDate,
                        TransactionType = "مشتريات",
                        ReferenceNumber = purchase.OrderNumber ?? $"PO-{purchase.Id}",
                        Description = $"فاتورة مشتريات - {purchase.OrderNumber ?? purchase.Id.ToString()}",
                        DebitAmount = purchase.TotalAmount,
                        CreditAmount = 0,
                        Balance = 0 // Will be calculated below
                    });

                    // Add payment transaction if any amount was paid (credit)
                    if (purchase.AmountPaid > 0)
                    {
                        transactions.Add(new SupplierAccountTransactionDto
                        {
                            TransactionDate = purchase.LastUpdatedDate,
                            TransactionType = "دفعة",
                            ReferenceNumber = $"PAY-{purchase.Id}",
                            Description = $"دفعة لفاتورة {purchase.OrderNumber ?? purchase.Id.ToString()}",
                            DebitAmount = 0,
                            CreditAmount = purchase.AmountPaid,
                            Balance = 0 // Will be calculated below
                        });
                    }
                }

                // Sort all transactions by date and calculate running balance
                var runningBalance = statement.OpeningBalance;
                statement.Transactions = transactions.OrderBy(t => t.TransactionDate).ToList();

                foreach (var transaction in statement.Transactions)
                {
                    runningBalance += transaction.DebitAmount - transaction.CreditAmount;
                    transaction.Balance = runningBalance;
                }

                // Calculate summary totals
                statement.TotalPurchases = transactions.Where(t => t.TransactionType == "مشتريات").Sum(t => t.DebitAmount);
                statement.TotalPayments = transactions.Where(t => t.TransactionType == "دفعة").Sum(t => t.CreditAmount);
                statement.TotalReturns = transactions.Where(t => t.TransactionType == "مرتجع").Sum(t => t.CreditAmount);
                statement.ClosingBalance = runningBalance;
                statement.OutstandingBalance = Math.Max(0, runningBalance); // Only positive balances are outstanding

                return statement;
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error getting supplier account statement: {ex.Message}");
                return null;
            }
        }

        public async Task<List<SupplierPerformanceReportDto>> GetSupplierPerformanceReport(DateTime? startDate, DateTime? endDate)
        {
            try
            {
                var query = from purchase in _context.Purchases
                            join supplier in _context.Suppliers on purchase.SupplierId equals supplier.Id
                            where purchase.CreationDate >= startDate && purchase.CreationDate <= endDate
                            group purchase by new { supplier.Id, supplier.Name } into grouped
                            select new SupplierPerformanceReportDto
                            {
                                SupplierId = grouped.Key.Id,
                                SupplierName = grouped.Key.Name,
                                TotalPurchases = grouped.Sum(p => p.TotalAmount),
                                OrderCount = grouped.Count(),
                                AverageOrderValue = grouped.Average(p => p.TotalAmount),
                                TotalPaid = grouped.Sum(p => p.AmountPaid),
                                OutstandingAmount = grouped.Sum(p => p.RemainingAmount),
                                LastOrderDate = grouped.Max(p => p.CreationDate),
                                DaysSinceLastOrder = (DateTime.UtcNow - grouped.Max(p => p.CreationDate)).Days,
                                PaymentReliability = grouped.Average(p => p.AmountPaid / p.TotalAmount) > 0.9m ? "ممتاز" :
                                                   grouped.Average(p => p.AmountPaid / p.TotalAmount) > 0.7m ? "جيد" :
                                                   grouped.Average(p => p.AmountPaid / p.TotalAmount) > 0.5m ? "متوسط" : "ضعيف"
                            };

                return await query.OrderByDescending(s => s.TotalPurchases).ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error getting supplier performance report: {ex.Message}");
                return new List<SupplierPerformanceReportDto>();
            }
        }

        public async Task<List<SupplierPaymentStatusDto>> GetSupplierPaymentStatusReport()
        {
            try
            {
                var query = from purchase in _context.Purchases
                            join supplier in _context.Suppliers on purchase.SupplierId equals supplier.Id
                            where purchase.RemainingAmount > 0
                            group purchase by new { supplier.Id, supplier.Name } into grouped
                            select new SupplierPaymentStatusDto
                            {
                                SupplierId = grouped.Key.Id,
                                SupplierName = grouped.Key.Name,
                                TotalDue = grouped.Sum(p => p.RemainingAmount),
                                OverdueAmount = grouped.Where(p => (DateTime.UtcNow - p.CreationDate).Days > 30).Sum(p => p.RemainingAmount),
                                OverdueDays = grouped.Where(p => p.RemainingAmount > 0).Max(p => (DateTime.UtcNow - p.CreationDate).Days),
                                PaymentStatus = grouped.Any(p => (DateTime.UtcNow - p.CreationDate).Days > 30) ? "متأخر" :
                                              grouped.Any(p => p.RemainingAmount > 0) ? "مستحق" : "مدفوع",
                                LastPaymentDate = grouped.Max(p => p.LastUpdatedDate),
                                PendingOrders = grouped.Count(p => p.RemainingAmount > 0)
                            };

                return await query.OrderByDescending(s => s.TotalDue).ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error getting supplier payment status report: {ex.Message}");
                return new List<SupplierPaymentStatusDto>();
            }
        }

        #endregion

        #region Audit Reports

        public async Task<List<TransactionLogDto>> GetTransactionLogReport(DateTime? startDate, DateTime? endDate)
        {
            try
            {
                // This is a simplified version - you might want to create a dedicated audit log table
                var salesLogs = await _context.Sales
                    .Where(s => s.CreationDate >= startDate && s.CreationDate <= endDate)
                    .Select(s => new TransactionLogDto
                    {
                        LogId = s.Id,
                        TransactionDate = s.CreationDate,
                        TransactionType = "مبيعات",
                        Reference = s.InvoiceNumber,
                        Amount = s.TotalAmount,
                        UserName = s.CreatedBy ?? "غير محدد",
                        Description = $"فاتورة مبيعات رقم {s.InvoiceNumber}",
                        Status = "مكتمل"
                    })
                    .ToListAsync();

                var purchaseLogs = await _context.Purchases
                    .Where(p => p.CreationDate >= startDate && p.CreationDate <= endDate)
                    .Select(p => new TransactionLogDto
                    {
                        LogId = p.Id + 100000, // Offset to avoid ID conflicts
                        TransactionDate = p.CreationDate,
                        TransactionType = "مشتريات",
                        Reference = p.OrderNumber,
                        Amount = p.TotalAmount,
                        UserName = p.CreatedBy ?? "غير محدد",
                        Description = $"أمر شراء رقم {p.OrderNumber}",
                        Status = "مكتمل"
                    })
                    .ToListAsync();

                return salesLogs.Concat(purchaseLogs).OrderByDescending(t => t.TransactionDate).ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error getting transaction log report: {ex.Message}");
                return new List<TransactionLogDto>();
            }
        }

        public async Task<List<SystemActivityDto>> GetSystemActivityReport(DateTime? startDate, DateTime? endDate)
        {
            try
            {
                // This is a placeholder - you would typically have a dedicated audit/activity log table
                var activities = new List<SystemActivityDto>();

                // You can add system activities from various sources
                var salesActivities = await _context.Sales
                    .Where(s => s.CreationDate >= startDate && s.CreationDate <= endDate)
                    .Select(s => new SystemActivityDto
                    {
                        ActivityId = s.Id,
                        ActivityDate = s.CreationDate,
                        UserName = s.CreatedBy ?? "غير محدد",
                        ActivityType = "إنشاء",
                        Description = $"إنشاء فاتورة مبيعات {s.InvoiceNumber}",
                        IPAddress = "غير متوفر",
                        Module = "المبيعات"
                    })
                    .ToListAsync();

                activities.AddRange(salesActivities);

                return activities.OrderByDescending(a => a.ActivityDate).ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error getting system activity report: {ex.Message}");
                return new List<SystemActivityDto>();
            }
        }

        public async Task<CompanyInfoDto> GetCompanyInfoAsync()
        {
            try
            {
                var companySettings = await _context.systemConfigurations
                    .Where(s => s.IsActive && (s.SettingName == "CompanyName" ||
                                               s.SettingName == "CompanyAddress" ||
                                               s.SettingName == "CompanyPhone"))
                    .ToDictionaryAsync(s => s.SettingName, s => s.SettingValue);

                return new CompanyInfoDto
                {
                    CompanyName = companySettings.GetValueOrDefault("CompanyName", ""),
                    CompanyAddress = companySettings.GetValueOrDefault("CompanyAddress", ""),
                    CompanyPhone = companySettings.GetValueOrDefault("CompanyPhone", "")
                };
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error getting company info: {ex.Message}");
                return new CompanyInfoDto(); // Return empty DTO with default values
            }
        }

        #endregion
    }
}
