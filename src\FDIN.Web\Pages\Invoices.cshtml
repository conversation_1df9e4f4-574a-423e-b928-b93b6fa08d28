@page
@model FDIN.Web.Pages.InvoicesModel
@{
    ViewData["Title"] = "My Invoices";
}

<div class="container-fluid">
    <div class="row">
        <!-- Sidebar -->
        <div class="col-md-3 col-lg-2 d-md-block bg-light sidebar">
            <div class="position-sticky pt-3">
                <div class="user-profile mb-4">
                    <div class="text-center">
                        <img src="@Model.UserProfileImage" alt="Profile" class="rounded-circle mb-2" style="width: 80px; height: 80px; object-fit: cover;">
                        <h6 class="mb-1">@Model.UserName</h6>
                        <p class="text-muted small">@Model.UserEmail</p>
                    </div>
                </div>
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="nav-link active" href="#">
                            <i class="fas fa-file-invoice"></i> Invoices
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#">
                            <i class="fas fa-user"></i> Profile
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#">
                            <i class="fas fa-cog"></i> Settings
                        </a>
                    </li>
                </ul>
            </div>
        </div>

        <!-- Main Content -->
        <div class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">My Invoices</h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <div class="btn-group me-2">
                        <button type="button" class="btn btn-sm btn-outline-secondary">Share</button>
                        <button type="button" class="btn btn-sm btn-outline-secondary">Export</button>
                    </div>
                    <button type="button" class="btn btn-sm btn-outline-primary">
                        <i class="fas fa-filter"></i> Filter
                    </button>
                </div>
            </div>

            <!-- Search and Filter -->
            <div class="row mb-4">
                <div class="col-md-6">
                    <div class="input-group">
                        <input type="text" class="form-control" placeholder="Search invoices...">
                        <button class="btn btn-outline-secondary" type="button">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="d-flex justify-content-end">
                        <select class="form-select w-auto me-2">
                            <option>All Status</option>
                            <option>Paid</option>
                            <option>Pending</option>
                            <option>Overdue</option>
                        </select>
                        <select class="form-select w-auto">
                            <option>Sort by Date</option>
                            <option>Sort by Amount</option>
                            <option>Sort by Status</option>
                        </select>
                    </div>
                </div>
            </div>

            <!-- Invoices List -->
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>Invoice #</th>
                            <th>Date</th>
                            <th>Store</th>
                            <th>Amount</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach (var invoice in Model.Invoices)
                        {
                                <tr>
                                    <td>@invoice.InvoiceNumber</td>
                                    <td>@invoice.SaleDate.ToString("MMM dd, yyyy")</td>
                                    <td>@invoice.StoreName</td>
                                    <td>@invoice.TotalAmount.ToString("C")</td>
                                    <td>
                                        <span class="badge bg-@(invoice.Status == "Paid" ? "success" : invoice.Status == "Pending" ? "warning" : "danger")">
                                            @invoice.Status
                                        </span>
                                    </td>
                                    <td>
                                        <div class="btn-group">
                                            <button type="button" class="btn btn-sm btn-outline-primary" onclick="viewInvoice(@invoice.Id)">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="downloadInvoice(@invoice.Id)">
                                                <i class="fas fa-download"></i>
                                            </button>
                                            <button type="button" class="btn btn-sm btn-outline-info" onclick="printInvoice(@invoice.Id)">
                                                <i class="fas fa-print"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                        }
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <nav aria-label="Page navigation" class="mt-4">
                <ul class="pagination justify-content-center">
                    <li class="page-item disabled">
                        <a class="page-link" href="#" tabindex="-1">Previous</a>
                    </li>
                    <li class="page-item active"><a class="page-link" href="#">1</a></li>
                    <li class="page-item"><a class="page-link" href="#">2</a></li>
                    <li class="page-item"><a class="page-link" href="#">3</a></li>
                    <li class="page-item">
                        <a class="page-link" href="#">Next</a>
                    </li>
                </ul>
            </nav>
        </div>
    </div>
</div>

@section Scripts {
        <script>
            function viewInvoice(id) {
                // Implement view invoice functionality
                window.location.href = `/Invoice/Details/${id}`;
            }

            function downloadInvoice(id) {
                // Implement download invoice functionality
                window.location.href = `/Invoice/Download/${id}`;
            }

            function printInvoice(id) {
                // Implement print invoice functionality
                window.location.href = `/Invoice/Print/${id}`;
            }
        </script>
}