﻿using FDIN.Web.Data;
using FDIN.Web.Dtos;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;

namespace FDIN.Web.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class InvoiceController : ControllerBase
    {
        private readonly FDINDbContext _dbContext;
        private readonly ILogger<InvoiceController> _logger;
        public InvoiceController(FDINDbContext dbContext, ILogger<InvoiceController> logger)
        {
            _dbContext = dbContext;
            _logger = logger;
        }

        [HttpPost("receive")]
        public async Task<IActionResult> Receive(InvoiceReceiveDto invoiceReceiveDto)
        {
            if (invoiceReceiveDto == null || invoiceReceiveDto.Sale == null || invoiceReceiveDto.User == null)
            {
                return BadRequest("Invalid invoice data received.");
            }

            try
            {
                using var transaction = await _dbContext.Database.BeginTransactionAsync();
                try
                {
                    // Check if user exists
                    var existingUser = await _dbContext.Users
                        .FirstOrDefaultAsync(x => x.Phone == invoiceReceiveDto.User.Phone);

                    if (existingUser == null)
                    {
                        // Add new user
                        _dbContext.Users.Add(invoiceReceiveDto.User);
                        await _dbContext.SaveChangesAsync();
                        _logger.LogInformation("New user added with phone: {Phone}", invoiceReceiveDto.User.Phone);
                        existingUser = invoiceReceiveDto.User; // Use the newly created user
                    }
                    else
                    {
                        // Update existing user's information if needed
                        existingUser.FullName = invoiceReceiveDto.User.FullName;
                        existingUser.Email = invoiceReceiveDto.User.Email;
                        _dbContext.Users.Update(existingUser);
                        await _dbContext.SaveChangesAsync();
                        _logger.LogInformation("Existing user updated with phone: {Phone}", existingUser.Phone);
                    }

                    // Ensure all DateTime values are in UTC
                    if (invoiceReceiveDto.Sale.SaleDate.Kind != DateTimeKind.Utc)
                    {
                        invoiceReceiveDto.Sale.SaleDate = DateTime.SpecifyKind(invoiceReceiveDto.Sale.SaleDate, DateTimeKind.Utc);
                    }
                    if (invoiceReceiveDto.Sale.CreationDate.Kind != DateTimeKind.Utc)
                    {
                        invoiceReceiveDto.Sale.CreationDate = DateTime.SpecifyKind(invoiceReceiveDto.Sale.CreationDate, DateTimeKind.Utc);
                    }
                    if (invoiceReceiveDto.Sale.LastUpdatedDate.Kind != DateTimeKind.Utc)
                    {
                        invoiceReceiveDto.Sale.LastUpdatedDate = DateTime.SpecifyKind(invoiceReceiveDto.Sale.LastUpdatedDate, DateTimeKind.Utc);
                    }

                    // Set the UserId for the sale
                    invoiceReceiveDto.Sale.UserId = existingUser.Id;

                    // Add the sale
                    _dbContext.Sales.Add(invoiceReceiveDto.Sale);
                    await _dbContext.SaveChangesAsync();
                    _logger.LogInformation("New sale added with ID: {SaleId}", invoiceReceiveDto.Sale.Id);

                    await transaction.CommitAsync();
                    return Ok(new { Message = "Invoice received successfully" });
                }
                catch (Exception ex)
                {
                    await transaction.RollbackAsync();
                    _logger.LogError(ex, "Error processing invoice data");
                    throw;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error receiving invoice");
                return StatusCode(500, "An error occurred while processing the invoice.");
            }
        }
    }
}
