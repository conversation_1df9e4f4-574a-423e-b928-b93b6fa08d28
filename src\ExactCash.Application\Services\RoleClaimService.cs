using AutoMapper;
using ExactCash.Application.DTOs;
using ExactCash.Application.Interfaces;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using System.Security.Claims;
#nullable disable

namespace ExactCash.Application.Services
{
    public class RoleClaimService : IRoleClaimService
    {
        private readonly RoleManager<IdentityRole> _roleManager;
        private readonly IMapper _mapper;
        private readonly ILogger<RoleClaimService> _logger;

        public RoleClaimService(
            RoleManager<IdentityRole> roleManager,
            IMapper mapper,
            ILogger<RoleClaimService> logger)
        {
            _roleManager = roleManager;
            _mapper = mapper;
            _logger = logger;
        }

        public async Task<RoleClaimDto> GetRoleClaimByIdAsync(int id)
        {
            try
            {
                var roles = await _roleManager.Roles.ToListAsync();
                foreach (var role in roles)
                {
                    var claims = await _roleManager.GetClaimsAsync(role);
                    var claim = claims.FirstOrDefault(c => c.Type == "Id" && c.Value == id.ToString());
                    if (claim != null)
                    {
                        return new RoleClaimDto
                        {
                            Id = id,
                            RoleId = role.Id,
                            ClaimType = claim.Type,
                            ClaimValue = claim.Value
                        };
                    }
                }
                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting role claim by ID: {Id}", id);
                return null;
            }
        }

        public async Task<List<RoleClaimDto>> GetRoleClaimsByRoleIdAsync(string roleId)
        {
            try
            {
                var role = await _roleManager.FindByIdAsync(roleId);
                if (role == null)
                    return new List<RoleClaimDto>();

                var claims = await _roleManager.GetClaimsAsync(role);
                return claims.Select(c => new RoleClaimDto
                {
                    RoleId = roleId,
                    ClaimType = c.Type,
                    ClaimValue = c.Value
                }).ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting role claims for role: {RoleId}", roleId);
                return null;
            }
        }

        public async Task<RoleClaimDto> CreateRoleClaimAsync(CreateRoleClaimDto roleClaimDto)
        {
            try
            {
                var role = await _roleManager.FindByIdAsync(roleClaimDto.RoleId);
                if (role == null)
                    return null;

                var claim = new Claim(roleClaimDto.ClaimType, roleClaimDto.ClaimValue);
                var result = await _roleManager.AddClaimAsync(role, claim);

                if (!result.Succeeded)
                {
                    _logger.LogError("Failed to create role claim: {Errors}",
                        string.Join(", ", result.Errors.Select(e => e.Description)));
                    return null;
                }

                return new RoleClaimDto
                {
                    RoleId = roleClaimDto.RoleId,
                    ClaimType = roleClaimDto.ClaimType,
                    ClaimValue = roleClaimDto.ClaimValue
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating role claim for role: {RoleId}", roleClaimDto.RoleId);
                return null;
            }
        }

        public async Task<RoleClaimDto> UpdateRoleClaimAsync(int id, UpdateRoleClaimDto roleClaimDto)
        {
            try
            {
                var roles = await _roleManager.Roles.ToListAsync();
                foreach (var role in roles)
                {
                    var claims = await _roleManager.GetClaimsAsync(role);
                    var claim = claims.FirstOrDefault(c => c.Type == "Id" && c.Value == id.ToString());
                    if (claim != null)
                    {
                        // Remove the old claim
                        await _roleManager.RemoveClaimAsync(role, claim);

                        // Add the new claim
                        var newClaim = new Claim(roleClaimDto.ClaimType, roleClaimDto.ClaimValue);
                        var result = await _roleManager.AddClaimAsync(role, newClaim);

                        if (!result.Succeeded)
                        {
                            _logger.LogError("Failed to update role claim: {Errors}",
                                string.Join(", ", result.Errors.Select(e => e.Description)));
                            return null;
                        }

                        return new RoleClaimDto
                        {
                            Id = id,
                            RoleId = role.Id,
                            ClaimType = roleClaimDto.ClaimType,
                            ClaimValue = roleClaimDto.ClaimValue
                        };
                    }
                }
                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating role claim: {Id}", id);
                return null;
            }
        }

        public async Task<bool> DeleteRoleClaimAsync(int id)
        {
            try
            {
                var roles = await _roleManager.Roles.ToListAsync();
                foreach (var role in roles)
                {
                    var claims = await _roleManager.GetClaimsAsync(role);
                    var claim = claims.FirstOrDefault(c => c.Type == "Id" && c.Value == id.ToString());
                    if (claim != null)
                    {
                        var result = await _roleManager.RemoveClaimAsync(role, claim);
                        if (!result.Succeeded)
                        {
                            _logger.LogError("Failed to delete role claim: {Errors}",
                                string.Join(", ", result.Errors.Select(e => e.Description)));
                            return false;
                        }
                        return true;
                    }
                }
                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting role claim: {Id}", id);
                return false;
            }
        }

        public async Task<bool> AddClaimsToRoleAsync(RoleClaimsDto roleClaimsDto)
        {
            try
            {
                var role = await _roleManager.FindByIdAsync(roleClaimsDto.RoleId);
                if (role == null)
                    return false;

                var claims = roleClaimsDto.Claims.Select(c =>
                    new Claim(c.ClaimType, c.ClaimValue)).ToList();

                foreach (var claim in claims)
                {
                    var result = await _roleManager.AddClaimAsync(role, claim);
                    if (!result.Succeeded)
                    {
                        _logger.LogError("Failed to add claim to role: {Errors}",
                            string.Join(", ", result.Errors.Select(e => e.Description)));
                        return false;
                    }
                }

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error adding claims to role: {RoleId}", roleClaimsDto.RoleId);
                return false;
            }
        }

        public async Task<bool> RemoveClaimsFromRoleAsync(RoleClaimsDto roleClaimsDto)
        {
            try
            {
                var role = await _roleManager.FindByIdAsync(roleClaimsDto.RoleId);
                if (role == null)
                    return false;

                var claims = roleClaimsDto.Claims.Select(c =>
                    new Claim(c.ClaimType, c.ClaimValue)).ToList();

                foreach (var claim in claims)
                {
                    var result = await _roleManager.RemoveClaimAsync(role, claim);
                    if (!result.Succeeded)
                    {
                        _logger.LogError("Failed to remove claim from role: {Errors}",
                            string.Join(", ", result.Errors.Select(e => e.Description)));
                        return false;
                    }
                }

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error removing claims from role: {RoleId}", roleClaimsDto.RoleId);
                return false;
            }
        }

        public async Task<List<string>> GetRoleClaimTypesAsync()
        {
            try
            {
                var roles = await _roleManager.Roles.ToListAsync();
                var claimTypes = new HashSet<string>();

                foreach (var role in roles)
                {
                    var claims = await _roleManager.GetClaimsAsync(role);
                    claimTypes.UnionWith(claims.Select(c => c.Type));
                }

                return claimTypes.ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting role claim types");
                return null;
            }
        }

        public async Task<List<string>> GetRoleClaimValuesAsync(string claimType)
        {
            try
            {
                var roles = await _roleManager.Roles.ToListAsync();
                var claimValues = new HashSet<string>();

                foreach (var role in roles)
                {
                    var claims = await _roleManager.GetClaimsAsync(role);
                    claimValues.UnionWith(claims
                        .Where(c => c.Type == claimType)
                        .Select(c => c.Value));
                }

                return claimValues.ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting role claim values for type: {ClaimType}", claimType);
                return null;
            }
        }

        public async Task<bool> HasClaimAsync(string roleId, string claimType, string claimValue)
        {
            try
            {
                var role = await _roleManager.FindByIdAsync(roleId);
                if (role == null)
                    return false;

                var claims = await _roleManager.GetClaimsAsync(role);
                return claims.Any(c => c.Type == claimType && c.Value == claimValue);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking claim for role: {RoleId}", roleId);
                return false;
            }
        }
    }
}