﻿using Microsoft.AspNetCore.Mvc;
using ExactCash.Application.Contracts;
using ExactCash.Domain.Entities;

namespace ExactCash.API.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class CategoryController : ControllerBase
    {
        private readonly ICategoryService _categoryService;
        private readonly ILogger<CategoryController> _logger;

        public CategoryController(ICategoryService categoryService, ILogger<CategoryController> logger)
        {
            _categoryService = categoryService;
            _logger = logger;
        }

        // POST: api/Category
        // Creates a new category
        [HttpPost]
        public async Task<IActionResult> CreateCategory([FromBody] Category category)
        {
            try
            {
                _logger.LogInformation("Creating category: {CategoryName}", category.Name);
                var createdCategory = await _categoryService.CreateAsync(category);

                if (createdCategory == null)
                {
                    return Conflict("Category already exists.");
                }

                return CreatedAtAction(nameof(GetCategoryById), new { id = createdCategory.Id }, createdCategory);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while creating category.");
                return StatusCode(500, "Internal server error");
            }
        }

        // GET: api/Category/{id}
        // Retrieves a category by its ID
        [HttpGet("{id}")]
        public async Task<IActionResult> GetCategoryById(int id)
        {
            try
            {
                var category = await _categoryService.GetByIdAsync(id);
                if (category == null)
                {
                    return NotFound();
                }
                return Ok(category);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while fetching category with ID: {CategoryId}", id);
                return StatusCode(500, "Internal server error");
            }
        }

        // GET: api/Category
        // Retrieves all categories
        [HttpGet]
        public async Task<IActionResult> GetAllCategories()
        {
            var categories = await _categoryService.GetAllAsync();
            return Ok(categories);
        }

        // PUT: api/Category
        // Updates an existing category
        [HttpPut]
        public async Task<IActionResult> UpdateCategory([FromBody] Category category)
        {
            var updatedCategory = await _categoryService.UpdateAsync(category);
            return Ok(updatedCategory);  // Returns 200 OK with the updated category data
        }

        // DELETE: api/Category/{id}
        // Deletes a category by ID
        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteCategory(int id)
        {
            var success = await _categoryService.DeleteAsync(id);
            if (!success)
            {
                return NotFound();
            }
            return NoContent();
        }
    }
}
