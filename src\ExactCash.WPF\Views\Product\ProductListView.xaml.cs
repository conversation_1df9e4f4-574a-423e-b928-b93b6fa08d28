using System.Windows;
using AutoMapper;
using ExactCash.WPF.Services;
using ExactCash.WPF.Services.Product;
using ExactCash.WPF.ViewModels.Product;

namespace ExactCash.WPF.Views.Product
{
    public partial class ProductListView : Window
    {
        public ProductListView(
            IProductService productService,
            ICategoryServiceClient categoryServiceClient,
            IBrandsServiceClient brandsServiceClient,
            IMapper mapper,
            IUnitServiceClient unitServiceClient
            )
        {
            InitializeComponent();
            DataContext = new ProductListViewModel(
                productService,
                categoryServiceClient,
                brandsServiceClient,
                mapper, unitServiceClient);
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            Close();
        }

        private void MinimizeButton_Click(object sender, RoutedEventArgs e)
        {
            WindowState = WindowState.Minimized;
        }

        // Enable window dragging
        protected override void OnMouseLeftButtonDown(System.Windows.Input.MouseButtonEventArgs e)
        {
            base.OnMouseLeftButtonDown(e);
            this.DragMove();
        }
    }
}