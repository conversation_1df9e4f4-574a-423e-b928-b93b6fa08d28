﻿namespace ExactCash.Application.Responses
{
    /// <summary>
    /// base response
    /// </summary>
    /// <typeparam name="T"></typeparam>
    public class BaseResponse<T>
    {
        /// <summary>
        /// success
        /// </summary>
        public bool Success { get; protected set; }
        /// <summary>
        /// message
        /// </summary>
        public string Message { get; protected set; }
        /// <summary>
        /// data
        /// </summary>
        public T Data { get; protected set; }
        /// <summary>
        /// StatusCode
        /// </summary>
        public int StatusCode { get; protected set; }
        /// <summary>
        /// ctor
        /// </summary>
        /// <param name="statusCode"></param>
        /// <param name="success"></param>
        /// <param name="message"></param>
        /// <param name="data"></param>
        public BaseResponse(int statusCode, bool success, string message, T data)
        {
            StatusCode = statusCode;
            Success = success;
            Message = message;
            Data = data;
        }
    }
}
