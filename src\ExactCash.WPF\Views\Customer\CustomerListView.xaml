<Window x:Name="CustomerListView1"
        x:Class="ExactCash.WPF.Views.Customer.CustomerListView"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:ExactCash.WPF.Views.Customer"
        mc:Ignorable="d"
        Title="إدارة العملاء"
        Height="Auto"
        Width="Auto"
        WindowState="Maximized"
        WindowStartupLocation="CenterScreen"
        WindowStyle="None"
        AllowsTransparency="True"
        Background="Transparent"
        FlowDirection="RightToLeft">

        <Window.Resources>
                <Style x:Key="ModernButton"
                       TargetType="Button">
                        <Setter Property="Background"
                                Value="#0078D4"/>
                        <Setter Property="Foreground"
                                Value="White"/>
                        <Setter Property="BorderThickness"
                                Value="0"/>
                        <Setter Property="Padding"
                                Value="15,8"/>
                        <Setter Property="FontSize"
                                Value="14"/>
                        <Setter Property="Template">
                                <Setter.Value>
                                        <ControlTemplate TargetType="Button">
                                                <Border Background="{TemplateBinding Background}"
                                                        BorderThickness="{TemplateBinding BorderThickness}"
                                                        BorderBrush="{TemplateBinding BorderBrush}"
                                                        CornerRadius="4">
                                                        <ContentPresenter HorizontalAlignment="Center"
                                                                          VerticalAlignment="Center"/>
                                                </Border>
                                        </ControlTemplate>
                                </Setter.Value>
                        </Setter>
                        <Style.Triggers>
                                <Trigger Property="IsMouseOver"
                                         Value="True">
                                        <Setter Property="Background"
                                                Value="#106EBE"/>
                                </Trigger>
                        </Style.Triggers>
                </Style>

                <Style x:Key="SuccessButton"
                       TargetType="Button"
                       BasedOn="{StaticResource ModernButton}">
                        <Setter Property="Background"
                                Value="#28a745"/>
                        <Style.Triggers>
                                <Trigger Property="IsMouseOver"
                                         Value="True">
                                        <Setter Property="Background"
                                                Value="#218838"/>
                                </Trigger>
                        </Style.Triggers>
                </Style>

                <Style x:Key="ModernTextBox"
                       TargetType="TextBox">
                        <Setter Property="Height"
                                Value="35"/>
                        <Setter Property="Padding"
                                Value="10,5"/>
                        <Setter Property="BorderThickness"
                                Value="1"/>
                        <Setter Property="BorderBrush"
                                Value="#CCCCCC"/>
                        <Setter Property="Background"
                                Value="White"/>
                        <Setter Property="FontSize"
                                Value="14"/>
                </Style>

                <DropShadowEffect x:Key="DropShadowEffect"
                                  ShadowDepth="2"
                                  Direction="270"
                                  Color="Black"
                                  Opacity="0.3"
                                  BlurRadius="5"/>
        </Window.Resources>

        <Border Background="White"
                CornerRadius="8"
                BorderBrush="#DDDDDD"
                BorderThickness="1"
                Effect="{StaticResource DropShadowEffect}">
                <Grid>
                        <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="*"/>
                        </Grid.RowDefinitions>

                        <!-- Header -->
                        <Border Grid.Row="0"
                                Background="#0078D4"
                                BorderThickness="0"
                                CornerRadius="8,8,0,0">
                                <Grid Margin="20,15">
                                        <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="*"/>
                                                <ColumnDefinition Width="Auto"/>
                                                <ColumnDefinition Width="Auto"/>
                                        </Grid.ColumnDefinitions>

                                        <TextBlock Text="إدارة العملاء"
                                                   FontSize="18"
                                                   FontWeight="SemiBold"
                                                   Foreground="White"/>

                                        <Button Grid.Column="1"
                                                Content="−"
                                                Click="MinimizeButton_Click"
                                                Style="{StaticResource NotificationCloseButtonStyle}"
                                                Margin="0,0,10,0"/>

                                        <Button Grid.Column="2"
                                                Content="✕"
                                                Click="CancelButton_Click"
                                                Style="{StaticResource NotificationCloseButtonStyle}"/>
                                </Grid>
                        </Border>

                        <!-- Content with Tabs -->
                        <TabControl Grid.Row="1"
                                    Margin="20"
                                    Background="White"
                                    BorderThickness="0">
                                <TabControl.Resources>
                                        <Style TargetType="TabItem">
                                                <Setter Property="FontSize"
                                                        Value="14"/>
                                                <Setter Property="FontWeight"
                                                        Value="SemiBold"/>
                                                <Setter Property="Padding"
                                                        Value="20,10"/>
                                                <Setter Property="Margin"
                                                        Value="0,0,5,0"/>
                                                <Setter Property="Background"
                                                        Value="#F8F9FA"/>
                                                <Setter Property="BorderThickness"
                                                        Value="1,1,1,0"/>
                                                <Setter Property="BorderBrush"
                                                        Value="#DDDDDD"/>
                                                <Style.Triggers>
                                                        <Trigger Property="IsSelected"
                                                                 Value="True">
                                                                <Setter Property="Background"
                                                                        Value="White"/>
                                                                <Setter Property="BorderBrush"
                                                                        Value="#0078D4"/>
                                                                <Setter Property="Foreground"
                                                                        Value="#0078D4"/>
                                                        </Trigger>
                                                </Style.Triggers>
                                        </Style>
                                </TabControl.Resources>

                                <!-- First Tab: Customer List -->
                                <TabItem Header="قائمة العملاء">
                                        <ScrollViewer VerticalScrollBarVisibility="Auto">
                                                <StackPanel Margin="10">
                                                        <!-- Search Panel -->
                                                        <GroupBox Header="بحث عن العملاء"
                                                                  Margin="0,0,0,15"
                                                                  Padding="10">
                                                                <Grid>
                                                                        <Grid.ColumnDefinitions>
                                                                                <ColumnDefinition Width="*"/>
                                                                                <ColumnDefinition Width="*"/>
                                                                                <ColumnDefinition Width="*"/>
                                                                                <ColumnDefinition Width="*"/>
                                                                                <ColumnDefinition Width="Auto"/>
                                                                        </Grid.ColumnDefinitions>

                                                                        <!-- Name Search -->
                                                                        <StackPanel Grid.Column="0"
                                                                                    Margin="0,0,10,0">
                                                                                <TextBlock Text="اسم العميل"/>
                                                                                <Grid Margin="0,5,0,0">
                                                                                        <TextBox Text="{Binding CustomerListViewModel.NameSearch, UpdateSourceTrigger=PropertyChanged}"
                                                                                                 Style="{StaticResource ModernTextBox}"
                                                                                                 FlowDirection="RightToLeft"/>
                                                                                        <TextBlock Text="البحث بالاسم..."
                                                                                                   Foreground="Gray"
                                                                                                   FontSize="14"
                                                                                                   Margin="15,0,0,0"
                                                                                                   VerticalAlignment="Center"
                                                                                                   HorizontalAlignment="Right"
                                                                                                   IsHitTestVisible="False"
                                                                                                   FlowDirection="RightToLeft">
                                                                                                <TextBlock.Style>
                                                                                                        <Style TargetType="TextBlock">
                                                                                                                <Setter Property="Visibility"
                                                                                                                        Value="Collapsed"/>
                                                                                                                <Style.Triggers>
                                                                                                                        <DataTrigger Binding="{Binding CustomerListViewModel.NameSearch}"
                                                                                                                                     Value="">
                                                                                                                                <Setter Property="Visibility"
                                                                                                                                        Value="Visible"/>
                                                                                                                        </DataTrigger>
                                                                                                                        <DataTrigger Binding="{Binding CustomerListViewModel.NameSearch}"
                                                                                                                                     Value="{x:Null}">
                                                                                                                                <Setter Property="Visibility"
                                                                                                                                        Value="Visible"/>
                                                                                                                        </DataTrigger>
                                                                                                                </Style.Triggers>
                                                                                                        </Style>
                                                                                                </TextBlock.Style>
                                                                                        </TextBlock>
                                                                                </Grid>
                                                                        </StackPanel>

                                                                        <!-- Phone Search -->
                                                                        <StackPanel Grid.Column="1"
                                                                                    Margin="10,0,10,0">
                                                                                <TextBlock Text="رقم الهاتف"/>
                                                                                <Grid Margin="0,5,0,0">
                                                                                        <TextBox Text="{Binding CustomerListViewModel.PhoneSearch, UpdateSourceTrigger=PropertyChanged}"
                                                                                                 Style="{StaticResource ModernTextBox}"
                                                                                                 FlowDirection="RightToLeft"/>
                                                                                        <TextBlock Text="البحث برقم الهاتف..."
                                                                                                   Foreground="Gray"
                                                                                                   FontSize="14"
                                                                                                   Margin="15,0,0,0"
                                                                                                   VerticalAlignment="Center"
                                                                                                   HorizontalAlignment="Right"
                                                                                                   IsHitTestVisible="False"
                                                                                                   FlowDirection="RightToLeft">
                                                                                                <TextBlock.Style>
                                                                                                        <Style TargetType="TextBlock">
                                                                                                                <Setter Property="Visibility"
                                                                                                                        Value="Collapsed"/>
                                                                                                                <Style.Triggers>
                                                                                                                        <DataTrigger Binding="{Binding CustomerListViewModel.PhoneSearch}"
                                                                                                                                     Value="">
                                                                                                                                <Setter Property="Visibility"
                                                                                                                                        Value="Visible"/>
                                                                                                                        </DataTrigger>
                                                                                                                        <DataTrigger Binding="{Binding CustomerListViewModel.PhoneSearch}"
                                                                                                                                     Value="{x:Null}">
                                                                                                                                <Setter Property="Visibility"
                                                                                                                                        Value="Visible"/>
                                                                                                                        </DataTrigger>
                                                                                                                </Style.Triggers>
                                                                                                        </Style>
                                                                                                </TextBlock.Style>
                                                                                        </TextBlock>
                                                                                </Grid>
                                                                        </StackPanel>

                                                                        <!-- Category Search -->
                                                                        <StackPanel Grid.Column="2"
                                                                                    Margin="10,0,10,0">
                                                                                <TextBlock Text="التصنيف"/>
                                                                                <ComboBox ItemsSource="{Binding CustomerListViewModel.SearchCategories}"
                                                                                          SelectedItem="{Binding CustomerListViewModel.SearchCategory}"
                                                                                          DisplayMemberPath="Name"
                                                                                          Height="35"
                                                                                          Margin="0,5,0,0"
                                                                                          FlowDirection="RightToLeft"
                                                                                          Background="White"
                                                                                          BorderBrush="#CCCCCC"
                                                                                          BorderThickness="1"
                                                                                          FontSize="14"/>
                                                                        </StackPanel>

                                                                        <!-- Email Search -->
                                                                        <StackPanel Grid.Column="3"
                                                                                    Margin="10,0,10,0">
                                                                                <TextBlock Text="البريد الإلكتروني"/>
                                                                                <TextBox Text="{Binding CustomerListViewModel.EmailSearch, UpdateSourceTrigger=PropertyChanged}"
                                                                                         Style="{StaticResource ModernTextBox}"
                                                                                         Margin="0,5,0,0"/>
                                                                        </StackPanel>

                                                                        <!-- Search Buttons -->
                                                                        <StackPanel Grid.Column="4"
                                                                                    Orientation="Horizontal"
                                                                                    VerticalAlignment="Bottom"
                                                                                    Margin="10,0,0,0">
                                                                                <Button Content="بحث"
                                                                                        Command="{Binding CustomerListViewModel.SearchCommand}"
                                                                                        Style="{StaticResource ModernButton}"
                                                                                        Height="35"
                                                                                        Width="100"
                                                                                        Margin="0,0,10,0"/>
                                                                                <Button Content="إعادة تعيين"
                                                                                        Command="{Binding CustomerListViewModel.ResetSearchCommand}"
                                                                                        Style="{StaticResource ModernButton}"
                                                                                        Height="35"
                                                                                        Width="100"/>
                                                                        </StackPanel>
                                                                </Grid>
                                                        </GroupBox>

                                                        <!-- Action Buttons -->
                                                        <StackPanel Orientation="Horizontal"
                                                                    HorizontalAlignment="Right"
                                                                    Margin="0,0,0,20">
                                                                <Button Content="إضافة عميل جديد"
                                                                        Command="{Binding CustomerListViewModel.AddNewCustomerCommand}"
                                                                        Style="{StaticResource SuccessButton}"
                                                                        Height="35"
                                                                        Width="120"
                                                                        Margin="0,0,10,0"/>
                                                                <Button Content="تحديث"
                                                                        Command="{Binding CustomerListViewModel.RefreshCommand}"
                                                                        Style="{StaticResource ModernButton}"
                                                                        Height="35"
                                                                        Width="80"
                                                                        Margin="0,0,10,0"/>
                                                                <Button Content="تصدير إلى Excel"
                                                                        Command="{Binding CustomerListViewModel.ExportToExcelCommand}"
                                                                        Style="{StaticResource ModernButton}"
                                                                        Height="35"
                                                                        Width="120"/>
                                                        </StackPanel>

                                                        <!-- Customers Grid -->
                                                        <DataGrid ItemsSource="{Binding CustomerListViewModel.Customers}"
                                                                  AutoGenerateColumns="False"
                                                                  IsReadOnly="True"
                                                                  SelectionMode="Single"
                                                                  SelectionUnit="FullRow"
                                                                  GridLinesVisibility="None"
                                                                  RowHeight="40"
                                                                  BorderThickness="1"
                                                                  BorderBrush="#DDDDDD"
                                                                  Background="White"
                                                                  AlternatingRowBackground="#F8F9FA">
                                                                <DataGrid.RowStyle>
                                                                        <Style TargetType="DataGridRow">
                                                                                <Setter Property="BorderThickness"
                                                                                        Value="0"/>
                                                                                <Setter Property="Margin"
                                                                                        Value="0"/>
                                                                                <Setter Property="Padding"
                                                                                        Value="0"/>
                                                                        </Style>
                                                                </DataGrid.RowStyle>
                                                                <DataGrid.Columns>
                                                                        <DataGridTextColumn Header="الاسم"
                                                                                            Binding="{Binding FullName}"
                                                                                            Width="2*"/>
                                                                        <DataGridTextColumn Header="رقم الهاتف"
                                                                                            Binding="{Binding Phone}"
                                                                                            Width="*"/>
                                                                        <DataGridTextColumn Header="التصنيف"
                                                                                            Binding="{Binding CategoryName, TargetNullValue='-'}"
                                                                                            Width="*"/>
                                                                        <DataGridTextColumn Header="البريد الإلكتروني"
                                                                                            Binding="{Binding Email}"
                                                                                            Width="*"/>
                                                                        <DataGridTextColumn Header="العنوان"
                                                                                            Binding="{Binding Address}"
                                                                                            Width="*"/>

                                                                        <DataGridTextColumn Header="تم الإنشاء بواسطة"
                                                                                            Binding="{Binding CreatedBy, TargetNullValue='-'}"
                                                                                            Width="120"/>

                                                                        <!-- Actions Column -->
                                                                        <DataGridTemplateColumn Header="الإجراءات"
                                                                                                Width="200">
                                                                                <DataGridTemplateColumn.CellTemplate>
                                                                                        <DataTemplate>
                                                                                                <StackPanel Orientation="Horizontal"
                                                                                                            HorizontalAlignment="Center">

                                                                                                        <!-- Collect Payment Button -->
                                                                                                        <Button Style="{StaticResource ModernButton}"
                                                                                                                Background="#28a745"
                                                                                                                Command="{Binding DataContext.CustomerListViewModel.CollectPaymentCommand, RelativeSource={RelativeSource AncestorType=DataGrid}}"
                                                                                                                CommandParameter="{Binding}"
                                                                                                                ToolTip="تحصيل دفعة"
                                                                                                                Width="35"
                                                                                                                Height="30"
                                                                                                                Margin="0,0,5,0">
                                                                                                                <TextBlock Text="💰"
                                                                                                                           FontSize="16"/>
                                                                                                        </Button>

                                                                                                        <Button Style="{StaticResource ModernButton}"
                                                                                                                Command="{Binding DataContext.CustomerListViewModel.EditCustomerCommand, RelativeSource={RelativeSource AncestorType=DataGrid}}"
                                                                                                                CommandParameter="{Binding}"
                                                                                                                ToolTip="تعديل"
                                                                                                                Width="35"
                                                                                                                Height="30"
                                                                                                                Margin="0,0,5,0">
                                                                                                                <TextBlock Text="✏️"
                                                                                                                           FontSize="16"/>
                                                                                                        </Button>
                                                                                                        <Button Style="{StaticResource ModernButton}"
                                                                                                                Background="#dc3545"
                                                                                                                Command="{Binding DataContext.CustomerListViewModel.DeleteCustomerCommand, RelativeSource={RelativeSource AncestorType=DataGrid}}"
                                                                                                                CommandParameter="{Binding}"
                                                                                                                ToolTip="حذف"
                                                                                                                Width="35"
                                                                                                                Height="30">
                                                                                                                <TextBlock Text="🗑️"
                                                                                                                           FontSize="16"/>
                                                                                                        </Button>
                                                                                                </StackPanel>
                                                                                        </DataTemplate>
                                                                                </DataGridTemplateColumn.CellTemplate>
                                                                        </DataGridTemplateColumn>
                                                                </DataGrid.Columns>
                                                        </DataGrid>

                                                        <!-- Pagination -->
                                                        <StackPanel Orientation="Horizontal"
                                                                    HorizontalAlignment="Center"
                                                                    Margin="0,20,0,0">
                                                                <Button Content="السابق"
                                                                        Command="{Binding CustomerListViewModel.PreviousPageCommand}"
                                                                        Style="{StaticResource ModernButton}"
                                                                        Height="35"
                                                                        Width="80"
                                                                        Margin="0,0,10,0"/>
                                                                <TextBlock Text="{Binding CustomerListViewModel.PaginationInfo}"
                                                                           VerticalAlignment="Center"
                                                                           Margin="10,0"/>
                                                                <Button Content="التالي"
                                                                        Command="{Binding CustomerListViewModel.NextPageCommand}"
                                                                        Style="{StaticResource ModernButton}"
                                                                        Height="35"
                                                                        Width="80"
                                                                        Margin="10,0,0,0"/>
                                                        </StackPanel>
                                                </StackPanel>
                                        </ScrollViewer>
                                </TabItem>

                                <!-- Second Tab: Customer Categories -->
                                <TabItem Header="تصنيفات العملاء">
                                        <ScrollViewer VerticalScrollBarVisibility="Auto">
                                                <StackPanel Margin="10">
                                                        <!-- Header -->
                                                        <TextBlock Text="إدارة تصنيفات العملاء"
                                                                   FontSize="20"
                                                                   FontWeight="Bold"
                                                                   Foreground="#0078D4"
                                                                   Margin="0,0,0,20"/>

                                                        <!-- Action Buttons -->
                                                        <StackPanel Orientation="Horizontal"
                                                                    HorizontalAlignment="Right"
                                                                    Margin="0,0,0,20">
                                                                <Button Content="إضافة تصنيف"
                                                                        Command="{Binding CustomerCategoryViewModel.AddCategoryCommand}"
                                                                        Style="{StaticResource SuccessButton}"
                                                                        Height="35"
                                                                        Width="120"
                                                                        Margin="0,0,10,0"/>
                                                                <Button Content="تحديث"
                                                                        Command="{Binding CustomerCategoryViewModel.RefreshCommand}"
                                                                        Style="{StaticResource ModernButton}"
                                                                        Height="35"
                                                                        Width="80"
                                                                        Margin="0,0,10,0"/>
                                                                <Button Content="تصدير إلى Excel"
                                                                        Command="{Binding CustomerCategoryViewModel.ExportToExcelCommand}"
                                                                        Style="{StaticResource ModernButton}"
                                                                        Height="35"
                                                                        Width="120"/>
                                                        </StackPanel>

                                                        <!-- Categories Grid -->
                                                        <DataGrid ItemsSource="{Binding CustomerCategoryViewModel.Categories}"
                                                                  AutoGenerateColumns="False"
                                                                  CanUserAddRows="False"
                                                                  CanUserDeleteRows="False"
                                                                  IsReadOnly="True"
                                                                  SelectionMode="Single"
                                                                  SelectionUnit="FullRow"
                                                                  GridLinesVisibility="None"
                                                                  RowHeight="40"
                                                                  BorderThickness="1"
                                                                  BorderBrush="#DDDDDD"
                                                                  Background="White"
                                                                  AlternatingRowBackground="#F8F9FA">
                                                                <DataGrid.RowStyle>
                                                                        <Style TargetType="DataGridRow">
                                                                                <Setter Property="BorderThickness"
                                                                                        Value="0"/>
                                                                                <Setter Property="Margin"
                                                                                        Value="0"/>
                                                                                <Setter Property="Padding"
                                                                                        Value="0"/>
                                                                        </Style>
                                                                </DataGrid.RowStyle>
                                                                <DataGrid.Columns>
                                                                        <DataGridTextColumn Header="اسم التصنيف"
                                                                                            Binding="{Binding Name}"
                                                                                            Width="2*"/>
                                                                        <DataGridTextColumn Header="الوصف"
                                                                                            Binding="{Binding Description, TargetNullValue='-'}"
                                                                                            Width="3*"/>
                                                                        <DataGridTextColumn Header="تاريخ الإنشاء"
                                                                                            Binding="{Binding CreationDate, StringFormat=dd/MM/yyyy HH:mm}"
                                                                                            Width="*"/>
                                                                        <DataGridTextColumn Header="تم الإنشاء بواسطة"
                                                                                            Binding="{Binding CreatedBy, TargetNullValue='-'}"
                                                                                            Width="*"/>
                                                                        <DataGridCheckBoxColumn Header="نشط"
                                                                                                Binding="{Binding IsActive}"
                                                                                                Width="80"/>

                                                                        <!-- Actions Column -->
                                                                        <DataGridTemplateColumn Header="الإجراءات"
                                                                                                Width="160">
                                                                                <DataGridTemplateColumn.CellTemplate>
                                                                                        <DataTemplate>
                                                                                                <StackPanel Orientation="Horizontal"
                                                                                                            HorizontalAlignment="Center">

                                                                                                        <Button Style="{StaticResource ModernButton}"
                                                                                                                Command="{Binding DataContext.CustomerCategoryViewModel.EditCommand, RelativeSource={RelativeSource AncestorType=DataGrid}}"
                                                                                                                CommandParameter="{Binding}"
                                                                                                                ToolTip="تعديل"
                                                                                                                Width="35"
                                                                                                                Height="30"
                                                                                                                Margin="0,0,5,0">
                                                                                                                <TextBlock Text="✏️"
                                                                                                                           FontSize="16"/>
                                                                                                        </Button>
                                                                                                        <Button Style="{StaticResource ModernButton}"
                                                                                                                Background="#dc3545"
                                                                                                                Command="{Binding DataContext.CustomerCategoryViewModel.DeleteCommand, RelativeSource={RelativeSource AncestorType=DataGrid}}"
                                                                                                                CommandParameter="{Binding}"
                                                                                                                ToolTip="حذف"
                                                                                                                Width="35"
                                                                                                                Height="30">
                                                                                                                <TextBlock Text="🗑️"
                                                                                                                           FontSize="16"/>
                                                                                                        </Button>
                                                                                                </StackPanel>
                                                                                        </DataTemplate>
                                                                                </DataGridTemplateColumn.CellTemplate>
                                                                        </DataGridTemplateColumn>
                                                                </DataGrid.Columns>
                                                        </DataGrid>
                                                </StackPanel>
                                        </ScrollViewer>
                                </TabItem>
                        </TabControl>
                </Grid>
        </Border>
</Window>