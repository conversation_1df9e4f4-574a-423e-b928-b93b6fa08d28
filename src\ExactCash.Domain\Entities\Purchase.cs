﻿using ExactCash.Domain.Common;
#nullable disable

namespace ExactCash.Domain.Entities
{
    /// <summary>
    /// Represents a purchase transaction from a supplier to the business.
    /// </summary>
    public class Purchase : BaseEntity
    {
        /// <summary>
        /// The ID of the supplier who supplied the products.
        /// </summary>
        public int SupplierId { get; set; }

        /// <summary>
        /// Navigation property to the supplier entity. Defines which supplier provided the goods.
        /// </summary>
        public Supplier Supplier { get; set; }

        /// <summary>
        /// The date when the purchase was made or received.
        /// </summary>
        public DateTime PurchaseDate { get; set; }

        /// <summary>
        /// Total cost of the purchase for accounting purposes.
        /// </summary>
        public decimal TotalAmount { get; set; }

        /// <summary>
        /// The amount paid to the supplier.
        /// </summary>
        public decimal AmountPaid { get; set; }

        /// <summary>
        /// Remaining amount to be paid (used for partial payments or credit).
        /// </summary>
        public decimal RemainingAmount { get; set; }

        /// <summary>
        /// OrderNumber
        /// </summary>
        public string OrderNumber { get; set; }

        /// <summary>
        /// Notes
        /// </summary>
        public string Notes { get; set; }

        /// <summary>
        /// Navigation property for items included in the purchase.
        /// </summary>
        public ICollection<PurchaseItem> Items { get; set; } = new List<PurchaseItem>();
    }

}
