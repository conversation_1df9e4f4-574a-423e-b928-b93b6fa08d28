html {
  font-size: 14px;
}

@media (min-width: 768px) {
  html {
    font-size: 16px;
  }
}

.btn:focus, .btn:active:focus, .btn-link.nav-link:focus, .form-control:focus, .form-check-input:focus {
  box-shadow: 0 0 0 0.1rem white, 0 0 0 0.25rem #258cfb;
}

html {
  position: relative;
  min-height: 100%;
}

body {
  margin-bottom: 60px;
}

.sidebar {
    position: fixed;
    top: 0;
    bottom: 0;
    left: 0;
    z-index: 100;
    padding: 48px 0 0;
    box-shadow: inset -1px 0 0 rgba(0, 0, 0, .1);
}

    .sidebar .nav-link {
        font-weight: 500;
        color: #333;
        padding: 0.5rem 1rem;
    }

        .sidebar .nav-link.active {
            color: #007bff;
        }

        .sidebar .nav-link:hover {
            color: #007bff;
        }

        .sidebar .nav-link i {
            margin-right: 0.5rem;
        }

.table th {
    font-weight: 600;
    background-color: #f8f9fa;
}

.badge {
    padding: 0.5em 0.75em;
}

.btn-group .btn {
    padding: 0.25rem 0.5rem;
}

.user-profile img {
    border: 3px solid #fff;
    box-shadow: 0 0 10px rgba(0,0,0,0.1);
}

@media (max-width: 767.98px) {
    .sidebar {
        position: static;
        height: auto;
        padding-top: 0;
    }
}

@media (min-width: 768px) {
    .logo-billmate {
        height: 64px;
    }
}

@media (max-width: 600px) {
    .invoice-card {
        padding: 1rem 0.7rem;
    }

    .invoice-header {
        font-size: 1rem;
    }

    .invoice-amount {
        font-size: 1rem;
    }
}

@media print {
    .invoice-box {
        box-shadow: none;
        border: none;
    }

    .no-print {
        display: none;
    }
}
