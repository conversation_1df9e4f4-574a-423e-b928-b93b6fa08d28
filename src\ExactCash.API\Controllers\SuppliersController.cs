using ExactCash.Application.DTOs;
using ExactCash.Application.DTOs.Common;
using ExactCash.Application.Interfaces;
using ExactCash.Application.Responses;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
#nullable disable

namespace ExactCash.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class SuppliersController : ControllerBase
    {
        private readonly ISupplierService _supplierService;

        public SuppliersController(ISupplierService supplierService)
        {
            _supplierService = supplierService;
        }

        [HttpGet]
        public async Task<ActionResult<PagedResponse<SupplierDto>>> GetAllSuppliers(
            [FromQuery] string name,
            [FromQuery] string phone,
            [FromQuery] PaginationFilter pagination)
        {
            var suppliers = await _supplierService.GetAllSuppliersAsync(name, phone, pagination);
            return Ok(suppliers);
        }


        [HttpGet("get-all-suppliers")]
        public async Task<ActionResult<IEnumerable<SupplierDto>>> GetAllSuppliers(
            [FromQuery] string name,
            [FromQuery] string phone)
        {
            var suppliers = await _supplierService.GetAllSuppliersAsync(name, phone);
            return Ok(suppliers);
        }



        [HttpGet("search")]
        public async Task<IActionResult> SearchProductsAsync([FromQuery] string term)
        {
            return Ok(await _supplierService.SearchSuppliersAsync(term));
        }

        [HttpGet("{id}")]
        public async Task<ActionResult<SupplierDto>> GetSupplier(int id)
        {
            var supplier = await _supplierService.GetSupplierByIdAsync(id);
            if (supplier == null)
                return NotFound();

            return Ok(supplier);
        }

        [HttpPost]
        public async Task<ActionResult<SupplierDto>> CreateSupplier(SupplierDto supplierDto)
        {
            var createdSupplier = await _supplierService.CreateSupplierAsync(supplierDto);
            return StatusCode(createdSupplier.StatusCode, createdSupplier);
        }

        [HttpPut("{id}")]
        public async Task<IActionResult> UpdateSupplier(int id, SupplierDto supplierDto)
        {
            var updateSupplier = await _supplierService.UpdateSupplierAsync(supplierDto);
            return StatusCode(updateSupplier.StatusCode, updateSupplier);
        }

        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteSupplier(int id)
        {
            try
            {
                await _supplierService.DeleteSupplierAsync(id);
                return NoContent();
            }
            catch (KeyNotFoundException)
            {
                return NotFound();
            }
        }
    }
}