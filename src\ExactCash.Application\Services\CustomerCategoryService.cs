using AutoMapper;
using ExactCash.Application.DTOs;
using ExactCash.Application.Interfaces;
using ExactCash.Application.Responses;
using ExactCash.Domain.Entities;
using ExactCash.Infrastructure.Persistence;
using Microsoft.EntityFrameworkCore;

namespace ExactCash.Application.Services
{
    public class CustomerCategoryService : ICustomerCategoryService
    {
        private readonly AppPostgreSQLDbContext _context;
        private readonly IMapper _mapper;

        public CustomerCategoryService(AppPostgreSQLDbContext context, IMapper mapper)
        {
            _context = context;
            _mapper = mapper;
        }

        public async Task<List<CustomerCategoryDto>> GetAllAsync()
        {
            var categories = await _context.CustomerCategories
                .Where(c => c.IsActive)
                .OrderBy(c => c.Name)
                .ToListAsync();

            return _mapper.Map<List<CustomerCategoryDto>>(categories);
        }

        public async Task<List<CustomerCategoryDto>> GetActiveAsync()
        {
            var categories = await _context.CustomerCategories
                .Where(c => c.IsActive)
                .OrderBy(c => c.Name)
                .ToListAsync();

            return _mapper.Map<List<CustomerCategoryDto>>(categories);
        }

        public async Task<CustomerCategoryDto> GetByIdAsync(int id)
        {
            var category = await _context.CustomerCategories
                .FirstOrDefaultAsync(c => c.Id == id && c.IsActive);

            return _mapper.Map<CustomerCategoryDto>(category);
        }

        public async Task<BaseResponse<CustomerCategoryDto>> CreateAsync(CustomerCategoryDto categoryDto, string createdBy)
        {
            try
            {
                // Check if category with same name already exists
                var existingCategory = await _context.CustomerCategories
                    .FirstOrDefaultAsync(c => c.Name.ToLower() == categoryDto.Name.ToLower() && c.IsActive);

                if (existingCategory != null)
                {
                    return ResponseHelper.Failure(500, categoryDto, "تصنيف بنفس الاسم موجود بالفعل");
                }

                var category = new CustomerCategory
                {
                    Name = categoryDto.Name,
                    Description = categoryDto.Description,
                    CreatedBy = createdBy,
                    CreationDate = DateTime.UtcNow,
                    IsActive = true
                };

                _context.CustomerCategories.Add(category);
                await _context.SaveChangesAsync();

                var result = _mapper.Map<CustomerCategoryDto>(category);

                return ResponseHelper.Success(200, result, "تم إنشاء التصنيف بنجاح");
            }
            catch (Exception ex)
            {
                return ResponseHelper.Failure(500, categoryDto, $"حدث خطأ أثناء إنشاء التصنيف: {ex.Message}");
            }
        }

        public async Task<BaseResponse<bool>> UpdateAsync(CustomerCategoryDto categoryDto, string updatedBy)
        {
            try
            {
                var category = await _context.CustomerCategories
                    .FirstOrDefaultAsync(c => c.Id == categoryDto.Id && c.IsActive);

                if (category == null)
                {
                    return ResponseHelper.Failure(500, false, "التصنيف غير موجود");
                }

                // Check if another category with same name exists
                var existingCategory = await _context.CustomerCategories
                    .FirstOrDefaultAsync(c => c.Name.ToLower() == categoryDto.Name.ToLower() 
                                            && c.Id != categoryDto.Id && c.IsActive);

                if (existingCategory != null)
                {
                    return ResponseHelper.Failure(500, false, "تصنيف بنفس الاسم موجود بالفعل");
                }

                category.Name = categoryDto.Name;
                category.Description = categoryDto.Description;
                category.LastUpdatedBy = updatedBy;
                category.LastUpdatedDate = DateTime.UtcNow;
                category.IsActive = categoryDto.IsActive;

                await _context.SaveChangesAsync();
                return ResponseHelper.Success(200, true, "تم تحديث التصنيف بنجاح");
            }
            catch (Exception ex)
            {
                return ResponseHelper.Failure(500, false, $"حدث خطأ أثناء تحديث التصنيف: {ex.Message}");
            }
        }

        public async Task<BaseResponse<bool>> DeleteAsync(int id)
        {
            try
            {
                var category = await _context.CustomerCategories
                    .FirstOrDefaultAsync(c => c.Id == id && c.IsActive);

                if (category == null)
                {
                    return ResponseHelper.Failure(500, false, "التصنيف غير موجود");
                }

                // Check if category is being used by any customers
                var customersCount = await _context.Customers
                    .CountAsync(c => c.CategoryId == id);

                if (customersCount > 0)
                {
                    return ResponseHelper.Failure(500, false, $"لا يمكن حذف التصنيف لأنه مرتبط بـ {customersCount} عميل");
                }

                category.IsActive = false;
                category.LastUpdatedDate = DateTime.UtcNow;

                await _context.SaveChangesAsync();
                return ResponseHelper.Success(200, true, "تم حذف التصنيف بنجاح");
            }
            catch (Exception ex)
            {
                return ResponseHelper.Failure(500, false, $"حدث خطأ أثناء تحديث التصنيف: {ex.Message}");
            }
        }
    }
}
