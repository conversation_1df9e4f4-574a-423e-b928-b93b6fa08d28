using ExactCash.Application.DTOs;
using ExactCash.WPF.Services.Interfaces;
using ExactCash.WPF.ViewModels.Common;
using Microsoft.Extensions.DependencyInjection;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Threading.Tasks;

namespace ExactCash.WPF.ViewModels.CreditSales
{
    public class PaymentHistoryViewModel : ViewModelBase, INotifyPropertyChanged
    {
        #region Fields
        private readonly IPaymentServiceClient _paymentService;
        private readonly OutstandingSaleDto _sale;
        private ObservableCollection<CreditPaymentHistoryDto> _paymentHistory;
        private string _invoiceInfo;
        private decimal _totalPaidAmount;
        private int _paymentCount;
        private decimal _remainingAmount;
        #endregion

        #region Properties
        public ObservableCollection<CreditPaymentHistoryDto> PaymentHistory
        {
            get => _paymentHistory;
            set => SetProperty(ref _paymentHistory, value);
        }

        public string InvoiceInfo
        {
            get => _invoiceInfo;
            set => SetProperty(ref _invoiceInfo, value);
        }

        public decimal TotalPaidAmount
        {
            get => _totalPaidAmount;
            set => SetProperty(ref _totalPaidAmount, value);
        }

        public int PaymentCount
        {
            get => _paymentCount;
            set => SetProperty(ref _paymentCount, value);
        }

        public decimal RemainingAmount
        {
            get => _remainingAmount;
            set => SetProperty(ref _remainingAmount, value);
        }
        #endregion

        #region Constructor
        public PaymentHistoryViewModel(OutstandingSaleDto sale)
        {
            _sale = sale ?? throw new ArgumentNullException(nameof(sale));

            // Get the payment service from DI container
            var app = (App)System.Windows.Application.Current;
            _paymentService = app.ServiceProvider.GetRequiredService<IPaymentServiceClient>();

            // Initialize collections
            PaymentHistory = new ObservableCollection<CreditPaymentHistoryDto>();

            // Set invoice information
            InvoiceInfo = $"فاتورة رقم: {sale.InvoiceNumber} - العميل: {sale.CustomerName} - إجمالي الفاتورة: {sale.TotalAmount:N2} ج.م";
            RemainingAmount = sale.RemainingAmount;

            // Load payment history
            LoadPaymentHistoryAsync();
        }
        #endregion

        #region Methods
        private async void LoadPaymentHistoryAsync()
        {
            try
            {
                // Create search criteria for this specific sale
                var searchDto = new CreditPaymentSearchDto
                {
                    // We would need to add SaleId or InvoiceNumber to the search DTO
                    // For now, we'll filter by customer name and use mock data
                    CustomerName = _sale.CustomerName,
                    FromDate = _sale.CreationDate.AddDays(-1),
                    ToDate = DateTime.Now.AddDays(1)
                };

                var paymentHistory = await GetPaymentHistoryForSaleAsync(searchDto);

                PaymentHistory.Clear();
                foreach (var payment in paymentHistory)
                {
                    PaymentHistory.Add(payment);
                }

                CalculateSummary();
            }
            catch (Exception ex)
            {
                // Handle error
                System.Diagnostics.Debug.WriteLine($"Error loading payment history: {ex.Message}");
            }
        }

        private async Task<List<CreditPaymentHistoryDto>> GetPaymentHistoryForSaleAsync(CreditPaymentSearchDto searchDto)
        {
            try
            {
                if (_paymentService != null)
                {
                    var allPayments = await _paymentService.GetCreditPaymentHistoryAsync(searchDto);
                    // Filter by invoice number if available
                    return allPayments?.Where(p => p.CustomerName == _sale.CustomerName).ToList() ?? new List<CreditPaymentHistoryDto>();
                }
                return new List<CreditPaymentHistoryDto>();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error getting payment history: {ex.Message}");
                return new List<CreditPaymentHistoryDto>();
            }
        }

        private void CalculateSummary()
        {
            if (PaymentHistory?.Any() == true)
            {
                TotalPaidAmount = PaymentHistory.Sum(p => p.PaymentAmount);
                PaymentCount = PaymentHistory.Count;
            }
            else
            {
                TotalPaidAmount = 0;
                PaymentCount = 0;
            }
        }
        #endregion

        #region INotifyPropertyChanged
        public event PropertyChangedEventHandler PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        protected bool SetProperty<T>(ref T field, T value, [CallerMemberName] string propertyName = null)
        {
            if (EqualityComparer<T>.Default.Equals(field, value))
                return false;

            field = value;
            OnPropertyChanged(propertyName);
            return true;
        }
        #endregion
    }
}
