<Window x:Class="ExactCash.WPF.Controls.BootstrapMessageBox"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:ExactCash.WPF.Controls"
        mc:Ignorable="d"
        Title="{Binding RelativeSource={RelativeSource Self}, Path=Title}"
        WindowStyle="None"
        ResizeMode="NoResize"
        Background="White"
        AllowsTransparency="True"
        MinWidth="400"
        MinHeight="200"
        SizeToContent="WidthAndHeight"
        WindowStartupLocation="CenterOwner"
        FlowDirection="RightToLeft">

    <Window.Resources>
        <Style x:Key="BootstrapButton"
                TargetType="Button">
            <Setter Property="Background"
                    Value="#0078D4"/>
            <Setter Property="Foreground"
                    Value="White"/>
            <Setter Property="BorderThickness"
                    Value="0"/>
            <Setter Property="Padding"
                    Value="15,8"/>
            <Setter Property="FontSize"
                    Value="14"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                CornerRadius="4">
                            <ContentPresenter HorizontalAlignment="Center"
                                              VerticalAlignment="Center"/>
                        </Border>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
            <Style.Triggers>
                <Trigger Property="IsMouseOver"
                        Value="True">
                    <Setter Property="Background"
                            Value="#106EBE"/>
                </Trigger>
            </Style.Triggers>
        </Style>
    </Window.Resources>

    <Border Background="White"
            CornerRadius="8"
            BorderBrush="#6C757D"
            BorderThickness="0.5">
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <!-- Header -->
            <Border Background="#0078D4"
                    CornerRadius="8,8,0,0"
                    Grid.Row="0">
                <Grid Margin="20,15">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>
                    <TextBlock Text="{Binding RelativeSource={RelativeSource AncestorType=Window}, Path=Title}"
                               Foreground="White"
                               FontSize="18"
                               FontWeight="SemiBold"
                               TextWrapping="Wrap"/>
                    <Button Grid.Column="1"
                            Content="✕"
                            Foreground="White"
                            Background="Transparent"
                            BorderThickness="0"
                            FontSize="16"
                            Margin="10,0,0,0"
                            Click="CloseButton_Click"/>
                </Grid>
            </Border>

            <!-- Message -->
            <ScrollViewer Grid.Row="1"
                          VerticalScrollBarVisibility="Auto"
                          MaxHeight="400">
                <TextBlock Text="{Binding RelativeSource={RelativeSource AncestorType=Window}, Path=Content}"
                           Margin="20"
                           TextWrapping="Wrap"
                           FontSize="14"
                           LineHeight="20"/>
            </ScrollViewer>

            <!-- Buttons -->
            <Border Grid.Row="2"
                    Background="#F8F9FA"
                    BorderThickness="0,1,0,0"
                    BorderBrush="#DEE2E6">
                <StackPanel Orientation="Horizontal"
                            HorizontalAlignment="Center"
                            Margin="20,15">
                    <Button Content="موافق"
                            Style="{StaticResource BootstrapButton}"
                            MinWidth="120"
                            Height="35"
                            FontSize="14"
                            Click="OKButton_Click"/>
                </StackPanel>
            </Border>
        </Grid>
    </Border>
</Window> 