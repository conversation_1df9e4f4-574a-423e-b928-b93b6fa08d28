using ExactCash.WASM.Services.Interfaces;

namespace ExactCash.WASM.Services;

// Placeholder service implementations - to be replaced with actual implementations

public class ProductService : IProductService
{
    private readonly IApiService _apiService;

    public ProductService(IApiService apiService)
    {
        _apiService = apiService;
    }
}

public class SaleService : ISaleService
{
    private readonly IApiService _apiService;

    public SaleService(IApiService apiService)
    {
        _apiService = apiService;
    }
}

public class SupplierService : ISupplierService
{
    private readonly IApiService _apiService;

    public SupplierService(IApiService apiService)
    {
        _apiService = apiService;
    }
}

public class PurchaseOrderService : IPurchaseOrderService
{
    private readonly IApiService _apiService;

    public PurchaseOrderService(IApiService apiService)
    {
        _apiService = apiService;
    }
}

public class ExpenseService : IExpenseService
{
    private readonly IApiService _apiService;

    public ExpenseService(IApiService apiService)
    {
        _apiService = apiService;
    }
}

public class ReportService : IReportService
{
    private readonly IApiService _apiService;

    public ReportService(IApiService apiService)
    {
        _apiService = apiService;
    }
}

public class InventoryService : IInventoryService
{
    private readonly IApiService _apiService;

    public InventoryService(IApiService apiService)
    {
        _apiService = apiService;
    }
}

public class UserService : IUserService
{
    private readonly IApiService _apiService;

    public UserService(IApiService apiService)
    {
        _apiService = apiService;
    }
}

public class SystemConfigurationService : ISystemConfigurationService
{
    private readonly IApiService _apiService;

    public SystemConfigurationService(IApiService apiService)
    {
        _apiService = apiService;
    }
}

public class ModalService : IModalService
{
    // Modal service implementation
}
