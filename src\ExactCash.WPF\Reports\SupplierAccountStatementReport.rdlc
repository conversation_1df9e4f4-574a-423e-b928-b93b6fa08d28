<?xml version="1.0" encoding="utf-8"?>
<Report xmlns="http://schemas.microsoft.com/sqlserver/reporting/2016/01/reportdefinition" xmlns:rd="http://schemas.microsoft.com/SQLServer/reporting/reportdesigner">
  <AutoRefresh>0</AutoRefresh>
  <DataSources>
    <DataSource Name="SupplierAccountStatementDataSet">
      <ConnectionProperties>
        <DataProvider>System.Data.DataSet</DataProvider>
        <ConnectString>/* Local Connection */</ConnectString>
      </ConnectionProperties>
      <rd:DataSourceID>b8c8c8c8-8c8c-8c8c-8c8c-8c8c8c8c8c8c</rd:DataSourceID>
    </DataSource>
  </DataSources>
  <DataSets>
    <DataSet Name="SupplierAccountStatementDataSet">
      <Query>
        <DataSourceName>SupplierAccountStatementDataSet</DataSourceName>
        <CommandText>/* Local Query */</CommandText>
      </Query>
      <Fields>
        <Field Name="SupplierId">
          <DataField>SupplierId</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="SupplierName">
          <DataField>SupplierName</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="FormattedStatementDate">
          <DataField>FormattedStatementDate</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="FormattedFromDate">
          <DataField>FormattedFromDate</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="FormattedToDate">
          <DataField>FormattedToDate</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="OpeningBalance">
          <DataField>OpeningBalance</DataField>
          <rd:TypeName>System.Decimal</rd:TypeName>
        </Field>
        <Field Name="FormattedOpeningBalance">
          <DataField>FormattedOpeningBalance</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="ClosingBalance">
          <DataField>ClosingBalance</DataField>
          <rd:TypeName>System.Decimal</rd:TypeName>
        </Field>
        <Field Name="FormattedClosingBalance">
          <DataField>FormattedClosingBalance</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="TotalDebits">
          <DataField>TotalDebits</DataField>
          <rd:TypeName>System.Decimal</rd:TypeName>
        </Field>
        <Field Name="FormattedTotalDebits">
          <DataField>FormattedTotalDebits</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="TotalCredits">
          <DataField>TotalCredits</DataField>
          <rd:TypeName>System.Decimal</rd:TypeName>
        </Field>
        <Field Name="FormattedTotalCredits">
          <DataField>FormattedTotalCredits</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
      </Fields>
    </DataSet>
    <DataSet Name="CompanyInfoDataSet">
      <Query>
        <DataSourceName>SupplierAccountStatementDataSet</DataSourceName>
        <CommandText>/* Local Query */</CommandText>
      </Query>
      <Fields>
        <Field Name="CompanyName">
          <DataField>CompanyName</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="CompanyAddress">
          <DataField>CompanyAddress</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="CompanyPhone">
          <DataField>CompanyPhone</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
      </Fields>
    </DataSet>
    <DataSet Name="SupplierTransactionsDataSet">
      <Query>
        <DataSourceName>SupplierAccountStatementDataSet</DataSourceName>
        <CommandText>/* Local Query */</CommandText>
      </Query>
      <Fields>
        <Field Name="FormattedTransactionDate">
          <DataField>FormattedTransactionDate</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="TransactionType">
          <DataField>TransactionType</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="ReferenceNumber">
          <DataField>ReferenceNumber</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="Description">
          <DataField>Description</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="FormattedDebitAmount">
          <DataField>FormattedDebitAmount</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="FormattedCreditAmount">
          <DataField>FormattedCreditAmount</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="FormattedBalance">
          <DataField>FormattedBalance</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="Balance">
          <DataField>Balance</DataField>
          <rd:TypeName>System.Decimal</rd:TypeName>
        </Field>
      </Fields>
    </DataSet>
  </DataSets>
  <ReportSections>
    <ReportSection>
      <Body>
        <ReportItems>
          <!-- Main Data Table -->
          <Tablix Name="SupplierAccountStatementTable">
            <TablixBody>
              <TablixColumns>
                <TablixColumn>
                  <Width>1.2in</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>1in</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>1.2in</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>2.5in</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>1.2in</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>1.2in</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>1.2in</Width>
                </TablixColumn>
              </TablixColumns>
              <TablixRows>
                <!-- Header Row -->
                <TablixRow>
                  <Height>0.4in</Height>
                  <TablixCells>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="HeaderTransactionDate">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>التاريخ</Value>
                                  <Style>
                                    <FontFamily>Arial</FontFamily>
                                    <FontSize>11pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                    <Color>White</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>HeaderTransactionDate</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>#2C3E50</Color>
                              <Style>Solid</Style>
                            </Border>
                            <BackgroundColor>#2C3E50</BackgroundColor>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="HeaderTransactionType">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>النوع</Value>
                                  <Style>
                                    <FontFamily>Arial</FontFamily>
                                    <FontSize>11pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                    <Color>White</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>HeaderTransactionType</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>#2C3E50</Color>
                              <Style>Solid</Style>
                            </Border>
                            <BackgroundColor>#2C3E50</BackgroundColor>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="HeaderReferenceNumber">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>رقم المرجع</Value>
                                  <Style>
                                    <FontFamily>Arial</FontFamily>
                                    <FontSize>11pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                    <Color>White</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>HeaderReferenceNumber</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>#2C3E50</Color>
                              <Style>Solid</Style>
                            </Border>
                            <BackgroundColor>#2C3E50</BackgroundColor>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="HeaderDescription">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>البيان</Value>
                                  <Style>
                                    <FontFamily>Arial</FontFamily>
                                    <FontSize>11pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                    <Color>White</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>HeaderDescription</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>#2C3E50</Color>
                              <Style>Solid</Style>
                            </Border>
                            <BackgroundColor>#2C3E50</BackgroundColor>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="HeaderDebitAmount">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>مدين</Value>
                                  <Style>
                                    <FontFamily>Arial</FontFamily>
                                    <FontSize>11pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                    <Color>White</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>HeaderDebitAmount</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>#2C3E50</Color>
                              <Style>Solid</Style>
                            </Border>
                            <BackgroundColor>#2C3E50</BackgroundColor>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="HeaderCreditAmount">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>دائن</Value>
                                  <Style>
                                    <FontFamily>Arial</FontFamily>
                                    <FontSize>11pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                    <Color>White</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>HeaderCreditAmount</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>#2C3E50</Color>
                              <Style>Solid</Style>
                            </Border>
                            <BackgroundColor>#2C3E50</BackgroundColor>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="HeaderBalance">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>الرصيد</Value>
                                  <Style>
                                    <FontFamily>Arial</FontFamily>
                                    <FontSize>11pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                    <Color>White</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>HeaderBalance</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>#2C3E50</Color>
                              <Style>Solid</Style>
                            </Border>
                            <BackgroundColor>#2C3E50</BackgroundColor>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                  </TablixCells>
                </TablixRow>

                <!-- Data Row -->
                <TablixRow>
                  <Height>0.35in</Height>
                  <TablixCells>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="TransactionDate">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!FormattedTransactionDate.Value</Value>
                                  <Style>
                                    <FontFamily>Arial</FontFamily>
                                    <FontSize>10pt</FontSize>
                                    <Color>#2C3E50</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>TransactionDate</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>#BDC3C7</Color>
                              <Style>Solid</Style>
                            </Border>
                            <BackgroundColor>=IIF(RowNumber(Nothing) Mod 2, "White", "#F8F9FA")</BackgroundColor>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="TransactionType">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!TransactionType.Value</Value>
                                  <Style>
                                    <FontFamily>Arial</FontFamily>
                                    <FontSize>10pt</FontSize>
                                    <Color>#2C3E50</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>TransactionType</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>#BDC3C7</Color>
                              <Style>Solid</Style>
                            </Border>
                            <BackgroundColor>=IIF(RowNumber(Nothing) Mod 2, "White", "#F8F9FA")</BackgroundColor>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="ReferenceNumber">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!ReferenceNumber.Value</Value>
                                  <Style>
                                    <FontFamily>Arial</FontFamily>
                                    <FontSize>10pt</FontSize>
                                    <Color>#2C3E50</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>ReferenceNumber</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>#BDC3C7</Color>
                              <Style>Solid</Style>
                            </Border>
                            <BackgroundColor>=IIF(RowNumber(Nothing) Mod 2, "White", "#F8F9FA")</BackgroundColor>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Description">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!Description.Value</Value>
                                  <Style>
                                    <FontFamily>Arial</FontFamily>
                                    <FontSize>10pt</FontSize>
                                    <Color>#2C3E50</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Right</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Description</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>#BDC3C7</Color>
                              <Style>Solid</Style>
                            </Border>
                            <BackgroundColor>=IIF(RowNumber(Nothing) Mod 2, "White", "#F8F9FA")</BackgroundColor>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="DebitAmount">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!FormattedDebitAmount.Value</Value>
                                  <Style>
                                    <FontFamily>Arial</FontFamily>
                                    <FontSize>10pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                    <Color>#E74C3C</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>DebitAmount</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>#BDC3C7</Color>
                              <Style>Solid</Style>
                            </Border>
                            <BackgroundColor>=IIF(RowNumber(Nothing) Mod 2, "White", "#F8F9FA")</BackgroundColor>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="CreditAmount">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!FormattedCreditAmount.Value</Value>
                                  <Style>
                                    <FontFamily>Arial</FontFamily>
                                    <FontSize>10pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                    <Color>#27AE60</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>CreditAmount</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>#BDC3C7</Color>
                              <Style>Solid</Style>
                            </Border>
                            <BackgroundColor>=IIF(RowNumber(Nothing) Mod 2, "White", "#F8F9FA")</BackgroundColor>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Balance">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!FormattedBalance.Value</Value>
                                  <Style>
                                    <FontFamily>Arial</FontFamily>
                                    <FontSize>10pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                    <Color>=IIF(Fields!Balance.Value >= 0, "#27AE60", "#E74C3C")</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Balance</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>#BDC3C7</Color>
                              <Style>Solid</Style>
                            </Border>
                            <BackgroundColor>=IIF(RowNumber(Nothing) Mod 2, "White", "#F8F9FA")</BackgroundColor>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                  </TablixCells>
                </TablixRow>
              </TablixRows>
            </TablixBody>
            <TablixColumnHierarchy>
              <TablixMembers>
                <TablixMember />
                <TablixMember />
                <TablixMember />
                <TablixMember />
                <TablixMember />
                <TablixMember />
                <TablixMember />
              </TablixMembers>
            </TablixColumnHierarchy>
            <TablixRowHierarchy>
              <TablixMembers>
                <TablixMember>
                  <KeepWithGroup>After</KeepWithGroup>
                </TablixMember>
                <TablixMember>
                  <Group Name="Details" />
                </TablixMember>
              </TablixMembers>
            </TablixRowHierarchy>
            <DataSetName>SupplierTransactionsDataSet</DataSetName>
            <Top>0.1in</Top>
            <Left>0in</Left>
            <Height>0.75in</Height>
            <Width>9.5in</Width>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
            </Style>
          </Tablix>

          <!-- Summary Section -->
          <Rectangle Name="SummarySection">
            <ReportItems>
              <Textbox Name="SummaryTitle">
                <CanGrow>true</CanGrow>
                <KeepTogether>true</KeepTogether>
                <Paragraphs>
                  <Paragraph>
                    <TextRuns>
                      <TextRun>
                        <Value>ملخص الحساب</Value>
                        <Style>
                          <FontFamily>Arial</FontFamily>
                          <FontSize>14pt</FontSize>
                          <FontWeight>Bold</FontWeight>
                          <Color>#2C3E50</Color>
                        </Style>
                      </TextRun>
                    </TextRuns>
                    <Style>
                      <TextAlign>Center</TextAlign>
                    </Style>
                  </Paragraph>
                </Paragraphs>
                <rd:DefaultName>SummaryTitle</rd:DefaultName>
                <Top>0.05in</Top>
                <Left>0in</Left>
                <Height>0.25in</Height>
                <Width>9.5in</Width>
              </Textbox>

              <Textbox Name="OpeningBalanceLabel">
                <CanGrow>true</CanGrow>
                <KeepTogether>true</KeepTogether>
                <Paragraphs>
                  <Paragraph>
                    <TextRuns>
                      <TextRun>
                        <Value>الرصيد الافتتاحي:</Value>
                        <Style>
                          <FontFamily>Arial</FontFamily>
                          <FontSize>11pt</FontSize>
                          <FontWeight>Bold</FontWeight>
                          <Color>#2C3E50</Color>
                        </Style>
                      </TextRun>
                    </TextRuns>
                    <Style>
                      <TextAlign>Right</TextAlign>
                    </Style>
                  </Paragraph>
                </Paragraphs>
                <rd:DefaultName>OpeningBalanceLabel</rd:DefaultName>
                <Top>0.35in</Top>
                <Left>6.5in</Left>
                <Height>0.2in</Height>
                <Width>1.5in</Width>
              </Textbox>

              <Textbox Name="OpeningBalanceValue">
                <CanGrow>true</CanGrow>
                <KeepTogether>true</KeepTogether>
                <Paragraphs>
                  <Paragraph>
                    <TextRuns>
                      <TextRun>
                        <Value>=First(Fields!FormattedOpeningBalance.Value, "SupplierAccountStatementDataSet")</Value>
                        <Style>
                          <FontFamily>Arial</FontFamily>
                          <FontSize>11pt</FontSize>
                          <FontWeight>Bold</FontWeight>
                          <Color>=IIF(First(Fields!OpeningBalance.Value, "SupplierAccountStatementDataSet") >= 0, "#27AE60", "#E74C3C")</Color>
                        </Style>
                      </TextRun>
                    </TextRuns>
                    <Style>
                      <TextAlign>Left</TextAlign>
                    </Style>
                  </Paragraph>
                </Paragraphs>
                <rd:DefaultName>OpeningBalanceValue</rd:DefaultName>
                <Top>0.35in</Top>
                <Left>8.1in</Left>
                <Height>0.2in</Height>
                <Width>1.4in</Width>
              </Textbox>

              <Textbox Name="TotalDebitsLabel">
                <CanGrow>true</CanGrow>
                <KeepTogether>true</KeepTogether>
                <Paragraphs>
                  <Paragraph>
                    <TextRuns>
                      <TextRun>
                        <Value>إجمالي المدين:</Value>
                        <Style>
                          <FontFamily>Arial</FontFamily>
                          <FontSize>11pt</FontSize>
                          <FontWeight>Bold</FontWeight>
                          <Color>#2C3E50</Color>
                        </Style>
                      </TextRun>
                    </TextRuns>
                    <Style>
                      <TextAlign>Right</TextAlign>
                    </Style>
                  </Paragraph>
                </Paragraphs>
                <rd:DefaultName>TotalDebitsLabel</rd:DefaultName>
                <Top>0.6in</Top>
                <Left>6.5in</Left>
                <Height>0.2in</Height>
                <Width>1.5in</Width>
              </Textbox>

              <Textbox Name="TotalDebitsValue">
                <CanGrow>true</CanGrow>
                <KeepTogether>true</KeepTogether>
                <Paragraphs>
                  <Paragraph>
                    <TextRuns>
                      <TextRun>
                        <Value>=First(Fields!FormattedTotalDebits.Value, "SupplierAccountStatementDataSet")</Value>
                        <Style>
                          <FontFamily>Arial</FontFamily>
                          <FontSize>11pt</FontSize>
                          <FontWeight>Bold</FontWeight>
                          <Color>#E74C3C</Color>
                        </Style>
                      </TextRun>
                    </TextRuns>
                    <Style>
                      <TextAlign>Left</TextAlign>
                    </Style>
                  </Paragraph>
                </Paragraphs>
                <rd:DefaultName>TotalDebitsValue</rd:DefaultName>
                <Top>0.6in</Top>
                <Left>8.1in</Left>
                <Height>0.2in</Height>
                <Width>1.4in</Width>
              </Textbox>

              <Textbox Name="TotalCreditsLabel">
                <CanGrow>true</CanGrow>
                <KeepTogether>true</KeepTogether>
                <Paragraphs>
                  <Paragraph>
                    <TextRuns>
                      <TextRun>
                        <Value>إجمالي الدائن:</Value>
                        <Style>
                          <FontFamily>Arial</FontFamily>
                          <FontSize>11pt</FontSize>
                          <FontWeight>Bold</FontWeight>
                          <Color>#2C3E50</Color>
                        </Style>
                      </TextRun>
                    </TextRuns>
                    <Style>
                      <TextAlign>Right</TextAlign>
                    </Style>
                  </Paragraph>
                </Paragraphs>
                <rd:DefaultName>TotalCreditsLabel</rd:DefaultName>
                <Top>0.85in</Top>
                <Left>6.5in</Left>
                <Height>0.2in</Height>
                <Width>1.5in</Width>
              </Textbox>

              <Textbox Name="TotalCreditsValue">
                <CanGrow>true</CanGrow>
                <KeepTogether>true</KeepTogether>
                <Paragraphs>
                  <Paragraph>
                    <TextRuns>
                      <TextRun>
                        <Value>=First(Fields!FormattedTotalCredits.Value, "SupplierAccountStatementDataSet")</Value>
                        <Style>
                          <FontFamily>Arial</FontFamily>
                          <FontSize>11pt</FontSize>
                          <FontWeight>Bold</FontWeight>
                          <Color>#27AE60</Color>
                        </Style>
                      </TextRun>
                    </TextRuns>
                    <Style>
                      <TextAlign>Left</TextAlign>
                    </Style>
                  </Paragraph>
                </Paragraphs>
                <rd:DefaultName>TotalCreditsValue</rd:DefaultName>
                <Top>0.85in</Top>
                <Left>8.1in</Left>
                <Height>0.2in</Height>
                <Width>1.4in</Width>
              </Textbox>

              <Line Name="SummaryLine">
                <Top>1.1in</Top>
                <Left>6.5in</Left>
                <Height>0in</Height>
                <Width>3in</Width>
                <Style>
                  <Border>
                    <Style>Solid</Style>
                    <Color>#2C3E50</Color>
                    <Width>1pt</Width>
                  </Border>
                </Style>
              </Line>

              <Textbox Name="ClosingBalanceLabel">
                <CanGrow>true</CanGrow>
                <KeepTogether>true</KeepTogether>
                <Paragraphs>
                  <Paragraph>
                    <TextRuns>
                      <TextRun>
                        <Value>الرصيد الختامي:</Value>
                        <Style>
                          <FontFamily>Arial</FontFamily>
                          <FontSize>12pt</FontSize>
                          <FontWeight>Bold</FontWeight>
                          <Color>#2C3E50</Color>
                        </Style>
                      </TextRun>
                    </TextRuns>
                    <Style>
                      <TextAlign>Right</TextAlign>
                    </Style>
                  </Paragraph>
                </Paragraphs>
                <rd:DefaultName>ClosingBalanceLabel</rd:DefaultName>
                <Top>1.15in</Top>
                <Left>6.5in</Left>
                <Height>0.25in</Height>
                <Width>1.5in</Width>
              </Textbox>

              <Textbox Name="ClosingBalanceValue">
                <CanGrow>true</CanGrow>
                <KeepTogether>true</KeepTogether>
                <Paragraphs>
                  <Paragraph>
                    <TextRuns>
                      <TextRun>
                        <Value>=First(Fields!FormattedClosingBalance.Value, "SupplierAccountStatementDataSet")</Value>
                        <Style>
                          <FontFamily>Arial</FontFamily>
                          <FontSize>12pt</FontSize>
                          <FontWeight>Bold</FontWeight>
                          <Color>=IIF(First(Fields!ClosingBalance.Value, "SupplierAccountStatementDataSet") >= 0, "#27AE60", "#E74C3C")</Color>
                        </Style>
                      </TextRun>
                    </TextRuns>
                    <Style>
                      <TextAlign>Left</TextAlign>
                    </Style>
                  </Paragraph>
                </Paragraphs>
                <rd:DefaultName>ClosingBalanceValue</rd:DefaultName>
                <Top>1.15in</Top>
                <Left>8.1in</Left>
                <Height>0.25in</Height>
                <Width>1.4in</Width>
              </Textbox>
            </ReportItems>
            <Top>1in</Top>
            <Left>0in</Left>
            <Height>1.5in</Height>
            <Width>9.5in</Width>
            <Style>
              <Border>
                <Style>Solid</Style>
                <Color>#BDC3C7</Color>
                <Width>1pt</Width>
              </Border>
              <BackgroundColor>#F8F9FA</BackgroundColor>
              <PaddingLeft>5pt</PaddingLeft>
              <PaddingRight>5pt</PaddingRight>
              <PaddingTop>5pt</PaddingTop>
              <PaddingBottom>5pt</PaddingBottom>
            </Style>
          </Rectangle>
        </ReportItems>
        <Height>3in</Height>
        <Style />
      </Body>
      <Width>10in</Width>
      <Page>
        <PageHeader>
          <Height>1.4in</Height>
          <PrintOnFirstPage>true</PrintOnFirstPage>
          <PrintOnLastPage>true</PrintOnLastPage>
          <ReportItems>
            <!-- Company Name -->
            <Textbox Name="CompanyName">
              <CanGrow>true</CanGrow>
              <KeepTogether>true</KeepTogether>
              <Paragraphs>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value>=First(Fields!CompanyName.Value, "CompanyInfoDataSet")</Value>
                      <Style>
                        <FontFamily>Arial</FontFamily>
                        <FontSize>18pt</FontSize>
                        <FontWeight>Bold</FontWeight>
                        <Color>#2C3E50</Color>
                      </Style>
                    </TextRun>
                  </TextRuns>
                  <Style>
                    <TextAlign>Center</TextAlign>
                  </Style>
                </Paragraph>
              </Paragraphs>
              <rd:DefaultName>CompanyName</rd:DefaultName>
              <Top>0.05in</Top>
              <Left>0in</Left>
              <Height>0.25in</Height>
              <Width>9.5in</Width>
            </Textbox>

            <!-- Company Address and Phone -->
            <Textbox Name="CompanyInfo">
              <CanGrow>true</CanGrow>
              <KeepTogether>true</KeepTogether>
              <Paragraphs>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value>=First(Fields!CompanyAddress.Value, "CompanyInfoDataSet") + " - " + First(Fields!CompanyPhone.Value, "CompanyInfoDataSet")</Value>
                      <Style>
                        <FontFamily>Arial</FontFamily>
                        <FontSize>10pt</FontSize>
                        <Color>#34495E</Color>
                      </Style>
                    </TextRun>
                  </TextRuns>
                  <Style>
                    <TextAlign>Center</TextAlign>
                  </Style>
                </Paragraph>
              </Paragraphs>
              <rd:DefaultName>CompanyInfo</rd:DefaultName>
              <Top>0.3in</Top>
              <Left>0in</Left>
              <Height>0.15in</Height>
              <Width>9.5in</Width>
            </Textbox>

            <!-- Separator Line -->
            <Line Name="SeparatorLine">
              <Top>0.5in</Top>
              <Left>0in</Left>
              <Height>0in</Height>
              <Width>9.5in</Width>
              <Style>
                <Border>
                  <Style>Solid</Style>
                  <Color>#BDC3C7</Color>
                  <Width>2pt</Width>
                </Border>
              </Style>
            </Line>

            <!-- Header Title -->
            <Textbox Name="HeaderTitle">
              <CanGrow>true</CanGrow>
              <KeepTogether>true</KeepTogether>
              <Paragraphs>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value>كشف حساب مورد</Value>
                      <Style>
                        <FontFamily>Arial</FontFamily>
                        <FontSize>16pt</FontSize>
                        <FontWeight>Bold</FontWeight>
                        <Color>#2C3E50</Color>
                      </Style>
                    </TextRun>
                  </TextRuns>
                  <Style>
                    <TextAlign>Center</TextAlign>
                  </Style>
                </Paragraph>
              </Paragraphs>
              <rd:DefaultName>HeaderTitle</rd:DefaultName>
              <Top>0.6in</Top>
              <Left>0in</Left>
              <Height>0.25in</Height>
              <Width>9.5in</Width>
            </Textbox>

            <!-- Supplier Name (Left) -->
            <Textbox Name="HeaderSupplierName">
              <CanGrow>true</CanGrow>
              <KeepTogether>true</KeepTogether>
              <Paragraphs>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value>اسم المورد: </Value>
                      <Style>
                        <FontFamily>Arial</FontFamily>
                        <FontSize>10pt</FontSize>
                        <FontWeight>Bold</FontWeight>
                        <Color>#2C3E50</Color>
                      </Style>
                    </TextRun>
                    <TextRun>
                      <Value>=First(Fields!SupplierName.Value, "SupplierAccountStatementDataSet")</Value>
                      <Style>
                        <FontFamily>Arial</FontFamily>
                        <FontSize>10pt</FontSize>
                        <Color>#34495E</Color>
                      </Style>
                    </TextRun>
                  </TextRuns>
                  <Style>
                    <TextAlign>Right</TextAlign>
                  </Style>
                </Paragraph>
              </Paragraphs>
              <rd:DefaultName>HeaderSupplierName</rd:DefaultName>
              <Top>0.9in</Top>
              <Left>0in</Left>
              <Height>0.2in</Height>
              <Width>3.1in</Width>
            </Textbox>

            <!-- Statement Date (Center) -->
            <Textbox Name="HeaderStatementDate">
              <CanGrow>true</CanGrow>
              <KeepTogether>true</KeepTogether>
              <Paragraphs>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value>تاريخ الكشف: </Value>
                      <Style>
                        <FontFamily>Arial</FontFamily>
                        <FontSize>10pt</FontSize>
                        <FontWeight>Bold</FontWeight>
                        <Color>#2C3E50</Color>
                      </Style>
                    </TextRun>
                    <TextRun>
                      <Value>=First(Fields!FormattedStatementDate.Value, "SupplierAccountStatementDataSet")</Value>
                      <Style>
                        <FontFamily>Arial</FontFamily>
                        <FontSize>10pt</FontSize>
                        <Color>#34495E</Color>
                      </Style>
                    </TextRun>
                  </TextRuns>
                  <Style>
                    <TextAlign>Center</TextAlign>
                  </Style>
                </Paragraph>
              </Paragraphs>
              <rd:DefaultName>HeaderStatementDate</rd:DefaultName>
              <Top>0.9in</Top>
              <Left>3.2in</Left>
              <Height>0.2in</Height>
              <Width>3.1in</Width>
            </Textbox>

            <!-- Period (Right) -->
            <Textbox Name="HeaderPeriod">
              <CanGrow>true</CanGrow>
              <KeepTogether>true</KeepTogether>
              <Paragraphs>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value>من </Value>
                      <Style>
                        <FontFamily>Arial</FontFamily>
                        <FontSize>9pt</FontSize>
                        <FontWeight>Bold</FontWeight>
                        <Color>#2C3E50</Color>
                      </Style>
                    </TextRun>
                    <TextRun>
                      <Value>=First(Fields!FormattedFromDate.Value, "SupplierAccountStatementDataSet")</Value>
                      <Style>
                        <FontFamily>Arial</FontFamily>
                        <FontSize>9pt</FontSize>
                        <Color>#34495E</Color>
                      </Style>
                    </TextRun>
                    <TextRun>
                      <Value> إلى </Value>
                      <Style>
                        <FontFamily>Arial</FontFamily>
                        <FontSize>9pt</FontSize>
                        <FontWeight>Bold</FontWeight>
                        <Color>#2C3E50</Color>
                      </Style>
                    </TextRun>
                    <TextRun>
                      <Value>=First(Fields!FormattedToDate.Value, "SupplierAccountStatementDataSet")</Value>
                      <Style>
                        <FontFamily>Arial</FontFamily>
                        <FontSize>9pt</FontSize>
                        <Color>#34495E</Color>
                      </Style>
                    </TextRun>
                  </TextRuns>
                  <Style>
                    <TextAlign>Left</TextAlign>
                  </Style>
                </Paragraph>
              </Paragraphs>
              <rd:DefaultName>HeaderPeriod</rd:DefaultName>
              <Top>0.9in</Top>
              <Left>6.4in</Left>
              <Height>0.2in</Height>
              <Width>3.1in</Width>
            </Textbox>
          </ReportItems>
        </PageHeader>
        <LeftMargin>0.25in</LeftMargin>
        <RightMargin>0.25in</RightMargin>
        <TopMargin>0.25in</TopMargin>
        <BottomMargin>0.25in</BottomMargin>
        <Style />
      </Page>
    </ReportSection>
  </ReportSections>

  <rd:ReportUnitType>Inch</rd:ReportUnitType>
  <rd:ReportID>b8c8c8c8-8c8c-8c8c-8c8c-8c8c8c8c8c8c</rd:ReportID>
</Report>