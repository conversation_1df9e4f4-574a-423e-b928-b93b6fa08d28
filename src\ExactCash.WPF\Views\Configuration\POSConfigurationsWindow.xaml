<Window x:Class="ExactCash.WPF.Views.Configuration.POSConfigurationsWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:ExactCash.WPF.Views.Configuration"
        mc:Ignorable="d"
        Title="إعدادات النظام"
        Height="750"
        Width="1200"
        x:Name="POSConfigurationsScreen"
        WindowStartupLocation="CenterScreen"
        FlowDirection="RightToLeft">

    <Window.Resources>
        <Style x:Key="ModernButton"
               TargetType="Button">
            <Setter Property="Background"
                    Value="#2196F3"/>
            <Setter Property="Foreground"
                    Value="White"/>
            <Setter Property="BorderThickness"
                    Value="0"/>
            <Setter Property="Padding"
                    Value="15,5"/>
            <Setter Property="FontSize"
                    Value="14"/>
            <Setter Property="Height"
                    Value="35"/>
            <Setter Property="Width"
                    Value="100"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="4">
                            <ContentPresenter HorizontalAlignment="Center"
                                              VerticalAlignment="Center"/>
                        </Border>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
            <Style.Triggers>
                <Trigger Property="IsMouseOver"
                         Value="True">
                    <Setter Property="Background"
                            Value="#1976D2"/>
                </Trigger>
            </Style.Triggers>
        </Style>

        <Style x:Key="SuccessButton"
               TargetType="Button"
               BasedOn="{StaticResource ModernButton}">
            <Setter Property="Background"
                    Value="#4CAF50"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver"
                         Value="True">
                    <Setter Property="Background"
                            Value="#388E3C"/>
                </Trigger>
            </Style.Triggers>
        </Style>

        <Style x:Key="IconButton"
               TargetType="Button">
            <Setter Property="Background"
                    Value="Transparent"/>
            <Setter Property="Foreground"
                    Value="White"/>
            <Setter Property="BorderThickness"
                    Value="0"/>
            <Setter Property="Padding"
                    Value="0"/>
            <Setter Property="FontSize"
                    Value="14"/>
            <Setter Property="Height"
                    Value="30"/>
            <Setter Property="Width"
                    Value="30"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="4">
                            <ContentPresenter HorizontalAlignment="Center"
                                              VerticalAlignment="Center"/>
                        </Border>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
            <Style.Triggers>
                <Trigger Property="IsMouseOver"
                         Value="True">
                    <Setter Property="Background"
                            Value="#1976D2"/>
                </Trigger>
            </Style.Triggers>
        </Style>

        <Style x:Key="DangerButton"
               TargetType="Button"
               BasedOn="{StaticResource ModernButton}">
            <Setter Property="Background"
                    Value="#F44336"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver"
                         Value="True">
                    <Setter Property="Background"
                            Value="#D32F2F"/>
                </Trigger>
            </Style.Triggers>
        </Style>
    </Window.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0"
                Background="#2196F3"
                Padding="20,10">
            <Grid>
                <TextBlock Text="إعدادات النظام"
                           Foreground="White"
                           FontSize="24"
                           HorizontalAlignment="Center"/>
            </Grid>
        </Border>

        <!-- Main Content -->
        <TabControl Grid.Row="1"
                    Margin="20">
            <!-- System Configurations Tab -->
            <TabItem Header="إعدادات النظام">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <DataGrid x:Name="ConfigurationsGrid"
                              ItemsSource="{Binding SystemConfigurations}"
                              AutoGenerateColumns="False"
                              IsReadOnly="False"
                              SelectionMode="Single"
                              Margin="0,0,0,10"
                              RowEditEnding="ConfigurationsGrid_RowEditEnding">
                        <DataGrid.Columns>
                            <DataGridTextColumn Header="اسم الإعداد"
                                                Binding="{Binding SettingName}"
                                                IsReadOnly="True"
                                                Width="*"/>
                            <DataGridTextColumn Header="القيمة"
                                                Binding="{Binding SettingValue, UpdateSourceTrigger=PropertyChanged}"
                                                Width="*"/>
                            <DataGridTextColumn Header="الوصف"
                                                Binding="{Binding Description, UpdateSourceTrigger=PropertyChanged}"
                                                Width="*"/>
                            <DataGridTemplateColumn Header="الإجراءات"
                                                    Width="80">
                                <DataGridTemplateColumn.CellTemplate>
                                    <DataTemplate>
                                        <Button Style="{StaticResource SuccessButton}"
                                                Click="SaveConfigurationButton_Click"
                                                Tag="{Binding}"
                                                ToolTip="حفظ"
                                                Width="60"
                                                Height="30"
                                                Content="حفظ"/>
                                    </DataTemplate>
                                </DataGridTemplateColumn.CellTemplate>
                            </DataGridTemplateColumn>
                        </DataGrid.Columns>
                    </DataGrid>

                    <StackPanel Grid.Row="1"
                                Orientation="Horizontal"
                                HorizontalAlignment="Left">
                        <TextBlock Text="ملاحظة: يمكنك تعديل القيم مباشرة في الجدول، ثم اضغط 'حفظ' لحفظ التغييرات"
                                   Foreground="Gray"
                                   VerticalAlignment="Center"
                                   FontSize="12"/>
                    </StackPanel>
                </Grid>
            </TabItem>

            <!-- Unit Definitions Tab -->
            <TabItem Header="تعريفات الوحدات">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <DataGrid x:Name="UnitsGrid"
                              ItemsSource="{Binding Units}"
                              AutoGenerateColumns="False"
                              IsReadOnly="False"
                              SelectionMode="Single"
                              Margin="0,0,0,10"
                              RowEditEnding="UnitsGrid_RowEditEnding">
                        <DataGrid.Columns>
                            <DataGridTextColumn Header="الاسم"
                                                Binding="{Binding Name, UpdateSourceTrigger=PropertyChanged}"
                                                Width="*"/>
                            <DataGridTextColumn Header="الرمز"
                                                Binding="{Binding Symbol, UpdateSourceTrigger=PropertyChanged}"
                                                Width="*"/>
                            <DataGridTextColumn Header="معامل التحويل للوحدة الأساسية"
                                                Binding="{Binding ConversionRateToBaseUnit, UpdateSourceTrigger=PropertyChanged}"
                                                Width="*"/>
                            <DataGridTemplateColumn Header="الإجراءات"
                                                    Width="80">
                                <DataGridTemplateColumn.CellTemplate>
                                    <DataTemplate>

                                        <Button Style="{StaticResource ModernButton}"
                                                Background="#dc3545"
                                                Click="DeleteUnitButton_Click"
                                                Tag="{Binding}"
                                                ToolTip="حذف"
                                                Width="35"
                                                Height="30">
                                            <TextBlock Text="🗑️"
                                                       FontSize="16"/>
                                        </Button>



                                    </DataTemplate>
                                </DataGridTemplateColumn.CellTemplate>
                            </DataGridTemplateColumn>
                        </DataGrid.Columns>
                    </DataGrid>

                    <StackPanel Grid.Row="1"
                                Orientation="Horizontal"
                                HorizontalAlignment="Left">
                        <Button Content="إضافة وحدة جديدة"
                                Style="{StaticResource ModernButton}"
                                Margin="0,0,10,0"
                                Click="AddUnitButton_Click"/>
                        <Button Content="حفظ الوحدة الجديدة"
                                Style="{StaticResource SuccessButton}"
                                Click="SaveUnitButton_Click"/>
                        <TextBlock Text="ملاحظة: الوحدات الموجودة يتم حفظها تلقائياً عند التعديل"
                                   Foreground="Gray"
                                   VerticalAlignment="Center"
                                   Margin="20,0,0,0"
                                   FontSize="12"/>
                    </StackPanel>
                </Grid>
            </TabItem>

            <!-- Brands Tab -->
            <TabItem Header="العلامات التجارية">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <DataGrid x:Name="BrandsGrid"
                              ItemsSource="{Binding Brands}"
                              AutoGenerateColumns="False"
                              IsReadOnly="False"
                              SelectionMode="Single"
                              Margin="0,0,0,10"
                              RowEditEnding="BrandsGrid_RowEditEnding">
                        <DataGrid.Columns>
                            <DataGridTextColumn Header="الاسم"
                                                Binding="{Binding Name, UpdateSourceTrigger=PropertyChanged}"
                                                Width="*"/>
                            <DataGridTextColumn Header="الوصف"
                                                Binding="{Binding Description, UpdateSourceTrigger=PropertyChanged}"
                                                Width="2*"/>
                            <DataGridTemplateColumn Header="الإجراءات"
                                                    Width="80">
                                <DataGridTemplateColumn.CellTemplate>
                                    <DataTemplate>


                                        <Button Style="{StaticResource ModernButton}"
                                                Background="#dc3545"
                                                Click="DeleteBrandButton_Click"
                                                Tag="{Binding}"
                                                ToolTip="حذف"
                                                Width="35"
                                                Height="30">
                                            <TextBlock Text="🗑️"
                                                       FontSize="16"/>
                                        </Button>



                                    </DataTemplate>
                                </DataGridTemplateColumn.CellTemplate>
                            </DataGridTemplateColumn>
                        </DataGrid.Columns>
                    </DataGrid>

                    <StackPanel Grid.Row="1"
                                Orientation="Horizontal"
                                HorizontalAlignment="Left">
                        <Button Content="إضافة"
                                Style="{StaticResource ModernButton}"
                                Margin="0,0,10,0"
                                Click="AddBrandButton_Click"/>
                        <Button Content="حفظ"
                                Style="{StaticResource SuccessButton}"
                                Click="SaveBrandButton_Click"/>
                    </StackPanel>
                </Grid>
            </TabItem>

            <!-- Categories Tab -->
            <TabItem Header="الفئات">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <DataGrid x:Name="CategoriesGrid"
                              ItemsSource="{Binding Categories}"
                              AutoGenerateColumns="False"
                              IsReadOnly="False"
                              SelectionMode="Single"
                              Margin="0,0,0,10"
                              RowEditEnding="CategoriesGrid_RowEditEnding">
                        <DataGrid.Columns>
                            <DataGridTextColumn Header="الاسم"
                                                Binding="{Binding Name, UpdateSourceTrigger=PropertyChanged}"
                                                Width="*"/>
                            <DataGridTemplateColumn Header="الإجراءات"
                                                    Width="80">
                                <DataGridTemplateColumn.CellTemplate>
                                    <DataTemplate>


                                        <Button Style="{StaticResource ModernButton}"
                                                Background="#dc3545"
                                                Click="DeleteCategoryButton_Click"
                                                Tag="{Binding}"
                                                ToolTip="حذف"
                                                Width="35"
                                                Height="30">
                                            <TextBlock Text="🗑️"
                                                       FontSize="16"/>
                                        </Button>



                                    </DataTemplate>
                                </DataGridTemplateColumn.CellTemplate>
                            </DataGridTemplateColumn>
                        </DataGrid.Columns>
                    </DataGrid>

                    <StackPanel Grid.Row="1"
                                Orientation="Horizontal"
                                HorizontalAlignment="Left">
                        <Button Content="إضافة فئة جديدة"
                                Style="{StaticResource ModernButton}"
                                Margin="0,0,10,0"
                                Click="AddCategoryButton_Click"/>
                        <Button Content="حفظ الفئة الجديدة"
                                Style="{StaticResource SuccessButton}"
                                Click="SaveCategoryButton_Click"/>
                        <TextBlock Text="ملاحظة: الفئات الموجودة يتم حفظها تلقائياً عند التعديل"
                                   Foreground="Gray"
                                   VerticalAlignment="Center"
                                   Margin="20,0,0,0"
                                   FontSize="12"/>
                    </StackPanel>
                </Grid>
            </TabItem>

        </TabControl>
    </Grid>
</Window>