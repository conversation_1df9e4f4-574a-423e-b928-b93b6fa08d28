﻿using ExactCash.Application.DTOs;
using ExactCash.Application.DTOs.Common;
using ExactCash.Application.Responses;
using ExactCash.WPF.Services.Interfaces;

namespace ExactCash.WPF.Services
{
    public class SystemConfigurationServiceClient : ISystemConfigurationServiceClient
    {
        private readonly HttpService _httpService;
        private const string BaseUrl = "api/SystemConfigurations";

        public SystemConfigurationServiceClient(string baseUrl)
        {
            _httpService = new HttpService(baseUrl);
        }

        public async Task<SystemConfigurationDto> CreateSystemConfigurationAsync(SystemConfigurationDto configurationDto)
        {
            return await _httpService.PostAsync<SystemConfigurationDto>($"{BaseUrl}", configurationDto);
        }

        public async Task DeleteSystemConfigurationAsync(int id)
        {
            await _httpService.DeleteAsync($"{BaseUrl}/{id}");
        }

        public async Task<PagedResponse<SystemConfigurationDto>> GetAllSystemConfigurationsAsync(string key, string category, PaginationFilter pagination)
        {
            var queryParams = new System.Collections.Generic.Dictionary<string, string>
            {
                { "key", key },
                { "category", category },
                { "pageNumber", pagination.PageNumber.ToString() },
                { "pageSize", pagination.PageSize.ToString() }
            };

            return await _httpService.GetAsync<PagedResponse<SystemConfigurationDto>>($"{BaseUrl}" + queryParams);
        }

        public async Task<List<SystemConfigurationDto>> GetAllSystemConfigurationsAsync()
        {
            return await _httpService.GetAsync<List<SystemConfigurationDto>>($"{BaseUrl}" + "/get-all-configs");
        }

        public async Task<SystemConfigurationDto> GetSystemConfigurationByIdAsync(int id)
        {
            return await _httpService.GetAsync<SystemConfigurationDto>($"{BaseUrl}/{id}");
        }

        public async Task UpdateSystemConfigurationAsync(SystemConfigurationDto configurationDto)
        {
            await _httpService.PutAsync($"{BaseUrl}/{configurationDto.Id}", configurationDto);
        }
    }
}
