using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using AutoMapper;
using ExactCash.WPF.Services;
using ExactCash.WPF.Services.Interfaces;
using ExactCash.WPF.Services.Product;
using ExactCash.WPF.ViewModels.Common;
using ExactCash.WPF.ViewModels.Sale;
using Microsoft.Extensions.Configuration;

namespace ExactCash.WPF.Views.Sale
{
    public partial class SaleView : Window
    {
        public SaleView(
            IApiUserService apiService,
            LoadingViewModel loadingViewModel,
            ICustomerService customerService,
            IProductService productService,
            ISaleServiceClient saleService,
            IUnitServiceClient unitServiceClient,
            IMapper mapper,
            ICategoryServiceClient categoryServiceClient,
            IBrandsServiceClient brandsServiceClient,
            ISupplierServiceClient supplierServiceClient,
            NotificationViewModel notificationViewModel,
            IPurchaseServiceClient purchaseServiceClient,
            IUserServiceClient userServiceClient,
            IRoleServiceClient roleServiceClient,
            IConfiguration configuration,
            IExpenseCategoryServiceClient expenseCategoryServiceClient
            , IExpenseServiceClient expenseServiceClient,
            ISystemConfigurationServiceClient systemConfigurationServiceClient,
            IReportServiceClient reportServiceClient,
            ISupplierCategoryServiceClient supplierCategoryServiceClient,
            ICustomerCategoryServiceClient customerCategoryServiceClient
            )
        {
            InitializeComponent();
            DataContext = new SaleViewModel(
                apiService,
                loadingViewModel,
                customerService,
                productService,
                saleService,
                unitServiceClient,
                mapper, categoryServiceClient, brandsServiceClient, notificationViewModel, configuration);
            var headerViewModel = new HeaderViewModel(
                apiService, loadingViewModel,
                customerService, productService,
                saleService, unitServiceClient, mapper,
                brandsServiceClient, categoryServiceClient,
                supplierServiceClient, notificationViewModel,
                purchaseServiceClient, userServiceClient,
                roleServiceClient, configuration,
                expenseCategoryServiceClient,
                expenseServiceClient,
                systemConfigurationServiceClient,
                reportServiceClient,
                supplierCategoryServiceClient,
                customerCategoryServiceClient
                );
            // Set the header's DataContext
            var headerBorder = (Border)FindName("HeaderBorder");
            if (headerBorder != null)
            {
                headerBorder.DataContext = headerViewModel;
            }
        }

        private void ProductSearchBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (DataContext is SaleViewModel viewModel && viewModel.AddProductToCartCommand.CanExecute(null))
            {
                viewModel.AddProductToCartCommand.Execute(null);
            }
        }

        private void BarcodeSearchBox_PreviewKeyDown(object sender, System.Windows.Input.KeyEventArgs e)
        {
            if (e.Key == Key.Tab)
            {
                e.Handled = true; // Prevent tab navigation
            }
            else if (e.Key == Key.Return)
            {
                if (DataContext is SaleViewModel viewModel && !string.IsNullOrWhiteSpace(viewModel.BarcodeSearchTerm))
                {
                    viewModel.SearchByBarcodeCommand.Execute(null);
                }
                e.Handled = true;
            }
        }

        private void ProductSearchBox_DropDownOpened(object sender, EventArgs e)
        {
            if (DataContext is SaleViewModel viewModel)
            {
                viewModel.LoadProductsOnDropdownOpen();
            }
        }

        private void ProductTextBox_GotFocus(object sender, RoutedEventArgs e)
        {
            if (DataContext is SaleViewModel viewModel)
            {
                viewModel.LoadProductsOnDropdownOpen();
            }
        }
    }
}