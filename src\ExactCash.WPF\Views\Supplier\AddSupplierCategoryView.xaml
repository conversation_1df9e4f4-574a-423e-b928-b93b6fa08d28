<Window x:Class="ExactCash.WPF.Views.Supplier.AddSupplierCategoryView"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="إضافة تصنيف مورد"
        Height="420"
        Width="500"
        WindowStartupLocation="CenterOwner"
        ResizeMode="NoResize"
        WindowStyle="None"
        Background="Transparent"
        FlowDirection="RightToLeft"
        AllowsTransparency="True">

    <Border Background="White"
            CornerRadius="10"
            BorderThickness="1"
            BorderBrush="#E0E0E0">
        <Border.Effect>
            <DropShadowEffect Color="Gray"
                              Direction="315"
                              ShadowDepth="5"
                              Opacity="0.3"
                              BlurRadius="10"/>
        </Border.Effect>
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="60"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="60"/>
            </Grid.RowDefinitions>

            <!-- Header -->
            <Border Grid.Row="0"
                    Background="#0078D4"
                    CornerRadius="10,10,0,0">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <TextBlock x:Name="HeaderText"
                               Text="إضافة تصنيف مورد"
                               Grid.Column="0"
                               VerticalAlignment="Center"
                               HorizontalAlignment="Center"
                               FontSize="18"
                               FontWeight="Bold"
                               Foreground="White"/>

                    <Button Grid.Column="1"
                            Content="✕"
                            Click="CloseButton_Click"
                            FontSize="16"
                            Foreground="White"
                            Width="40"
                            Height="40"
                            Background="Transparent"
                            BorderThickness="0"
                            Margin="10"/>
                </Grid>
            </Border>

            <!-- Content -->
            <Grid Grid.Row="1"
                  Margin="30">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="20"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="20"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="20"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <!-- Name -->
                <TextBlock Grid.Row="0"
                           Text="اسم التصنيف *"
                           FontWeight="Bold"
                           Margin="0,0,0,5"/>
                <TextBox x:Name="NameTextBox"
                         Grid.Row="0"
                         Margin="0,25,0,0"
                         Height="35"
                         FontSize="14"
                         Padding="10,5"
                         KeyDown="NameTextBox_KeyDown"/>

                <!-- Description -->
                <TextBlock Grid.Row="2"
                           Text="الوصف"
                           FontWeight="Bold"
                           Margin="0,0,0,5"/>
                <TextBox x:Name="DescriptionTextBox"
                         Grid.Row="2"
                         Margin="0,25,0,0"
                         Height="80"
                         FontSize="14"
                         Padding="10,5"
                         TextWrapping="Wrap"
                         AcceptsReturn="True"
                         VerticalScrollBarVisibility="Auto"
                         KeyDown="DescriptionTextBox_KeyDown"/>

                <!-- IsActive -->
                <CheckBox x:Name="IsActiveCheckBox"
                          Grid.Row="4"
                          Content="نشط"
                          FontSize="14"
                          IsChecked="True"
                          Margin="0,10,0,0"/>

                <!-- Validation Message -->
                <TextBlock x:Name="ValidationMessage"
                           Grid.Row="6"
                           Foreground="Red"
                           FontSize="12"
                           Visibility="Collapsed"
                           TextWrapping="Wrap"/>
            </Grid>

            <!-- Footer -->
            <Border Grid.Row="2"
                    Background="#F8F9FA"
                    CornerRadius="0,0,10,10"
                    BorderThickness="0,1,0,0"
                    BorderBrush="#E0E0E0">
                <StackPanel Orientation="Horizontal"
                            HorizontalAlignment="Center"
                            VerticalAlignment="Center">
                    <Button x:Name="SaveButton"
                            Content="حفظ"
                            Click="SaveButton_Click"
                            Width="100"
                            Height="35"
                            Margin="0,0,10,0"
                            Background="#28A745"
                            Foreground="White"
                            BorderThickness="0"
                            FontWeight="Bold"
                            Cursor="Hand"/>
                    <Button Content="إلغاء"
                            Click="CloseButton_Click"
                            Width="100"
                            Height="35"
                            Background="#6C757D"
                            Foreground="White"
                            BorderThickness="0"
                            FontWeight="Bold"
                            Cursor="Hand"/>
                </StackPanel>
            </Border>
        </Grid>
    </Border>
</Window>
