using System.Windows;
using ExactCash.Application.DTOs;
using ExactCash.Application.Interfaces;
using ExactCash.WPF.Services.Interfaces;

namespace ExactCash.WPF.Views.Expense
{
    public partial class AddExpenseCategoryView : Window
    {
        private readonly IExpenseCategoryServiceClient _service;

        public string CategoryName => NameTextBox.Text;
        public string CategoryDescription => DescriptionTextBox.Text;

        public AddExpenseCategoryView(IExpenseCategoryServiceClient service)
        {
            InitializeComponent();
            _service = service;
        }

        private async void SaveButton_Click(object sender, RoutedEventArgs e)
        {
            if (string.IsNullOrWhiteSpace(NameTextBox.Text))
            {
                System.Windows.MessageBox.Show("يرجى إدخال اسم التصنيف.");
                return;
            }
            var newCategory = new ExpenseCategoryDto
            {
                Name = NameTextBox.Text,
                Description = DescriptionTextBox.Text
            };
            await _service.CreateAsync(newCategory);
            DialogResult = true;
            Close();
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }

    }
}