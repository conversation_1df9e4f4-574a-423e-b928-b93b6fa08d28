using ExactCash.Application.DTOs;
using ExactCash.Application.Responses;

namespace ExactCash.WPF.Services
{
    /// <summary>
    /// WPF service client interface for managing supplier categories.
    /// </summary>
    public interface ISupplierCategoryServiceClient
    {
        /// <summary>
        /// Gets a supplier category by its ID.
        /// </summary>
        /// <param name="id">The ID of the supplier category.</param>
        /// <returns>The supplier category DTO.</returns>
        Task<SupplierCategoryDto> GetByIdAsync(int id);

        /// <summary>
        /// Gets all supplier categories.
        /// </summary>
        /// <returns>A list of all supplier categories.</returns>
        Task<List<SupplierCategoryDto>> GetAllAsync();

        /// <summary>
        /// Gets all active supplier categories.
        /// </summary>
        /// <returns>A list of active supplier categories.</returns>
        Task<List<SupplierCategoryDto>> GetActiveAsync();

        /// <summary>
        /// Creates a new supplier category.
        /// </summary>
        /// <param name="categoryDto">The supplier category to create.</param>
        /// <returns>The created supplier category.</returns>
        Task<BaseResponse<SupplierCategoryDto>> CreateAsync(SupplierCategoryDto categoryDto);

        /// <summary>
        /// Updates an existing supplier category.
        /// </summary>
        /// <param name="categoryDto">The supplier category to update.</param>
        /// <returns>The updated supplier category.</returns>
        Task<BaseResponse<SupplierCategoryDto>> UpdateAsync(SupplierCategoryDto categoryDto);

        /// <summary>
        /// Deletes a supplier category by its ID.
        /// </summary>
        /// <param name="id">The ID of the supplier category to delete.</param>
        /// <returns>A task representing the asynchronous operation.</returns>
        Task DeleteAsync(int id);

        /// <summary>
        /// Soft deletes a supplier category by its ID.
        /// </summary>
        /// <param name="id">The ID of the supplier category to soft delete.</param>
        /// <returns>A task representing the asynchronous operation.</returns>
        Task SoftDeleteAsync(int id);
    }
}
