<Window x:Class="ExactCash.WPF.Views.User.AddUserView"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="إضافة مستخدم جديد"
        Height="510"
        Width="400"
        WindowStartupLocation="CenterOwner"
        WindowStyle="None"
        ResizeMode="NoResize"
        Background="White"
        FlowDirection="RightToLeft"
        FontFamily="Segoe UI">
    <Window.Resources>
        <Style x:Key="ModernButton"
               TargetType="Button">
            <Setter Property="Background"
                    Value="#0078D4"/>
            <Setter Property="Foreground"
                    Value="White"/>
            <Setter Property="BorderThickness"
                    Value="0"/>
            <Setter Property="Padding"
                    Value="15,8"/>
            <Setter Property="FontSize"
                    Value="14"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                CornerRadius="4">
                            <ContentPresenter HorizontalAlignment="Center"
                                              VerticalAlignment="Center"/>
                        </Border>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
            <Style.Triggers>
                <Trigger Property="IsMouseOver"
                         Value="True">
                    <Setter Property="Background"
                            Value="#106EBE"/>
                </Trigger>
            </Style.Triggers>
        </Style>
        <Style x:Key="ModernTextBox"
               TargetType="TextBox">
            <Setter Property="Height"
                    Value="35"/>
            <Setter Property="Padding"
                    Value="10,5"/>
            <Setter Property="BorderThickness"
                    Value="1"/>
            <Setter Property="BorderBrush"
                    Value="#CCCCCC"/>
            <Setter Property="Background"
                    Value="White"/>
            <Setter Property="FontSize"
                    Value="14"/>
        </Style>
        <Style x:Key="ModernComboBox"
               TargetType="ComboBox">
            <Setter Property="Height"
                    Value="35"/>
            <Setter Property="Padding"
                    Value="10,5"/>
            <Setter Property="BorderThickness"
                    Value="1"/>
            <Setter Property="BorderBrush"
                    Value="#CCCCCC"/>
            <Setter Property="Background"
                    Value="White"/>
            <Setter Property="FontSize"
                    Value="14"/>
        </Style>
        <Style x:Key="ModernListBox"
                TargetType="ListBox">
            <Setter Property="Height"
                    Value="35"/>
            <Setter Property="Padding"
                    Value="10,5"/>
            <Setter Property="BorderThickness"
                    Value="1"/>
            <Setter Property="BorderBrush"
                    Value="#CCCCCC"/>
            <Setter Property="Background"
                    Value="White"/>
            <Setter Property="FontSize"
                    Value="14"/>
        </Style>
    </Window.Resources>
    <Border BorderBrush="#DEE2E6"
            BorderThickness="1"
            CornerRadius="8">
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>
            <!-- Header -->
            <Border Background="#0078D4"
                    Grid.Row="0"
                    CornerRadius="8,8,0,0">
                <Grid Margin="20,15">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>
                    <TextBlock Text="إضافة مستخدم جديد"
                               Foreground="White"
                               FontSize="18"
                               FontWeight="SemiBold"/>
                    <Button Grid.Column="1"
                            Content="✕"
                            Foreground="White"
                            Background="Transparent"
                            BorderThickness="0"
                            FontSize="16"
                            Click="CancelButton_Click"/>
                </Grid>
            </Border>
            <!-- Content -->
            <StackPanel Grid.Row="1"
                        Margin="20">
                <TextBlock Text="اسم المستخدم *"
                           Margin="0,0,0,5"/>
                <TextBox x:Name="UserNameBox"
                         Style="{StaticResource ModernTextBox}"
                         Margin="0,0,0,15"/>
                <TextBlock Text="البريد الإلكتروني *"
                           Margin="0,0,0,5"/>
                <TextBox x:Name="EmailBox"
                         Style="{StaticResource ModernTextBox}"
                         Margin="0,0,0,15"/>
                <TextBlock Text="رقم الجوال *"
                           Margin="0,0,0,5"/>
                <TextBox x:Name="PhoneBox"
                         Style="{StaticResource ModernTextBox}"
                         Margin="0,0,0,15"/>
                <TextBlock Text="كلمة المرور *"
                           Margin="0,0,0,5"/>
                <PasswordBox x:Name="PasswordBox"
                             Height="35"
                             Padding="10,0"
                             Margin="0,0,0,15"/>
                <TextBlock Text="الأدوار"
                           Margin="0,0,0,5"/>
                <ListBox x:Name="RolesComboBox"
                         Style="{StaticResource ModernListBox}"
                         SelectionMode="Multiple"
                         Margin="0,0,0,0"/>
            </StackPanel>
            <!-- Footer -->
            <Border Grid.Row="2"
                    Background="#F8F9FA"
                    BorderThickness="0,1,0,0"
                    BorderBrush="#DEE2E6"
                    CornerRadius="0,0,8,8">
                <StackPanel Orientation="Horizontal"
                            HorizontalAlignment="Center"
                            Margin="20,15">
                    <Button Content="إضافة"
                            Width="120"
                            Height="35"
                            Margin="0,0,10,0"
                            Style="{StaticResource ModernButton}"
                            Click="AddButton_Click"/>
                    <Button Content="إلغاء"
                            Width="120"
                            Height="35"
                            Style="{StaticResource ModernButton}"
                            Background="#6C757D"
                            Click="CancelButton_Click"/>
                </StackPanel>
            </Border>
        </Grid>
    </Border>
</Window> 