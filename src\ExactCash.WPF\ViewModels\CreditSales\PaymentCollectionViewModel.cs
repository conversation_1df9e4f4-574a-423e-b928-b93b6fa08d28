using ExactCash.Application.DTOs;
using ExactCash.WPF.Commands;
using ExactCash.WPF.Helpers;
using ExactCash.WPF.Models;
using ExactCash.WPF.Services;
using ExactCash.WPF.Services.Interfaces;
using ExactCash.WPF.ViewModels;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using System.Windows;
using System.Windows.Input;
using System.Windows.Media;
using Microsoft.Extensions.DependencyInjection;

namespace ExactCash.WPF.ViewModels.CreditSales
{
    public class PaymentCollectionViewModel : ViewModelBase, INotifyPropertyChanged
    {
        #region Fields
        private IPaymentServiceClient _paymentService;
        private ISaleServiceClient _saleService;
        private ICustomerServiceClient _customerService;

        private int _customerId;
        private string _customerName;
        private string _customerPhone;
        private decimal _creditLimit;
        private decimal _totalOutstandingAmount;

        private ObservableCollection<OutstandingSaleDto> _outstandingInvoices;
        private OutstandingSaleDto _selectedInvoice;

        private decimal _paymentAmount;
        private string _selectedPaymentMethod;
        private DateTime _paymentDate;
        private string _collectedBy;
        private string _notes;

        private ObservableCollection<string> _paymentMethods;
        #endregion

        #region Properties
        public int CustomerId
        {
            get => _customerId;
            set => SetProperty(ref _customerId, value);
        }

        public string CustomerName
        {
            get => _customerName;
            set => SetProperty(ref _customerName, value);
        }

        public string CustomerPhone
        {
            get => _customerPhone;
            set => SetProperty(ref _customerPhone, value);
        }

        public decimal CreditLimit
        {
            get => _creditLimit;
            set => SetProperty(ref _creditLimit, value);
        }

        public decimal TotalOutstandingAmount
        {
            get => _totalOutstandingAmount;
            set => SetProperty(ref _totalOutstandingAmount, value);
        }

        public ObservableCollection<OutstandingSaleDto> OutstandingInvoices
        {
            get => _outstandingInvoices;
            set => SetProperty(ref _outstandingInvoices, value);
        }

        public OutstandingSaleDto SelectedInvoice
        {
            get => _selectedInvoice;
            set
            {
                if (SetProperty(ref _selectedInvoice, value))
                {
                    if (value != null)
                    {
                        PaymentAmount = value.RemainingAmount;
                    }
                }
            }
        }

        public decimal PaymentAmount
        {
            get => _paymentAmount;
            set
            {
                if (SetProperty(ref _paymentAmount, value))
                {
                    OnPropertyChanged(nameof(RemainingAfterPayment));
                    OnPropertyChanged(nameof(PaymentStatus));
                    OnPropertyChanged(nameof(PaymentStatusColor));
                    OnPropertyChanged(nameof(IsValidPayment));
                }
            }
        }

        public string SelectedPaymentMethod
        {
            get => _selectedPaymentMethod;
            set => SetProperty(ref _selectedPaymentMethod, value);
        }

        public DateTime PaymentDate
        {
            get => _paymentDate;
            set => SetProperty(ref _paymentDate, value);
        }

        public string CollectedBy
        {
            get => _collectedBy;
            set => SetProperty(ref _collectedBy, value);
        }

        public string Notes
        {
            get => _notes;
            set => SetProperty(ref _notes, value);
        }

        public ObservableCollection<string> PaymentMethods
        {
            get => _paymentMethods;
            set => SetProperty(ref _paymentMethods, value);
        }

        // Calculated Properties
        public decimal RemainingAfterPayment => TotalOutstandingAmount - PaymentAmount;

        public string PaymentStatus
        {
            get
            {
                if (PaymentAmount <= 0) return "لا يوجد دفع";
                if (PaymentAmount >= TotalOutstandingAmount) return "دفع كامل";
                return "دفع جزئي";
            }
        }

        public string PaymentStatusColor
        {
            get
            {
                if (PaymentAmount <= 0) return "#6C757D";
                if (PaymentAmount >= TotalOutstandingAmount) return "#28A745";
                return "#FFC107";
            }
        }

        public bool IsValidPayment => PaymentAmount > 0 && PaymentAmount <= TotalOutstandingAmount && !string.IsNullOrEmpty(SelectedPaymentMethod);
        #endregion

        #region Commands
        public ICommand CollectPaymentCommand { get; private set; }
        public ICommand PrintReceiptCommand { get; private set; }
        public ICommand CancelCommand { get; private set; }
        #endregion

        #region Constructors
        public PaymentCollectionViewModel()
        {
            // Get services from DI container
            var app = (App)System.Windows.Application.Current;
            _paymentService = app.ServiceProvider.GetRequiredService<IPaymentServiceClient>();
            _saleService = app.ServiceProvider.GetRequiredService<ISaleServiceClient>();
            _customerService = app.ServiceProvider.GetRequiredService<ICustomerServiceClient>();

            InitializeViewModel();
        }

        public PaymentCollectionViewModel(OutstandingSaleDto sale)
        {
            // Get services from DI container
            var app = (App)System.Windows.Application.Current;
            _paymentService = app.ServiceProvider.GetRequiredService<IPaymentServiceClient>();
            _saleService = app.ServiceProvider.GetRequiredService<ISaleServiceClient>();
            _customerService = app.ServiceProvider.GetRequiredService<ICustomerServiceClient>();

            InitializeViewModel();
            LoadCustomerDataFromSale(sale);
            SelectedInvoice = sale;
        }

        public PaymentCollectionViewModel(int customerId)
        {
            // Get services from DI container
            var app = (App)System.Windows.Application.Current;
            _paymentService = app.ServiceProvider.GetRequiredService<IPaymentServiceClient>();
            _saleService = app.ServiceProvider.GetRequiredService<ISaleServiceClient>();
            _customerService = app.ServiceProvider.GetRequiredService<ICustomerServiceClient>();

            InitializeViewModel();
            LoadCustomerData(customerId);
        }

        public PaymentCollectionViewModel(CustomerDto customer)
        {
            // Get services from DI container
            var app = (App)System.Windows.Application.Current;
            _paymentService = app.ServiceProvider.GetRequiredService<IPaymentServiceClient>();
            _saleService = app.ServiceProvider.GetRequiredService<ISaleServiceClient>();
            _customerService = app.ServiceProvider.GetRequiredService<ICustomerServiceClient>();

            InitializeViewModel();
            LoadCustomerDataFromCustomer(customer);
        }
        #endregion

        #region Methods
        private void InitializeViewModel()
        {
            // Services are already initialized in constructors via DI

            // Initialize collections
            OutstandingInvoices = new ObservableCollection<OutstandingSaleDto>();
            PaymentMethods = new ObservableCollection<string>
            {
                "نقدي",
                "بطاقة ائتمان",
                "بطاقة خصم",
                "تحويل بنكي",
                "شيك",
                "محفظة إلكترونية"
            };

            // Initialize default values
            PaymentDate = DateTime.Today;
            CollectedBy = Environment.UserName; // Or get from current user session
            SelectedPaymentMethod = "نقدي";

            // Initialize commands
            CollectPaymentCommand = new Commands.RelayCommand(ExecuteCollectPayment, CanExecuteCollectPayment);
            PrintReceiptCommand = new Commands.RelayCommand(ExecutePrintReceipt, CanExecutePrintReceipt);
            CancelCommand = new Commands.RelayCommand(ExecuteCancel);
        }

        private async void LoadCustomerData(int customerId)
        {
            try
            {
                CustomerId = customerId;

                // Load customer information
                // TODO: Implement actual customer service call when available
                // var customer = await _customerService.GetByIdAsync(customerId);
                // CustomerName = customer.FullName;
                // CustomerPhone = customer.Phone;
                // CreditLimit = customer.CreditLimit;

                // For now, set default values
                CustomerName = $"عميل رقم {customerId}";
                CustomerPhone = "غير محدد";
                CreditLimit = 0;

                // Load outstanding invoices for this customer
                await LoadOutstandingInvoicesAsync();

                // Calculate total outstanding amount
                TotalOutstandingAmount = OutstandingInvoices.Sum(i => i.RemainingAmount);
            }
            catch (Exception ex)
            {
                Helpers.BootstrapMessageBoxHelper.ShowError($"حدث خطأ أثناء تحميل بيانات العميل: {ex.Message}", "خطأ");
            }
        }

        private async void LoadCustomerDataFromSale(OutstandingSaleDto sale)
        {
            try
            {
                // Set customer information directly from the sale
                CustomerId = sale.CustomerId;
                CustomerName = sale.CustomerName;
                CustomerPhone = sale.CustomerPhone ?? "غير محدد";
                CreditLimit = 0; // Will be loaded from customer service if needed

                // Create a single-item collection with the current sale
                OutstandingInvoices.Clear();
                OutstandingInvoices.Add(sale);

                // Set total outstanding amount to this sale's remaining amount
                TotalOutstandingAmount = sale.RemainingAmount;
            }
            catch (Exception ex)
            {
                Helpers.BootstrapMessageBoxHelper.ShowError($"حدث خطأ أثناء تحميل بيانات العميل: {ex.Message}", "خطأ");
            }
        }

        private async void LoadCustomerDataFromCustomer(CustomerDto customer)
        {
            try
            {
                // Set customer information directly from the customer
                CustomerId = customer.Id;
                CustomerName = customer.FullName;
                CustomerPhone = customer.Phone ?? "غير محدد";
                CreditLimit = customer.CreditLimit;

                // Load outstanding invoices for this customer
                await LoadOutstandingInvoicesAsync();

                // Calculate total outstanding amount
                TotalOutstandingAmount = OutstandingInvoices.Sum(i => i.RemainingAmount);
            }
            catch (Exception ex)
            {
                Helpers.BootstrapMessageBoxHelper.ShowError($"حدث خطأ أثناء تحميل بيانات العميل: {ex.Message}", "خطأ");
            }
        }

        private async Task LoadOutstandingInvoicesAsync()
        {
            try
            {
                // Try to call the actual service to get outstanding invoices for the customer
                var result = await _paymentService.GetOutstandingSalesForCustomerAsync(CustomerId);

                if (result != null && result.Count > 0)
                {
                    OutstandingInvoices.Clear();
                    foreach (var invoice in result)
                    {
                        OutstandingInvoices.Add(invoice);
                    }
                }
            }
            catch (Exception ex)
            {
                Helpers.BootstrapMessageBoxHelper.ShowError($"حدث خطأ أثناء تحميل الفواتير المستحقة: {ex.Message}", "خطأ");
            }
        }

        private bool CanExecuteCollectPayment()
        {
            return IsValidPayment;
        }

        private Window FindParentWindow()
        {
            // Find all windows
            foreach (Window window in System.Windows.Application.Current.Windows)
            {
                // Look for the window that contains a SaleView
                if (window.Content != null &&
                    VisualTreeHelper.GetChild(window.Content as DependencyObject, 0) is FrameworkElement content &&
                    window.Name == "PaymentCollectionViewScreen")
                {
                    return window;
                }
            }
            return System.Windows.Application.Current.MainWindow;
        }

        private async void ExecuteCollectPayment()
        {
            try
            {
                var paymentDto = new PaymentCollectionDto
                {
                    SaleId = SelectedInvoice?.Id ?? 0,
                    InvoiceNumber = SelectedInvoice?.InvoiceNumber ?? "",
                    CustomerId = CustomerId,
                    CustomerName = CustomerName,
                    RemainingAmount = TotalOutstandingAmount,
                    PaymentAmount = PaymentAmount,
                    PaymentMethod = SelectedPaymentMethod,
                    PaymentDate = PaymentDate,
                    Notes = Notes,
                    CollectedBy = CollectedBy
                };

                // Validate payment
                if (!paymentDto.IsValidPayment)
                {
                    Helpers.BootstrapMessageBoxHelper.ShowError("مبلغ الدفعة غير صحيح", "خطأ في التحقق", owner: FindParentWindow());
                    return;
                }

                // Process payment
                var result = await _paymentService.CollectPaymentAsync(paymentDto);

                if (result.Success)
                {
                    Helpers.BootstrapMessageBoxHelper.ShowSuccess("تم تحصيل الدفعة بنجاح", "نجح العملية", owner: FindParentWindow());
                }
                else
                {
                    Helpers.BootstrapMessageBoxHelper.ShowError($"فشل في تحصيل الدفعة: {result.Message}", "خطأ", owner: FindParentWindow());
                    return;
                }

                // Close the window with success result
                var window = System.Windows.Application.Current.Windows.OfType<Window>().FirstOrDefault(w => w.DataContext == this);
                if (window != null)
                {
                    window.DialogResult = true;
                    window.Close();
                }
            }
            catch (Exception ex)
            {
                Helpers.BootstrapMessageBoxHelper.ShowError($"حدث خطأ أثناء تحصيل الدفعة: {ex.Message}", "خطأ", owner: FindParentWindow());
            }
        }

        private bool CanExecutePrintReceipt()
        {
            return PaymentAmount > 0;
        }

        private void ExecutePrintReceipt()
        {
            try
            {
                // Generate and print payment receipt
                Helpers.BootstrapMessageBoxHelper.ShowSuccess("سيتم طباعة الإيصال قريباً", "طباعة الإيصال");
            }
            catch (Exception ex)
            {
                Helpers.BootstrapMessageBoxHelper.ShowError($"حدث خطأ أثناء طباعة الإيصال: {ex.Message}", "خطأ");
            }
        }

        private void ExecuteCancel()
        {
            var window = System.Windows.Application.Current.Windows.OfType<Window>().FirstOrDefault(w => w.DataContext == this);
            if (window != null)
            {
                window.DialogResult = false;
                window.Close();
            }
        }
        #endregion

        #region INotifyPropertyChanged
        public event PropertyChangedEventHandler PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
        #endregion
    }
}
