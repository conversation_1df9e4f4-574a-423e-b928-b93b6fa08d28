﻿using AutoMapper;
using ExactCash.Application.DTOs;
using ExactCash.Application.DTOs.Common;
using ExactCash.Application.Enums;
using ExactCash.Application.Interfaces;
using ExactCash.Application.Responses;
using ExactCash.Domain.Entities;
using ExactCash.Infrastructure.Persistence;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using System.Linq;
#nullable disable

namespace ExactCash.Application.Services
{
    public class SaleService : ISaleService
    {
        private readonly AppPostgreSQLDbContext _context;
        private readonly IMapper _mapper;
        private readonly ILogger<SaleService> _logger;

        public SaleService(AppPostgreSQLDbContext context, IMapper mapper, ILogger<SaleService> logger)
        {
            _context = context;
            _mapper = mapper;
            _logger = logger;
        }

        public async Task<SaleDto> GetByIdAsync(int id)
        {
            var sale = await _context.Sales
            .Include(s => s.Customer)
            .Include(s => s.SaleItems)
            .ThenInclude(x => x.Product)
            .ThenInclude(p => p.Category)
            .Include(s => s.SaleItems)
            .ThenInclude(x => x.Product)
            .ThenInclude(p => p.Brand)
            .FirstOrDefaultAsync(s => s.Id == id);

            if (sale == null)
                return null;

            return _mapper.Map<SaleDto>(sale);
        }

        public async Task<PagedResponse<SaleDto>> GetAllAsync(int? customerId, string invoiceNumber, DateTime? startDate, DateTime? endDate, PaginationFilter pagination)
        {
            try
            {
                var query = _context.Sales
                .Include(x => x.Customer)
                .Include(s => s.SaleItems)
                .ThenInclude(si => si.Product)
                .AsQueryable();

                if (customerId.HasValue)
                    query = query.Where(s => s.CustomerId == customerId);

                if (startDate.HasValue)
                {
                    var startDateUtc = startDate.Value.Kind == DateTimeKind.Utc
                        ? startDate.Value
                        : DateTime.SpecifyKind(startDate.Value, DateTimeKind.Utc);
                    query = query.Where(s => s.CreationDate >= startDateUtc);
                }

                if (endDate.HasValue)
                {
                    var endDateUtc = endDate.Value.Kind == DateTimeKind.Utc
                        ? endDate.Value.AddDays(1)
                        : DateTime.SpecifyKind(endDate.Value.AddDays(1), DateTimeKind.Utc);
                    query = query.Where(s => s.CreationDate <= endDateUtc);
                }

                if (!string.IsNullOrEmpty(invoiceNumber))
                    query = query.Where(x => x.InvoiceNumber.ToLower().Contains(invoiceNumber.ToLower()));

                var totalRecords = await query.CountAsync();

                query = pagination.SortField switch
                {
                    nameof(Sale.CreationDate) => pagination.SortOrder == SortOrder.Asc ?
                        query.OrderBy(s => s.CreationDate) : query.OrderByDescending(s => s.CreationDate),
                    nameof(Sale.TotalAmount) => pagination.SortOrder == SortOrder.Asc ?
                        query.OrderBy(s => s.TotalAmount) : query.OrderByDescending(s => s.TotalAmount),
                    _ => pagination.SortOrder == SortOrder.Desc ?
                        query.OrderBy(s => s.CreationDate) : query.OrderByDescending(s => s.CreationDate)
                };

                var result = await query
                    .Skip((pagination.PageNumber - 1) * pagination.PageSize)
                    .Take(pagination.PageSize)
                    .ToListAsync();

                return PaginationHelper.CreatePagedResponse(
                    _mapper.Map<List<SaleDto>>(result),
                    pagination.PageNumber,
                    pagination.PageSize,
                    totalRecords);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving sales");
            }
            return null;
        }

        public async Task<int> GetLastSaleIdAsync()
        {
            return await _context.Sales
                  .OrderByDescending(s => s.Id)
                  .Select(s => s.Id)
                  .FirstOrDefaultAsync();
        }

        /// <summary>
        /// Creates a new sale and associated sale items, and updates product stock quantities.
        /// </summary>
        /// <param name="saleDto"></param>
        /// <returns></returns>
        public async Task<BaseResponse<string>> CreateAsync(SaleDto saleDto, string createdBy)
        {
            using var transaction = await _context.Database.BeginTransactionAsync();
            try
            {
                var sale = new Sale
                {
                    CustomerId = saleDto.CustomerId,
                    TotalAmount = saleDto.TotalAmount,
                    InvoiceNumber = saleDto.InvoiceNumber,
                    InvoiceType = saleDto.InvoiceType,
                    DiscountAmount = saleDto.DiscountAmount,
                    TaxAmount = saleDto.TaxAmount,
                    NetAmount = saleDto.NetAmount,
                    PaidAmount = saleDto.PaidAmount,
                    RemainingAmount = saleDto.RemainingAmount,
                    Notes = saleDto.Notes,
                    CreationDate = DateTime.UtcNow,
                    SaleDate = DateTime.UtcNow,
                    CreatedBy = createdBy
                };

                await _context.Sales.AddAsync(sale);
                await _context.SaveChangesAsync();

                // Create CreditSaleTransaction if this is a credit sale (RemainingAmount > 0)
                if (sale.RemainingAmount > 0 && sale.CustomerId > 0)
                {
                    var creditTransaction = new CreditSaleTransaction(
                        saleId: sale.Id,
                        customerId: sale.CustomerId,
                        originalCreditAmount: sale.RemainingAmount,
                        dueDate: DateTime.UtcNow.AddDays(30) // Default 30 days credit term
                    );
                    creditTransaction.CreatedBy = createdBy;

                    await _context.CreditSaleTransactions.AddAsync(creditTransaction);

                    // Update customer's outstanding balance
                    var customer = await _context.Customers.FindAsync(sale.CustomerId);
                    if (customer != null)
                    {
                        customer.AddToOutstandingBalance(sale.RemainingAmount);
                        _context.Entry(customer).State = EntityState.Modified;
                    }
                }

                foreach (var item in saleDto.Payments)
                {
                    var payment = new Payment
                    {
                        SaleId = sale.Id,
                        Amount = item.Amount,
                        PaymentMethod = item.PaymentMethod,
                        PaymentDate = DateTime.UtcNow
                    };

                    await _context.Payments.AddAsync(payment);
                }

                foreach (var item in saleDto.SaleItems)
                {
                    var saleItem = new SaleItem
                    {
                        SaleId = sale.Id,
                        ProductId = item.ProductId,
                        UnitId = item.UnitId,
                        Quantity = item.Quantity,
                        Price = item.Price,
                        Discount = item.Discount
                    };

                    await _context.SaleItems.AddAsync(saleItem);

                    // Update product stock quantity
                    var product = await _context.Products.FindAsync(item.ProductId);
                    if (product != null)
                    {
                        product.StockQuantity -= item.Quantity;
                        _context.Entry(product).State = EntityState.Modified;
                    }
                }

                await _context.SaveChangesAsync();
                await transaction.CommitAsync();

                return ResponseHelper.Success(StatusCodes.Status200OK, saleDto.InvoiceNumber, "تم حفظ الفاتوره بنجاح");
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                _logger.LogError(ex, "Error creating sale");
                return ResponseHelper.Failure<string>(StatusCodes.Status500InternalServerError, string.Empty, $"Error creating sale {ex.Message}");
            }
        }

        public async Task UpdateAsync(SaleDto saleDto)
        {
            var sale = await _context.Sales
                .Include(s => s.SaleItems)
                .FirstOrDefaultAsync(s => s.Id == saleDto.Id);

            if (sale == null)
                throw new KeyNotFoundException($"Sale with ID {saleDto.Id} not found.");

            using var transaction = await _context.Database.BeginTransactionAsync();
            try
            {
                // Restore product stock for old items
                foreach (var oldItem in sale.SaleItems)
                {
                    var product = await _context.Products.FindAsync(oldItem.ProductId);
                    if (product != null)
                    {
                        product.StockQuantity += oldItem.Quantity;
                        _context.Entry(product).State = EntityState.Modified;
                    }
                }

                // Remove old items
                _context.SaleItems.RemoveRange(sale.SaleItems);

                // Update sale details
                sale.CustomerId = saleDto.CustomerId;
                sale.TotalAmount = saleDto.TotalAmount;
                sale.RemainingAmount = saleDto.RemainingAmount;
                sale.NetAmount = saleDto.NetAmount;
                sale.DiscountAmount = saleDto.DiscountAmount;
                sale.TaxAmount = saleDto.TaxAmount;
                sale.PaidAmount = saleDto.PaidAmount;
                sale.LastUpdatedDate = DateTime.UtcNow;

                // Add new items
                foreach (var item in saleDto.SaleItems)
                {
                    var saleItem = new SaleItem
                    {
                        SaleId = sale.Id,
                        ProductId = item.ProductId,
                        Quantity = item.Quantity,
                        Price = item.Price,
                        UnitId = item.UnitId,
                        Discount = item.Discount
                    };

                    await _context.SaleItems.AddAsync(saleItem);

                    // Update product stock
                    var product = await _context.Products.FindAsync(item.ProductId);
                    if (product != null)
                    {
                        product.StockQuantity -= item.Quantity;
                        _context.Entry(product).State = EntityState.Modified;
                    }
                }

                await _context.SaveChangesAsync();
                await transaction.CommitAsync();
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                _logger.LogError(ex, "Error updating sale");
            }
        }

        public async Task DeleteAsync(int id)
        {
            var sale = await _context.Sales
                .Include(s => s.SaleItems)
                .FirstOrDefaultAsync(s => s.Id == id);

            if (sale == null)
                throw new KeyNotFoundException($"Sale with ID {id} not found.");

            using var transaction = await _context.Database.BeginTransactionAsync();
            try
            {
                // Restore product stock
                foreach (var item in sale.SaleItems)
                {
                    var product = await _context.Products.FindAsync(item.ProductId);
                    if (product != null)
                    {
                        product.StockQuantity += item.Quantity;
                        _context.Entry(product).State = EntityState.Modified;
                    }
                }

                _context.SaleItems.RemoveRange(sale.SaleItems);
                _context.Sales.Remove(sale);
                await _context.SaveChangesAsync();
                await transaction.CommitAsync();
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                _logger.LogError(ex, "Error deleting sale");
            }
        }

        public async Task<List<DailySalesReportDto>> GetDailySalesReport(DateTime? startDate, DateTime? endDate)
        {
            try
            {
                var query = from sale in _context.Sales
                            where sale.CreationDate >= startDate && sale.CreationDate <= endDate
                            group sale by new
                            {
                                Date = sale.SaleDate.Date,
                                CashierName = sale.CreatedBy ?? "غير محدد"
                            } into grouped
                            select new DailySalesReportDto
                            {
                                SaleDate = grouped.Key.Date,
                                TotalSales = grouped.Sum(s => s.TotalAmount),
                                InvoiceCount = grouped.Count(),
                                CashierName = grouped.Key.CashierName,
                                AverageTransactionValue = grouped.Average(s => s.TotalAmount),
                                TotalItemsSold = grouped.Sum(s => s.SaleItems.Sum(si => (int)si.Quantity)),
                                DiscountAmount = grouped.Sum(s => s.DiscountAmount),
                                TaxAmount = grouped.Sum(s => s.TaxAmount),
                                NetAmount = grouped.Sum(s => s.NetAmount),
                                PaidAmount = grouped.Sum(s => s.PaidAmount),
                                RemainingAmount = grouped.Sum(s => s.RemainingAmount),
                                UniqueCustomers = grouped.Select(s => s.CustomerId).Distinct().Count(),
                                CashPayments = grouped.Where(s => s.Payments.Any(p => p.PaymentMethod == "نقدي")).Sum(s => s.PaidAmount),
                                CreditPayments = grouped.Where(s => s.Payments.Any(p => p.PaymentMethod == "آجل")).Sum(s => s.PaidAmount)
                            };

                return await query.OrderBy(x => x.SaleDate).ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError($"an error occurred {ex.Message}");
                return new List<DailySalesReportDto>();
            }
        }

        public async Task<DailySalesSummaryDto> GetDailySalesSummary(DateTime? startDate, DateTime? endDate)
        {
            try
            {
                var sales = await _context.Sales
                    .Where(s => s.CreationDate >= startDate && s.CreationDate <= endDate)
                    .Include(s => s.SaleItems)
                    .Include(s => s.Payments)
                    .ToListAsync();

                if (!sales.Any())
                {
                    return new DailySalesSummaryDto
                    {
                        ReportStartDate = startDate ?? DateTime.Today,
                        ReportEndDate = endDate ?? DateTime.Today
                    };
                }

                var dailyTotals = sales
                    .GroupBy(s => s.SaleDate.Date)
                    .Select(g => new
                    {
                        Date = g.Key,
                        TotalSales = g.Sum(s => s.TotalAmount),
                        InvoiceCount = g.Count(),
                        CashierName = g.First().CreatedBy ?? "غير محدد"
                    })
                    .ToList();

                var bestDay = dailyTotals.OrderByDescending(d => d.TotalSales).First();
                var worstDay = dailyTotals.OrderBy(d => d.TotalSales).First();

                var topCashier = sales
                    .GroupBy(s => s.CreatedBy ?? "غير محدد")
                    .Select(g => new
                    {
                        CashierName = g.Key,
                        TotalSales = g.Sum(s => s.TotalAmount)
                    })
                    .OrderByDescending(c => c.TotalSales)
                    .FirstOrDefault();

                var totalDays = (endDate?.Date - startDate?.Date)?.Days + 1 ?? 1;

                return new DailySalesSummaryDto
                {
                    ReportStartDate = startDate ?? DateTime.Today,
                    ReportEndDate = endDate ?? DateTime.Today,
                    TotalDays = totalDays,
                    TotalSales = sales.Sum(s => s.TotalAmount),
                    TotalInvoices = sales.Count,
                    AverageDailySales = sales.Sum(s => s.TotalAmount) / Math.Max(totalDays, 1),
                    AverageTransactionValue = sales.Any() ? sales.Average(s => s.TotalAmount) : 0,
                    TotalItemsSold = sales.Sum(s => s.SaleItems.Sum(si => (int)si.Quantity)),
                    TotalDiscounts = sales.Sum(s => s.DiscountAmount),
                    TotalTax = sales.Sum(s => s.TaxAmount),
                    TotalNetAmount = sales.Sum(s => s.NetAmount),
                    TotalPaidAmount = sales.Sum(s => s.PaidAmount),
                    TotalRemainingAmount = sales.Sum(s => s.RemainingAmount),
                    TotalUniqueCustomers = sales.Select(s => s.CustomerId).Distinct().Count(),
                    TotalCashPayments = sales.Where(s => s.Payments.Any(p => p.PaymentMethod == "نقدي")).Sum(s => s.PaidAmount),
                    TotalCreditPayments = sales.Where(s => s.Payments.Any(p => p.PaymentMethod == "آجل")).Sum(s => s.PaidAmount),
                    BestSalesDate = bestDay.Date,
                    BestSalesAmount = bestDay.TotalSales,
                    WorstSalesDate = worstDay.Date,
                    WorstSalesAmount = worstDay.TotalSales,
                    TopCashier = topCashier?.CashierName ?? "غير محدد",
                    TopCashierSales = topCashier?.TotalSales ?? 0
                };
            }
            catch (Exception ex)
            {
                _logger.LogError($"an error occurred {ex.Message}");
                return new DailySalesSummaryDto
                {
                    ReportStartDate = startDate ?? DateTime.Today,
                    ReportEndDate = endDate ?? DateTime.Today
                };
            }
        }

        public async Task<List<SalesByProductReportDto>> GetSalesByProductReport(DateTime? startDate, DateTime? endDate)
        {
            try
            {
                var query = from sale in _context.Sales
                            join saleItem in _context.SaleItems on sale.Id equals saleItem.SaleId
                            join product in _context.Products on saleItem.ProductId equals product.Id
                            join category in _context.Categories on product.CategoryId equals category.Id into categoryGroup
                            from category in categoryGroup.DefaultIfEmpty()
                            join brand in _context.Brands on product.BrandId equals brand.Id into brandGroup
                            from brand in brandGroup.DefaultIfEmpty()
                            where sale.CreationDate >= startDate && sale.CreationDate <= endDate
                            group new { saleItem, product, category, brand } by new
                            {
                                product.Id,
                                product.Name,
                                product.SKU,
                                CategoryName = category != null ? category.Name : "غير محدد",
                                BrandName = brand != null ? brand.Name : "غير محدد"
                            } into grouped
                            select new SalesByProductReportDto
                            {
                                ProductId = grouped.Key.Id,
                                ProductName = grouped.Key.Name,
                                ProductSKU = grouped.Key.SKU,
                                CategoryName = grouped.Key.CategoryName,
                                BrandName = grouped.Key.BrandName,
                                QuantitySold = grouped.Sum(x => x.saleItem.Quantity),
                                TotalSales = grouped.Sum(x => x.saleItem.Quantity * x.saleItem.Price),
                                AveragePrice = grouped.Average(x => x.saleItem.Price),
                                TransactionCount = grouped.Count(),
                                ReportStartDate = startDate ?? DateTime.MinValue,
                                ReportEndDate = endDate ?? DateTime.MaxValue
                            };

                return await query.OrderByDescending(x => x.TotalSales).ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError($"an error occurred {ex.Message}");
                return new List<SalesByProductReportDto>();
            }
        }

        public async Task<List<SalesByCategoryReportDto>> GetSalesByCategoryReport(DateTime? startDate, DateTime? endDate)
        {
            try
            {
                var query = from sale in _context.Sales
                            join saleItem in _context.SaleItems on sale.Id equals saleItem.SaleId
                            join product in _context.Products on saleItem.ProductId equals product.Id
                            join category in _context.Categories on product.CategoryId equals category.Id into categoryGroup
                            from category in categoryGroup.DefaultIfEmpty()
                            where sale.CreationDate >= startDate && sale.CreationDate <= endDate
                            group new { saleItem, product, category } by new
                            {
                                CategoryId = category != null ? category.Id : 0,
                                CategoryName = category != null ? category.Name : "غير محدد"
                            } into grouped
                            select new SalesByCategoryReportDto
                            {
                                CategoryId = grouped.Key.CategoryId,
                                CategoryName = grouped.Key.CategoryName,
                                TotalSales = grouped.Sum(x => x.saleItem.Quantity * x.saleItem.Price),
                                QuantitySold = grouped.Sum(x => x.saleItem.Quantity),
                                ProductCount = grouped.Select(x => x.product.Id).Distinct().Count(),
                                TransactionCount = grouped.Count(),
                                AverageTransactionValue = grouped.Average(x => x.saleItem.Quantity * x.saleItem.Price),
                                ReportStartDate = startDate ?? DateTime.MinValue,
                                ReportEndDate = endDate ?? DateTime.MaxValue
                            };

                var result = await query.OrderByDescending(x => x.TotalSales).ToListAsync();

                // Calculate percentage of total sales
                var totalSales = result.Sum(x => x.TotalSales);
                foreach (var item in result)
                {
                    item.PercentageOfTotalSales = totalSales > 0 ? (item.TotalSales / totalSales) * 100 : 0;
                }

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError($"an error occurred {ex.Message}");
                return new List<SalesByCategoryReportDto>();
            }
        }

        /// <summary>
        /// Collects a payment against a credit sale transaction.
        /// </summary>
        /// <param name="creditTransactionId">The credit transaction ID</param>
        /// <param name="amount">Payment amount</param>
        /// <param name="paymentMethod">Payment method</param>
        /// <param name="referenceNumber">Reference number (optional)</param>
        /// <param name="collectedBy">User who collected the payment</param>
        /// <param name="notes">Payment notes (optional)</param>
        /// <returns></returns>
        public async Task<BaseResponse<string>> CollectCreditPaymentAsync(
            int creditTransactionId,
            decimal amount,
            string paymentMethod,
            string referenceNumber = null,
            string collectedBy = null,
            string notes = null)
        {
            using var transaction = await _context.Database.BeginTransactionAsync();
            try
            {
                // Get the credit transaction
                var creditTransaction = await _context.CreditSaleTransactions
                    .Include(ct => ct.Sale)
                    .Include(ct => ct.Customer)
                    .FirstOrDefaultAsync(ct => ct.Id == creditTransactionId);

                if (creditTransaction == null)
                    return ResponseHelper.Failure<string>(StatusCodes.Status404NotFound, string.Empty, "Credit transaction not found");

                if (amount <= 0 || amount > creditTransaction.RemainingAmount)
                    return ResponseHelper.Failure<string>(StatusCodes.Status400BadRequest, string.Empty, "Invalid payment amount");

                // Create credit payment record
                var creditPayment = new CreditPayment(
                    creditSaleTransactionId: creditTransactionId,
                    amount: amount,
                    paymentMethod: paymentMethod,
                    referenceNumber: referenceNumber,
                    collectedBy: collectedBy,
                    notes: notes
                );

                await _context.CreditPayments.AddAsync(creditPayment);

                // Update credit transaction
                creditTransaction.RecordPayment(amount, paymentMethod, notes);
                _context.Entry(creditTransaction).State = EntityState.Modified;

                // Update the original sale
                var sale = creditTransaction.Sale;
                sale.PaidAmount += amount;
                sale.RemainingAmount -= amount;
                sale.LastUpdatedDate = DateTime.UtcNow;
                _context.Entry(sale).State = EntityState.Modified;

                // Update customer's outstanding balance
                var customer = creditTransaction.Customer;
                customer.ReduceOutstandingBalance(amount);
                customer.UpdateTotalSpent(amount);
                _context.Entry(customer).State = EntityState.Modified;

                // Create a regular payment record as well for consistency
                var payment = new Payment
                {
                    SaleId = sale.Id,
                    Amount = amount,
                    PaymentMethod = paymentMethod,
                    PaymentDate = DateTime.UtcNow
                };
                await _context.Payments.AddAsync(payment);

                await _context.SaveChangesAsync();
                await transaction.CommitAsync();

                return ResponseHelper.Success(StatusCodes.Status200OK, creditPayment.Id.ToString(), "Payment collected successfully");
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                _logger.LogError(ex, "Error collecting credit payment");
                return ResponseHelper.Failure<string>(StatusCodes.Status500InternalServerError, string.Empty, $"Error collecting payment: {ex.Message}");
            }
        }
    }
}