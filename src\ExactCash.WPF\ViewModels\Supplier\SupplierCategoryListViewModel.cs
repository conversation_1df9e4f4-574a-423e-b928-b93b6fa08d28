using ExactCash.Application.DTOs;
using ExactCash.WPF.Commands;
using ExactCash.WPF.Services;
using ExactCash.WPF.Views.Supplier;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using System.Windows;
using System.Windows.Input;

namespace ExactCash.WPF.ViewModels.Supplier
{
    /// <summary>
    /// ViewModel for managing supplier categories list.
    /// </summary>
    public class SupplierCategoryListViewModel : INotifyPropertyChanged
    {
        private readonly ISupplierCategoryServiceClient _service;
        private ObservableCollection<SupplierCategoryDto> _categories;

        public SupplierCategoryListViewModel(ISupplierCategoryServiceClient service)
        {
            _service = service;
            Categories = new ObservableCollection<SupplierCategoryDto>();

            // Initialize commands
            AddCategoryCommand = new RelayCommand(OpenAddCategoryDialog);
            LoadCategoriesCommand = new RelayCommand(async () => await LoadCategoriesAsync());
            EditCommand = new RelayCommand<SupplierCategoryDto>(EditCategory);
            DeleteCommand = new RelayCommand<SupplierCategoryDto>(DeleteCategory);
            ExportToExcelCommand = new RelayCommand(ExportToExcel);
            RefreshCommand = new RelayCommand(async () => await LoadCategoriesAsync());

            // Load categories on initialization
            LoadCategoriesCommand.Execute(null);
        }

        public ObservableCollection<SupplierCategoryDto> Categories
        {
            get => _categories;
            set
            {
                _categories = value;
                OnPropertyChanged();
            }
        }

        public ICommand AddCategoryCommand { get; }
        public ICommand LoadCategoriesCommand { get; }
        public ICommand EditCommand { get; }
        public ICommand DeleteCommand { get; }
        public ICommand ExportToExcelCommand { get; }
        public ICommand RefreshCommand { get; }

        private void OpenAddCategoryDialog()
        {
            var addCategoryView = new AddSupplierCategoryView(_service);
            addCategoryView.Owner = FindParentWindow();
            if (addCategoryView.ShowDialog() == true)
            {
                LoadCategoriesCommand.Execute(null);
            }
        }

        private void EditCategory(SupplierCategoryDto category)
        {
            if (category == null) return;

            var editCategoryView = new AddSupplierCategoryView(_service, category);
            editCategoryView.Owner = FindParentWindow();
            if (editCategoryView.ShowDialog() == true)
            {
                LoadCategoriesCommand.Execute(null);
            }
        }

        private async void DeleteCategory(SupplierCategoryDto category)
        {
            if (category == null) return;

            var result = System.Windows.MessageBox.Show(
                $"هل أنت متأكد من حذف تصنيف المورد '{category.Name}'؟",
                "تأكيد الحذف",
                MessageBoxButton.YesNo,
                MessageBoxImage.Question);

            if (result == MessageBoxResult.Yes)
            {
                try
                {
                    await _service.SoftDeleteAsync(category.Id);
                    Helpers.BootstrapMessageBoxHelper.Show("تم حذف تصنيف المورد بنجاح", owner: FindParentWindow());
                    LoadCategoriesCommand.Execute(null);
                }
                catch (Exception ex)
                {
                    Helpers.BootstrapMessageBoxHelper.Show($"حدث خطأ أثناء حذف تصنيف المورد: {ex.Message}", owner: FindParentWindow());
                }
            }
        }

        private void ExportToExcel()
        {
            try
            {
                // TODO: Implement Excel export functionality
                Helpers.BootstrapMessageBoxHelper.Show("سيتم تنفيذ تصدير Excel قريباً", owner: FindParentWindow());
            }
            catch (Exception ex)
            {
                Helpers.BootstrapMessageBoxHelper.Show($"حدث خطأ أثناء التصدير: {ex.Message}", owner: FindParentWindow());
            }
        }

        private Window FindParentWindow()
        {
            foreach (Window window in System.Windows.Application.Current.Windows)
            {
                if (window.IsActive)
                {
                    return window;
                }
            }
            return System.Windows.Application.Current.MainWindow;
        }

        public async Task LoadCategoriesAsync()
        {
            try
            {
                Categories.Clear();
                var categories = await _service.GetAllAsync();
                foreach (var category in categories)
                {
                    Categories.Add(category);
                }
            }
            catch (Exception ex)
            {
                Helpers.BootstrapMessageBoxHelper.Show($"حدث خطأ أثناء تحميل تصنيفات الموردين: {ex.Message}", owner: FindParentWindow());
            }
        }

        public event PropertyChangedEventHandler PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }
}
