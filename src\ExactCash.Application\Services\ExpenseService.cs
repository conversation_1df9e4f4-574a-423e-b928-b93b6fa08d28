﻿using ExactCash.Application.DTOs;
using ExactCash.Application.Interfaces;
using ExactCash.Domain.Entities;
using ExactCash.Infrastructure.Persistence;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace ExactCash.Application.Services
{
    public class ExpenseService : IExpenseService
    {
        private readonly AppPostgreSQLDbContext _context;
        private readonly ILogger<ExpenseCategoryService> _logger;

        public ExpenseService(AppPostgreSQLDbContext context, ILogger<ExpenseCategoryService> logger)
        {
            _context = context;
            _logger = logger;
        }

        public async Task<ExpenseDto> CreateAsync(ExpenseDto dto)
        {
            try
            {
                var entity = new Expense
                {
                    Amount = dto.Amount,
                    CategoryId = dto.CategoryId,
                    ReferenceNumber = dto.ReferenceNumber,
                    IsDeleted = false,
                    Description = dto.Description,
                    CreationDate = DateTime.UtcNow,
                    LastUpdatedDate = DateTime.UtcNow,
                    ExpenseDate = DateTime.UtcNow,
                };

                _context.Expenses.Add(entity);
                await _context.SaveChangesAsync();

                dto.Id = entity.Id;
                return dto;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating Expense: {@Dto}", dto);
                return null;
            }
        }

        public async Task<bool> DeleteAsync(int id)
        {
            try
            {
                var entity = await _context.Expenses.FirstOrDefaultAsync(x => x.Id == id && !x.IsDeleted);
                if (entity == null)
                {
                    _logger.LogWarning("Delete failed: ExpenseCategory with Id {Id} not found.", id);
                    return false;
                }

                entity.IsDeleted = true;
                _context.Expenses.Update(entity);
                await _context.SaveChangesAsync();
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting ExpenseCategory with Id {Id}", id);
                return false;
            }
        }

        public async Task<IEnumerable<ExpenseDto>> GetAllAsync(ExpenseSearchDto search)
        {
            try
            {
                var query = _context.Expenses
                    .Where(x => !x.IsDeleted);

                if (search.ExpenseDateFrom.HasValue)
                    query = query.Where(x => x.ExpenseDate >= search.ExpenseDateFrom.Value);

                if (search.ExpenseDateTo.HasValue)
                    query = query.Where(x => x.ExpenseDate <= search.ExpenseDateTo.Value);

                if (search.CategoryId.HasValue)
                    query = query.Where(x => x.CategoryId == search.CategoryId.Value);

                return await query
                    .Select(x => new ExpenseDto
                    {
                        Id = x.Id,
                        Amount = x.Amount,
                        CategoryId = x.CategoryId,
                        CategoryName = x.Category.Name,
                        Description = x.Description,
                        ExpenseDate = x.ExpenseDate
                    })
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving filtered Expenses.");
                return Enumerable.Empty<ExpenseDto>();
            }
        }

        public async Task<ExpenseDto> GetByIdAsync(int id)
        {
            try
            {
                var entity = await _context.Expenses
                    .FirstOrDefaultAsync(x => x.Id == id && !x.IsDeleted);

                if (entity == null)
                {
                    _logger.LogWarning("GetById failed: ExpenseCategory with Id {Id} not found.", id);
                    return null;
                }

                return new ExpenseDto
                {
                    Id = entity.Id,
                    Amount = entity.Amount,
                    CategoryId = entity.CategoryId,
                    CategoryName = entity.Category.Name,
                    Description = entity.Description,
                    ExpenseDate = entity.ExpenseDate
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving ExpenseCategory with Id {Id}", id);
                return null;
            }
        }

        public async Task<bool> UpdateAsync(ExpenseDto dto)
        {
            try
            {
                var entity = await _context.Expenses.FirstOrDefaultAsync(x => x.Id == dto.Id && !x.IsDeleted);
                if (entity == null)
                {
                    _logger.LogWarning("Update failed: ExpenseCategory with Id {Id} not found.", dto.Id);
                    return false;
                }

                entity.Amount = dto.Amount;
                entity.Description = dto.Description;
                entity.LastUpdatedDate = DateTime.UtcNow;
                entity.CategoryId = dto.CategoryId;
                entity.ExpenseDate = dto.ExpenseDate;
                _context.Expenses.Update(entity);
                await _context.SaveChangesAsync();
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating ExpenseCategory: {@Dto}", dto);
                return false;
            }
        }
    }
}
