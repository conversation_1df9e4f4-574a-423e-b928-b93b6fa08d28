﻿using ExactCash.WPF.Services;
using ExactCash.WPF.Commands;
using System.Windows;
using System.Windows.Input;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using System.Threading.Tasks;
using ExactCash.WPF.ViewModels.Common;
using ExactCash.WPF.Views.Sale;
using ExactCash.WPF.Helpers;
using AutoMapper;
using ExactCash.WPF.Services.Interfaces;
using Microsoft.Extensions.Configuration;
#nullable disable

namespace ExactCash.WPF.ViewModels.Auth
{
    public class LoginViewModel : ViewModelBase
    {
        private readonly IApiUserService _apiService;
        private readonly LoadingViewModel _loadingViewModel;
        private readonly ICustomerService _customerService;
        private readonly Services.Product.IProductService _productService;
        private readonly ISaleServiceClient _salesService;
        private readonly IUnitServiceClient _unitServiceClient;
        private string _email;
        private string _password;
        private bool _rememberMe;
        private bool _isLoading;
        private string _errorMessage;
        private readonly IMapper _mapper;
        private readonly ICategoryServiceClient _categoryServiceClient;
        private readonly IBrandsServiceClient _brandsServiceClient;
        private readonly ISupplierServiceClient _supplierServiceClient;
        private readonly NotificationViewModel _notificationViewModel;
        private readonly IPurchaseServiceClient _purchaseOrderService;
        private readonly IUserServiceClient _userServiceClient;
        private readonly IRoleServiceClient _roleServiceClient;
        private readonly IConfiguration _configuration;
        private readonly IExpenseCategoryServiceClient _expenseCategoryServiceClient;
        private readonly IExpenseServiceClient _expenseServiceClient;
        private readonly ISystemConfigurationServiceClient _systemConfigurationServiceClient;
        private readonly IReportServiceClient _reportServiceClient;
        private readonly ISupplierCategoryServiceClient _supplierCategoryServiceClient;
        private readonly ICustomerCategoryServiceClient _customerCategoryServiceClient;
        public LoginViewModel(
            IApiUserService apiService,
            LoadingViewModel loadingViewModel,
            ICustomerService customerService,
            Services.Product.IProductService productService,
            ISaleServiceClient salesService,
            IUnitServiceClient unitServiceClient,
            IMapper mapper, ICategoryServiceClient categoryServiceClient,
            IBrandsServiceClient brandsServiceClient,
            ISupplierServiceClient supplierServiceClient,
            NotificationViewModel notificationViewModel,
            IPurchaseServiceClient purchaseOrderService,
            IUserServiceClient userServiceClient,
            IRoleServiceClient roleServiceClient,
            IConfiguration configuration
            , IExpenseCategoryServiceClient expenseCategoryServiceClient,
            IExpenseServiceClient expenseServiceClient
, ISystemConfigurationServiceClient systemConfigurationServiceClient, IReportServiceClient reportServiceClient,
            ISupplierCategoryServiceClient supplierCategoryServiceClient,
            ICustomerCategoryServiceClient customerCategoryServiceClient)
        {
            _apiService = apiService;
            _loadingViewModel = loadingViewModel;
            _customerService = customerService;
            _productService = productService;
            _unitServiceClient = unitServiceClient;
            LoginCommand = new RelayCommand(async () => await ExecuteLogin(), CanExecuteLogin);
            ForgotPasswordCommand = new RelayCommand(() => System.Windows.MessageBox.Show("نسيت كلمة المرور؟ سيتم تنفيذ هذه الميزة قريباً."));
            _salesService = salesService;
            _mapper = mapper;
            _categoryServiceClient = categoryServiceClient;
            _brandsServiceClient = brandsServiceClient;
            _supplierServiceClient = supplierServiceClient;
            _notificationViewModel = notificationViewModel;
            _purchaseOrderService = purchaseOrderService;
            _userServiceClient = userServiceClient;
            _roleServiceClient = roleServiceClient;
            _configuration = configuration;
            _expenseCategoryServiceClient = expenseCategoryServiceClient;
            _expenseServiceClient = expenseServiceClient;
            _systemConfigurationServiceClient = systemConfigurationServiceClient;
            _reportServiceClient = reportServiceClient;
            _supplierCategoryServiceClient = supplierCategoryServiceClient;
            _customerCategoryServiceClient = customerCategoryServiceClient;
        }

        public string Email
        {
            get => _email;
            set
            {
                _email = value;
                OnPropertyChanged();
                (LoginCommand as RelayCommand)?.RaiseCanExecuteChanged();
            }
        }

        public string Password
        {
            get => _password;
            set
            {
                _password = value;
                OnPropertyChanged();
                (LoginCommand as RelayCommand)?.RaiseCanExecuteChanged();
            }
        }

        public bool RememberMe
        {
            get => _rememberMe;
            set => SetProperty(ref _rememberMe, value);
        }

        public bool IsLoading
        {
            get => _isLoading;
            private set => SetProperty(ref _isLoading, value);
        }

        public string ErrorMessage
        {
            get => _errorMessage;
            set => SetProperty(ref _errorMessage, value);
        }

        public RelayCommand LoginCommand { get; }
        public ICommand ForgotPasswordCommand { get; }

        private bool CanExecuteLogin()
        {
            return !string.IsNullOrWhiteSpace(Email) && !string.IsNullOrWhiteSpace(Password);
        }

        private async Task ExecuteLogin()
        {
            try
            {
                // Validate username
                if (string.IsNullOrWhiteSpace(Email))
                {
                    BootstrapMessageBoxHelper.ShowError("الرجاء إدخال اسم المستخدم");
                    return;
                }

                // Validate password
                if (string.IsNullOrWhiteSpace(Password))
                {
                    BootstrapMessageBoxHelper.ShowError("الرجاء إدخال كلمة المرور");
                    return;
                }

                // Attempt login
                var loginResult = await _apiService.LoginAsync(Email, Password);

                if (loginResult == null)
                {
                    BootstrapMessageBoxHelper.ShowError("اسم المستخدم أو كلمة المرور غير صحيحة");
                    return;
                }

                // Store the token
                _apiService.SetToken(loginResult.Token);

                // Show success message
                BootstrapMessageBoxHelper.ShowSuccess("تم تسجيل الدخول بنجاح");

                // Navigate to main window
                //var saleView = new SaleView(_apiService, _loadingViewModel, _customerService, _productService, _salesService);
                //saleView.Show();
                var dashboardView = new Views.Dashboard.DashboardView(
                    _apiService, _loadingViewModel,
                    _customerService, _productService,
                    _salesService, _unitServiceClient,
                    _mapper, _categoryServiceClient,
                    _brandsServiceClient, _supplierServiceClient,
                    _notificationViewModel, _purchaseOrderService,
                    _userServiceClient, _roleServiceClient, _configuration,
                    _expenseCategoryServiceClient, _expenseServiceClient
                    , _systemConfigurationServiceClient,
                    _reportServiceClient,
                    _supplierCategoryServiceClient,
                    _customerCategoryServiceClient
                    );
                dashboardView.Show();
                System.Windows.Application.Current.MainWindow.Close();
                System.Windows.Application.Current.MainWindow = dashboardView;
            }
            catch (Exception ex)
            {
                BootstrapMessageBoxHelper.ShowError($"حدث خطأ أثناء تسجيل الدخول: {ex.Message}");
            }
        }

        public event PropertyChangedEventHandler PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }
}
