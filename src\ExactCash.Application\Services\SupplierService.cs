using AutoMapper;
using ExactCash.Application.DTOs;
using ExactCash.Application.DTOs.Common;
using ExactCash.Application.Enums;
using ExactCash.Application.Interfaces;
using ExactCash.Application.Responses;
using ExactCash.Domain.Entities;
using ExactCash.Infrastructure.Persistence;
using Grpc.Core;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;

namespace ExactCash.Application.Services
{
    public class SupplierService : ISupplierService
    {
        private readonly AppPostgreSQLDbContext _context;
        private readonly IMapper _mapper;

        public SupplierService(AppPostgreSQLDbContext context, IMapper mapper)
        {
            _context = context;
            _mapper = mapper;
        }

        public async Task<SupplierDto> GetSupplierByIdAsync(int id)
        {
            var supplier = await _context.Suppliers
                .Include(s => s.Category)
                .FirstOrDefaultAsync(s => s.Id == id);
            if (supplier == null)
                return null;

            return _mapper.Map<SupplierDto>(supplier);
        }

        public async Task<PagedResponse<SupplierDto>> GetAllSuppliersAsync(string name, string phoneNumber, PaginationFilter pagination)
        {
            var query = _context.Suppliers
                .Include(s => s.Category)
                .Where(x => !x.IsDeleted).AsQueryable();

            if (!string.IsNullOrEmpty(name))
                query = query.Where(s => s.Name.ToLower().Contains(name.ToLower()));

            if (!string.IsNullOrEmpty(phoneNumber))
                query = query.Where(s => s.Phone.Contains(phoneNumber));

            var totalRecords = await query.CountAsync();

            query = pagination.SortField switch
            {
                nameof(Supplier.CreationDate) => pagination.SortOrder == SortOrder.Asc ?
                    query.OrderBy(s => s.CreationDate) : query.OrderByDescending(s => s.CreationDate),
                nameof(Supplier.Name) => pagination.SortOrder == SortOrder.Asc ?
                    query.OrderBy(s => s.Name) : query.OrderByDescending(s => s.Name),
                _ => pagination.SortOrder == SortOrder.Desc ?
                    query.OrderBy(s => s.CreationDate) : query.OrderByDescending(s => s.CreationDate)
            };

            var result = await query
                .Skip((pagination.PageNumber - 1) * pagination.PageSize)
                .Take(pagination.PageSize)
                .ToListAsync();

            return PaginationHelper.CreatePagedResponse(
                _mapper.Map<List<SupplierDto>>(result),
                pagination.PageNumber,
                pagination.PageSize,
                totalRecords);
        }

        public async Task<IEnumerable<SupplierDto>> GetAllSuppliersAsync(string name, string phoneNumber)
        {
            var query = _context.Suppliers
                .Include(s => s.Category)
                .Where(x => !x.IsDeleted).AsQueryable();

            if (!string.IsNullOrEmpty(name))
                query = query.Where(s => s.Name.ToLower().Contains(name.ToLower()));

            if (!string.IsNullOrEmpty(phoneNumber))
                query = query.Where(s => s.Phone.Contains(phoneNumber));

            var result = await query
                .ToListAsync();

            return _mapper.Map<List<SupplierDto>>(result);
        }

        public async Task<BaseResponse<SupplierDto>> CreateSupplierAsync(SupplierDto supplierDto)
        {
            var supplier = new Supplier
            {
                Name = supplierDto.Name,
                Email = supplierDto.Email,
                Phone = supplierDto.Phone,
                Address = supplierDto.Address,
                CreationDate = DateTime.UtcNow,
                Representative = supplierDto.Representative,
                TaxId = supplierDto.TaxId,
                BankAccountNumber = supplierDto.BankAccountNumber,
                BankName = supplierDto.BankName,
                Notes = supplierDto.Notes,
                CategoryId = supplierDto.CategoryId,
                IsActive = true,
                IsDeleted = false
            };

            await _context.Suppliers.AddAsync(supplier);
            await _context.SaveChangesAsync();
            return ResponseHelper.Success(StatusCodes.Status200OK, _mapper.Map<SupplierDto>(supplier), "Supplier created successfully.");
        }

        public async Task<BaseResponse<SupplierDto>> UpdateSupplierAsync(SupplierDto supplierDto)
        {
            var supplier = await _context.Suppliers.FindAsync(supplierDto.Id);
            if (supplier == null)
                throw new KeyNotFoundException($"Supplier with ID {supplierDto.Id} not found.");

            supplier.Name = supplierDto.Name;
            supplier.Email = supplierDto.Email;
            supplier.Phone = supplierDto.Phone;
            supplier.Address = supplierDto.Address;
            supplier.LastUpdatedDate = DateTime.UtcNow;
            supplier.BankAccountNumber = supplierDto.BankAccountNumber;
            supplier.BankName = supplierDto.BankName;
            supplier.Representative = supplierDto.Representative;
            supplier.IsDeleted = supplierDto.IsDeleted;
            supplier.TaxId = supplierDto.TaxId;
            supplier.Notes = supplierDto.Notes;
            supplier.CategoryId = supplierDto.CategoryId;

            _context.Entry(supplier).State = EntityState.Modified;
            await _context.SaveChangesAsync();
            return ResponseHelper.Success(StatusCodes.Status200OK, _mapper.Map<SupplierDto>(supplier), "Supplier updated successfully.");
        }


        public async Task<List<SupplierDto>> SearchSuppliersAsync(string searchTerm)
        {
            var query = _context.Suppliers
                .Include(s => s.Category)
                .Where(x => !x.IsDeleted).AsQueryable();

            query = query.Where(s => s.Name.ToLower().Contains(searchTerm.ToLower()) ||
                             s.Phone.Contains(searchTerm) ||
                             s.Email.ToLower().Contains(searchTerm.ToLower()) ||
                             s.Address.ToLower().Contains(searchTerm.ToLower()));

            var result = await query
                .ToListAsync();

            return _mapper.Map<List<SupplierDto>>(result);
        }

        public async Task DeleteSupplierAsync(int id)
        {
            var supplier = await _context.Suppliers.FindAsync(id);
            if (supplier == null)
                throw new KeyNotFoundException($"Supplier with ID {id} not found.");

            supplier.IsDeleted = true;
            await _context.SaveChangesAsync();
        }
    }
}