using ExactCash.Application.DTOs;
using ExactCash.Application.Responses;

namespace ExactCash.WPF.Services
{
    /// <summary>
    /// WPF service client implementation for managing supplier categories.
    /// </summary>
    public class SupplierCategoryServiceClient : ISupplierCategoryServiceClient
    {
        private readonly HttpService _httpService;
        private const string BaseEndpoint = "api/SupplierCategory";

        public SupplierCategoryServiceClient(string baseUrl)
        {
            _httpService = new HttpService(baseUrl);
        }

        public async Task<SupplierCategoryDto> GetByIdAsync(int id)
        {
            try
            {
                return await _httpService.GetAsync<SupplierCategoryDto>($"{BaseEndpoint}/{id}");
            }
            catch (Exception ex)
            {
                throw new Exception($"Error getting supplier category by ID: {ex.Message}", ex);
            }
        }

        public async Task<List<SupplierCategoryDto>> GetAllAsync()
        {
            try
            {
                return await _httpService.GetAsync<List<SupplierCategoryDto>>(BaseEndpoint);
            }
            catch (Exception ex)
            {
                throw new Exception($"Error getting all supplier categories: {ex.Message}", ex);
            }
        }

        public async Task<List<SupplierCategoryDto>> GetActiveAsync()
        {
            try
            {
                return await _httpService.GetAsync<List<SupplierCategoryDto>>($"{BaseEndpoint}/active");
            }
            catch (Exception ex)
            {
                throw new Exception($"Error getting active supplier categories: {ex.Message}", ex);
            }
        }

        public async Task<BaseResponse<SupplierCategoryDto>> CreateAsync(SupplierCategoryDto categoryDto)
        {
            try
            {
                return await _httpService.PostAsync<BaseResponse<SupplierCategoryDto>>(BaseEndpoint, categoryDto);
            }
            catch (Exception ex)
            {
                return new BaseResponse<SupplierCategoryDto>(500, false, $"Error creating supplier category: {ex.Message}", default);
            }
        }

        public async Task<BaseResponse<SupplierCategoryDto>> UpdateAsync(SupplierCategoryDto categoryDto)
        {
            try
            {
                return await _httpService.PutAsync<BaseResponse<SupplierCategoryDto>>($"{BaseEndpoint}/{categoryDto.Id}", categoryDto);
            }
            catch (Exception ex)
            {
                return new BaseResponse<SupplierCategoryDto>(500, false, $"Error updating supplier category: {ex.Message}", default);
            }
        }

        public async Task DeleteAsync(int id)
        {
            try
            {
                await _httpService.DeleteAsync($"{BaseEndpoint}/{id}");
            }
            catch (Exception ex)
            {
                throw new Exception($"Error deleting supplier category: {ex.Message}", ex);
            }
        }

        public async Task SoftDeleteAsync(int id)
        {
            try
            {
                await _httpService.DeleteAsync($"{BaseEndpoint}/soft/{id}");
            }
            catch (Exception ex)
            {
                throw new Exception($"Error soft deleting supplier category: {ex.Message}", ex);
            }
        }
    }
}
