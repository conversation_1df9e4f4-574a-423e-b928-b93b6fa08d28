using ExactCash.WASM.Application.DTOs.Common;
#nullable disable

namespace ExactCash.WASM.Application.DTOs
{
    public class PurchaseDto : BaseEntityDto
    {
        /// <summary>
        /// The ID of the supplier who provided the products.
        /// </summary>
        public int SupplierId { get; set; }

        /// <summary>
        /// Supplier
        /// </summary>
        public SupplierDto Supplier { get; set; }

        /// <summary>
        /// SupplierName
        /// </summary>
        public string SupplierName => Supplier?.Name;   

        /// <summary>
        /// The total amount of the purchase.
        /// </summary>
        public decimal TotalAmount { get; set; }

        /// <summary>
        /// The amount paid to the supplier.
        /// </summary>
        public decimal AmountPaid { get; set; }

        /// <summary>
        /// Remaining amount to be paid (used for partial payments or credit).
        /// </summary>
        public decimal RemainingAmount { get; set; }

        /// <summary>
        /// The payment method used for the purchase.
        /// </summary>
        public string PaymentMethod { get; set; }

        /// <summary>
        /// The status of the purchase (e.g., Completed, Pending, Cancelled).
        /// </summary>
        public string Status { get; set; }

        /// <summary>
        /// OrderNumber
        /// </summary>
        public string OrderNumber { get; set; }

        /// <summary>
        /// Notes
        /// </summary>
        public string Notes { get; set; }

        /// <summary>
        /// The items included in the purchase.
        /// </summary>
        public List<PurchaseItemDto> Items { get; set; } = new List<PurchaseItemDto>();
    }

    public class PurchaseItemDto
    {
        /// <summary>
        /// The unique identifier for this purchase item.
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// Foreign key to the purchase this item belongs to.
        /// </summary>
        public int PurchaseId { get; set; }

        /// <summary>
        /// Foreign key to the product being purchased.
        /// </summary>
        public int ProductId { get; set; }

        /// <summary>
        /// ProductName
        /// </summary>
        public string ProductName { get; set; }

        /// <summary>
        /// Product
        /// </summary>
        public ProductDto Product { get; set; }

        /// <summary>
        /// Unit of measure for this item in the purchase (can be different from the product's unit)
        /// </summary>
        public int UnitId { get; set; }

        /// <summary>
        /// Unit
        /// </summary>
        public UnitDto Unit { get; set; }

        /// <summary>
        /// Quantity of the product purchased.
        /// </summary>
        public decimal Quantity { get; set; }

        /// <summary>
        /// Purchase price of a single unit of the product.
        /// </summary>
        public decimal CostPrice { get; set; }

        /// <summary>
        /// Total price for this item (Quantity * Price)
        /// </summary>
        public decimal TotalPrice => Quantity * CostPrice;
    }
} 