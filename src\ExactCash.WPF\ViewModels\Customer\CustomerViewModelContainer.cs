using System.ComponentModel;
using System.Runtime.CompilerServices;

namespace ExactCash.WPF.ViewModels.Customer
{
    public class CustomerViewModelContainer : INotifyPropertyChanged
    {
        private CustomerListViewModel _customerListViewModel;
        private CustomerCategoryListViewModel _customerCategoryViewModel;

        public CustomerListViewModel CustomerListViewModel
        {
            get => _customerListViewModel;
            set
            {
                _customerListViewModel = value;
                OnPropertyChanged();
            }
        }

        public CustomerCategoryListViewModel CustomerCategoryViewModel
        {
            get => _customerCategoryViewModel;
            set
            {
                _customerCategoryViewModel = value;
                OnPropertyChanged();
            }
        }

        public event PropertyChangedEventHandler PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }
}
