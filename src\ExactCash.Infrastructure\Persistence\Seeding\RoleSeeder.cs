using Microsoft.AspNetCore.Identity;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace ExactCash.Infrastructure.Persistence.Seeding
{
    public static class RoleSeeder
    {
        public static async Task SeedRolesAsync(RoleManager<IdentityRole> roleManager, ILogger logger)
        {
            try
            {
                var defaultRoles = new List<string>
                {
                    "Admin",
                    "Manager",
                    "User",
                    "Cashier",
                    "Accountant"
                };

                foreach (var roleName in defaultRoles)
                {
                    if (!await roleManager.RoleExistsAsync(roleName))
                    {
                        var role = new IdentityRole
                        {
                            Name = roleName,
                            NormalizedName = roleName.ToUpper()
                        };

                        var result = await roleManager.CreateAsync(role);
                        if (result.Succeeded)
                        {
                            logger.LogInformation("Role {RoleName} created successfully", roleName);
                        }
                        else
                        {
                            logger.LogError("Failed to create role {RoleName}: {Errors}",
                                roleName, string.Join(", ", result.Errors.Select(e => e.Description)));
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "An error occurred while seeding roles");
                throw;
            }
        }
    }
}