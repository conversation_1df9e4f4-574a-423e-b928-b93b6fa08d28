@inherits LayoutComponentBase
@inject ILoadingService LoadingService
@inject INotificationService NotificationService
@inject AppState AppState

<div class="page">
    <div class="sidebar">
        <NavMenu />
    </div>

    <main>
        <div class="top-row px-4">
            <TopBar />
        </div>

        <article class="content px-4">
            @if (LoadingService.IsLoading)
            {
                <div class="loading-overlay">
                    <div class="loading-spinner-container">
                        <div class="loading-spinner"></div>
                        <div class="loading-text">@LoadingService.LoadingMessage</div>
                    </div>
                </div>
            }
            @Body
        </article>
    </main>
</div>

<div id="blazor-error-ui">
    حدث خطأ غير متوقع.
    <a href="" class="reload">إعادة تحميل</a>
    <a class="dismiss">🗙</a>
</div>

@code {
    protected override void OnInitialized()
    {
        LoadingService.OnChange += StateHasChanged;
        NotificationService.OnChange += StateHasChanged;
        AppState.OnChange += StateHasChanged;
    }

    public void Dispose()
    {
        LoadingService.OnChange -= StateHasChanged;
        NotificationService.OnChange -= StateHasChanged;
        AppState.OnChange -= StateHasChanged;
    }
}
