﻿using ExactCash.Domain.Common;
using System.Text.Json.Serialization;
#nullable disable

namespace ExactCash.Domain.Entities
{
    /// <summary>
    /// Represents a customer in the POS system.
    /// </summary>
    public class Customer : BaseEntity
    {
        #region Props.
        /// <summary>
        /// The full name of the customer.
        /// </summary>
        public string FullName { get; set; }

        /// <summary>
        /// The phone number of the customer for contact purposes.
        /// </summary>
        public string Phone { get; set; }

        /// <summary>
        /// The email address of the customer.
        /// </summary>
        public string Email { get; set; }

        /// <summary>
        /// The physical address of the customer.
        /// </summary>
        public string Address { get; set; }

        /// <summary>
        /// The total amount of money spent by the customer.
        /// </summary>
        public decimal TotalSpent { get; set; }

        /// <summary>
        /// The maximum credit limit allowed for this customer.
        /// </summary>
        public decimal CreditLimit { get; set; }

        /// <summary>
        /// The current outstanding credit balance for this customer.
        /// </summary>
        public decimal OutstandingBalance { get; set; }

        /// <summary>
        /// Foreign key referencing the customer's category.
        /// </summary>
        public int? CategoryId { get; set; }

        /// <summary>
        /// Navigation property for the customer's category.
        /// </summary>
        [JsonIgnore]
        public CustomerCategory Category { get; set; }

        /// <summary>
        /// Navigation property for the customer's sales.
        /// </summary>
        [JsonIgnore]
        public ICollection<Sale> Sales { get; set; } = new List<Sale>();

        /// <summary>
        /// Navigation property for the customer's credit sale transactions.
        /// </summary>
        [JsonIgnore]
        public ICollection<CreditSaleTransaction> CreditSaleTransactions { get; set; } = new List<CreditSaleTransaction>();

        /// <summary>
        /// Navigation property for the customer's credit limit history.
        /// </summary>
        [JsonIgnore]
        public ICollection<CustomerCreditLimit> CreditLimitHistory { get; set; } = new List<CustomerCreditLimit>();
        #endregion

        #region ctor
        public Customer()
        {

        }

        public Customer(string fullName, string phone, string email, string address, string createdBy, int? categoryId)
        {
            FullName = fullName;
            Phone = phone;
            Email = email;
            Address = address;
            CreatedBy = createdBy;
            CreationDate = DateTime.UtcNow;
            CategoryId = categoryId;
            CreditLimit = 0; // Default no credit limit
            OutstandingBalance = 0;
        }

        public Customer(string fullName, string phone, string email, string address, decimal totalSpent)
        {
            FullName = fullName;
            Phone = phone;
            Email = email;
            Address = address;
            TotalSpent = totalSpent;
            CreationDate = DateTime.UtcNow;
            CreditLimit = 0; // Default no credit limit
            OutstandingBalance = 0;
        }
        #endregion

        #region Methods
        public void Update(string fullName, string phone, string email, string address, int? categoryId)
        {
            FullName = fullName;
            Phone = phone;
            Email = email;
            Address = address;
            CategoryId = categoryId;
            LastUpdatedDate = DateTime.UtcNow;
        }

        public void UpdateTotalSpent(decimal amount)
        {
            TotalSpent += amount;
            LastUpdatedDate = DateTime.UtcNow;
        }

        public void ResetTotalSpent()
        {
            TotalSpent = 0;
            LastUpdatedDate = DateTime.UtcNow;
        }

        /// <summary>
        /// Updates the customer's credit limit.
        /// </summary>
        /// <param name="newCreditLimit">The new credit limit amount</param>
        public void UpdateCreditLimit(decimal newCreditLimit)
        {
            CreditLimit = newCreditLimit;
            LastUpdatedDate = DateTime.UtcNow;
        }

        /// <summary>
        /// Increases the outstanding balance when a credit sale is made.
        /// </summary>
        /// <param name="amount">The amount to add to outstanding balance</param>
        public void AddToOutstandingBalance(decimal amount)
        {
            OutstandingBalance += amount;
            LastUpdatedDate = DateTime.UtcNow;
        }

        /// <summary>
        /// Decreases the outstanding balance when a payment is made.
        /// </summary>
        /// <param name="amount">The amount to subtract from outstanding balance</param>
        public void ReduceOutstandingBalance(decimal amount)
        {
            OutstandingBalance = Math.Max(0, OutstandingBalance - amount);
            LastUpdatedDate = DateTime.UtcNow;
        }

        /// <summary>
        /// Checks if the customer can make a credit purchase of the specified amount.
        /// </summary>
        /// <param name="amount">The amount of the potential credit purchase</param>
        /// <returns>True if the purchase is within credit limit, false otherwise</returns>
        public bool CanMakeCreditPurchase(decimal amount)
        {
            return (OutstandingBalance + amount) <= CreditLimit;
        }

        /// <summary>
        /// Gets the available credit amount for this customer.
        /// </summary>
        /// <returns>The remaining credit available</returns>
        public decimal GetAvailableCredit()
        {
            return Math.Max(0, CreditLimit - OutstandingBalance);
        }
        #endregion
    }

}
