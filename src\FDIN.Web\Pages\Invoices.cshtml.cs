using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.EntityFrameworkCore;
using FDIN.Web.Data;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Linq;

namespace FDIN.Web.Pages
{
    public class InvoicesModel : PageModel
    {
        private readonly FDINDbContext _dbContext;

        public InvoicesModel(FDINDbContext dbContext)
        {
            _dbContext = dbContext;
        }

        public string UserName { get; set; }
        public string UserEmail { get; set; }
        public string UserProfileImage { get; set; }
        public List<InvoiceViewModel> Invoices { get; set; }

        public async Task<IActionResult> OnGetAsync()
        {
            // TODO: Get the current user's ID from authentication
            var userId = 1; // Replace with actual user ID from authentication

            var user = await _dbContext.Users.FindAsync(userId);
            if (user == null)
            {
                return NotFound();
            }

            UserName = user.FullName;
            UserEmail = user.Email;
            //UserProfileImage = user.ProfilePicture ?? "/images/default-profile.png";

            Invoices = await _dbContext.Sales
                .Where(s => s.UserId == userId)
                .OrderByDescending(s => s.SaleDate)
                .Select(s => new InvoiceViewModel
                {
                    Id = s.Id,
                    InvoiceNumber = s.InvoiceNumber,
                    SaleDate = s.SaleDate,
                    StoreName = s.StoreName,
                    TotalAmount = s.TotalAmount,
                    Status = s.PaidAmount >= s.TotalAmount ? "Paid" :
                            s.PaidAmount > 0 ? "Partial" : "Pending"
                })
                .ToListAsync();

            return Page();
        }
    }

    public class InvoiceViewModel
    {
        public int Id { get; set; }
        public string InvoiceNumber { get; set; }
        public DateTime SaleDate { get; set; }
        public string StoreName { get; set; }
        public decimal TotalAmount { get; set; }
        public string Status { get; set; }
    }
}