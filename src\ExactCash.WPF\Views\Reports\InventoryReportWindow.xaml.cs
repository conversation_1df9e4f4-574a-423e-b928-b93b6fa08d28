﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Text.Json;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using ExactCash.WPF.Services;
using MessageBox = System.Windows.MessageBox;
using PrintDialog = System.Windows.Controls.PrintDialog;

namespace ExactCash.WPF.Views.Reports
{
    public partial class InventoryReportWindow : Window
    {
        private readonly ApiService _apiService;
        private List<InventoryReportItem> _inventoryData;

        public InventoryReportWindow()
        {
            InitializeComponent();
            _apiService = new ApiService();
            LoadInventoryData();
        }

        private async void LoadInventoryData()
        {
            try
            {
                // Show loading indicator
                InventoryDataGrid.ItemsSource = null;

                // Call the API to get inventory data
                var response = await _apiService.GetAsync("api/Reports/inventory");

                if (response.IsSuccessStatusCode)
                {
                    var jsonContent = await response.Content.ReadAsStringAsync();
                    var options = new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    };

                    _inventoryData = JsonSerializer.Deserialize<List<InventoryReportItem>>(jsonContent, options);

                    // Bind data to grid
                    InventoryDataGrid.ItemsSource = _inventoryData;

                    // Update summary cards
                    UpdateSummaryCards();
                }
                else
                {
                    MessageBox.Show("فشل في تحميل بيانات المخزون", "خطأ",
                                  MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء تحميل البيانات: {ex.Message}", "خطأ",
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void UpdateSummaryCards()
        {
            if (_inventoryData == null) return;

            TotalProductsText.Text = _inventoryData.Count.ToString();
            LowStockProductsText.Text = _inventoryData.Count(x => x.StockStatus == "مخزون منخفض").ToString();
            OutOfStockProductsText.Text = _inventoryData.Count(x => x.StockStatus == "نفد المخزون").ToString();

            var totalValue = _inventoryData.Sum(x => x.StockValue);
            TotalValueText.Text = $"{totalValue:N2} ج.م";
        }

        private void RefreshData_Click(object sender, RoutedEventArgs e)
        {
            LoadInventoryData();
        }

        private void PrintReport_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // Create a simple print dialog
                var printDialog = new PrintDialog();
                if (printDialog.ShowDialog() == true)
                {
                    // For now, just show a message
                    MessageBox.Show("سيتم تطوير وظيفة الطباعة قريباً", "طباعة",
                                  MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء الطباعة: {ex.Message}", "خطأ",
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void ExportToExcel_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // For now, just show a message
                MessageBox.Show("سيتم تطوير وظيفة التصدير إلى Excel قريباً", "تصدير",
                              MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء التصدير: {ex.Message}", "خطأ",
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void Close_Click(object sender, RoutedEventArgs e)
        {
            this.Close();
        }
    }

    // Data model for inventory report items
    public class InventoryReportItem
    {
        public int ProductId { get; set; }
        public string ProductName { get; set; } = string.Empty;
        public string ProductSKU { get; set; } = string.Empty;
        public string Barcode { get; set; } = string.Empty;
        public string CategoryName { get; set; } = string.Empty;
        public string BrandName { get; set; } = string.Empty;
        public string UnitName { get; set; } = string.Empty;
        public int CurrentStock { get; set; }
        public int MinStock { get; set; }
        public decimal CostPrice { get; set; }
        public decimal SellingPrice { get; set; }
        public decimal StockValue { get; set; }
        public string StockStatus { get; set; } = string.Empty;
        public DateTime LastStockUpdate { get; set; }
        public int DaysWithoutMovement { get; set; }
        public decimal ProfitMargin { get; set; }
        public bool IsActive { get; set; }

        // Formatted properties for display
        public string FormattedCostPrice => $"{CostPrice:N2} ج.م";
        public string FormattedSellingPrice => $"{SellingPrice:N2} ج.م";
        public string FormattedStockValue => $"{StockValue:N2} ج.م";
        public string FormattedProfitMargin => $"{ProfitMargin:N1}%";
        public string StockStatusColor => StockStatus switch
        {
            "نفد المخزون" => "#DC3545", // Red
            "مخزون منخفض" => "#FFC107", // Yellow
            "مخزون جيد" => "#28A745", // Green
            _ => "#6C757D" // Gray
        };
    }
}
