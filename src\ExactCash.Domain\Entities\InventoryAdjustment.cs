﻿using ExactCash.Domain.Common;
#nullable disable

namespace ExactCash.Domain.Entities
{
    /// <summary>
    /// Represents a manual inventory adjustment (e.g., stock corrections).
    /// </summary>
    public class InventoryAdjustment : BaseEntity
    {
        /// <summary>
        /// The ID of the product being adjusted.
        /// </summary>
        public int ProductId { get; set; }

        /// <summary>
        /// Navigation property to the product being adjusted.
        /// </summary>
        public Product Product { get; set; }

        /// <summary>
        /// Unit of measure for this item in the sale (can be different from the product's unit)
        /// </summary>
        public int UnitId { get; set; }
        /// <summary>
        /// Navigation property to the untit of measure for this item.
        /// </summary>
        public Unit Unit { get; set; }

        /// <summary>
        /// The quantity adjustment (positive for increase, negative for decrease).
        /// </summary>
        public int QuantityAdjustment { get; set; }

        /// <summary>
        /// The reason for the inventory adjustment.
        /// </summary>
        public string Reason { get; set; }

        /// <summary>
        /// The date the adjustment was made.
        /// </summary>
        public DateTime AdjustmentDate { get; set; }
    }
}
