﻿using ExactCash.Domain.Common;
using Newtonsoft.Json;
#nullable disable

namespace ExactCash.Domain.Entities
{
    public class SystemConfiguration : BaseEntity
    {
        #region Props
        /// <summary>
        /// Name of the setting (e.g., "Currency", "TaxRate", "DiscountPolicy", etc.)
        /// </summary>
        public string SettingName { get; set; }

        /// <summary>
        /// Value of the setting. Can be string, decimal, or other types depending on the setting.
        /// </summary>
        public string SettingValue { get; set; }

        /// <summary>
        /// Description of the setting for administrative purposes.
        /// </summary>
        public string Description { get; set; }

        /// <summary>
        /// Type of the setting (e.g., "Currency", "TaxRate", etc.), to identify the setting's category.
        /// </summary>
        public string SettingType { get; set; }

        /// <summary>
        /// Indicates whether this setting is active or not.
        /// </summary>
        public bool IsActive { get; set; }
        #endregion

        #region Methods
        public object GetSettingValue(SystemConfiguration config)
        {
            switch (config.SettingType.ToLower())
            {
                case "string":
                    return config.SettingValue;
                case "int":
                    return int.Parse(config.SettingValue);
                case "bool":
                    return bool.Parse(config.SettingValue);
                case "decimal":
                    return decimal.Parse(config.SettingValue);
                case "json":
                    return JsonConvert.DeserializeObject(config.SettingValue);
                case "date":
                    return DateTime.Parse(config.SettingValue);
                default:
                    throw new ArgumentException($"Unsupported setting type: {config.SettingType}");
            }
        }
        #endregion
    }
}
