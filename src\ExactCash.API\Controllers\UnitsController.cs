using ExactCash.Application.DTOs;
using ExactCash.Application.Interfaces;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace ExactCash.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class UnitsController : ControllerBase
    {
        private readonly IUnitService _unitService;

        public UnitsController(IUnitService unitService)
        {
            _unitService = unitService;
        }

        [HttpGet]
        public async Task<ActionResult<IEnumerable<UnitDto>>> GetAllUnits()
        {
            var units = await _unitService.GetAllUnitsAsync();
            return Ok(units);
        }

        [HttpGet("{id}")]
        public async Task<ActionResult<UnitDto>> GetUnit(int id)
        {
            var unit = await _unitService.GetUnitByIdAsync(id);
            if (unit == null)
                return NotFound();

            return Ok(unit);
        }

        [HttpPost]
        public async Task<ActionResult<UnitDto>> CreateUnit(UnitDto unitDto)
        {
            var createdUnit = await _unitService.CreateUnitAsync(unitDto);
            return CreatedAtAction(nameof(GetUnit), new { id = createdUnit.Id }, createdUnit);
        }

        [HttpPut("{id}")]
        public async Task<IActionResult> UpdateUnit(int id, UnitDto unitDto)
        {
            if (id != unitDto.Id)
                return BadRequest();

            try
            {
                await _unitService.UpdateUnitAsync(unitDto);
                return NoContent();
            }
            catch (KeyNotFoundException)
            {
                return NotFound();
            }
        }

        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteUnit(int id)
        {
            try
            {
                await _unitService.DeleteUnitAsync(id);
                return NoContent();
            }
            catch (KeyNotFoundException)
            {
                return NotFound();
            }
        }
    }
} 