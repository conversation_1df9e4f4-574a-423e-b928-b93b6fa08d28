using ExactCash.WPF.ViewModels.Inventory;
using System.Windows;

namespace ExactCash.WPF.Views.Inventory
{
    /// <summary>
    /// Interaction logic for InventoryStatsWindow.xaml
    /// </summary>
    public partial class InventoryStatsWindow : Window
    {
        public InventoryStatsWindow()
        {
            InitializeComponent();
            DataContext = new InventoryStatsViewModel();
        }

        public InventoryStatsWindow(InventoryStatsViewModel viewModel)
        {
            InitializeComponent();
            DataContext = viewModel;
        }
    }
}
