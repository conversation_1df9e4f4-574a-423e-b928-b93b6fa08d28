<?xml version="1.0" encoding="utf-8"?>
<Report xmlns="http://schemas.microsoft.com/sqlserver/reporting/2016/01/reportdefinition" xmlns:rd="http://schemas.microsoft.com/SQLServer/reporting/reportdesigner">
  <AutoRefresh>0</AutoRefresh>
  <DataSources>
    <DataSource Name="DailySalesDataSet">
      <ConnectionProperties>
        <DataProvider>System.Data.DataSet</DataProvider>
        <ConnectString>/* Local Connection */</ConnectString>
      </ConnectionProperties>
      <rd:DataSourceID>b8c8c8c8-8c8c-8c8c-8c8c-8c8c8c8c8c8c</rd:DataSourceID>
    </DataSource>
  </DataSources>
  <DataSets>
    <DataSet Name="DailySalesDataSet">
      <Query>
        <DataSourceName>DailySalesDataSet</DataSourceName>
        <CommandText>/* Local Query */</CommandText>
      </Query>
      <Fields>
        <Field Name="SaleDate">
          <DataField>SaleDate</DataField>
          <rd:TypeName>System.DateTime</rd:TypeName>
        </Field>
        <Field Name="FormattedTotalSales">
          <DataField>FormattedTotalSales</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="InvoiceCount">
          <DataField>InvoiceCount</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="CashierName">
          <DataField>CashierName</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="AverageTransactionValue">
          <DataField>AverageTransactionValue</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="TotalItemsSold">
          <DataField>TotalItemsSold</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="FormattedDiscountAmount">
          <DataField>FormattedDiscountAmount</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="FormattedTaxAmount">
          <DataField>FormattedTaxAmount</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
      </Fields>
    </DataSet>
    <DataSet Name="CompanyInfoDataSet">
      <Query>
        <DataSourceName>DailySalesDataSet</DataSourceName>
        <CommandText>/* Local Query */</CommandText>
      </Query>
      <Fields>
        <Field Name="CompanyName">
          <DataField>CompanyName</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="CompanyAddress">
          <DataField>CompanyAddress</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="CompanyPhone">
          <DataField>CompanyPhone</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
      </Fields>
    </DataSet>
  </DataSets>
  <ReportSections>
    <ReportSection>
      <Body>
        <ReportItems>
          <!-- Main Data Table -->
          <Tablix Name="DailySalesTable">
            <TablixBody>
              <TablixColumns>
                <TablixColumn>
                  <Width>1.8in</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>2in</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>1.3in</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>2in</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>1.8in</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>1.2in</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>1.2in</Width>
                </TablixColumn>
              </TablixColumns>
              <TablixRows>
                <!-- Header Row -->
                <TablixRow>
                  <Height>0.4in</Height>
                  <TablixCells>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="HeaderSaleDate">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>تاريخ البيع</Value>
                                  <Style>
                                    <FontFamily>Arial</FontFamily>
                                    <FontSize>11pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                    <Color>White</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>HeaderSaleDate</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>#2E4A6B</Color>
                              <Style>Solid</Style>
                            </Border>
                            <BackgroundColor>#2E4A6B</BackgroundColor>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="HeaderTotalSales">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>إجمالي المبيعات</Value>
                                  <Style>
                                    <FontFamily>Arial</FontFamily>
                                    <FontSize>11pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                    <Color>White</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>HeaderTotalSales</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>#2E4A6B</Color>
                              <Style>Solid</Style>
                            </Border>
                            <BackgroundColor>#2E4A6B</BackgroundColor>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="HeaderInvoiceCount">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>عدد الفواتير</Value>
                                  <Style>
                                    <FontFamily>Arial</FontFamily>
                                    <FontSize>11pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                    <Color>White</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>HeaderInvoiceCount</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>#2E4A6B</Color>
                              <Style>Solid</Style>
                            </Border>
                            <BackgroundColor>#2E4A6B</BackgroundColor>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="HeaderCashierName">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>اسم الكاشير</Value>
                                  <Style>
                                    <FontFamily>Arial</FontFamily>
                                    <FontSize>11pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                    <Color>White</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>HeaderCashierName</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>#2E4A6B</Color>
                              <Style>Solid</Style>
                            </Border>
                            <BackgroundColor>#2E4A6B</BackgroundColor>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="HeaderAverageTransaction">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>متوسط الفاتورة</Value>
                                  <Style>
                                    <FontFamily>Arial</FontFamily>
                                    <FontSize>11pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                    <Color>White</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>HeaderAverageTransaction</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>#2E4A6B</Color>
                              <Style>Solid</Style>
                            </Border>
                            <BackgroundColor>#2E4A6B</BackgroundColor>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="HeaderItemsSold">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>عدد الأصناف</Value>
                                  <Style>
                                    <FontFamily>Arial</FontFamily>
                                    <FontSize>11pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                    <Color>White</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>HeaderItemsSold</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>#2E4A6B</Color>
                              <Style>Solid</Style>
                            </Border>
                            <BackgroundColor>#2E4A6B</BackgroundColor>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="HeaderDiscount">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>الخصم</Value>
                                  <Style>
                                    <FontFamily>Arial</FontFamily>
                                    <FontSize>11pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                    <Color>White</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>HeaderDiscount</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>#2E4A6B</Color>
                              <Style>Solid</Style>
                            </Border>
                            <BackgroundColor>#2E4A6B</BackgroundColor>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                  </TablixCells>
                </TablixRow>

                <!-- Data Row -->
                <TablixRow>
                  <Height>0.35in</Height>
                  <TablixCells>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="SaleDate">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Format(Fields!SaleDate.Value, "dd/MM/yyyy")</Value>
                                  <Style>
                                    <FontFamily>Arial</FontFamily>
                                    <FontSize>10pt</FontSize>
                                    <Color>#333333</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>SaleDate</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>#CCCCCC</Color>
                              <Style>Solid</Style>
                            </Border>
                            <BackgroundColor>=IIF(RowNumber(Nothing) Mod 2, "White", "#F8F9FA")</BackgroundColor>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="TotalSales">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!FormattedTotalSales.Value</Value>
                                  <Style>
                                    <FontFamily>Arial</FontFamily>
                                    <FontSize>10pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                    <Color>#2E4A6B</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>TotalSales</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>#CCCCCC</Color>
                              <Style>Solid</Style>
                            </Border>
                            <BackgroundColor>=IIF(RowNumber(Nothing) Mod 2, "White", "#F8F9FA")</BackgroundColor>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="InvoiceCount">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!InvoiceCount.Value</Value>
                                  <Style>
                                    <FontFamily>Arial</FontFamily>
                                    <FontSize>10pt</FontSize>
                                    <Color>#333333</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>InvoiceCount</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>#CCCCCC</Color>
                              <Style>Solid</Style>
                            </Border>
                            <BackgroundColor>=IIF(RowNumber(Nothing) Mod 2, "White", "#F8F9FA")</BackgroundColor>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="CashierName">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!CashierName.Value</Value>
                                  <Style>
                                    <FontFamily>Arial</FontFamily>
                                    <FontSize>10pt</FontSize>
                                    <Color>#333333</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>CashierName</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>#CCCCCC</Color>
                              <Style>Solid</Style>
                            </Border>
                            <BackgroundColor>=IIF(RowNumber(Nothing) Mod 2, "White", "#F8F9FA")</BackgroundColor>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="AverageTransactionValue">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!AverageTransactionValue.Value</Value>
                                  <Style>
                                    <FontFamily>Arial</FontFamily>
                                    <FontSize>10pt</FontSize>
                                    <Color>#333333</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>AverageTransactionValue</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>#CCCCCC</Color>
                              <Style>Solid</Style>
                            </Border>
                            <BackgroundColor>=IIF(RowNumber(Nothing) Mod 2, "White", "#F8F9FA")</BackgroundColor>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="TotalItemsSold">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!TotalItemsSold.Value</Value>
                                  <Style>
                                    <FontFamily>Arial</FontFamily>
                                    <FontSize>10pt</FontSize>
                                    <Color>#333333</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>TotalItemsSold</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>#CCCCCC</Color>
                              <Style>Solid</Style>
                            </Border>
                            <BackgroundColor>=IIF(RowNumber(Nothing) Mod 2, "White", "#F8F9FA")</BackgroundColor>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="DiscountAmount">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!FormattedDiscountAmount.Value</Value>
                                  <Style>
                                    <FontFamily>Arial</FontFamily>
                                    <FontSize>10pt</FontSize>
                                    <Color>#DC3545</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>DiscountAmount</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>#CCCCCC</Color>
                              <Style>Solid</Style>
                            </Border>
                            <BackgroundColor>=IIF(RowNumber(Nothing) Mod 2, "White", "#F8F9FA")</BackgroundColor>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                  </TablixCells>
                </TablixRow>
              </TablixRows>
            </TablixBody>
            <TablixColumnHierarchy>
              <TablixMembers>
                <TablixMember />
                <TablixMember />
                <TablixMember />
                <TablixMember />
                <TablixMember />
                <TablixMember />
                <TablixMember />
              </TablixMembers>
            </TablixColumnHierarchy>
            <TablixRowHierarchy>
              <TablixMembers>
                <TablixMember>
                  <KeepWithGroup>After</KeepWithGroup>
                </TablixMember>
                <TablixMember>
                  <Group Name="Details" />
                </TablixMember>
              </TablixMembers>
            </TablixRowHierarchy>
            <DataSetName>DailySalesDataSet</DataSetName>
            <Top>0.1in</Top>
            <Left>0in</Left>
            <Height>0.75in</Height>
            <Width>11.3in</Width>
            <Style>
              <Border>
                <Style>None</Style>
              </Border>
            </Style>
          </Tablix>
        </ReportItems>
        <Height>2.5in</Height>
        <Style />
      </Body>
      <Width>11.5in</Width>
      <Page>
        <PageHeader>
          <Height>1.4in</Height>
          <PrintOnFirstPage>true</PrintOnFirstPage>
          <PrintOnLastPage>true</PrintOnLastPage>
          <ReportItems>
            <!-- Company Name -->
            <Textbox Name="CompanyName">
              <CanGrow>true</CanGrow>
              <KeepTogether>true</KeepTogether>
              <Paragraphs>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value>=First(Fields!CompanyName.Value, "CompanyInfoDataSet")</Value>
                      <Style>
                        <FontFamily>Arial</FontFamily>
                        <FontSize>18pt</FontSize>
                        <FontWeight>Bold</FontWeight>
                        <Color>#2C3E50</Color>
                      </Style>
                    </TextRun>
                  </TextRuns>
                  <Style>
                    <TextAlign>Center</TextAlign>
                  </Style>
                </Paragraph>
              </Paragraphs>
              <rd:DefaultName>CompanyName</rd:DefaultName>
              <Top>0.05in</Top>
              <Left>0in</Left>
              <Height>0.25in</Height>
              <Width>11.3in</Width>
            </Textbox>

            <!-- Company Address and Phone -->
            <Textbox Name="CompanyInfo">
              <CanGrow>true</CanGrow>
              <KeepTogether>true</KeepTogether>
              <Paragraphs>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value>=First(Fields!CompanyAddress.Value, "CompanyInfoDataSet") + " - " + First(Fields!CompanyPhone.Value, "CompanyInfoDataSet")</Value>
                      <Style>
                        <FontFamily>Arial</FontFamily>
                        <FontSize>10pt</FontSize>
                        <Color>#34495E</Color>
                      </Style>
                    </TextRun>
                  </TextRuns>
                  <Style>
                    <TextAlign>Center</TextAlign>
                  </Style>
                </Paragraph>
              </Paragraphs>
              <rd:DefaultName>CompanyInfo</rd:DefaultName>
              <Top>0.3in</Top>
              <Left>0in</Left>
              <Height>0.15in</Height>
              <Width>11.3in</Width>
            </Textbox>

            <!-- Separator Line -->
            <Line Name="SeparatorLine">
              <Top>0.5in</Top>
              <Left>0in</Left>
              <Height>0in</Height>
              <Width>11.3in</Width>
              <Style>
                <Border>
                  <Style>Solid</Style>
                  <Color>#BDC3C7</Color>
                  <Width>2pt</Width>
                </Border>
              </Style>
            </Line>

            <!-- Header Title -->
            <Textbox Name="HeaderTitle">
              <CanGrow>true</CanGrow>
              <KeepTogether>true</KeepTogether>
              <Paragraphs>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value>تقرير المبيعات اليومية</Value>
                      <Style>
                        <FontFamily>Arial</FontFamily>
                        <FontSize>16pt</FontSize>
                        <FontWeight>Bold</FontWeight>
                        <Color>#2C3E50</Color>
                      </Style>
                    </TextRun>
                  </TextRuns>
                  <Style>
                    <TextAlign>Center</TextAlign>
                  </Style>
                </Paragraph>
              </Paragraphs>
              <rd:DefaultName>HeaderTitle</rd:DefaultName>
              <Top>0.6in</Top>
              <Left>0in</Left>
              <Height>0.25in</Height>
              <Width>11.3in</Width>
            </Textbox>

            <!-- Report Period -->
            <Textbox Name="HeaderPeriod">
              <CanGrow>true</CanGrow>
              <KeepTogether>true</KeepTogether>
              <Paragraphs>
                <Paragraph>
                  <TextRuns>
                    <TextRun>
                      <Value>من </Value>
                      <Style>
                        <FontFamily>Arial</FontFamily>
                        <FontSize>10pt</FontSize>
                        <FontWeight>Bold</FontWeight>
                        <Color>#2C3E50</Color>
                      </Style>
                    </TextRun>
                    <TextRun>
                      <Value>=Parameters!StartDate.Value.ToString("dd/MM/yyyy")</Value>
                      <Style>
                        <FontFamily>Arial</FontFamily>
                        <FontSize>10pt</FontSize>
                        <Color>#34495E</Color>
                      </Style>
                    </TextRun>
                    <TextRun>
                      <Value> إلى </Value>
                      <Style>
                        <FontFamily>Arial</FontFamily>
                        <FontSize>10pt</FontSize>
                        <FontWeight>Bold</FontWeight>
                        <Color>#2C3E50</Color>
                      </Style>
                    </TextRun>
                    <TextRun>
                      <Value>=Parameters!EndDate.Value.ToString("dd/MM/yyyy")</Value>
                      <Style>
                        <FontFamily>Arial</FontFamily>
                        <FontSize>10pt</FontSize>
                        <Color>#34495E</Color>
                      </Style>
                    </TextRun>
                  </TextRuns>
                  <Style>
                    <TextAlign>Center</TextAlign>
                  </Style>
                </Paragraph>
              </Paragraphs>
              <rd:DefaultName>HeaderPeriod</rd:DefaultName>
              <Top>0.9in</Top>
              <Left>0in</Left>
              <Height>0.2in</Height>
              <Width>11.3in</Width>
            </Textbox>
          </ReportItems>
        </PageHeader>
        <LeftMargin>0.25in</LeftMargin>
        <RightMargin>0.25in</RightMargin>
        <TopMargin>0.25in</TopMargin>
        <BottomMargin>0.25in</BottomMargin>
        <Style />
      </Page>
    </ReportSection>
  </ReportSections>
  <ReportParameters>
    <ReportParameter Name="StartDate">
      <DataType>DateTime</DataType>
      <DefaultValue>
        <Values>
          <Value>=Today()</Value>
        </Values>
      </DefaultValue>
      <Prompt>تاريخ البداية</Prompt>
    </ReportParameter>
    <ReportParameter Name="EndDate">
      <DataType>DateTime</DataType>
      <DefaultValue>
        <Values>
          <Value>=Today()</Value>
        </Values>
      </DefaultValue>
      <Prompt>تاريخ النهاية</Prompt>
    </ReportParameter>
  </ReportParameters>
  <ReportParametersLayout>
    <GridLayoutDefinition>
      <NumberOfColumns>2</NumberOfColumns>
      <NumberOfRows>1</NumberOfRows>
      <CellDefinitions>
        <CellDefinition>
          <ColumnIndex>0</ColumnIndex>
          <RowIndex>0</RowIndex>
          <ParameterName>StartDate</ParameterName>
        </CellDefinition>
        <CellDefinition>
          <ColumnIndex>1</ColumnIndex>
          <RowIndex>0</RowIndex>
          <ParameterName>EndDate</ParameterName>
        </CellDefinition>
      </CellDefinitions>
    </GridLayoutDefinition>
  </ReportParametersLayout>
  <rd:ReportUnitType>Inch</rd:ReportUnitType>
  <rd:ReportID>b8c8c8c8-8c8c-8c8c-8c8c-8c8c8c8c8c8c</rd:ReportID>
</Report>