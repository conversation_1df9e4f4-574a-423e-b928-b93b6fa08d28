﻿using Microsoft.EntityFrameworkCore;

namespace FDIN.Web.Data
{
    public class FDINDbContext : DbContext
    {
        public FDINDbContext(DbContextOptions<FDINDbContext> options) : base(options)
        {
        }

        #region DbSets
        public DbSet<User> Users{ get; set; }

        public DbSet<Sale> Sales { get; set; }

        public DbSet<SaleItem> SaleItems { get; set; }
        #endregion

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);
        }
    }
}
