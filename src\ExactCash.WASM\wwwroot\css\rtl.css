/* RTL (Right-to-Left) Support for Arabic */

/* Base RTL Styles */
html[dir="rtl"] {
    direction: rtl;
    text-align: right;
}

html[dir="rtl"] body {
    direction: rtl;
    text-align: right;
}

/* Bootstrap RTL Overrides */
html[dir="rtl"] .navbar-nav {
    flex-direction: row;
}

html[dir="rtl"] .navbar-nav .nav-link {
    padding-right: 0;
    padding-left: 1rem;
}

html[dir="rtl"] .dropdown-menu {
    right: 0;
    left: auto;
}

html[dir="rtl"] .dropdown-menu-end {
    right: auto;
    left: 0;
}

/* Form Controls RTL */
html[dir="rtl"] .form-control {
    text-align: right;
}

html[dir="rtl"] .input-group-text {
    border-left: 1px solid #ced4da;
    border-right: 0;
}

html[dir="rtl"] .input-group > .form-control:not(:last-child) {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
    border-top-right-radius: 0.375rem;
    border-bottom-right-radius: 0.375rem;
}

html[dir="rtl"] .input-group > .form-control:not(:first-child) {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
    border-top-left-radius: 0.375rem;
    border-bottom-left-radius: 0.375rem;
}

/* Table RTL */
html[dir="rtl"] .table th,
html[dir="rtl"] .table td {
    text-align: right;
}

/* Button Groups RTL */
html[dir="rtl"] .btn-group > .btn:not(:last-child):not(.dropdown-toggle) {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
}

html[dir="rtl"] .btn-group > .btn:not(:first-child) {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
}

/* Modal RTL */
html[dir="rtl"] .modal-header .btn-close {
    margin: -0.5rem auto -0.5rem -0.5rem;
}

/* Pagination RTL */
html[dir="rtl"] .page-link {
    margin-left: -1px;
    margin-right: 0;
}

html[dir="rtl"] .page-item:first-child .page-link {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
    border-top-right-radius: 0.375rem;
    border-bottom-right-radius: 0.375rem;
}

html[dir="rtl"] .page-item:last-child .page-link {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
    border-top-left-radius: 0.375rem;
    border-bottom-left-radius: 0.375rem;
}

/* Sidebar RTL */
html[dir="rtl"] .sidebar {
    right: 0;
    left: auto;
}

html[dir="rtl"] .content {
    margin-right: 260px;
    margin-left: 0;
}

/* Navigation RTL */
html[dir="rtl"] .nav-item .nav-link {
    text-align: right;
}

html[dir="rtl"] .navbar-brand {
    margin-right: 0;
    margin-left: 1rem;
}

/* Icons RTL */
html[dir="rtl"] .bi {
    margin-left: 0.5rem;
    margin-right: 0;
}

/* Alerts RTL */
html[dir="rtl"] .alert-dismissible .btn-close {
    position: absolute;
    top: 0;
    left: 0;
    right: auto;
    z-index: 2;
    padding: 1.25rem 1rem;
}

/* Breadcrumb RTL */
html[dir="rtl"] .breadcrumb-item + .breadcrumb-item::before {
    float: right;
    padding-right: 0;
    padding-left: 0.5rem;
    content: var(--bs-breadcrumb-divider, "\\");
}

/* Card RTL */
html[dir="rtl"] .card-header-tabs .nav-link {
    margin-right: 0;
    margin-left: -1px;
}

/* List Group RTL */
html[dir="rtl"] .list-group-horizontal > .list-group-item:first-child {
    border-bottom-left-radius: 0;
    border-top-right-radius: 0.375rem;
}

html[dir="rtl"] .list-group-horizontal > .list-group-item:last-child {
    border-top-right-radius: 0;
    border-bottom-left-radius: 0.375rem;
}

/* Progress RTL */
html[dir="rtl"] .progress {
    direction: ltr;
}

/* Responsive RTL */
@media (max-width: 768px) {
    html[dir="rtl"] .sidebar {
        transform: translateX(100%);
    }
    
    html[dir="rtl"] .sidebar.show {
        transform: translateX(0);
    }
    
    html[dir="rtl"] .content {
        margin-right: 0;
        margin-left: 0;
    }
}

/* Custom RTL Utilities */
.text-right-rtl {
    text-align: right !important;
}

.text-left-rtl {
    text-align: left !important;
}

.float-right-rtl {
    float: right !important;
}

.float-left-rtl {
    float: left !important;
}

.mr-rtl-1 { margin-right: 0.25rem !important; }
.mr-rtl-2 { margin-right: 0.5rem !important; }
.mr-rtl-3 { margin-right: 1rem !important; }
.mr-rtl-4 { margin-right: 1.5rem !important; }
.mr-rtl-5 { margin-right: 3rem !important; }

.ml-rtl-1 { margin-left: 0.25rem !important; }
.ml-rtl-2 { margin-left: 0.5rem !important; }
.ml-rtl-3 { margin-left: 1rem !important; }
.ml-rtl-4 { margin-left: 1.5rem !important; }
.ml-rtl-5 { margin-left: 3rem !important; }

.pr-rtl-1 { padding-right: 0.25rem !important; }
.pr-rtl-2 { padding-right: 0.5rem !important; }
.pr-rtl-3 { padding-right: 1rem !important; }
.pr-rtl-4 { padding-right: 1.5rem !important; }
.pr-rtl-5 { padding-right: 3rem !important; }

.pl-rtl-1 { padding-left: 0.25rem !important; }
.pl-rtl-2 { padding-left: 0.5rem !important; }
.pl-rtl-3 { padding-left: 1rem !important; }
.pl-rtl-4 { padding-left: 1.5rem !important; }
.pl-rtl-5 { padding-left: 3rem !important; }
