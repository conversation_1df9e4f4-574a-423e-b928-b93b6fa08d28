using ExactCash.WASM.Models;

namespace ExactCash.WASM.Services;

public class AppState
{
    private UserInfo? _currentUser;
    private bool _isOnline = true;
    private string _currentLanguage = "ar";

    public UserInfo? CurrentUser
    {
        get => _currentUser;
        set
        {
            _currentUser = value;
            NotifyStateChanged();
        }
    }

    public bool IsOnline
    {
        get => _isOnline;
        set
        {
            _isOnline = value;
            NotifyStateChanged();
        }
    }

    public string CurrentLanguage
    {
        get => _currentLanguage;
        set
        {
            _currentLanguage = value;
            NotifyStateChanged();
        }
    }

    public event Action? OnChange;

    private void NotifyStateChanged() => OnChange?.Invoke();

    public void SetUser(UserInfo user)
    {
        CurrentUser = user;
    }

    public void ClearUser()
    {
        CurrentUser = null;
    }

    public void SetOnlineStatus(bool isOnline)
    {
        IsOnline = isOnline;
    }

    public void SetLanguage(string language)
    {
        CurrentLanguage = language;
    }
}
