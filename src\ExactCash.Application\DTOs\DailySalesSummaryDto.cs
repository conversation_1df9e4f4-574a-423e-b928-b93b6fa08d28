﻿#nullable disable

namespace ExactCash.Application.DTOs
{
    public class DailySalesSummaryDto
    {
        public DateTime ReportStartDate { get; set; }
        public DateTime ReportEndDate { get; set; }
        public int TotalDays { get; set; }
        public decimal TotalSales { get; set; }
        public int TotalInvoices { get; set; }
        public decimal AverageDailySales { get; set; }
        public decimal AverageTransactionValue { get; set; }
        public int TotalItemsSold { get; set; }
        public decimal TotalDiscounts { get; set; }
        public decimal TotalTax { get; set; }
        public decimal TotalNetAmount { get; set; }
        public decimal TotalPaidAmount { get; set; }
        public decimal TotalRemainingAmount { get; set; }
        public int TotalUniqueCustomers { get; set; }
        public decimal TotalCashPayments { get; set; }
        public decimal TotalCreditPayments { get; set; }
        public DateTime BestSalesDate { get; set; }
        public decimal BestSalesAmount { get; set; }
        public DateTime WorstSalesDate { get; set; }
        public decimal WorstSalesAmount { get; set; }
        public string TopCashier { get; set; }
        public decimal TopCashierSales { get; set; }

        // Formatted properties for display
        public string FormattedTotalSales => $"{TotalSales:N2} ج.م";
        public string FormattedAverageDailySales => $"{AverageDailySales:N2} ج.م";
        public string FormattedAverageTransactionValue => $"{AverageTransactionValue:N2} ج.م";
        public string FormattedTotalDiscounts => $"{TotalDiscounts:N2} ج.م";
        public string FormattedTotalTax => $"{TotalTax:N2} ج.م";
        public string FormattedTotalNetAmount => $"{TotalNetAmount:N2} ج.م";
        public string FormattedTotalPaidAmount => $"{TotalPaidAmount:N2} ج.م";
        public string FormattedTotalRemainingAmount => $"{TotalRemainingAmount:N2} ج.م";
        public string FormattedTotalCashPayments => $"{TotalCashPayments:N2} ج.م";
        public string FormattedTotalCreditPayments => $"{TotalCreditPayments:N2} ج.م";
        public string FormattedBestSalesAmount => $"{BestSalesAmount:N2} ج.م";
        public string FormattedWorstSalesAmount => $"{WorstSalesAmount:N2} ج.م";
        public string FormattedTopCashierSales => $"{TopCashierSales:N2} ج.م";

        public DailySalesSummaryDto()
        {

        }
    }
}
