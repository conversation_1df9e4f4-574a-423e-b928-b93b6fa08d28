using AutoMapper;
using ExactCash.WPF.Services;
using ExactCash.WPF.ViewModels.Supplier;
using System.Windows;

namespace ExactCash.WPF.Views.Supplier
{
    /// <summary>
    /// Interaction logic for SupplierMainView.xaml
    /// </summary>
    public partial class SupplierMainView : Window
    {
        public SupplierMainView(
            ISupplierServiceClient supplierServiceClient,
            ISupplierCategoryServiceClient supplierCategoryServiceClient,
            IMapper mapper)
        {
            InitializeComponent();

            // Create a container for both ViewModels
            var container = new SupplierViewModelContainer
            {
                SupplierListViewModel = new SupplierListViewModel(supplierServiceClient, supplierCategoryServiceClient, mapper),
                SupplierCategoryListViewModel = new SupplierCategoryListViewModel(supplierCategoryServiceClient)
            };

            DataContext = container;
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            this.Close();
        }

        private void MinimizeButton_Click(object sender, RoutedEventArgs e)
        {
            this.WindowState = WindowState.Minimized;
        }
    }

    /// <summary>
    /// Container class to hold both supplier and supplier category view models.
    /// </summary>
    public class SupplierViewModelContainer
    {
        public SupplierListViewModel SupplierListViewModel { get; set; }
        public SupplierCategoryListViewModel SupplierCategoryListViewModel { get; set; }
    }
}
