using ExactCash.WASM.Services.Interfaces;
using ExactCash.WASM.Models;

namespace ExactCash.WASM.Services;

public class CustomerService : ICustomerService
{
    private readonly IApiService _apiService;

    public CustomerService(IApiService apiService)
    {
        _apiService = apiService;
    }

    public async Task<PagedResult<Customer>> GetCustomersAsync(int pageNumber = 1, int pageSize = 10, string searchTerm = "")
    {
        var endpoint = $"api/customers?pageNumber={pageNumber}&pageSize={pageSize}";
        
        if (!string.IsNullOrEmpty(searchTerm))
        {
            endpoint += $"&searchTerm={Uri.EscapeDataString(searchTerm)}";
        }

        var result = await _apiService.GetAsync<PagedResult<Customer>>(endpoint);
        return result ?? new PagedResult<Customer>();
    }

    public async Task<Customer?> GetCustomerByIdAsync(int id)
    {
        return await _apiService.GetAsync<Customer>($"api/customers/{id}");
    }

    public async Task<Customer?> CreateCustomerAsync(Customer customer)
    {
        return await _apiService.PostAsync<Customer>("api/customers", customer);
    }

    public async Task<Customer?> UpdateCustomerAsync(Customer customer)
    {
        return await _apiService.PutAsync<Customer>($"api/customers/{customer.Id}", customer);
    }

    public async Task<bool> DeleteCustomerAsync(int id)
    {
        return await _apiService.DeleteAsync($"api/customers/{id}");
    }

    public async Task<List<CustomerCategory>> GetCustomerCategoriesAsync()
    {
        var result = await _apiService.GetAsync<List<CustomerCategory>>("api/customers/categories");
        return result ?? new List<CustomerCategory>();
    }

    public async Task<CustomerAccountStatement> GetCustomerAccountStatementAsync(int customerId, DateTime? fromDate = null, DateTime? toDate = null)
    {
        var endpoint = $"api/customers/{customerId}/account-statement";
        
        var queryParams = new List<string>();
        if (fromDate.HasValue)
            queryParams.Add($"fromDate={fromDate.Value:yyyy-MM-dd}");
        if (toDate.HasValue)
            queryParams.Add($"toDate={toDate.Value:yyyy-MM-dd}");

        if (queryParams.Any())
            endpoint += "?" + string.Join("&", queryParams);

        var result = await _apiService.GetAsync<CustomerAccountStatement>(endpoint);
        return result ?? new CustomerAccountStatement();
    }
}
