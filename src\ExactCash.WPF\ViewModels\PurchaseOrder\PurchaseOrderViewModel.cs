using System;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Input;
using AutoMapper;
using ExactCash.Application.DTOs;
using ExactCash.Application.Interfaces;
using ExactCash.WPF.Commands;
using ExactCash.WPF.Services;
using ExactCash.WPF.Services.Product;
using ExactCash.WPF.ViewModels.Common;
using System.Collections.Generic;
using System.Windows.Media;
using ExactCash.Application.Services;
using ExactCash.Application.Responses;
using System.ComponentModel;
using System.Runtime.CompilerServices;

namespace ExactCash.WPF.ViewModels.PurchaseOrder
{
    public class PurchaseItemViewModel : ViewModelBase
    {
        private int _productId;
        private string _productName;
        private decimal _quantity;
        private decimal _price;
        private int? _selectedUnitId;
        private ObservableCollection<UnitDto> _availableUnits;

        public int ProductId
        {
            get => _productId;
            set => SetProperty(ref _productId, value);
        }

        public string ProductName
        {
            get => _productName;
            set => SetProperty(ref _productName, value);
        }

        public decimal Quantity
        {
            get => _quantity;
            set
            {
                if (SetProperty(ref _quantity, value))
                {
                    OnPropertyChanged(nameof(TotalPrice));
                }
            }
        }

        public decimal Price
        {
            get => _price;
            set
            {
                if (SetProperty(ref _price, value))
                {
                    OnPropertyChanged(nameof(TotalPrice));
                }
            }
        }

        public int? SelectedUnitId
        {
            get => _selectedUnitId;
            set => SetProperty(ref _selectedUnitId, value);
        }

        public ObservableCollection<UnitDto> AvailableUnits
        {
            get => _availableUnits;
            set => SetProperty(ref _availableUnits, value);
        }

        public decimal TotalPrice => Quantity * Price;

        public PurchaseItemViewModel()
        {
            AvailableUnits = new ObservableCollection<UnitDto>();
        }
    }

    public class PurchaseOrderViewModel : ViewModelBase
    {
        private readonly ISupplierServiceClient _supplierService;
        private readonly IProductService _productService;
        private readonly IPurchaseServiceClient _purchaseOrderService;
        private readonly IUnitServiceClient _unitService;
        private readonly IMapper _mapper;
        private readonly NotificationViewModel _notificationViewModel;
        private readonly LoadingViewModel _loadingViewModel;

        private ObservableCollection<SupplierDto> _suppliers;
        private ObservableCollection<ProductDto> _products;
        private ObservableCollection<PurchaseItemViewModel> _selectedProducts;
        private int? _selectedSupplierId;
        private DateTime? _orderDate;
        private string _orderNumber;
        private int? _selectedProductId;
        private decimal _productQuantity;
        private bool _isReadOnly;
        private string _supplierSearchTerm;
        private string _productSearchTerm;
        private List<SupplierDto> _allSuppliers;
        private List<ProductDto> _allProducts;
        private ObservableCollection<ProductDto> _productSearchResults;
        private bool _hasSearchResults;
        private ProductDto _selectedProduct;
        private ObservableCollection<SupplierDto> _supplierSearchResults;
        private bool _hasSupplierSearchResults;
        private bool _isSettingSelectedSupplier;
        private bool _isSettingSelectedProduct;
        private SupplierDto _selectedSupplier;
        private bool _isSupplierDropDownOpen;
        private IEnumerable<UnitDto> _allUnits;

        public PurchaseOrderViewModel(
            IPurchaseServiceClient purchaseOrderService,
            IProductService productService,
            ISupplierServiceClient supplierService,
            IUnitServiceClient unitService,
            IMapper mapper,
            NotificationViewModel notificationViewModel,
            LoadingViewModel loadingViewModel)
        {
            _supplierService = supplierService;
            _productService = productService;
            _purchaseOrderService = purchaseOrderService;
            _unitService = unitService;
            _mapper = mapper;
            _notificationViewModel = notificationViewModel;
            _loadingViewModel = loadingViewModel;

            Suppliers = new ObservableCollection<SupplierDto>();
            Products = new ObservableCollection<ProductDto>();
            SelectedProducts = new ObservableCollection<PurchaseItemViewModel>();
            OrderDate = DateTime.Now;

            AddProductCommand = new RelayCommand(ExecuteAddProduct, CanExecuteAddProduct);
            RemoveProductCommand = new RelayCommand<PurchaseItemViewModel>(ExecuteRemoveProduct);
            SaveOrderCommand = new RelayCommand(async () => await ExecuteSaveOrder());
            CancelCommand = new RelayCommand(ExecuteCancel);
            GeneratePONumberAsync();
            LoadInitialDataAsync();
        }

        public ObservableCollection<SupplierDto> Suppliers
        {
            get => _suppliers;
            set
            {
                _suppliers = value;
                OnPropertyChanged();
            }
        }

        public ObservableCollection<ProductDto> Products
        {
            get => _products;
            set
            {
                _products = value;
                OnPropertyChanged();
            }
        }

        public ObservableCollection<PurchaseItemViewModel> SelectedProducts
        {
            get => _selectedProducts;
            set => SetProperty(ref _selectedProducts, value);
        }

        public int? SelectedSupplierId
        {
            get => _selectedSupplierId;
            set
            {
                _selectedSupplierId = value;
                OnPropertyChanged();
            }
        }

        public DateTime? OrderDate
        {
            get => _orderDate;
            set
            {
                _orderDate = value;
                OnPropertyChanged();
            }
        }

        public string OrderNumber
        {
            get => _orderNumber;
            set
            {
                _orderNumber = value;
                OnPropertyChanged();
            }
        }

        public int? SelectedProductId
        {
            get => _selectedProductId;
            set
            {
                _selectedProductId = value;
                OnPropertyChanged();
            }
        }

        public decimal ProductQuantity
        {
            get => _productQuantity;
            set
            {
                _productQuantity = value;
                OnPropertyChanged();
            }
        }

        public bool IsReadOnly
        {
            get => _isReadOnly;
            set
            {
                _isReadOnly = value;
                OnPropertyChanged();
            }
        }

        public string SupplierSearchTerm
        {
            get => _supplierSearchTerm;
            set
            {
                _supplierSearchTerm = value;
                OnPropertyChanged();
                // Only search if we're not setting the selected supplier name
                if (!_isSettingSelectedSupplier)
                {
                    SearchSuppliers();
                }
            }
        }

        public string ProductSearchTerm
        {
            get => _productSearchTerm;
            set
            {
                _productSearchTerm = value;
                OnPropertyChanged();
                // Only search if we're not setting the selected product name
                if (!_isSettingSelectedProduct)
                {
                    SearchProducts();
                }
            }
        }

        public ObservableCollection<ProductDto> ProductSearchResults
        {
            get => _productSearchResults;
            set
            {
                _productSearchResults = value;
                OnPropertyChanged();
            }
        }

        public bool HasSearchResults
        {
            get => _hasSearchResults;
            set
            {
                _hasSearchResults = value;
                OnPropertyChanged();
            }
        }

        public ProductDto SelectedProduct
        {
            get => _selectedProduct;
            set
            {
                _selectedProduct = value;
                OnPropertyChanged();
                if (value != null)
                {
                    SelectedProductId = value.Id;
                    ProductQuantity = 1; // Default quantity when selecting a product
                    // Update the search term to show the selected product name
                    _isSettingSelectedProduct = true;
                    ProductSearchTerm = value.Name;
                    _isSettingSelectedProduct = false;
                    // Close the dropdown
                    HasSearchResults = false;
                }
            }
        }

        public ObservableCollection<SupplierDto> SupplierSearchResults
        {
            get => _supplierSearchResults;
            set
            {
                _supplierSearchResults = value;
                OnPropertyChanged();
            }
        }

        public bool HasSupplierSearchResults
        {
            get => _hasSupplierSearchResults;
            set
            {
                _hasSupplierSearchResults = value;
                OnPropertyChanged();
            }
        }

        public SupplierDto SelectedSupplier
        {
            get => _selectedSupplier;
            set
            {
                _selectedSupplier = value;
                OnPropertyChanged();
                if (value != null)
                {
                    SelectedSupplierId = value.Id;
                    // Update the search term to show the selected supplier name
                    _isSettingSelectedSupplier = true;
                    SupplierSearchTerm = value.Name;
                    _isSettingSelectedSupplier = false;
                    // Close the dropdown
                    HasSupplierSearchResults = false;
                }
            }
        }

        public bool IsSupplierDropDownOpen
        {
            get => _isSupplierDropDownOpen;
            set
            {
                _isSupplierDropDownOpen = value;
                OnPropertyChanged();
            }
        }

        public ICommand AddProductCommand { get; }
        public ICommand RemoveProductCommand { get; }
        public ICommand SaveOrderCommand { get; }
        public ICommand CancelCommand { get; }

        private async Task LoadInitialDataAsync()
        {
            try
            {
                //_loadingViewModel.IsLoading = true;

                _allUnits = await _unitService.GetAllUnitsAsync();

                var suppliers = await _supplierService.GetAllSuppliersAsync(null, null, new Application.DTOs.Common.PaginationFilter { PageNumber = 1, PageSize = 50 });
                if (suppliers != null)
                {
                    _allSuppliers = suppliers.Data.ToList();
                    Suppliers = new ObservableCollection<SupplierDto>(_allSuppliers);
                }
                else
                {
                    _allSuppliers = new List<SupplierDto>();
                    Helpers.BootstrapMessageBoxHelper.ShowError("Failed to load suppliers.", owner: FindParentWindow());
                }

                if (!IsReadOnly)
                {
                    //await GeneratePONumberAsync();
                }
            }
            catch (Exception ex)
            {
                Helpers.BootstrapMessageBoxHelper.ShowError($"Error loading initial data: {ex.Message}", owner: FindParentWindow());
            }
            finally
            {
                //_loadingViewModel.IsLoading = false;
            }
        }

        private void FilterSuppliers()
        {
            if (string.IsNullOrWhiteSpace(SupplierSearchTerm))
            {
                Suppliers = new ObservableCollection<SupplierDto>(_allSuppliers);
            }
            else
            {
                var filteredSuppliers = _allSuppliers
                    .Where(s => s.Name.Contains(SupplierSearchTerm, StringComparison.OrdinalIgnoreCase))
                    .ToList();
                Suppliers = new ObservableCollection<SupplierDto>(filteredSuppliers);
            }
        }

        private void FilterProducts()
        {
            if (string.IsNullOrWhiteSpace(ProductSearchTerm))
            {
                Products = new ObservableCollection<ProductDto>(_allProducts);
            }
            else
            {
                var filteredProducts = _allProducts
                    .Where(p => p.Name.Contains(ProductSearchTerm, StringComparison.OrdinalIgnoreCase) ||
                               p.Barcode.Contains(ProductSearchTerm, StringComparison.OrdinalIgnoreCase))
                    .ToList();
                Products = new ObservableCollection<ProductDto>(filteredProducts);
            }
        }

        private async Task<string> GenerateOrderNumberAsync()
        {
            return $"PO-{DateTime.Now:yyyyMMdd}-{Guid.NewGuid().ToString().Substring(0, 4)}";
        }

        private Window FindParentWindow()
        {
            foreach (Window window in System.Windows.Application.Current.Windows)
            {
                if (window.Content != null &&
                    VisualTreeHelper.GetChild(window.Content as DependencyObject, 0) is FrameworkElement content &&
                    window.Name == "PurchaseOrderScreen")
                {
                    return window;
                }
            }
            return System.Windows.Application.Current.MainWindow;
        }

        private async void GeneratePONumberAsync()
        {
            try
            {
                var lastSaleId = await _purchaseOrderService.GetLastPOIdAsync();
                var nextNumber = lastSaleId + 1;
                var today = DateTime.Now;
                if (string.IsNullOrEmpty(OrderNumber))
                {
                    OrderNumber = $"PO-{today:yyyyMMddHHmmss}-{nextNumber:D3}";
                }
            }
            catch (Exception ex)
            {
                var today = DateTime.Now;
                OrderNumber = $"PO-{today:yyyyMMddHHmmss}-001";
                //Helpers.BootstrapMessageBoxHelper.ShowError($"Failed to generate PO number: {ex.Message}", owner: FindParentWindow());
            }
        }

        private bool CanExecuteAddProduct()
        {
            return SelectedProduct != null && ProductQuantity > 0;
        }

        private void ExecuteAddProduct()
        {
            if (SelectedProduct == null || ProductQuantity <= 0)
            {
                Helpers.BootstrapMessageBoxHelper.ShowError("Please select a product and enter a valid quantity.", owner: FindParentWindow());
                return;
            }

            var existingItem = SelectedProducts.FirstOrDefault(p => p.ProductId == SelectedProduct.Id);
            if (existingItem != null)
            {
                existingItem.Quantity += ProductQuantity;
                Helpers.BootstrapMessageBoxHelper.Show($"Quantity updated for {existingItem.ProductName}.", owner: FindParentWindow());
            }
            else
            {
                var newItem = new PurchaseItemViewModel
                {
                    ProductId = SelectedProduct.Id,
                    ProductName = SelectedProduct.Name,
                    Quantity = ProductQuantity,
                    Price = SelectedProduct.CostPrice,
                    SelectedUnitId = SelectedProduct.DefaultUnitId,
                    AvailableUnits = new ObservableCollection<UnitDto>(_allUnits ?? new List<UnitDto>())
                };
                SelectedProducts.Add(newItem);
            }

            ProductSearchTerm = string.Empty;
            ProductSearchResults?.Clear();
            HasSearchResults = false;
            SelectedProduct = null;
            SelectedProductId = null;
            ProductQuantity = 0;
        }

        private void ExecuteRemoveProduct(PurchaseItemViewModel item)
        {
            if (item == null) return;
            SelectedProducts.Remove(item);
        }

        public Action CloseWindowAction { get; set; }

        private async Task ExecuteSaveOrder()
        {
            if (SelectedSupplier == null)
            {
                Helpers.BootstrapMessageBoxHelper.ShowError("Please select a supplier.", owner: FindParentWindow());
                return;
            }

            if (!SelectedProducts.Any())
            {
                Helpers.BootstrapMessageBoxHelper.ShowError("Please add products to the order.", owner: FindParentWindow());
                return;
            }

            try
            {
                //_loadingViewModel.IsLoading = true;
                var isUpdate = false;
                int? purchaseId = null;
                // Try to find an existing purchase by OrderNumber (or use a unique Id if available)

                if (IsReadOnly)
                {
                    isUpdate = true;
                    purchaseId = purchaseToUpdate.Id;
                }

                var purchaseOrderDto = new PurchaseDto
                {
                    Id = purchaseId ?? 0,
                    SupplierId = SelectedSupplier.Id,
                    CreationDate = OrderDate ?? DateTime.UtcNow,
                    OrderNumber = OrderNumber,
                    Items = SelectedProducts.Select(item => new PurchaseItemDto
                    {
                        ProductId = item.ProductId,
                        Quantity = item.Quantity,
                        CostPrice = item.Price,
                        UnitId = item.SelectedUnitId ?? 0
                    }).ToList(),
                    TotalAmount = SelectedProducts.Sum(p => p.TotalPrice)
                };

                if (isUpdate)
                {
                    await _purchaseOrderService.UpdatePurchaseAsync(purchaseOrderDto);
                    _notificationViewModel.ShowSuccess("تم تحديث طلب الشراء بنجاح.");
                }
                else
                {
                    BaseResponse<bool> result = await _purchaseOrderService.CreatePurchaseAsync(purchaseOrderDto);
                    if (result.Success)
                    {
                        _notificationViewModel.ShowSuccess(result.Message ?? "تم حفظ امر الشراء بنجاح.");
                        CloseWindowAction?.Invoke();
                        ResetForm();
                    }
                    else
                    {
                        _notificationViewModel.ShowError(result.Message ?? "Error saving purchase order.");
                    }
                }
            }
            catch (Exception ex)
            {
                _notificationViewModel.ShowError($"Error saving/updating purchase order: {ex.Message}");
            }
            finally
            {
                //_loadingViewModel.IsLoading = false;
            }
        }

        private void ExecuteCancel()
        {
            System.Windows.Application.Current.Windows.OfType<Window>().FirstOrDefault(w => w.DataContext == this)?.Close();
        }

        private async void SearchProducts()
        {
            try
            {
                // Clear selection if search term is empty and we're not setting selected product
                if (string.IsNullOrWhiteSpace(ProductSearchTerm) && !_isSettingSelectedProduct && SelectedProduct != null)
                {
                    SelectedProduct = null;
                    SelectedProductId = null;
                }

                // Use SearchProductsAsync for both empty and non-empty search terms
                // Backend now handles empty search terms by returning all products
                var products = await _productService.SearchProductsAsync(ProductSearchTerm ?? "");

                ProductSearchResults = new ObservableCollection<ProductDto>(products);
                HasSearchResults = products.Any();
            }
            catch (Exception ex)
            {
                Helpers.BootstrapMessageBoxHelper.ShowError($"حدث خطأ أثناء البحث عن المنتجات: {ex.Message}", owner: FindParentWindow());
                ProductSearchResults = new ObservableCollection<ProductDto>();
                HasSearchResults = false;
            }
        }

        private async void SearchSuppliers()
        {
            try
            {
                List<SupplierDto> suppliers;

                if (string.IsNullOrWhiteSpace(SupplierSearchTerm))
                {
                    // Clear selection if search term is empty and we're not setting selected supplier
                    if (!_isSettingSelectedSupplier && SelectedSupplier != null)
                    {
                        SelectedSupplier = null;
                        SelectedSupplierId = null;
                    }

                    // Load first page of suppliers when no search term
                    var allSuppliers = await _supplierService.GetAllSuppliersAsync(null, null);
                    suppliers = allSuppliers.Take(20).ToList();
                }
                else
                {
                    // Search suppliers when there's a search term
                    suppliers = await _supplierService.SearchSuppliersAsync(SupplierSearchTerm);
                }

                SupplierSearchResults = new ObservableCollection<SupplierDto>(suppliers);
                HasSupplierSearchResults = suppliers.Any();
            }
            catch (Exception ex)
            {
                Helpers.BootstrapMessageBoxHelper.ShowError($"حدث خطأ أثناء البحث عن الموردين: {ex.Message}", "خطأ", owner: FindParentWindow());
                SupplierSearchResults = new ObservableCollection<SupplierDto>();
                HasSupplierSearchResults = false;
            }
        }

        public async void LoadProductsOnDropdownOpen()
        {
            // Load first page of products when dropdown is opened
            if (ProductSearchResults == null || !ProductSearchResults.Any())
            {
                await LoadInitialProducts();
            }
            else
            {
                // Show existing results
                HasSearchResults = ProductSearchResults.Any();
            }
        }

        private async Task LoadInitialProducts()
        {
            try
            {
                // Use SearchProductsAsync with empty string to get all products
                var products = await _productService.SearchProductsAsync("");
                ProductSearchResults = new ObservableCollection<ProductDto>(products);
                HasSearchResults = ProductSearchResults.Any();
            }
            catch (Exception ex)
            {
                Helpers.BootstrapMessageBoxHelper.ShowError($"حدث خطأ أثناء تحميل المنتجات: {ex.Message}", "خطأ", owner: FindParentWindow());
                ProductSearchResults = new ObservableCollection<ProductDto>();
                HasSearchResults = false;
            }
        }

        public async void LoadSuppliersOnDropdownOpen()
        {
            // Load first page of suppliers when dropdown is opened
            if (SupplierSearchResults == null || !SupplierSearchResults.Any())
            {
                await LoadInitialSuppliers();
            }
            else
            {
                // Show existing results
                HasSupplierSearchResults = SupplierSearchResults.Any();
            }
        }

        private async Task LoadInitialSuppliers()
        {
            try
            {
                var suppliers = await _supplierService.GetAllSuppliersAsync(null, null);
                SupplierSearchResults = new ObservableCollection<SupplierDto>(suppliers.Take(20));
                HasSupplierSearchResults = SupplierSearchResults.Any();
            }
            catch (Exception ex)
            {
                Helpers.BootstrapMessageBoxHelper.ShowError($"حدث خطأ أثناء تحميل الموردين: {ex.Message}", "خطأ", owner: FindParentWindow());
                SupplierSearchResults = new ObservableCollection<SupplierDto>();
                HasSupplierSearchResults = false;
            }
        }

        private void ResetForm()
        {
            SelectedSupplier = null;
            SelectedSupplierId = null;
            _isSettingSelectedSupplier = true;
            SupplierSearchTerm = string.Empty;
            _isSettingSelectedSupplier = false;
            OrderDate = DateTime.Now;
            SelectedProducts.Clear();
            _isSettingSelectedProduct = true;
            ProductSearchTerm = string.Empty;
            _isSettingSelectedProduct = false;
            ProductQuantity = 0;
            SelectedProduct = null;
            GeneratePONumberAsync();
        }

        private PurchaseDto purchaseToUpdate = new PurchaseDto();
        public async void LoadPurchaseOrderData(PurchaseDto purchase)
        {
            if (purchase == null) return;
            await LoadInitialDataAsync();
            OrderNumber = purchase.OrderNumber;
            OrderDate = purchase.CreationDate;
            SelectedSupplier = purchase.Supplier;
            SelectedSupplierId = purchase.SupplierId;
            SelectedProducts.Clear();
            if (purchase.Items != null)
            {
                foreach (var item in purchase.Items)
                {
                    SelectedProducts.Add(new PurchaseItemViewModel
                    {
                        ProductId = item.ProductId,
                        ProductName = item.ProductName,
                        Quantity = item.Quantity,
                        Price = item.CostPrice,
                        SelectedUnitId = item.UnitId,
                        AvailableUnits = new ObservableCollection<UnitDto>(_allUnits?.ToList() ?? new List<UnitDto>())
                    });
                }
            }
            purchaseToUpdate = purchase;
            IsReadOnly = true;
        }
    }
}