using ExactCash.Domain.Common;
using System.Text.Json.Serialization;

namespace ExactCash.Domain.Entities
{
    /// <summary>
    /// Represents the credit limit history for a customer.
    /// </summary>
    public class CustomerCreditLimit : BaseEntity
    {
        /// <summary>
        /// Foreign key to the customer.
        /// </summary>
        public int CustomerId { get; set; }

        /// <summary>
        /// Navigation property to the customer.
        /// </summary>
        [JsonIgnore]
        public Customer Customer { get; set; }

        /// <summary>
        /// The previous credit limit amount.
        /// </summary>
        public decimal PreviousLimit { get; set; }

        /// <summary>
        /// The new credit limit amount.
        /// </summary>
        public decimal NewLimit { get; set; }

        /// <summary>
        /// The reason for the credit limit change.
        /// </summary>
        public string Reason { get; set; }

        /// <summary>
        /// The user who approved or made the credit limit change.
        /// </summary>
        public string ApprovedBy { get; set; }

        /// <summary>
        /// Date when the credit limit change was approved.
        /// </summary>
        public DateTime ApprovalDate { get; set; }

        /// <summary>
        /// Date when the new credit limit becomes effective.
        /// </summary>
        public DateTime EffectiveDate { get; set; }

        /// <summary>
        /// Status of the credit limit change (Pending, Approved, Rejected, Active, Expired).
        /// </summary>
        public string Status { get; set; }

        /// <summary>
        /// Additional notes about the credit limit change.
        /// </summary>
        public string? Notes { get; set; }

        #region Constructors
        public CustomerCreditLimit()
        {
            Status = "Pending";
            ApprovalDate = DateTime.UtcNow;
            EffectiveDate = DateTime.UtcNow;
        }

        public CustomerCreditLimit(int customerId, decimal previousLimit, decimal newLimit,
            string reason, string approvedBy)
        {
            CustomerId = customerId;
            PreviousLimit = previousLimit;
            NewLimit = newLimit;
            Reason = reason;
            ApprovedBy = approvedBy;
            ApprovalDate = DateTime.UtcNow;
            EffectiveDate = DateTime.UtcNow;
            Status = "Approved";
            CreationDate = DateTime.UtcNow;
        }
        #endregion

        #region Methods
        /// <summary>
        /// Approves the credit limit change.
        /// </summary>
        /// <param name="approvedBy">The user who approved the change</param>
        /// <param name="effectiveDate">When the change becomes effective</param>
        public void Approve(string approvedBy, DateTime? effectiveDate = null)
        {
            ApprovedBy = approvedBy;
            ApprovalDate = DateTime.UtcNow;
            EffectiveDate = effectiveDate ?? DateTime.UtcNow;
            Status = "Approved";
            LastUpdatedDate = DateTime.UtcNow;
        }

        /// <summary>
        /// Rejects the credit limit change.
        /// </summary>
        /// <param name="rejectedBy">The user who rejected the change</param>
        /// <param name="reason">Reason for rejection</param>
        public void Reject(string rejectedBy, string reason = null)
        {
            ApprovedBy = rejectedBy;
            ApprovalDate = DateTime.UtcNow;
            Status = "Rejected";
            if (!string.IsNullOrWhiteSpace(reason))
            {
                Notes = string.IsNullOrWhiteSpace(Notes) ? reason : $"{Notes}. Rejection reason: {reason}";
            }
            LastUpdatedDate = DateTime.UtcNow;
        }

        /// <summary>
        /// Activates the credit limit change (makes it effective).
        /// </summary>
        public void Activate()
        {
            if (Status == "Approved" && EffectiveDate <= DateTime.UtcNow)
            {
                Status = "Active";
                LastUpdatedDate = DateTime.UtcNow;
            }
        }

        /// <summary>
        /// Checks if this credit limit change is currently active.
        /// </summary>
        /// <returns>True if active, false otherwise</returns>
        public bool IsActive()
        {
            return Status == "Active" && EffectiveDate <= DateTime.UtcNow;
        }

        /// <summary>
        /// Gets the difference between new and previous limits.
        /// </summary>
        /// <returns>The change amount (positive for increase, negative for decrease)</returns>
        public decimal GetLimitChange()
        {
            return NewLimit - PreviousLimit;
        }

        /// <summary>
        /// Checks if this is a credit limit increase.
        /// </summary>
        /// <returns>True if increase, false otherwise</returns>
        public bool IsIncrease()
        {
            return NewLimit > PreviousLimit;
        }

        /// <summary>
        /// Checks if this is a credit limit decrease.
        /// </summary>
        /// <returns>True if decrease, false otherwise</returns>
        public bool IsDecrease()
        {
            return NewLimit < PreviousLimit;
        }
        #endregion
    }
}
