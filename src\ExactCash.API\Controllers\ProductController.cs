﻿using Microsoft.AspNetCore.Mvc;
using ExactCash.Application.Contracts;
using ExactCash.Domain.Entities;
using Swashbuckle.AspNetCore.Annotations;
using ExactCash.Application.DTOs.Common;
using System.Security.Claims;
#nullable disable

namespace YourProject.API.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class ProductController : ControllerBase
    {
        private readonly IProductService _productService;
        private readonly ILogger<ProductController> _logger;

        public ProductController(IProductService productService, ILogger<ProductController> logger)
        {
            _productService = productService;
            _logger = logger;
        }

        [SwaggerIgnore]
        [HttpPost("create-product")]
        public async Task<IActionResult> CreateProduct([FromForm] Product product, [FromForm] IFormFile imageFile)
        {
            var userEmail = User.FindFirst(ClaimTypes.Email)?.Value;
            var createdProduct = await _productService.CreateAsync(product, imageFile, userEmail);
            return StatusCode(createdProduct.StatusCode, createdProduct);
        }

        [HttpGet("{id}")]
        public async Task<IActionResult> GetProductById(int id)
        {
            try
            {
                var product = await _productService.GetByIdAsync(id);

                if (product == null)
                {
                    return NotFound();
                }

                return Ok(product);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while fetching product.");
                return StatusCode(500, "Internal server error");
            }
        }

        [HttpPost("update-product")]
        public async Task<IActionResult> Update([FromForm] Product product,[FromForm] IFormFile imageFile)
        {
            var updatedProduct = await _productService.UpdateAsync(product.Id, product, imageFile);
            return Ok(updatedProduct);
        }

        [HttpGet("get-all")]
        public async Task<IActionResult> GetAll([FromQuery] string name, [FromQuery] string sku, [FromQuery] int? categoryId, [FromQuery] int? brandId, [FromQuery] int? unitId, [FromQuery] string barcode, [FromQuery] PaginationFilter pagination)
        {
            var result = await _productService.GetAllAsync(name, sku, categoryId,brandId, unitId, barcode, pagination);
            return Ok(result);
        }

        [HttpGet("search")]
        public async Task<IActionResult> SearchProductsAsync([FromQuery] string term)
        {
            return Ok(await _productService.SearchProductsAsync(term));
        }

        [HttpGet("get-product-by-barcode/{barcode}")]
        public async Task<IActionResult> GetProductByBarcodeAsync(string barcode)
        {
            return Ok(await _productService.GetProductByBarcodeAsync(barcode));
        }

        [HttpDelete("{id}")]
        public async Task<IActionResult> Delete(int id)
        {
            var result = await _productService.DeleteAsync(id);
            return Ok(result);
        }

        /// <summary>
        /// GenerateBarcodeImage
        /// </summary>
        /// <param name="productId"></param>
        /// <returns></returns>
        [HttpGet("generate-barcode-image/{productId}")]
        public async Task<IActionResult> GenerateBarcodeImage(int productId)
        {
            var barcodeImage =await _productService.GenerateBarcodeImageAsync(productId);
            //return File(barcodeImage, "image/png");
            return Ok(barcodeImage);
        }
    }
}
