using ExactCash.Application.DTOs;
using ExactCash.Application.Interfaces;
using ExactCash.Domain.Entities;
using ExactCash.Infrastructure.Persistence;
using Microsoft.EntityFrameworkCore;

namespace ExactCash.Application.Services
{
    public class UnitService : IUnitService
    {
        private readonly AppPostgreSQLDbContext _context;

        public UnitService(AppPostgreSQLDbContext context)
        {
            _context = context;
        }

        public async Task<UnitDto> GetUnitByIdAsync(int id)
        {
            var unit = await _context.Units.FindAsync(id);
            if (unit == null)
                return null;

            return MapToDto(unit);
        }

        public async Task<IEnumerable<UnitDto>> GetAllUnitsAsync()
        {
            var units = await _context.Units.ToListAsync();
            var unitDtos = new List<UnitDto>();
            
            foreach (var unit in units)
            {
                unitDtos.Add(MapToDto(unit));
            }

            return unitDtos;
        }

        public async Task<UnitDto> CreateUnitAsync(UnitDto unitDto)
        {
            var unit = new Unit(unitDto.Name, unitDto.Symbol)
            {
                IsActive = unitDto.IsActive,
                ConversionRateToBaseUnit = unitDto.ConversionRateToBaseUnit
            };

            await _context.Units.AddAsync(unit);
            await _context.SaveChangesAsync();
            return MapToDto(unit);
        }

        public async Task UpdateUnitAsync(UnitDto unitDto)
        {
            var unit = await _context.Units.FindAsync(unitDto.Id);
            if (unit == null)
                throw new KeyNotFoundException($"Unit with ID {unitDto.Id} not found.");

            unit.Update(unitDto.ConversionRateToBaseUnit, unitDto.Name, unitDto.Symbol, unitDto.IsActive);
            _context.Entry(unit).State = EntityState.Modified;
            await _context.SaveChangesAsync();
        }

        public async Task DeleteUnitAsync(int id)
        {
            var unit = await _context.Units.FindAsync(id);
            if (unit == null)
                throw new KeyNotFoundException($"Unit with ID {id} not found.");

            _context.Units.Remove(unit);
            await _context.SaveChangesAsync();
        }

        private UnitDto MapToDto(Unit unit)
        {
            return new UnitDto
            {
                Id = unit.Id,
                Name = unit.Name,
                Symbol = unit.Symbol,
                IsActive = unit.IsActive,
                ConversionRateToBaseUnit = unit.ConversionRateToBaseUnit,
                CreatedBy = unit.CreatedBy,
                LastUpdatedBy = unit.LastUpdatedBy, 
                CreationDate = unit.CreationDate,
                LastUpdatedDate = unit.LastUpdatedDate
            };
        }
    }
} 