.auth-container {
    min-height: 100vh;
    display: flex;
    background-color: #f8fafc;
}

.auth-content {
    display: flex;
    width: 50%;
    min-height: 100vh;
}

.auth-left-panel {
    flex: 1;
    background: linear-gradient(135deg, #1e293b 0%, #0f172a 100%);
    color: white;
    padding: 2rem;
    display: flex;
    flex-direction: column;
}

.logo-container {
    margin-bottom: 3rem;
}

.logo {
    max-width: 180px;
    height: auto;
}

.auth-info {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
    max-width: 600px;
    margin: 0 auto;
}

.auth-info h1 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    line-height: 1.2;
}

.subtitle {
    font-size: 1.25rem;
    color: #94a3b8;
    margin-bottom: 2.5rem;
}

.features-list {
    display: flex;
    flex-direction: column;
    gap: 1.25rem;
}

.feature-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.feature-item i {
    color: #22c55e;
    font-size: 1.25rem;
}

    .feature-item span {
        font-size: 1.125rem;
        color: #94a3b8;
    }

.auth-right-panel {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: left;
    padding: 2rem;
}

.auth-form-container {
    width: 100%;
    max-width: 420px;
    background: white;
    padding: 2.5rem;
    border-radius: 1rem;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.auth-form-header {
    text-align: center;
    margin-bottom: 2rem;
}

.auth-form-header h2 {
    font-size: 1.875rem;
    font-weight: 700;
    color: #1e293b;
    margin-bottom: 0.5rem;
}

.auth-form-header p {
    color: #64748b;
    font-size: 1rem;
}

.auth-form {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.form-group label {
    font-size: 0.875rem;
    font-weight: 500;
    color: #475569;
}

.form-control {
    width: 100%;
    padding: 0.75rem 1rem;
    border: 1px solid #e2e8f0;
    border-radius: 0.5rem;
    font-size: 1rem;
    transition: all 0.2s;
}

.form-control:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.password-input {
    position: relative;
}

.toggle-password {
    position: absolute;
    right: 1rem;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: #64748b;
    cursor: pointer;
    padding: 0.25rem;
}

.toggle-password:hover {
    color: #3b82f6;
}

.btn-primary {
    width: 100%;
    padding: 0.875rem;
    background-color: #3b82f6;
    color: white;
    border: none;
    border-radius: 0.5rem;
    font-size: 1rem;
    font-weight: 500;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    cursor: pointer;
    transition: all 0.2s;
}

.btn-primary:hover {
    background-color: #2563eb;
}

.btn-primary i {
    font-size: 1.25rem;
}

/* Loading overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

/* Responsive design */
@media (max-width: 1024px) {
    .auth-content {
        flex-direction: column;
    }

    .auth-left-panel {
        padding: 1.5rem;
    }

    .auth-info {
        text-align: center;
    }

    .auth-right-panel {
        padding: 1.5rem;
    }
}

@media (max-width: 640px) {
    .auth-form-container {
        padding: 1.5rem;
    }

    .auth-info h1 {
        font-size: 2rem;
    }

    .subtitle {
        font-size: 1.125rem;
    }
}

.container {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin: auto;
    padding: 20px;
}

.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
}

.logo {
    max-width: 150px;
}

/* Main Content */
.main-content {
    display: flex;
    flex-direction: column;
    gap: 40px;
    width: 100%;
    margin-top: 20px;
}

.info-section {
    flex: 1;
}

    .info-section h1 {
        font-size: 2rem;
        margin-bottom: 10px;
    }

    .info-section p {
        margin-bottom: 10px;
    }

    .info-section ul {
        list-style: none;
        padding: 0;
    }

        .info-section ul li {
            margin: 5px 0;
        }

.cloud-logos img {
    max-height: 50px;
    margin: 10px;
}

/* Signup Section */
.signup-section {
    background-color: #f9f9f9;
    padding: 20px;
    border-radius: 8px;
    max-width: 450px;
    text-align: center;
}

    .signup-section h2 {
        margin-bottom: 10px;
    }

    .signup-section p {
        margin: 10px 0;
    }

    .signup-section .btn {
        display: block;
        width: 100%;
        padding: 10px;
        margin: 10px 0;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        font-size: 16px;
    }

        .signup-section .btn.google {
            background-color: #4285F4;
            color: #fff;
        }

        .signup-section .btn.microsoft {
            background-color: #0078D4;
            color: #fff;
        }

        .signup-section .btn.github {
            background-color: #333;
            color: #fff;
        }

        .signup-section .btn.email {
            background-color: #6c63ff;
            color: #fff;
        }

.info-section {
    flex: 2;
}

.signup-section {
    flex: 1;
}