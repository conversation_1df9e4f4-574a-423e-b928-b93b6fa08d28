﻿#nullable disable

namespace ExactCash.Domain.Entities
{
    /// <summary>
    /// Represents a line item in a purchase (the product, quantity, and cost for that item).
    /// </summary>
    public class PurchaseItem
    {
        /// <summary>
        /// The unique identifier for this purchase item.
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// Foreign key to the purchase this item is part of.
        /// </summary>
        public int PurchaseId { get; set; }

        /// <summary>
        /// Navigation property to the parent purchase this item belongs to.
        /// </summary>
        public Purchase Purchase { get; set; }

        /// <summary>
        /// Foreign key to the product being purchased.
        /// </summary>
        public int ProductId { get; set; }

        /// <summary>
        /// Navigation property to the product being purchased.
        /// </summary>
        public Product Product { get; set; }

        /// <summary>
        /// Unit of measure for this item in the purchase (can be different from the product's unit)
        /// </summary>
        public int UnitId { get; set; }

        /// <summary>
        /// Navigation property to the unit of measure for this item.
        /// </summary>
        public Unit Unit { get; set; }

        /// <summary>
        /// Quantity of the product purchased.
        /// </summary>
        public decimal Quantity { get; set; }

        /// <summary>
        /// Cost price of a single unit of the product at the time of purchase.
        /// </summary>
        public decimal CostPrice { get; set; }
    }

}
