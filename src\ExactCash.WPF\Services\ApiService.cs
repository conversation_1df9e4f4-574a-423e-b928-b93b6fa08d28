﻿using System;
using System.Net.Http;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using System.Configuration;

namespace ExactCash.WPF.Services
{
    public class ApiService
    {
        private readonly HttpClient _httpClient;
        private readonly string _baseUrl;

        public ApiService()
        {
            _httpClient = new HttpClient();

            // Get base URL from app.config or use default
            _baseUrl = ConfigurationManager.AppSettings["ApiBaseUrl"] ?? "https://localhost:7001/";

            // Set default headers
            _httpClient.DefaultRequestHeaders.Add("Accept", "application/json");

            // Add authorization header if token exists
            var token = GetAuthToken();
            if (!string.IsNullOrEmpty(token))
            {
                _httpClient.DefaultRequestHeaders.Add("Authorization", $"Bearer {token}");
            }
        }

        public async Task<HttpResponseMessage> GetAsync(string endpoint)
        {
            try
            {
                var url = $"{_baseUrl.TrimEnd('/')}/{endpoint.TrimStart('/')}";
                return await _httpClient.GetAsync(url);
            }
            catch (Exception ex)
            {
                throw new Exception($"Error calling GET {endpoint}: {ex.Message}", ex);
            }
        }

        public async Task<HttpResponseMessage> PostAsync(string endpoint, object data)
        {
            try
            {
                var url = $"{_baseUrl.TrimEnd('/')}/{endpoint.TrimStart('/')}";
                var json = JsonSerializer.Serialize(data);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                return await _httpClient.PostAsync(url, content);
            }
            catch (Exception ex)
            {
                throw new Exception($"Error calling POST {endpoint}: {ex.Message}", ex);
            }
        }

        public async Task<HttpResponseMessage> PutAsync(string endpoint, object data)
        {
            try
            {
                var url = $"{_baseUrl.TrimEnd('/')}/{endpoint.TrimStart('/')}";
                var json = JsonSerializer.Serialize(data);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                return await _httpClient.PutAsync(url, content);
            }
            catch (Exception ex)
            {
                throw new Exception($"Error calling PUT {endpoint}: {ex.Message}", ex);
            }
        }

        public async Task<HttpResponseMessage> DeleteAsync(string endpoint)
        {
            try
            {
                var url = $"{_baseUrl.TrimEnd('/')}/{endpoint.TrimStart('/')}";
                return await _httpClient.DeleteAsync(url);
            }
            catch (Exception ex)
            {
                throw new Exception($"Error calling DELETE {endpoint}: {ex.Message}", ex);
            }
        }

        public async Task<T> GetAsync<T>(string endpoint)
        {
            var response = await GetAsync(endpoint);

            if (response.IsSuccessStatusCode)
            {
                var jsonContent = await response.Content.ReadAsStringAsync();
                var options = new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                };

                return JsonSerializer.Deserialize<T>(jsonContent, options);
            }

            throw new Exception($"API call failed with status: {response.StatusCode}");
        }

        public async Task<T> PostAsync<T>(string endpoint, object data)
        {
            var response = await PostAsync(endpoint, data);

            if (response.IsSuccessStatusCode)
            {
                var jsonContent = await response.Content.ReadAsStringAsync();
                var options = new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                };

                return JsonSerializer.Deserialize<T>(jsonContent, options);
            }

            throw new Exception($"API call failed with status: {response.StatusCode}");
        }

        private string GetAuthToken()
        {
            // This would typically come from secure storage or user session
            // For now, return empty string - you can implement proper authentication later
            return string.Empty;
        }

        public void SetAuthToken(string token)
        {
            _httpClient.DefaultRequestHeaders.Remove("Authorization");
            if (!string.IsNullOrEmpty(token))
            {
                _httpClient.DefaultRequestHeaders.Add("Authorization", $"Bearer {token}");
            }
        }

        public void Dispose()
        {
            _httpClient?.Dispose();
        }
    }

    // Response wrapper for API calls
    public class ApiResponse<T>
    {
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
        public T Data { get; set; }
        public int StatusCode { get; set; }
    }

    // Generic list response
    public class ApiListResponse<T>
    {
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
        public List<T> Data { get; set; } = new List<T>();
        public int TotalCount { get; set; }
        public int PageNumber { get; set; }
        public int PageSize { get; set; }
        public int StatusCode { get; set; }
    }
}
