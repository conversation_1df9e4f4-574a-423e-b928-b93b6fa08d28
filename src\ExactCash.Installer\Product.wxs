<?xml version="1.0" encoding="UTF-8"?>
<Wix xmlns="http://schemas.microsoft.com/wix/2006/wi">
  <Product Id="*" Name="ExactCash POS System" Language="1033" Version="1.0.0.0" Manufacturer="ExactCash Solutions" UpgradeCode="12345678-1234-1234-1234-123456789012">
    <Package InstallerVersion="200" Compressed="yes" InstallScope="perMachine" />

    <MajorUpgrade DowngradeErrorMessage="A newer version of [ProductName] is already installed." />
    <MediaTemplate EmbedCab="yes" />

    <Feature Id="ProductFeature" Title="ExactCash POS System" Level="1">
      <ComponentGroupRef Id="ProductComponents" />
      <ComponentRef Id="ApplicationShortcut" />
    </Feature>

    <!-- Directory structure -->
    <Directory Id="TARGETDIR" Name="SourceDir">
      <Directory Id="ProgramFiles64Folder">
        <Directory Id="INSTALLFOLDER" Name="ExactCash POS" />
      </Directory>
      <Directory Id="ProgramMenuFolder">
        <Directory Id="ApplicationProgramsFolder" Name="ExactCash POS" />
      </Directory>
      <Directory Id="DesktopFolder" Name="Desktop" />
    </Directory>

    <!-- Application files -->
    <ComponentGroup Id="ProductComponents" Directory="INSTALLFOLDER">
      <!-- Main executable -->
      <Component Id="MainExecutable" Guid="*">
        <File Id="ExactCashWPFExe" Source="$(var.ExactCash.WPF.TargetPath)" KeyPath="yes" Checksum="yes">
          <Shortcut Id="ApplicationStartMenuShortcut" Directory="ApplicationProgramsFolder" Name="ExactCash POS" WorkingDirectory="INSTALLFOLDER" Icon="ExactCash.exe" IconIndex="0" Advertise="yes" />
          <Shortcut Id="ApplicationDesktopShortcut" Directory="DesktopFolder" Name="ExactCash POS" WorkingDirectory="INSTALLFOLDER" Icon="ExactCash.exe" IconIndex="0" Advertise="yes" />
        </File>
      </Component>

      <!-- Configuration files -->
      <Component Id="ConfigFiles" Guid="*">
        <File Id="AppSettings" Source="$(var.ExactCash.WPF.TargetDir)appsettings.json" KeyPath="yes" />
        <File Id="AppConfig" Source="$(var.ExactCash.WPF.TargetDir)ExactCash.WPF.dll.config" />
      </Component>

      <!-- Application DLLs -->
      <Component Id="ApplicationDLLs" Guid="*">
        <File Id="ExactCashWPFDLL" Source="$(var.ExactCash.WPF.TargetDir)ExactCash.WPF.dll" KeyPath="yes" />
        <File Id="ExactCashApplicationDLL" Source="$(var.ExactCash.WPF.TargetDir)ExactCash.Application.dll" />
        <File Id="ExactCashDomainDLL" Source="$(var.ExactCash.WPF.TargetDir)ExactCash.Domain.dll" />
        <File Id="ExactCashInfrastructureDLL" Source="$(var.ExactCash.WPF.TargetDir)ExactCash.Infrastructure.dll" />
      </Component>

      <!-- Entity Framework Core DLLs -->
      <Component Id="EntityFrameworkDLLs" Guid="*">
        <File Id="EFCore" Source="$(var.ExactCash.WPF.TargetDir)Microsoft.EntityFrameworkCore.dll" KeyPath="yes" />
        <File Id="EFCoreAbstractions" Source="$(var.ExactCash.WPF.TargetDir)Microsoft.EntityFrameworkCore.Abstractions.dll" />
        <File Id="EFCoreRelational" Source="$(var.ExactCash.WPF.TargetDir)Microsoft.EntityFrameworkCore.Relational.dll" />
        <File Id="EFCoreSqlServer" Source="$(var.ExactCash.WPF.TargetDir)Microsoft.EntityFrameworkCore.SqlServer.dll" />
        <File Id="NpgsqlEFCore" Source="$(var.ExactCash.WPF.TargetDir)Npgsql.EntityFrameworkCore.PostgreSQL.dll" />
        <File Id="Npgsql" Source="$(var.ExactCash.WPF.TargetDir)Npgsql.dll" />
      </Component>

      <!-- Microsoft Extensions DLLs -->
      <Component Id="MicrosoftExtensionsDLLs" Guid="*">
        <File Id="MSExtDI" Source="$(var.ExactCash.WPF.TargetDir)Microsoft.Extensions.DependencyInjection.dll" KeyPath="yes" />
        <File Id="MSExtDIAbstractions" Source="$(var.ExactCash.WPF.TargetDir)Microsoft.Extensions.DependencyInjection.Abstractions.dll" />
        <File Id="MSExtLogging" Source="$(var.ExactCash.WPF.TargetDir)Microsoft.Extensions.Logging.dll" />
        <File Id="MSExtLoggingAbstractions" Source="$(var.ExactCash.WPF.TargetDir)Microsoft.Extensions.Logging.Abstractions.dll" />
        <File Id="MSExtConfiguration" Source="$(var.ExactCash.WPF.TargetDir)Microsoft.Extensions.Configuration.dll" />
        <File Id="MSExtConfigurationAbstractions" Source="$(var.ExactCash.WPF.TargetDir)Microsoft.Extensions.Configuration.Abstractions.dll" />
      </Component>
    </ComponentGroup>

    <!-- Start Menu folder -->
    <Component Id="ApplicationShortcut" Directory="ApplicationProgramsFolder" Guid="*">
      <Shortcut Id="UninstallProduct" Name="Uninstall ExactCash POS" Description="Uninstalls ExactCash POS System" Target="[SystemFolder]msiexec.exe" Arguments="/x [ProductCode]" />
      <RemoveFolder Id="ApplicationProgramsFolder" On="uninstall" />
      <RegistryValue Root="HKCU" Key="Software\ExactCash\POS" Name="installed" Type="integer" Value="1" KeyPath="yes" />
    </Component>

    <!-- Icon -->
    <Icon Id="ExactCash.exe" SourceFile="$(var.ExactCash.WPF.TargetPath)" />

    <!-- Properties -->
    <Property Id="ARPPRODUCTICON" Value="ExactCash.exe" />
    <Property Id="ARPHELPLINK" Value="https://exactcash.com/support" />
    <Property Id="ARPURLINFOABOUT" Value="https://exactcash.com" />
    <Property Id="ARPNOREPAIR" Value="1" />
    <Property Id="ARPNOMODIFY" Value="1" />
  </Product>
</Wix>
