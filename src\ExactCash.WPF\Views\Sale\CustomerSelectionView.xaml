<Window x:Class="ExactCash.WPF.Views.Sale.CustomerSelectionView"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="اختيار عميل"
        Height="700"
        Width="900"
        WindowStartupLocation="CenterOwner"
        ResizeMode="CanResize"
        FlowDirection="RightToLeft">

        <Window.Resources>
                <!-- Modern Button Style -->
                <Style x:Key="ModernButton"
                       TargetType="Button">
                        <Setter Property="Background"
                                Value="#007BFF"/>
                        <Setter Property="Foreground"
                                Value="White"/>
                        <Setter Property="BorderThickness"
                                Value="0"/>
                        <Setter Property="Padding"
                                Value="15,8"/>
                        <Setter Property="FontSize"
                                Value="14"/>
                        <Setter Property="FontWeight"
                                Value="SemiBold"/>
                        <Setter Property="Cursor"
                                Value="Hand"/>
                        <Setter Property="Template">
                                <Setter.Value>
                                        <ControlTemplate TargetType="Button">
                                                <Border Background="{TemplateBinding Background}"
                                                        CornerRadius="5"
                                                        Padding="{TemplateBinding Padding}">
                                                        <ContentPresenter HorizontalAlignment="Center"
                                                                          VerticalAlignment="Center"/>
                                                </Border>
                                                <ControlTemplate.Triggers>
                                                        <Trigger Property="IsMouseOver"
                                                                 Value="True">
                                                                <Setter Property="Background"
                                                                        Value="#0056B3"/>
                                                        </Trigger>
                                                        <Trigger Property="IsPressed"
                                                                 Value="True">
                                                                <Setter Property="Background"
                                                                        Value="#004085"/>
                                                        </Trigger>
                                                </ControlTemplate.Triggers>
                                        </ControlTemplate>
                                </Setter.Value>
                        </Setter>
                </Style>

                <!-- Modern TextBox Style -->
                <Style x:Key="ModernTextBox"
                       TargetType="TextBox">
                        <Setter Property="Padding"
                                Value="10,8"/>
                        <Setter Property="FontSize"
                                Value="14"/>
                        <Setter Property="BorderThickness"
                                Value="1"/>
                        <Setter Property="BorderBrush"
                                Value="#DDDDDD"/>
                        <Setter Property="Background"
                                Value="White"/>
                        <Setter Property="Template">
                                <Setter.Value>
                                        <ControlTemplate TargetType="TextBox">
                                                <Border Background="{TemplateBinding Background}"
                                                        BorderBrush="{TemplateBinding BorderBrush}"
                                                        BorderThickness="{TemplateBinding BorderThickness}"
                                                        CornerRadius="5"
                                                        Padding="{TemplateBinding Padding}">
                                                        <ScrollViewer x:Name="PART_ContentHost"/>
                                                </Border>
                                        </ControlTemplate>
                                </Setter.Value>
                        </Setter>
                </Style>

                <!-- Modern Border Style -->
                <Style x:Key="ModernBorder"
                       TargetType="Border">
                        <Setter Property="Background"
                                Value="White"/>
                        <Setter Property="BorderBrush"
                                Value="#E5E5E5"/>
                        <Setter Property="BorderThickness"
                                Value="1"/>
                        <Setter Property="CornerRadius"
                                Value="8"/>
                        <Setter Property="Padding"
                                Value="20"/>
                </Style>
        </Window.Resources>

        <Grid Background="#F8F9FA">
                <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <!-- Header -->
                <Border Grid.Row="0"
                        Style="{StaticResource ModernBorder}"
                        Margin="10">
                        <TextBlock Text="اختيار عميل للفاتورة"
                                   FontSize="18"
                                   FontWeight="Bold"
                                   HorizontalAlignment="Center"
                                   Foreground="#333333"/>
                </Border>

                <!-- Search Section -->
                <Border Grid.Row="1"
                        Style="{StaticResource ModernBorder}"
                        Margin="10,0,10,10">
                        <Grid>
                                <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="Auto"/>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="Auto"/>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="Auto"/>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>

                                <TextBlock Text="البحث بالاسم"
                                           VerticalAlignment="Center"
                                           FontSize="14"
                                           Margin="0,0,10,0"/>
                                <TextBox Grid.Column="1"
                                         Text="{Binding NameSearch, UpdateSourceTrigger=PropertyChanged}"
                                         Style="{StaticResource ModernTextBox}"
                                         Margin="0,0,10,0"/>

                                <TextBlock Text="البحث بالهاتف"
                                           Grid.Column="2"
                                           VerticalAlignment="Center"
                                           FontSize="14"
                                           Margin="0,0,10,0"/>
                                <TextBox Grid.Column="3"
                                         Text="{Binding PhoneSearch, UpdateSourceTrigger=PropertyChanged}"
                                         Style="{StaticResource ModernTextBox}"
                                         Margin="0,0,10,0"/>

                                <TextBlock Text="البحث بالتصنيف"
                                           Grid.Column="4"
                                           VerticalAlignment="Center"
                                           FontSize="14"
                                           Margin="0,0,10,0"/>
                                <ComboBox Grid.Column="5"
                                          ItemsSource="{Binding SearchCategories}"
                                          SelectedItem="{Binding SearchCategory}"
                                          DisplayMemberPath="Name"
                                          Style="{StaticResource ModernComboBox}"
                                          Margin="0,0,10,0">
                                        <ComboBox.ItemContainerStyle>
                                                <Style TargetType="ComboBoxItem">
                                                        <Setter Property="FlowDirection"
                                                                Value="RightToLeft"/>
                                                        <Setter Property="HorizontalContentAlignment"
                                                                Value="Right"/>
                                                </Style>
                                        </ComboBox.ItemContainerStyle>
                                </ComboBox>

                                <Button Grid.Column="6"
                                        Content="بحث"
                                        Command="{Binding SearchCommand}"
                                        Style="{StaticResource ModernButton}"
                                        Width="80"/>
                        </Grid>
                </Border>

                <!-- Customers List -->
                <Border Grid.Row="2"
                        Style="{StaticResource ModernBorder}"
                        Margin="10,0,10,10">
                        <Grid>
                                <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="*"/>
                                        <RowDefinition Height="Auto"/>
                                </Grid.RowDefinitions>

                                <TextBlock Text="قائمة العملاء"
                                           FontSize="16"
                                           FontWeight="SemiBold"
                                           Margin="0,0,0,10"/>

                                <DataGrid Grid.Row="1"
                                          ItemsSource="{Binding Customers}"
                                          SelectedItem="{Binding SelectedCustomer}"
                                          AutoGenerateColumns="False"
                                          IsReadOnly="True"
                                          SelectionMode="Single"
                                          SelectionUnit="FullRow"
                                          GridLinesVisibility="None"
                                          RowHeight="40"
                                          BorderThickness="1"
                                          BorderBrush="#DDDDDD"
                                          Background="White"
                                          AlternatingRowBackground="#F8F9FA">
                                        <DataGrid.RowStyle>
                                                <Style TargetType="DataGridRow">
                                                        <Setter Property="BorderThickness"
                                                                Value="0"/>
                                                        <Setter Property="Margin"
                                                                Value="0"/>
                                                        <Setter Property="Padding"
                                                                Value="0"/>
                                                        <Style.Triggers>
                                                                <Trigger Property="IsMouseOver"
                                                                         Value="True">
                                                                        <Setter Property="Background"
                                                                                Value="#E3F2FD"/>
                                                                </Trigger>
                                                                <Trigger Property="IsSelected"
                                                                         Value="True">
                                                                        <Setter Property="Background"
                                                                                Value="#2196F3"/>
                                                                        <Setter Property="Foreground"
                                                                                Value="White"/>
                                                                </Trigger>
                                                        </Style.Triggers>
                                                </Style>
                                        </DataGrid.RowStyle>
                                        <DataGrid.Columns>
                                                <DataGridTextColumn Header="الاسم"
                                                                    Binding="{Binding FullName}"
                                                                    Width="2*"/>
                                                <DataGridTextColumn Header="رقم الهاتف"
                                                                    Binding="{Binding Phone}"
                                                                    Width="*"/>
                                                <DataGridTextColumn Header="التصنيف"
                                                                    Binding="{Binding CategoryName}"
                                                                    Width="*"/>
                                                <DataGridTextColumn Header="البريد الإلكتروني"
                                                                    Binding="{Binding Email}"
                                                                    Width="2*"/>
                                                <DataGridTextColumn Header="العنوان"
                                                                    Binding="{Binding Address}"
                                                                    Width="2*"/>
                                        </DataGrid.Columns>
                                </DataGrid>

                                <!-- Pagination -->
                                <StackPanel Grid.Row="2"
                                            Orientation="Horizontal"
                                            HorizontalAlignment="Center"
                                            Margin="0,10,0,0">
                                        <Button Content="السابق"
                                                Command="{Binding PreviousPageCommand}"
                                                Style="{StaticResource ModernButton}"
                                                Width="80"
                                                Margin="0,0,10,0"/>
                                        <TextBlock Text="{Binding PaginationInfo}"
                                                   VerticalAlignment="Center"
                                                   FontSize="14"
                                                   Margin="10,0"/>
                                        <Button Content="التالي"
                                                Command="{Binding NextPageCommand}"
                                                Style="{StaticResource ModernButton}"
                                                Width="80"
                                                Margin="10,0,0,0"/>
                                </StackPanel>
                        </Grid>
                </Border>

                <!-- Action Buttons -->
                <Border Grid.Row="3"
                        Style="{StaticResource ModernBorder}"
                        Margin="10,0,10,10">
                        <StackPanel Orientation="Horizontal"
                                    HorizontalAlignment="Center">
                                <Button Content="اختيار العميل"
                                        Command="{Binding SelectCustomerCommand}"
                                        Style="{StaticResource ModernButton}"
                                        Width="120"
                                        Height="40"
                                        Margin="0,0,10,0"/>
                                <Button Content="إلغاء"
                                        Command="{Binding CancelCommand}"
                                        Width="120"
                                        Height="40">
                                        <Button.Style>
                                                <Style TargetType="Button"
                                                       BasedOn="{StaticResource ModernButton}">
                                                        <Setter Property="Background"
                                                                Value="#6C757D"/>
                                                        <Style.Triggers>
                                                                <Trigger Property="IsMouseOver"
                                                                         Value="True">
                                                                        <Setter Property="Background"
                                                                                Value="#5A6268"/>
                                                                </Trigger>
                                                                <Trigger Property="IsPressed"
                                                                         Value="True">
                                                                        <Setter Property="Background"
                                                                                Value="#495057"/>
                                                                </Trigger>
                                                        </Style.Triggers>
                                                </Style>
                                        </Button.Style>
                                </Button>
                        </StackPanel>
                </Border>
        </Grid>
</Window>
