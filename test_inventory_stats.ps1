# Test script to verify Inventory Stats API endpoint
Write-Host "Testing Inventory Stats API endpoint..." -ForegroundColor Green

try {
    # Test the inventory stats endpoint
    $response = Invoke-RestMethod -Uri "http://localhost:5220/api/inventory/stats" -Method GET -ContentType "application/json"

    Write-Host "API Response received successfully!" -ForegroundColor Green
    Write-Host "Response:" -ForegroundColor Yellow
    $response | ConvertTo-Json -Depth 3

    # Test the low stock items endpoint
    Write-Host "`nTesting low stock items endpoint..." -ForegroundColor Cyan
    $lowStockResponse = Invoke-RestMethod -Uri "http://localhost:5220/api/inventory/low-stock" -Method GET -ContentType "application/json"

    Write-Host "Low stock API Response received successfully!" -ForegroundColor Green
    Write-Host "Low Stock Response:" -ForegroundColor Yellow
    $lowStockResponse | ConvertTo-Json -Depth 3

}
catch {
    Write-Host "Error testing API:" -ForegroundColor Red
    Write-Host $_.Exception.Message -ForegroundColor Red
}

Write-Host "`nTest completed!" -ForegroundColor Green
