﻿using ExactCash.Application.Contracts;
using ExactCash.Domain.Entities;
using Microsoft.AspNetCore.Mvc;

namespace ExactCash.API.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class SystemConfigurationController : ControllerBase
    {
        private readonly ISystemConfigurationService _configService;

        // Injecting the service into the controller
        public SystemConfigurationController(ISystemConfigurationService configService)
        {
            _configService = configService;
        }

        // POST: api/SystemConfiguration
        // Creates a new system configuration
        [HttpPost]
        public async Task<IActionResult> CreateSystemConfig([FromBody] SystemConfiguration config)
        {
            try
            {
                await _configService.CreateAsync(config);
                return CreatedAtAction(nameof(GetConfigByKey), new { key = config.SettingName }, config);
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"Internal server error: {ex.Message}");
            }
        }

        // GET: api/SystemConfiguration/{key}
        // Gets a system configuration by key
        [HttpGet("{key}")]
        public async Task<IActionResult> GetConfigByKey(string key)
        {
            try
            {
                var config = await _configService.GetByKeyAsync(key);
                if (config == null)
                {
                    return NotFound();
                }
                return Ok(config);
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"Internal server error: {ex.Message}");
            }
        }

        // GET: api/SystemConfiguration
        // Gets all system configurations
        [HttpGet]
        public async Task<IActionResult> GetAllConfigs()
        {
            try
            {
                var configs = await _configService.GetAllAsync();
                return Ok(configs);
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"Internal server error: {ex.Message}");
            }
        }

        // PUT: api/SystemConfiguration
        // Updates an existing system configuration
        [HttpPut]
        public async Task<IActionResult> UpdateConfig([FromBody] SystemConfiguration config)
        {
            try
            {
                await _configService.UpdateAsync(config);
                return NoContent();
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"Internal server error: {ex.Message}");
            }
        }

        // DELETE: api/SystemConfiguration/{id}
        // Deletes a system configuration by ID
        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteConfig(int id)
        {
            try
            {
                await _configService.DeleteAsync(id);
                return NoContent();
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"Internal server error: {ex.Message}");
            }
        }
    }
}
