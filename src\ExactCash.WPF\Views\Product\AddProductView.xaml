<Window x:Class="ExactCash.WPF.Views.Product.AddProductView"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:ExactCash.WPF.Views.Product"
        mc:Ignorable="d"
        Title="{Binding WindowTitle}"
        Height="750"
        Width="800"
        WindowStartupLocation="CenterOwner"
        WindowStyle="None"
        AllowsTransparency="True"
        Background="Transparent"
        FlowDirection="RightToLeft">

    <Window.Resources>
        <Style x:Key="ModernButton"
               TargetType="Button">
            <Setter Property="Background"
                    Value="#0078D4"/>
            <Setter Property="Foreground"
                    Value="White"/>
            <Setter Property="BorderThickness"
                    Value="0"/>
            <Setter Property="Padding"
                    Value="15,8"/>
            <Setter Property="FontSize"
                    Value="14"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                CornerRadius="4">
                            <ContentPresenter HorizontalAlignment="Center"
                                              VerticalAlignment="Center"/>
                        </Border>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
            <Style.Triggers>
                <Trigger Property="IsMouseOver"
                         Value="True">
                    <Setter Property="Background"
                            Value="#106EBE"/>
                </Trigger>
            </Style.Triggers>
        </Style>

        <Style x:Key="ModernTextBox"
               TargetType="TextBox">
            <Setter Property="Height"
                    Value="35"/>
            <Setter Property="Padding"
                    Value="10,5"/>
            <Setter Property="BorderThickness"
                    Value="1"/>
            <Setter Property="BorderBrush"
                    Value="#CCCCCC"/>
            <Setter Property="Background"
                    Value="White"/>
            <Setter Property="FontSize"
                    Value="14"/>
        </Style>

        <DropShadowEffect x:Key="DropShadowEffect"
                          ShadowDepth="2"
                          Direction="270"
                          Color="Black"
                          Opacity="0.3"
                          BlurRadius="5"/>
    </Window.Resources>

    <Border Background="White"
            CornerRadius="8"
            BorderBrush="#DDDDDD"
            BorderThickness="1"
            Effect="{StaticResource DropShadowEffect}">
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <!-- Header -->
            <Border Grid.Row="0"
                    Background="#0078D4"
                    BorderThickness="0"
                    CornerRadius="8,8,0,0">
                <Grid Margin="20,15">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <TextBlock Text="{Binding WindowTitle}"
                               FontSize="18"
                               FontWeight="SemiBold"
                               Foreground="White"/>

                    <Button Grid.Column="1"
                            Content="✕"
                            Click="CancelButton_Click"
                            Style="{StaticResource NotificationCloseButtonStyle}"/>
                </Grid>
            </Border>

            <!-- Content -->
            <ScrollViewer Grid.Row="1"
                          Margin="20"
                          VerticalScrollBarVisibility="Auto">
                <StackPanel>
                    <!-- Basic Information -->
                    <GroupBox Header="معلومات المنتج الأساسية"
                              Margin="0,0,0,15"
                              Padding="10">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>

                            <!-- Name -->
                            <StackPanel Grid.Column="0"
                                        Grid.Row="0"
                                        Margin="0,0,10,10">
                                <TextBlock Text="اسم المنتج"/>
                                <TextBox x:Name="ProductNameTextBox"
                                         Style="{StaticResource ModernTextBox}"
                                         Text="{Binding Name, UpdateSourceTrigger=PropertyChanged}"
                                         Margin="0,0,0,10"/>
                            </StackPanel>

                            <!-- Barcode -->
                            <StackPanel Grid.Column="1"
                                        Grid.Row="0"
                                        Margin="10,0,0,10">
                                <TextBlock Text="الباركود"/>
                                <TextBox Text="{Binding Barcode, UpdateSourceTrigger=PropertyChanged}"
                                         Style="{StaticResource ModernTextBox}"/>
                            </StackPanel>

                            <!-- SKU -->
                            <StackPanel Grid.Column="0"
                                        Grid.Row="1"
                                        Margin="0,0,10,10">
                                <TextBlock Text="رمز التخزين (SKU)"/>
                                <TextBox Text="{Binding SKU, UpdateSourceTrigger=PropertyChanged}"
                                         Style="{StaticResource ModernTextBox}"/>
                            </StackPanel>

                            <!-- Category -->
                            <StackPanel Grid.Column="1"
                                        Grid.Row="1"
                                        Margin="10,0,0,10">
                                <TextBlock Text="الفئة"/>
                                <ComboBox ItemsSource="{Binding Categories}"
                                          DisplayMemberPath="Name"
                                          SelectedValuePath="Id"
                                          SelectedValue="{Binding CategoryId}"
                                          IsSynchronizedWithCurrentItem="True"
                                          Style="{StaticResource ModernComboBox}"/>
                            </StackPanel>

                            <!-- Brand -->
                            <StackPanel Grid.Column="0"
                                        Grid.Row="2"
                                        Margin="0,0,10,10">
                                <TextBlock Text="العلامة التجارية"/>
                                <ComboBox ItemsSource="{Binding Brands}"
                                          DisplayMemberPath="Name"
                                          SelectedValuePath="Id"
                                          SelectedValue="{Binding BrandId}"
                                          Style="{StaticResource ModernComboBox}"/>
                            </StackPanel>

                            <!-- Default Unit -->
                            <StackPanel Grid.Column="1"
                                        Grid.Row="2"
                                        Margin="10,0,0,10">
                                <TextBlock Text="الوحدة الافتراضية"/>
                                <ComboBox ItemsSource="{Binding Units}"
                                          DisplayMemberPath="Name"
                                          SelectedValuePath="Id"
                                          SelectedValue="{Binding DefaultUnitId}"
                                          Style="{StaticResource ModernComboBox}"/>
                            </StackPanel>

                            <!-- Image Path -->
                            <StackPanel Grid.Column="0"
                                        Grid.Row="3"
                                        Grid.ColumnSpan="2"
                                        Margin="0,0,0,10">
                                <TextBlock Text="صورة المنتج"/>
                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="Auto"/>
                                    </Grid.ColumnDefinitions>
                                    <TextBox Text="{Binding ImagePath, UpdateSourceTrigger=PropertyChanged}"
                                             IsReadOnly="True"
                                             Style="{StaticResource ModernTextBox}"/>
                                    <Button Content="اختيار"
                                            Grid.Column="1"
                                            Width="60"
                                            Height="35"
                                            Margin="5,0,0,0"
                                            Command="{Binding SelectImageCommand}"
                                            Style="{StaticResource SuccessButton}"/>
                                </Grid>
                            </StackPanel>
                        </Grid>
                    </GroupBox>

                    <!-- Pricing Information -->
                    <GroupBox Header="معلومات السعر"
                              Margin="0,0,0,15"
                              Padding="10">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <!-- Cost Price -->
                            <StackPanel Grid.Column="0"
                                        Margin="0,0,10,0">
                                <TextBlock Text="سعر التكلفة"/>
                                <TextBox Text="{Binding CostPrice, UpdateSourceTrigger=PropertyChanged}"
                                         Style="{StaticResource ModernTextBox}"/>
                            </StackPanel>

                            <!-- Selling Price -->
                            <StackPanel Grid.Column="1"
                                        Margin="5,0,5,0">
                                <TextBlock Text="سعر البيع"/>
                                <TextBox Text="{Binding SellingPrice, UpdateSourceTrigger=PropertyChanged}"
                                         Style="{StaticResource ModernTextBox}"/>
                            </StackPanel>

                            <!-- Quantity -->
                            <StackPanel Grid.Column="2"
                                        Margin="5,0,5,0">
                                <TextBlock Text="الكمية"/>
                                <TextBox Text="{Binding StockQuantity, UpdateSourceTrigger=PropertyChanged}"
                                         Style="{StaticResource ModernTextBox}"/>
                            </StackPanel>

                            <!-- Discount -->
                            <StackPanel Grid.Column="3"
                                        Margin="10,0,0,0">
                                <TextBlock Text="الخصم"/>
                                <TextBox Text="{Binding Discount, UpdateSourceTrigger=PropertyChanged}"
                                         Style="{StaticResource ModernTextBox}"/>
                            </StackPanel>
                        </Grid>
                    </GroupBox>

                    <!-- Description -->
                    <GroupBox Header="الوصف"
                              Margin="0,0,0,15"
                              Padding="10">
                        <TextBox Text="{Binding Description, UpdateSourceTrigger=PropertyChanged}"
                                 Style="{StaticResource ModernTextBox}"
                                 Height="100"
                                 TextWrapping="Wrap"
                                 AcceptsReturn="True"
                                 VerticalScrollBarVisibility="Auto"/>
                    </GroupBox>
                </StackPanel>
            </ScrollViewer>

            <!-- Footer -->
            <Border Grid.Row="2"
                    Background="#F5F5F5"
                    BorderThickness="0"
                    CornerRadius="0,0,8,8">
                <StackPanel Orientation="Horizontal"
                            HorizontalAlignment="Left"
                            Margin="20,15">

                    <Button Content="{Binding SaveButtonText}"
                            Style="{StaticResource ModernButton}"
                            Command="{Binding SaveCommand}"
                            MinWidth="120"
                            Height="40"
                            FontSize="16"/>

                    <Button Content="إلغاء"
                            Style="{StaticResource ModernButton}"
                            Background="#6C757D"
                            Margin="10,0,0,0"
                            MinWidth="120"
                            Height="40"
                            FontSize="16"
                            Click="CancelButton_Click"/>

                </StackPanel>
            </Border>
        </Grid>
    </Border>
</Window> 