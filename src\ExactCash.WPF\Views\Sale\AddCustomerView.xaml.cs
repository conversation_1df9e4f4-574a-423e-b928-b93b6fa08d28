using ExactCash.Application.DTOs;
using ExactCash.WPF.Services;
using ExactCash.WPF.ViewModels.Sale;
using System.Windows;
using Microsoft.Extensions.DependencyInjection;

namespace ExactCash.WPF.Views.Sale
{
    public partial class AddCustomerView : Window
    {
        public AddCustomerView()
        {
            InitializeComponent();
            var customerService = ((App)System.Windows.Application.Current).ServiceProvider.GetRequiredService<ICustomerService>();
            DataContext = new AddCustomerViewModel(customerService);
            ((AddCustomerViewModel)DataContext).CloseWindow = () => DialogResult = true;
        }

        public AddCustomerView(CustomerDto customer)
        {
            InitializeComponent();
            var customerService = ((App)System.Windows.Application.Current).ServiceProvider.GetRequiredService<ICustomerService>();
            DataContext = new AddCustomerViewModel(customerService, customer);
            ((AddCustomerViewModel)DataContext).CloseWindow = () => DialogResult = true;
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }
    }
}