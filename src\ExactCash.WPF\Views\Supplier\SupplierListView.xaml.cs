using AutoMapper;
using ExactCash.WPF.Services;
using ExactCash.WPF.ViewModels.Supplier;
using System.Windows;

namespace ExactCash.WPF.Views.Supplier
{
    public partial class SupplierListView : Window
    {
        public SupplierListView(ISupplierServiceClient supplierServiceClient, ISupplierCategoryServiceClient supplierCategoryServiceClient, IMapper mapper)
        {
            InitializeComponent();
            DataContext = new SupplierListViewModel(
                supplierServiceClient,
                supplierCategoryServiceClient,
                mapper);
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            Close();
        }

        private void MinimizeButton_Click(object sender, RoutedEventArgs e)
        {
            WindowState = WindowState.Minimized;
        }
    }
}