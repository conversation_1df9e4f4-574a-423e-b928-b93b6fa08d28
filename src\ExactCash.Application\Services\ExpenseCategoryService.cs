﻿using ExactCash.Application.DTOs;
using ExactCash.Application.Interfaces;
using ExactCash.Domain.Entities;
using ExactCash.Infrastructure.Persistence;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

public class ExpenseCategoryService : IExpenseCategoryService
{
    private readonly AppPostgreSQLDbContext _context;
    private readonly ILogger<ExpenseCategoryService> _logger;

    public ExpenseCategoryService(AppPostgreSQLDbContext context, ILogger<ExpenseCategoryService> logger)
    {
        _context = context;
        _logger = logger;
    }

    public async Task<ExpenseCategoryDto> CreateAsync(ExpenseCategoryDto dto)
    {
        try
        {
            var entity = new ExpenseCategory
            {
                Name = dto.Name,
                Description = dto.Description,
                CreationDate = DateTime.UtcNow,
                LastUpdatedDate = DateTime.UtcNow
            };

            _context.ExpenseCategories.Add(entity);
            await _context.SaveChangesAsync();

            dto.Id = entity.Id;
            return dto;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating ExpenseCategory: {@Dto}", dto);
            return null;
        }
    }

    public async Task<bool> DeleteAsync(int id)
    {
        try
        {
            var entity = await _context.ExpenseCategories.FirstOrDefaultAsync(x => x.Id == id && !x.IsDeleted);
            if (entity == null)
            {
                _logger.LogWarning("Delete failed: ExpenseCategory with Id {Id} not found.", id);
                return false;
            }

            entity.IsDeleted = true;
            _context.ExpenseCategories.Update(entity);
            await _context.SaveChangesAsync();
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting ExpenseCategory with Id {Id}", id);
            return false;
        }
    }

    public async Task<IEnumerable<ExpenseCategoryDto>> GetAllAsync()
    {
        try
        {
            return await _context.ExpenseCategories
                .Where(x => !x.IsDeleted)
                .Select(x => new ExpenseCategoryDto
                {
                    Id = x.Id,
                    Name = x.Name,
                    Description = x.Description
                })
                .ToListAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving all ExpenseCategories.");
            return Enumerable.Empty<ExpenseCategoryDto>();
        }
    }

    public async Task<ExpenseCategoryDto> GetByIdAsync(int id)
    {
        try
        {
            var entity = await _context.ExpenseCategories
                .FirstOrDefaultAsync(x => x.Id == id && !x.IsDeleted);

            if (entity == null)
            {
                _logger.LogWarning("GetById failed: ExpenseCategory with Id {Id} not found.", id);
                return null;
            }

            return new ExpenseCategoryDto
            {
                Id = entity.Id,
                Name = entity.Name,
                Description = entity.Description
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving ExpenseCategory with Id {Id}", id);
            return null;
        }
    }

    public async Task<bool> UpdateAsync(ExpenseCategoryDto dto)
    {
        try
        {
            var entity = await _context.ExpenseCategories.FirstOrDefaultAsync(x => x.Id == dto.Id && !x.IsDeleted);
            if (entity == null)
            {
                _logger.LogWarning("Update failed: ExpenseCategory with Id {Id} not found.", dto.Id);
                return false;
            }

            entity.Name = dto.Name;
            entity.Description = dto.Description;
            entity.LastUpdatedDate = DateTime.UtcNow;
            _context.ExpenseCategories.Update(entity);
            await _context.SaveChangesAsync();
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating ExpenseCategory: {@Dto}", dto);
            return false;
        }
    }
}
