using ExactCash.Application.DTOs;
using ExactCash.Application.DTOs.Common;
using ExactCash.Application.Interfaces;
using ExactCash.Application.Responses;
using Microsoft.AspNetCore.Mvc;

namespace ExactCash.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class DiscountsController : ControllerBase
    {
        private readonly IDiscountService _discountService;

        public DiscountsController(IDiscountService discountService)
        {
            _discountService = discountService;
        }

        [HttpGet]
        public async Task<ActionResult<PagedResponse<DiscountDto>>> GetAllDiscounts(
            [FromQuery] string name,
            [FromQuery] PaginationFilter pagination)
        {
            var discounts = await _discountService.GetAllDiscountsAsync(name, pagination);
            return Ok(discounts);
        }

        [HttpGet("{id}")]
        public async Task<ActionResult<DiscountDto>> GetDiscount(int id)
        {
            var discount = await _discountService.GetDiscountByIdAsync(id);
            if (discount == null)
                return NotFound();

            return Ok(discount);
        }

        [HttpPost]
        public async Task<ActionResult<DiscountDto>> CreateDiscount(DiscountDto discountDto)
        {
            var createdDiscount = await _discountService.CreateDiscountAsync(discountDto);
            return CreatedAtAction(nameof(GetDiscount), new { id = createdDiscount.Id }, createdDiscount);
        }

        [HttpPut("{id}")]
        public async Task<IActionResult> UpdateDiscount(int id, DiscountDto discountDto)
        {
            if (id != discountDto.Id)
                return BadRequest();

            try
            {
                await _discountService.UpdateDiscountAsync(discountDto);
                return NoContent();
            }
            catch (KeyNotFoundException)
            {
                return NotFound();
            }
        }

        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteDiscount(int id)
        {
            try
            {
                await _discountService.DeleteDiscountAsync(id);
                return NoContent();
            }
            catch (KeyNotFoundException)
            {
                return NotFound();
            }
        }
    }
} 