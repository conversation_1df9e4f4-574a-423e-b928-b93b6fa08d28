using System;
using System.Text.Json.Serialization;
using ExactCash.WASM.Application.DTOs.Common;
using ExactCash.Domain.Entities;
#nullable disable

namespace ExactCash.WASM.Application.DTOs
{
    public class CustomerDto : BaseEntityDto
    {
        #region Props.
        /// <summary>
        /// The full name of the customer.
        /// </summary>
        public string FullName { get; set; }

        /// <summary>
        /// The phone number of the customer for contact purposes.
        /// </summary>
        public string Phone { get; set; }

        /// <summary>
        /// The email address of the customer.
        /// </summary>
        public string Email { get; set; }

        /// <summary>
        /// The physical address of the customer.
        /// </summary>
        public string Address { get; set; }

        /// <summary>
        /// The total amount of money spent by the customer.
        /// </summary>
        public decimal TotalSpent { get; set; }

        /// <summary>
        /// The category ID of the customer.
        /// </summary>
        public int? CategoryId { get; set; }

        /// <summary>
        /// The category name of the customer.
        /// </summary>
        public string CategoryName { get; set; }

        /// <summary>
        /// The maximum credit limit allowed for this customer.
        /// </summary>
        public decimal CreditLimit { get; set; }

        /// <summary>
        /// The current outstanding credit balance for this customer.
        /// </summary>
        public decimal OutstandingBalance { get; set; }

        /// <summary>
        /// Navigation property for the customer's sales.
        /// </summary>
        [JsonIgnore]
        public ICollection<SaleDto> Sales { get; set; } = new List<SaleDto>();
        #endregion

        #region ctor
        public CustomerDto()
        {

        }

        public CustomerDto(string fullName, string phone, string email, string address)
        {
            FullName = fullName;
            Phone = phone;
            Email = email;
            Address = address;
            CreationDate = DateTime.UtcNow;
        }

        public CustomerDto(string fullName, string phone, string email, string address, decimal totalSpent)
        {
            FullName = fullName;
            Phone = phone;
            Email = email;
            Address = address;
            TotalSpent = totalSpent;
            CreationDate = DateTime.UtcNow;
        }
        #endregion
    }
}