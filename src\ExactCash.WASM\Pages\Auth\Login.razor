﻿@page "/login"
@using ExactCash.WASM.Identity
@inject IAccountManagement AccountManagement
@inject NavigationManager Navigation




<div class="auth-form-header">
    <h2>Get Connected!</h2>
    <p>Start supporting your customers</p>
</div>


<div class="auth-form">
    <div class="form-group">
        <label for="txtEmail">Email</label>
        <input @bind-value=@txtEmail type="email" class="form-control" id="txtEmail"
               placeholder="Enter your email">
    </div>

    <div class="form-group">
        <label for="txtPassword">Password</label>
        <div class="password-input">
            <input @bind-value=@txtPassword type="@(password ? "password" : "text")"
                   class="form-control" id="txtPassword" placeholder="Enter your password">
            <button class="toggle-password" @onclick="TogglePassword">
                <i class="bi @(password ? "bi-eye" : "bi-eye-slash")"></i>
            </button>
        </div>
        <div class="col-12 @(errorList.Any() ? string.Empty : "d-none")">
            <div class="alert alert-danger">
                @foreach (var error in errorList)
                {
                    <span>@error</span>
                }
            </div>
        </div>
    </div>

    <button class="btn-primary" @onclick="LoginUser" disabled="@isLoading">
        @if (isLoading)
        {
            <span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
            <span>Signing in...</span>
        }
        else
        {
            <i class="bi bi-box-arrow-in-right"></i>
            <span>Sign In</span>
        }
    </button>
</div>




@code {
    private string txtEmail = string.Empty;
    private string txtPassword = string.Empty;
    private bool isLoading = false;
    private bool password = true;
    private bool isDisposed;
    private string[] errorList = [];

    private async Task LoginUser()
    {
        isLoading = true;

        var result = await AccountManagement.LoginAsync(new Models.LoginModel { Email = txtEmail, Password = txtPassword });

        if (result.Succeeded)
        {
            Navigation.NavigateTo("/");
        }
        else
        {
            errorList = result.ErrorList;
        }

        isLoading = false;
    }

    private void TogglePassword()
    {
        if (!isDisposed)
        {
            password = !password;
            InvokeAsync(StateHasChanged);
        }
    }
}
