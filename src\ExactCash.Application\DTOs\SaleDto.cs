using ExactCash.Domain.Common;
using ExactCash.Domain.Entities;
#nullable disable

namespace ExactCash.Application.DTOs
{
    /// <summary>
    /// Represents a sale transaction in the POS system.
    /// </summary>
    public class SaleDto : BaseEntity
    {
        /// <summary>
        /// The ID of the customer making the purchase.
        /// </summary>
        public int CustomerId { get; set; }

        /// <summary>
        /// Navigation property to the customer making the sale.
        /// </summary>
        public CustomerDto Customer { get; set; }

        /// <summary>
        /// CustomerName
        /// </summary>
        public string CustomerName => Customer?.FullName;

        /// <summary>
        /// The date the sale was made.
        /// </summary>
        public DateTime SaleDate { get; set; }

        /// <summary>
        /// The unique invoice number for this sale.
        /// </summary>
        public string InvoiceNumber { get; set; }

        /// <summary>
        /// Type of invoice (e.g., Sale, Return).
        /// </summary>
        public string InvoiceType { get; set; }

        /// <summary>
        /// Total discount applied to the entire sale.
        /// </summary>
        public decimal DiscountAmount { get; set; }

        /// <summary>
        /// Total tax applied to the entire sale.
        /// </summary>
        public decimal TaxAmount { get; set; }

        /// <summary>
        /// Net amount before tax (i.e., after discount).
        /// </summary>
        public decimal NetAmount { get; set; }

        /// <summary>
        /// Total amount after discount and tax.
        /// </summary>
        public decimal TotalAmount { get; set; }

        /// <summary>
        /// Amount actually paid by the customer.
        /// </summary>
        public decimal PaidAmount { get; set; }

        /// <summary>
        /// Remaining amount to be paid (used for partial payments or credit).
        /// </summary>
        public decimal RemainingAmount { get; set; }

        /// <summary>
        /// Notes
        /// </summary>
        public string Notes { get; set; }

        /// <summary>
        /// Navigation property for items sold in the sale.
        /// </summary>
        public ICollection<SaleItemDto> SaleItems { get; set; } = new List<SaleItemDto>();

        /// <summary>
        /// Navigation property for payments made by the customer.
        /// </summary>
        public ICollection<PaymentDto> Payments { get; set; } = new List<PaymentDto>();

        public SaleDto() { } // Important: default constructor
    }

    public class SaleItemDto
    {
        /// <summary>
        /// The unique identifier for this sale item.
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// Foreign key to the sale this item belongs to.
        /// </summary>
        public int SaleId { get; set; }

        /// <summary>
        /// The name of the product.
        /// </summary>
        public string ProductName { get; set; }

        public ProductDto Product { get; set; }
        /// <summary>
        /// Foreign key to the product being sold.
        /// </summary>
        public int ProductId { get; set; }

        public string BrandName { get; set; }

        public int BrandId { get; set; }


        public BrandDto Brand { get; set; }

        /// <summary>
        /// Unit of measure for this item in the sale (can be different from the product's unit)
        /// </summary>
        public int? UnitId { get; set; }
        /// <summary>
        /// Navigation property to the untit of measure for this item.
        /// </summary>
        public UnitDto Unit { get; set; }

        /// <summary>
        /// Quantity of the product sold.
        /// </summary>
        public decimal Quantity { get; set; }

        /// <summary>
        /// Selling price of a single unit of the product.
        /// </summary>
        public decimal Price { get; set; }

        /// <summary>
        /// TotalPrice
        /// </summary>
        public decimal TotalPrice => Quantity * Price;

        /// <summary>
        /// Discount applied to this item.
        /// </summary>
        public decimal Discount { get; set; }
    }
} 