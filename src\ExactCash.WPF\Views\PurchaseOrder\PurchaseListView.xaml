<Window x:Name="PurchaseListScreen"
        x:Class="ExactCash.WPF.Views.PurchaseOrder.PurchaseListView"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:ExactCash.WPF.Views.PurchaseOrder"
        mc:Ignorable="d"
        Title="قائمة طلبات الشراء"
        WindowState="Maximized"
        WindowStartupLocation="CenterOwner"
        ResizeMode="CanResize"
        Background="#F8F9FA"
        FlowDirection="RightToLeft"
        FontFamily="Segoe UI">

    <Window.Resources>
        <Style x:Key="ModernBorder"
                       TargetType="Border">
            <Setter Property="Background"
                                Value="White"/>
            <Setter Property="BorderBrush"
                                Value="#DEE2E6"/>
            <Setter Property="BorderThickness"
                                Value="1"/>
            <Setter Property="CornerRadius"
                                Value="6"/>
            <Setter Property="Padding"
                                Value="15"/>
        </Style>

        <Style x:Key="ModernButton"
                       TargetType="Button">
            <Setter Property="Height"
                                Value="35"/>
            <Setter Property="Padding"
                                Value="15,5"/>
            <Setter Property="Background"
                                Value="#0078D4"/>
            <Setter Property="Foreground"
                                Value="White"/>
            <Setter Property="BorderThickness"
                                Value="0"/>
            <Setter Property="Cursor"
                                Value="Hand"/>
            <Setter Property="FontSize"
                                Value="14"/>
        </Style>

        <Style x:Key="ModernTextBox"
                       TargetType="TextBox">
            <Setter Property="Height"
                                Value="35"/>
            <Setter Property="Padding"
                                Value="10,5"/>
            <Setter Property="BorderThickness"
                                Value="1"/>
            <Setter Property="BorderBrush"
                                Value="#CCCCCC"/>
            <Setter Property="Background"
                                Value="White"/>
            <Setter Property="FontSize"
                                Value="14"/>
            <Setter Property="TextAlignment"
                                Value="Right"/>
        </Style>

        <Style x:Key="ModernComboBox"
                       TargetType="ComboBox">
            <Setter Property="Height"
                                Value="35"/>
            <Setter Property="Padding"
                                Value="10,5"/>
            <Setter Property="BorderThickness"
                                Value="1"/>
            <Setter Property="BorderBrush"
                                Value="#CCCCCC"/>
            <Setter Property="Background"
                                Value="White"/>
            <Setter Property="FontSize"
                                Value="14"/>
            <Setter Property="HorizontalContentAlignment"
                                Value="Left"/>
        </Style>

        <Style x:Key="ModernDatePicker"
                       TargetType="DatePicker">
            <Setter Property="Height"
                                Value="35"/>
            <Setter Property="Padding"
                                Value="10,5"/>
            <Setter Property="BorderThickness"
                                Value="1"/>
            <Setter Property="BorderBrush"
                                Value="#CCCCCC"/>
            <Setter Property="Background"
                                Value="White"/>
            <Setter Property="FontSize"
                                Value="14"/>
        </Style>

        <Style x:Key="DataGridHeaderStyle"
                       TargetType="DataGridColumnHeader">
            <Setter Property="Background"
                                Value="#F8F9FA"/>
            <Setter Property="BorderThickness"
                                Value="0,0,0,1"/>
            <Setter Property="BorderBrush"
                                Value="#DEE2E6"/>
            <Setter Property="Padding"
                                Value="10"/>
            <Setter Property="FontWeight"
                                Value="Bold"/>
            <Setter Property="HorizontalContentAlignment"
                                Value="Left"/>
        </Style>

        <Style x:Key="DataGridCellStyle"
                       TargetType="DataGridCell">
            <Setter Property="Padding"
                                Value="10"/>
            <Setter Property="HorizontalContentAlignment"
                                Value="Right"/>
        </Style>

        <Style x:Key="DataGridRowStyle"
                       TargetType="DataGridRow">
            <Setter Property="Background"
                                Value="White"/>
            <Setter Property="BorderThickness"
                                Value="0"/>
            <Setter Property="SnapsToDevicePixels"
                                Value="True"/>
            <Style.Triggers>
                <Trigger Property="ItemsControl.AlternationIndex"
                                         Value="1">
                    <Setter Property="Background"
                                                Value="#F5F5F5"/>
                </Trigger>
                <Trigger Property="IsMouseOver"
                                         Value="True">
                    <Setter Property="Background"
                                                Value="#E8E8E8"/>
                </Trigger>
                <Trigger Property="IsSelected"
                                         Value="True">
                    <Setter Property="Background"
                                                Value="#D1E7FD"/>
                    <Setter Property="Foreground"
                                                Value="#000"/>
                </Trigger>
            </Style.Triggers>
        </Style>
    </Window.Resources>

    <Border BorderThickness="1"
                BorderBrush="#dee2e6"
                Background="White"
                CornerRadius="6">
        <DockPanel>
            <!-- Header -->
            <Border DockPanel.Dock="Top"
                                Background="#0078D4"
                                Height="60"
                                BorderThickness="0,0,0,1"
                                BorderBrush="White">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <StackPanel Grid.Column="0"
                                                    Orientation="Horizontal"
                                                    Margin="20,0,0,0">
                        <Button x:Name="MinimizeButton"
                                                        Click="MinimizeButton_Click"
                                                        Style="{StaticResource ModernButton}"
                                                        Background="Transparent"
                                                        BorderThickness="0"
                                                        Width="30"
                                                        Height="30"
                                                        Margin="0,0,5,0">
                            <TextBlock Text="─"
                                                                   FontSize="16"/>
                        </Button>
                        <Button x:Name="CloseButton"
                                                        Click="CloseButton_Click"
                                                        Style="{StaticResource ModernButton}"
                                                        Background="Transparent"
                                                        BorderThickness="0"
                                                        Width="30"
                                                        Height="30">
                            <TextBlock Text="✕"
                                                                   FontSize="16"/>
                        </Button>
                    </StackPanel>

                    <TextBlock Grid.Column="2"
                                                   Text="قائمة طلبات الشراء"
                                                   Foreground="White"
                                                   FontSize="20"
                                                   FontWeight="Bold"
                                                   VerticalAlignment="Center"
                                                   HorizontalAlignment="Right"
                                                   Margin="0,0,20,0"/>
                </Grid>
            </Border>

            <!-- Main Content -->
            <Grid Margin="20">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <!-- Add New Button -->
                <Button Grid.Row="0"
                                        Command="{Binding AddNewPurchaseOrderCommand}"
                                        Style="{StaticResource ModernButton}"
                                        Margin="0,0,161,10"
                                        HorizontalAlignment="Right" RenderTransformOrigin="1.09,0.568">
                    طلب شراء جديد
                </Button>
                <Button Grid.Row="0"
                                        Command="{Binding ExportToExcelCommand}"
                                        Style="{StaticResource ModernButton}"
                                        Margin="0,0,21,10"
                                        HorizontalAlignment="Right">
                    تصدير إلى Excel
                </Button>

                <!-- Search Panel -->
                <Border Grid.Row="1"
                                        Style="{StaticResource ModernBorder}"
                                        Margin="0,0,0,10">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>

                        <!-- Row 1 -->
                        <TextBlock Grid.Row="0"
                                                           Grid.Column="0"
                                                           Text="رقم الطلب:"
                                                           VerticalAlignment="Center"
                                                           Margin="0,0,10,0"/>
                        <TextBox Grid.Row="0"
                                                         Grid.Column="1"
                                                         Text="{Binding SearchOrderNumber}"
                                                         Style="{StaticResource ModernTextBox}"
                                                         Margin="0,0,10,0"/>

                        <TextBlock Grid.Row="0"
                                                           Grid.Column="2"
                                                           Text="المورد:"
                                                           VerticalAlignment="Center"
                                                           Margin="10,0,10,0"/>
                        <ComboBox Grid.Row="0"
                                                          Grid.Column="3"
                                                          ItemsSource="{Binding Suppliers}"
                                                          DisplayMemberPath="Name"
                                                          SelectedValuePath="Id"
                                                          SelectedValue="{Binding SelectedSupplierId}"
                                                          Style="{StaticResource ModernComboBox}"
                                                          Margin="0,0,10,0"/>

                        <TextBlock Grid.Row="0"
                                                           Grid.Column="4"
                                                           Text="من تاريخ:"
                                                           VerticalAlignment="Center"
                                                           Margin="10,0,10,0"/>
                        <DatePicker Grid.Row="0"
                                                            Grid.Column="5"
                                                            SelectedDate="{Binding StartDate}"
                                                            Style="{StaticResource ModernDatePicker}"
                                                            Margin="0,0,10,0"/>

                        <TextBlock Grid.Row="0"
                                                           Grid.Column="6"
                                                           Text="إلى تاريخ:"
                                                           VerticalAlignment="Center"
                                                           Margin="10,0,10,0"/>
                        <DatePicker Grid.Row="0"
                                                            Grid.Column="7"
                                                            SelectedDate="{Binding EndDate}"
                                                            Style="{StaticResource ModernDatePicker}"
                                                            Margin="0,0,10,0"/>

                        <Button Grid.Row="0"
                                                        Grid.Column="8"
                                                        Command="{Binding SearchCommand}"
                                                        Style="{StaticResource ModernButton}"
                                                        Margin="10,0,0,0">بحث</Button>

                    </Grid>
                </Border>
                <!-- Statistics Panel -->
                <Border Grid.Row="2"
                                        Background="#F8F9FA"
                                        BorderThickness="1"
                                        BorderBrush="#DEE2E6"
                                        CornerRadius="4"
                                        Padding="2"
                                        Margin="0,0,0,8">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                            <!-- Add more columns if you want more stats -->
                        </Grid.ColumnDefinitions>

                        <!-- Total Purchase Orders -->
                        <Border Grid.Column="0"
                                                        Background="White"
                                                        BorderThickness="1"
                                                        BorderBrush="#DEE2E6"
                                                        CornerRadius="4"
                                                        Padding="4"
                                                        Margin="2">
                            <StackPanel>
                                <TextBlock Text="إجمالي الطلبات"
                                                                           FontSize="14"
                                                                           Foreground="#666666"/>
                                <TextBlock Text="{Binding TotalPurchaseOrders}"
                                                                           FontSize="24"
                                                                           FontWeight="Bold"
                                                                           Foreground="#0078D4"/>
                            </StackPanel>
                        </Border>

                        <!-- Total Amount -->
                        <Border Grid.Column="1"
                                                        Background="White"
                                                        BorderThickness="1"
                                                        BorderBrush="#DEE2E6"
                                                        CornerRadius="4"
                                                        Padding="4"
                                                        Margin="2">
                            <StackPanel>
                                <TextBlock Text="إجمالي المبلغ"
                                                                           FontSize="14"
                                                                           Foreground="#666666"/>
                                <TextBlock Text="{Binding TotalPurchaseAmount, StringFormat='{}{0:N2} ج.م'}"
                                                                           FontSize="24"
                                                                           FontWeight="Bold"
                                                                           Foreground="#28a745"/>
                            </StackPanel>
                        </Border>


                        <!-- Current Period Orders -->
                        <Border Grid.Column="2"
                                                        Background="White"
                                                        BorderThickness="1"
                                                        BorderBrush="#DEE2E6"
                                                        CornerRadius="4"
                                                        Padding="4"
                                                        Margin="2">
                            <StackPanel>
                                <TextBlock Text="طلبات الفترة الحالية"
                                                                           FontSize="14"
                                                                           Foreground="#666666"/>
                                <TextBlock Text="{Binding CurrentPeriodPurchaseOrders}"
                                                                           FontSize="24"
                                                                           FontWeight="Bold"
                                                                           Foreground="#dc3545"/>
                            </StackPanel>
                        </Border>

                        <!-- Current Period Total Amount -->
                        <Border Grid.Column="3"
                                                        Background="White"
                                                        BorderThickness="1"
                                                        BorderBrush="#DEE2E6"
                                                        CornerRadius="4"
                                                        Padding="4"
                                                        Margin="2">
                            <StackPanel>
                                <TextBlock Text="إجمالي المبلغ للفتره الحاليه"
                                                                           FontSize="14"
                                                                           Foreground="#666666"/>
                                <TextBlock Text="{Binding CurrentPeriodTotalAmount, StringFormat='{}{0:N2} ج.م'}"
                                                                           FontSize="24"
                                                                           FontWeight="Bold"
                                                                           Foreground="#28a745"/>
                            </StackPanel>
                        </Border>
                    </Grid>
                </Border>

                <!-- Purchase Orders Grid -->
                <DataGrid Grid.Row="3"
                                          ItemsSource="{Binding PurchaseOrders}"
                                          AutoGenerateColumns="False"
                                          CanUserAddRows="False"
                                          IsReadOnly="True"
                                          GridLinesVisibility="None"
                                          AlternationCount="2"
                                          RowStyle="{StaticResource DataGridRowStyle}"
                                          ColumnHeaderStyle="{StaticResource DataGridHeaderStyle}"
                                          CellStyle="{StaticResource DataGridCellStyle}">
                    <DataGrid.Columns>
                        <DataGridTextColumn Header="رقم الطلب"
                                                                    Binding="{Binding OrderNumber}"
                                                                    Width="200"/>
                        <DataGridTextColumn Header="المورد"
                                                                    Binding="{Binding SupplierName}"
                                                                    Width="200"/>
                        <DataGridTextColumn Header="تاريخ الطلب"
                                                                    Binding="{Binding CreationDate, StringFormat=yyyy/MM/dd}"
                                                                    Width="200"/>
                        <DataGridTextColumn Header="المبلغ الإجمالي"
                                                                    Binding="{Binding TotalAmount, StringFormat=N2}"
                                                                    Width="200"/>
                        <DataGridTemplateColumn Header="إجراءات"
                                                                        Width="Auto">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <StackPanel Orientation="Horizontal"
                                                                                    HorizontalAlignment="Center">
                                        <Button ToolTip="عرض"
                                                                                        Command="{Binding DataContext.ViewPurchaseOrderCommand, RelativeSource={RelativeSource AncestorType=DataGrid}}"
                                                                                        CommandParameter="{Binding}"
                                                                                        Style="{StaticResource ModernButton}"
                                                                                        Background="#17a2b8"
                                                                                        Width="30"
                                                                                        Height="30"
                                                                                        Padding="0"
                                                                                        Margin="2">
                                            <TextBlock Text="👁️"
                                                                                                   FontSize="14"
                                                                                                   VerticalAlignment="Center"
                                                                                                   HorizontalAlignment="Center"/>
                                        </Button>
                                        <Button ToolTip="طباعة"
                                                                                        Command="{Binding DataContext.PrintPurchaseOrderCommand, RelativeSource={RelativeSource AncestorType=DataGrid}}"
                                                                                        CommandParameter="{Binding}"
                                                                                        Style="{StaticResource ModernButton}"
                                                                                        Background="#6c757d"
                                                                                        Width="30"
                                                                                        Height="30"
                                                                                        Padding="0"
                                                                                        Margin="2">
                                            <TextBlock Text="🖨️"
                                                                                                   FontSize="14"
                                                                                                   VerticalAlignment="Center"
                                                                                                   HorizontalAlignment="Center"/>
                                        </Button>
                                        <Button ToolTip="حذف"
                                                                                        Command="{Binding DataContext.DeletePurchaseOrderCommand, RelativeSource={RelativeSource AncestorType=DataGrid}}"
                                                                                        CommandParameter="{Binding}"
                                                                                        Style="{StaticResource ModernButton}"
                                                                                        Background="#dc3545"
                                                                                        Width="30"
                                                                                        Height="30"
                                                                                        Padding="0"
                                                                                        Margin="2">
                                            <TextBlock Text="🗑️"
                                                                                                   FontSize="14"
                                                                                                   VerticalAlignment="Center"
                                                                                                   HorizontalAlignment="Center"/>
                                        </Button>
                                    </StackPanel>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>
                    </DataGrid.Columns>
                </DataGrid>

                <!-- Pagination Controls -->
                <Border Grid.Row="4"
                                        Margin="0,10,0,0"
                                        Padding="5"
                                        Background="#F8F9FA">
                    <StackPanel Orientation="Horizontal"
                                                    HorizontalAlignment="Center">
                        <Button Content="&lt; السابق"
                                                        Command="{Binding PreviousPageCommand}"
                                                        IsEnabled="{Binding Path=CanExecutePreviousPage}"
                                                        Style="{StaticResource ModernButton}"
                                                        Background="#6C757D"
                                                        Margin="0,0,5,0"/>
                        <TextBlock Text="{Binding PaginationInfo}"
                                                           VerticalAlignment="Center"
                                                           Margin="10,0"/>
                        <Button Content="التالي &gt;"
                                                        Command="{Binding NextPageCommand}"
                                                        IsEnabled="{Binding Path=CanExecuteNextPage}"
                                                        Style="{StaticResource ModernButton}"
                                                        Background="#6C757D"
                                                        Margin="5,0,0,0"/>
                    </StackPanel>
                </Border>

            </Grid>
        </DockPanel>
    </Border>
</Window> 