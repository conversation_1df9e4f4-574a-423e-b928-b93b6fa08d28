using ExactCash.Application.DTOs;
using ExactCash.Application.DTOs.Common;
using ExactCash.Application.Responses;
using ExactCash.WPF.Models;
using ExactCash.WPF.Services;
using System;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Threading.Tasks;
using System.Windows.Input;

namespace ExactCash.WPF.ViewModels.Sale
{
    public class CustomerSelectionViewModel : INotifyPropertyChanged
    {
        private readonly ICustomerService _customerService;
        private readonly ICustomerCategoryServiceClient _customerCategoryService;
        private ObservableCollection<CustomerDto> _customers;
        private ObservableCollection<CustomerCategoryDto> _searchCategories;
        private CustomerDto _selectedCustomer;
        private CustomerCategoryDto _searchCategory;
        private string _nameSearch;
        private string _phoneSearch;
        private int _currentPage = 1;
        private int _pageSize = 20;
        private int _totalPages;
        private int _totalItems;

        public event Action<CustomerDto> CustomerSelected;
        public event Action CancelRequested;
        public event PropertyChangedEventHandler PropertyChanged;

        public ObservableCollection<CustomerDto> Customers
        {
            get => _customers;
            set
            {
                _customers = value;
                OnPropertyChanged();
            }
        }

        public ObservableCollection<CustomerCategoryDto> SearchCategories
        {
            get => _searchCategories;
            set
            {
                _searchCategories = value;
                OnPropertyChanged();
            }
        }

        public CustomerDto SelectedCustomer
        {
            get => _selectedCustomer;
            set
            {
                _selectedCustomer = value;
                OnPropertyChanged();
            }
        }

        public CustomerCategoryDto SearchCategory
        {
            get => _searchCategory;
            set
            {
                _searchCategory = value;
                OnPropertyChanged();
                // Trigger search when category changes
                if (_searchCategory != null)
                {
                    CurrentPage = 1; // Reset to first page
                    ExecuteSearch();
                }
            }
        }

        public string NameSearch
        {
            get => _nameSearch;
            set
            {
                _nameSearch = value;
                OnPropertyChanged();
            }
        }

        public string PhoneSearch
        {
            get => _phoneSearch;
            set
            {
                _phoneSearch = value;
                OnPropertyChanged();
            }
        }

        public int CurrentPage
        {
            get => _currentPage;
            set
            {
                _currentPage = value;
                OnPropertyChanged();
                OnPropertyChanged(nameof(PaginationInfo));
            }
        }

        public int PageSize
        {
            get => _pageSize;
            set
            {
                _pageSize = value;
                OnPropertyChanged();
            }
        }

        public int TotalPages
        {
            get => _totalPages;
            set
            {
                _totalPages = value;
                OnPropertyChanged();
                OnPropertyChanged(nameof(PaginationInfo));
            }
        }

        public int TotalItems
        {
            get => _totalItems;
            set
            {
                _totalItems = value;
                OnPropertyChanged();
                OnPropertyChanged(nameof(PaginationInfo));
            }
        }

        public string PaginationInfo => $"صفحة {CurrentPage} من {TotalPages} (إجمالي العناصر: {TotalItems})";

        public ICommand SearchCommand { get; }
        public ICommand SelectCustomerCommand { get; }
        public ICommand CancelCommand { get; }
        public ICommand PreviousPageCommand { get; }
        public ICommand NextPageCommand { get; }

        public CustomerSelectionViewModel(ICustomerService customerService, ICustomerCategoryServiceClient customerCategoryService)
        {
            _customerService = customerService;
            _customerCategoryService = customerCategoryService;

            Customers = new ObservableCollection<CustomerDto>();
            SearchCategories = new ObservableCollection<CustomerCategoryDto>();

            SearchCommand = new RelayCommand(ExecuteSearch);
            SelectCustomerCommand = new RelayCommand(ExecuteSelectCustomer, CanSelectCustomer);
            CancelCommand = new RelayCommand(ExecuteCancel);
            PreviousPageCommand = new RelayCommand(ExecutePreviousPage, CanExecutePreviousPage);
            NextPageCommand = new RelayCommand(ExecuteNextPage, CanExecuteNextPage);

            // Load initial data
            LoadData();
        }

        private async void LoadData()
        {
            try
            {
                await LoadSearchCategories();
                ExecuteSearch();
            }
            catch (Exception ex)
            {
                Helpers.BootstrapMessageBoxHelper.ShowError($"حدث خطأ أثناء تحميل البيانات: {ex.Message}", "خطأ");
            }
        }

        private async Task LoadSearchCategories()
        {
            try
            {
                var categories = await _customerCategoryService.GetActiveAsync();

                SearchCategories.Clear();
                SearchCategories.Add(new CustomerCategoryDto { Id = 0, Name = "-- جميع التصنيفات --" });

                foreach (var category in categories)
                {
                    SearchCategories.Add(category);
                }

                // Set default selection to "All Categories"
                SearchCategory = SearchCategories.FirstOrDefault();
            }
            catch (Exception ex)
            {
                // If categories fail to load, just show the "All Categories" option
                SearchCategories.Clear();
                SearchCategories.Add(new CustomerCategoryDto { Id = 0, Name = "-- جميع التصنيفات --" });
                SearchCategory = SearchCategories.FirstOrDefault();
            }
        }

        private async void ExecuteSearch()
        {
            try
            {
                var filter = new PaginationFilter
                {
                    PageNumber = CurrentPage,
                    PageSize = PageSize
                };

                // Get category ID for filtering (0 means all categories)
                var categoryId = SearchCategory?.Id == 0 ? (int?)null : SearchCategory?.Id;

                var result = await _customerService.GetAllCustomersAsync(NameSearch, PhoneSearch, categoryId, filter);

                Customers.Clear();
                foreach (var customer in result.Data)
                {
                    Customers.Add(customer);
                }

                TotalPages = result.TotalPages;
                TotalItems = result.TotalItems;
            }
            catch (Exception ex)
            {
                Helpers.BootstrapMessageBoxHelper.ShowError($"حدث خطأ أثناء البحث: {ex.Message}", "خطأ");
            }
        }

        private void ExecuteSelectCustomer()
        {
            if (SelectedCustomer != null)
            {
                CustomerSelected?.Invoke(SelectedCustomer);
            }
        }

        private bool CanSelectCustomer()
        {
            return SelectedCustomer != null;
        }

        private void ExecuteCancel()
        {
            CancelRequested?.Invoke();
        }

        private bool CanExecutePreviousPage()
        {
            return CurrentPage > 1;
        }

        private void ExecutePreviousPage()
        {
            if (CurrentPage > 1)
            {
                CurrentPage--;
                ExecuteSearch();
            }
        }

        private bool CanExecuteNextPage()
        {
            return CurrentPage < TotalPages;
        }

        private void ExecuteNextPage()
        {
            if (CurrentPage < TotalPages)
            {
                CurrentPage++;
                ExecuteSearch();
            }
        }

        protected virtual void OnPropertyChanged([CallerMemberName] string propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }
}
