using ExactCash.WASM.Services.Interfaces;
using Blazored.Toast.Services;

namespace ExactCash.WASM.Services;

public class NotificationService : INotificationService
{
    private readonly IToastService _toastService;

    public NotificationService(IToastService toastService)
    {
        _toastService = toastService;
    }

    public event Action? OnChange;

    public void ShowSuccess(string message)
    {
        _toastService.ShowSuccess(message);
        OnChange?.Invoke();
    }

    public void ShowError(string message)
    {
        _toastService.ShowError(message);
        OnChange?.Invoke();
    }

    public void ShowWarning(string message)
    {
        _toastService.ShowWarning(message);
        OnChange?.Invoke();
    }

    public void ShowInfo(string message)
    {
        _toastService.ShowInfo(message);
        OnChange?.Invoke();
    }

    public void Clear()
    {
        _toastService.ClearAll();
        OnChange?.Invoke();
    }
}
