/* ExactCash WASM Styles */

:root {
    --primary-color: #007bff;
    --secondary-color: #6c757d;
    --success-color: #28a745;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #17a2b8;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
    --border-radius: 0.375rem;
    --box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

/* Base Styles */
html, body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    direction: rtl;
    text-align: right;
}

.page {
    position: relative;
    display: flex;
    flex-direction: column;
    min-height: 100vh;
}

.sidebar {
    background-image: linear-gradient(180deg, rgb(5, 39, 103) 0%, #3a0647 70%);
    width: 260px;
    height: 100vh;
    position: fixed;
    top: 0;
    right: 0;
    z-index: 1000;
}

.top-row {
    background-color: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
    height: 3.5rem;
    display: flex;
    align-items: center;
}

.content {
    margin-right: 260px;
    padding: 1rem;
    flex: 1;
}

/* Navigation Styles */
.navbar-brand {
    font-size: 1.1rem;
    font-weight: bold;
}

.nav-item {
    font-size: 0.9rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.nav-item:last-child {
    border-bottom: none;
}

.nav-item .nav-link {
    color: rgba(255, 255, 255, 0.8);
    border-radius: 0;
    height: 3rem;
    display: flex;
    align-items: center;
    padding: 0.5rem 1rem;
}

.nav-item .nav-link:hover {
    color: #fff;
    background-color: rgba(255, 255, 255, 0.1);
}

.nav-item .nav-link.active {
    color: #fff;
    background-color: rgba(255, 255, 255, 0.2);
}

/* Dashboard Styles */
.dashboard-container {
    padding: 1rem;
}

.stat-card {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    transition: transform 0.2s ease-in-out;
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.stat-icon {
    font-size: 2rem;
    opacity: 0.8;
}

/* Loading Styles */
.loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100vh;
    background-color: #f8f9fa;
}

.loading-spinner {
    width: 3rem;
    height: 3rem;
    border: 0.3rem solid #f3f3f3;
    border-top: 0.3rem solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 1rem;
}

.loading-text {
    font-size: 1.1rem;
    color: var(--secondary-color);
}

.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
}

.loading-spinner-container {
    background-color: white;
    padding: 2rem;
    border-radius: var(--border-radius);
    text-align: center;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Form Styles */
.form-control {
    border-radius: var(--border-radius);
    border: 1px solid #ced4da;
    padding: 0.5rem 0.75rem;
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.btn {
    border-radius: var(--border-radius);
    padding: 0.5rem 1rem;
    font-weight: 500;
}

/* Card Styles */
.card {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
}

.card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
    font-weight: 600;
}

/* Table Styles */
.table {
    margin-bottom: 0;
}

.table th {
    border-top: none;
    font-weight: 600;
    background-color: #f8f9fa;
}

/* Toast Styles */
.success-toast {
    background-color: var(--success-color);
    color: white;
}

.error-toast {
    background-color: var(--danger-color);
    color: white;
}

/* Responsive Design */
@media (max-width: 768px) {
    .sidebar {
        transform: translateX(100%);
        transition: transform 0.3s ease-in-out;
    }
    
    .sidebar.show {
        transform: translateX(0);
    }
    
    .content {
        margin-right: 0;
    }
    
    .navbar-toggler {
        display: block;
    }
}

/* Error UI */
#blazor-error-ui {
    background: lightyellow;
    bottom: 0;
    box-shadow: 0 -1px 2px rgba(0, 0, 0, 0.2);
    display: none;
    left: 0;
    padding: 0.6rem 1.25rem 0.7rem 1.25rem;
    position: fixed;
    width: 100%;
    z-index: 1000;
}

#blazor-error-ui .dismiss {
    cursor: pointer;
    position: absolute;
    right: 0.75rem;
    top: 0.5rem;
}

/* Utility Classes */
.text-primary { color: var(--primary-color) !important; }
.text-success { color: var(--success-color) !important; }
.text-danger { color: var(--danger-color) !important; }
.text-warning { color: var(--warning-color) !important; }
.text-info { color: var(--info-color) !important; }

.bg-primary { background-color: var(--primary-color) !important; }
.bg-success { background-color: var(--success-color) !important; }
.bg-danger { background-color: var(--danger-color) !important; }
.bg-warning { background-color: var(--warning-color) !important; }
.bg-info { background-color: var(--info-color) !important; }
