using Microsoft.AspNetCore.Identity;
using System.Security.Claims;

namespace ExactCash.API.Seeds
{
    public static class DefaultRoleClaims
    {
        public static async Task SeedAsync(RoleManager<IdentityRole> roleManager, ILogger logger)
        {
            try
            {
                var adminRole = await roleManager.FindByNameAsync("Admin");
                if (adminRole == null)
                {
                    logger.LogWarning("Admin role not found. Please ensure the Admin role is created first.");
                    return;
                }

                // Define default claims for Admin role
                var adminClaims = new[]
                {
                    // User Management Claims
                    new Claim("Permission", "Users.View"),
                    new Claim("Permission", "Users.Create"),
                    new Claim("Permission", "Users.Edit"),
                    new Claim("Permission", "Users.Delete"),
                    new Claim("Permission", "Users.ResetPassword"),

                    // Role Management Claims
                    new Claim("Permission", "Roles.View"),
                    new Claim("Permission", "Roles.Create"),
                    new Claim("Permission", "Roles.Edit"),
                    new Claim("Permission", "Roles.Delete"),
                    new Claim("Permission", "Roles.Assign"),

                    // Role Claims Management
                    new Claim("Permission", "RoleClaims.View"),
                    new Claim("Permission", "RoleClaims.Create"),
                    new Claim("Permission", "RoleClaims.Edit"),
                    new Claim("Permission", "RoleClaims.Delete"),

                    // Brand Management Claims
                    new Claim("Permission", "Brands.View"),
                    new Claim("Permission", "Brands.Create"),
                    new Claim("Permission", "Brands.Edit"),
                    new Claim("Permission", "Brands.Delete"),

                    // Category Management Claims
                    new Claim("Permission", "Categories.View"),
                    new Claim("Permission", "Categories.Create"),
                    new Claim("Permission", "Categories.Edit"),
                    new Claim("Permission", "Categories.Delete"),

                    // Product Management Claims
                    new Claim("Permission", "Products.View"),
                    new Claim("Permission", "Products.Create"),
                    new Claim("Permission", "Products.Edit"),
                    new Claim("Permission", "Products.Delete"),

                    // Order Management Claims
                    new Claim("Permission", "Orders.View"),
                    new Claim("Permission", "Orders.Create"),
                    new Claim("Permission", "Orders.Edit"),
                    new Claim("Permission", "Orders.Delete"),
                    new Claim("Permission", "Orders.Process"),

                    // Report Claims
                    new Claim("Permission", "Reports.View"),
                    new Claim("Permission", "Reports.Generate"),
                    new Claim("Permission", "Reports.Export"),

                    // System Settings Claims
                    new Claim("Permission", "Settings.View"),
                    new Claim("Permission", "Settings.Edit")
                };

                // Get existing claims for the Admin role
                var existingClaims = await roleManager.GetClaimsAsync(adminRole);

                // Add claims that don't already exist
                foreach (var claim in adminClaims)
                {
                    if (!existingClaims.Any(c => c.Type == claim.Type && c.Value == claim.Value))
                    {
                        var result = await roleManager.AddClaimAsync(adminRole, claim);
                        if (result.Succeeded)
                        {
                            logger.LogInformation("Added claim {ClaimType}:{ClaimValue} to Admin role",
                                claim.Type, claim.Value);
                        }
                        else
                        {
                            logger.LogError("Failed to add claim {ClaimType}:{ClaimValue} to Admin role: {Errors}",
                                claim.Type, claim.Value,
                                string.Join(", ", result.Errors.Select(e => e.Description)));
                        }
                    }
                }

                logger.LogInformation("Default role claims for Admin role seeded successfully");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error seeding default role claims for Admin role");
            }
        }
    }
}