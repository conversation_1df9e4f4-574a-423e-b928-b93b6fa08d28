using ExactCash.Application.DTOs;
using ExactCash.Application.DTOs.Common;
using ExactCash.Application.Responses;
using ExactCash.Domain.Entities;

namespace ExactCash.WPF.Services
{
    public interface ICustomerService
    {
        Task<Customer> GetCustomerByIdAsync(int id);
        Task<Customer> SearchCustomersAsync(string searchTerm);
        Task<CustomerDto> CreateCustomerAsync(CustomerDto customer);
        Task<BaseResponse<bool>> UpdateCustomerAsync(Customer customer);
        Task<bool> DeleteCustomerAsync(int id);
        Task<PagedResponse<CustomerDto>> GetAllCustomersAsync(string name, string phone, int? categoryId, PaginationFilter pagination);

        Task<List<CustomerDto>> GetAllCustomersAsync();
    }
}