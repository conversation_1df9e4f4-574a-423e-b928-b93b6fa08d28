﻿using ExactCash.Application.Responses;

namespace ExactCash.Application.Responses
{
    /// <summary>
    /// PaginationHelper
    /// </summary>
    public static class PaginationHelper
    {
        /// <summary>
        /// ctor
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="data"></param>
        /// <param name="pageNumber"></param>
        /// <param name="pageSize"></param>
        /// <param name="totalItems"></param>
        /// <returns></returns>
        public static PagedResponse<T> CreatePagedResponse<T>(List<T> data, int pageNumber, int pageSize, int totalItems)
        {
            return new PagedResponse<T>(data, pageNumber, pageSize, totalItems);
        }
    }
}
