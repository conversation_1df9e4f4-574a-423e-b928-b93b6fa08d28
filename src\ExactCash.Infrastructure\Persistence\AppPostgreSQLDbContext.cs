﻿using ExactCash.Domain.Entities;
using Microsoft.AspNetCore.Identity.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;

namespace ExactCash.Infrastructure.Persistence
{
    public class AppPostgreSQLDbContext : IdentityDbContext
    {
        /// <summary>
         // ** Constructor that passes options to the base class (IdentityDbContext) **
        /// </summary>
        /// <param name="options"></param>
        public AppPostgreSQLDbContext(DbContextOptions<AppPostgreSQLDbContext> options) : base(options)
        {

        }

        #region dbSet
        // ** DbSets for custom entities **

        /// <summary>
        /// Represents the products in the inventory.
        /// </summary>
        public DbSet<Product> Products { get; set; }

        /// <summary>
        /// Represents the categories for products.
        /// </summary>
        public DbSet<Category> Categories { get; set; }

        /// <summary>
        /// The unit of product refers to the measurement or quantity in which a product is sold or stocked.
        /// </summary>
        public DbSet<Unit> Units { get; set; }

        /// <summary>
        /// Represents the suppliers who provide products to the store.
        /// </summary>
        public DbSet<Supplier> Suppliers { get; set; }

        /// <summary>
        /// SupplierCategories
        /// </summary>
        public DbSet<SupplierCategory> SupplierCategories { get; set; }

        /// <summary>
        /// Represents the purchase orders made to suppliers.
        /// </summary>
        public DbSet<Purchase> Purchases { get; set; }

        /// <summary>
        /// Represents individual items in a purchase order.
        /// </summary>
        public DbSet<PurchaseItem> PurchaseItems { get; set; }

        /// <summary>
        /// Represents stock movements (entries and exits) in the inventory.
        /// </summary>
        public DbSet<StockMovement> StockMovements { get; set; }

        /// <summary>
        /// Represents customers of the store.
        /// </summary>
        public DbSet<Customer> Customers { get; set; }

        /// <summary>
        /// Represents customer categories.
        /// </summary>
        public DbSet<CustomerCategory> CustomerCategories { get; set; }

        /// <summary>
        /// Represents the sales transactions made to customers.
        /// </summary>
        public DbSet<Sale> Sales { get; set; }

        /// <summary>
        /// Represents individual items in a sales transaction.
        /// </summary>
        public DbSet<SaleItem> SaleItems { get; set; }

        /// <summary>
        /// Represents payments made by customers for sales transactions.
        /// </summary>
        public DbSet<Payment> Payments { get; set; }

        /// <summary>
        /// Represents manual adjustments to inventory (e.g., corrections, stocktaking).
        /// </summary>
        public DbSet<InventoryAdjustment> InventoryAdjustments { get; set; }

        /// <summary>
        /// discounts
        /// </summary>
        public DbSet<Discount> Discounts { get; set; }

        /// <summary>
        /// promotions
        /// </summary>
        public DbSet<Promotion> Promotions { get; set; }

        /// <summary>
        ///  Refer to the global settings or parameters that control how the system behaves or interacts with different components
        /// </summary>
        public DbSet<SystemConfiguration> systemConfigurations { get; set; }

        /// <summary>
        /// Brands
        /// </summary>
        public DbSet<Brand> Brands { get; set; }

        /// <summary>
        /// ExpenseCategories
        /// </summary>
        public DbSet<ExpenseCategory> ExpenseCategories { get; set; }

        /// <summary>
        /// Expenses
        /// </summary>
        public DbSet<Expense> Expenses { get; set; }


        /// <summary>
        /// CreditPayments
        /// </summary>
        public DbSet<CreditPayment> CreditPayments { get; set; }
        /// <summary>
        /// CreditSaleTransactions
        /// </summary>
        public DbSet<CreditSaleTransaction> CreditSaleTransactions { get; set; }

        /// <summary>
        /// CustomerCreditLimits
        /// </summary>
        public DbSet<CustomerCreditLimit> CustomerCreditLimits { get; set; }
        #endregion

        protected override void OnModelCreating(ModelBuilder builder)
        {
            base.OnModelCreating(builder);

            // Configure one-to-many relationship between Product and Category
            builder.Entity<Product>()
                .HasOne(p => p.Category)
                .WithMany(c => c.Products)
                .HasForeignKey(p => p.CategoryId)
                .OnDelete(DeleteBehavior.Restrict);

            // Configure one-to-many relationship between Customer and CustomerCategory
            builder.Entity<Customer>()
                .HasOne(c => c.Category)
                .WithMany(cc => cc.Customers)
                .HasForeignKey(c => c.CategoryId)
                .OnDelete(DeleteBehavior.Restrict);

            // Configure table names for credit sales entities to match migration
            builder.Entity<CreditSaleTransaction>()
                .ToTable("CreditSaleTransaction");

            builder.Entity<CreditPayment>()
                .ToTable("CreditPayment");

            builder.Entity<CustomerCreditLimit>()
                .ToTable("CustomerCreditLimit");

            // Configure relationships for credit sales entities
            builder.Entity<CreditSaleTransaction>()
                .HasOne(cst => cst.Sale)
                .WithOne(s => s.CreditSaleTransaction)
                .HasForeignKey<CreditSaleTransaction>(cst => cst.SaleId)
                .OnDelete(DeleteBehavior.Cascade);

            builder.Entity<CreditSaleTransaction>()
                .HasOne(cst => cst.Customer)
                .WithMany(c => c.CreditSaleTransactions)
                .HasForeignKey(cst => cst.CustomerId)
                .OnDelete(DeleteBehavior.Cascade);

            builder.Entity<CreditPayment>()
                .HasOne(cp => cp.CreditSaleTransaction)
                .WithMany(cst => cst.CreditPayments)
                .HasForeignKey(cp => cp.CreditSaleTransactionId)
                .OnDelete(DeleteBehavior.Cascade);

            builder.Entity<CustomerCreditLimit>()
                .HasOne(ccl => ccl.Customer)
                .WithMany(c => c.CreditLimitHistory)
                .HasForeignKey(ccl => ccl.CustomerId)
                .OnDelete(DeleteBehavior.Cascade);
        }
    }
}
