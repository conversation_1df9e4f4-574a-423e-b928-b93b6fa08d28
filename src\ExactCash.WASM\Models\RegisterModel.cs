﻿using System.ComponentModel.DataAnnotations;

namespace ExactCash.WASM.Models
{
    public class RegisterModel
    {
        [Required, EmailAddress]
        public string Email { get; set; } = string.Empty;

        [Required]
        public string Password { get; set; } = string.Empty;

        [Required, Comp<PERSON>(nameof(Password))]
        public string ConfirmPassword { get; set; } = string.Empty;
    }
}
