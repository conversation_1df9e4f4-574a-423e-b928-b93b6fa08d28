using ExactCash.Application.DTOs;
using ExactCash.Application.DTOs.Common;
using ExactCash.Application.Responses;

namespace ExactCash.Application.Interfaces
{
    public interface IPurchaseService
    {
        /// <summary>
        /// Gets a purchase by its ID.
        /// </summary>
        Task<PurchaseDto> GetPurchaseByIdAsync(int id);

        /// <summary>
        /// Gets all purchases with optional filtering.
        /// </summary>
        Task<PagedResponse<PurchaseDto>> GetAllPurchasesAsync(
            int? supplierId,
            string poNumber,
            DateTime? startDate,
            DateTime? endDate,
            PaginationFilter pagination);

        /// <summary>
        /// Creates a new purchase and updates product stock.
        /// </summary>
        Task<BaseResponse<bool>> CreatePurchaseAsync(PurchaseDto purchaseDto, string createdBy);

        /// <summary>
        /// Updates an existing purchase and adjusts product stock accordingly.
        /// </summary>
        Task UpdatePurchaseAsync(PurchaseDto purchaseDto);

        /// <summary>
        /// Deletes a purchase and restores product stock.
        /// </summary>
        Task DeletePurchaseAsync(int id);

        /// <summary>
        /// Gets the last PO ID from the database.
        /// </summary>
        /// <returns></returns>
        Task<int> GetLastPOIdAsync();

        /// <summary>
        /// Gets purchase order report
        /// </summary>
        Task<List<PurchaseOrderReportDto>> GetPurchaseOrderReport(DateTime? startDate, DateTime? endDate);
    }
}