namespace ExactCash.WASM.Services.Interfaces;

public interface INotificationService
{
    event Action? OnChange;
    
    void ShowSuccess(string message);
    void ShowError(string message);
    void ShowWarning(string message);
    void ShowInfo(string message);
    void Clear();
}

public enum NotificationType
{
    Success,
    Error,
    Warning,
    Info
}

public class NotificationMessage
{
    public string Message { get; set; } = string.Empty;
    public NotificationType Type { get; set; }
    public DateTime Timestamp { get; set; } = DateTime.Now;
    public bool IsVisible { get; set; } = true;
}
