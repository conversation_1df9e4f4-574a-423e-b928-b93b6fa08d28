<UserControl x:Class="ExactCash.WPF.Views.Common.FooterStatusView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:local="clr-namespace:ExactCash.WPF.Views.Common"
             mc:Ignorable="d"
             d:DesignHeight="40"
        d:DesignWidth="800">
    <Grid Background="#F5F5F5">
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="*"/>
            <ColumnDefinition Width="Auto"/>
        </Grid.ColumnDefinitions>

        <!-- Status Message -->
        <TextBlock Grid.Column="0"
                   Text="{Binding StatusMessage}"
                   FontFamily="Droid Arabic Kufi"
                   FontSize="14"
                   Foreground="#333333"
                   Margin="10,0"
                   VerticalAlignment="Center"/>

        <!-- Connection Status -->
        <StackPanel Grid.Column="1"
                    Orientation="Horizontal"
                    Margin="0,0,10,0"
                    VerticalAlignment="Center">
            <Ellipse Width="10"
                     Height="10"
                     Fill="{Binding ConnectionStatusColor}"/>
            <TextBlock Text="{Binding ConnectionStatusText}"
                       FontFamily="Droid Arabic Kufi"
                       FontSize="12"
                       Foreground="#666666"
                       Margin="5,0,0,0"
                       VerticalAlignment="Center"/>
        </StackPanel>
    </Grid>
</UserControl> 