using System.Collections.ObjectModel;
using System.Windows;
using System.Windows.Input;
using System.Windows.Media;
using AutoMapper;
using ExactCash.Application.DTOs;
using ExactCash.Application.DTOs.Common;
using ExactCash.WPF.Commands;
using ExactCash.WPF.Services;
using ExactCash.WPF.Services.Product;
using ExactCash.WPF.ViewModels.Common;
using ExactCash.WPF.Views.PurchaseOrder;
using System.ComponentModel;
using System.Linq;
using ExactCash.Application.Responses;
using Microsoft.Win32;
using System.IO;
using System.Text;

namespace ExactCash.WPF.ViewModels.PurchaseOrder
{
    public class PurchaseListViewModel : ViewModelBase
    {
        private readonly IPurchaseServiceClient _purchaseOrderService;
        private readonly IProductService _productService;
        private readonly ISupplierServiceClient _supplierService;
        private readonly IUnitServiceClient _unitService;
        private readonly IMapper _mapper;
        private readonly NotificationViewModel _notificationViewModel;
        private readonly LoadingViewModel _loadingViewModel;

        private string _searchOrderNumber;
        private int? _selectedSupplierId;
        private DateTime? _startDate = DateTime.Today.AddDays(-30);
        private DateTime? _endDate = DateTime.Today;
        private ObservableCollection<PurchaseDto> _purchaseOrders;
        private ObservableCollection<SupplierDto> _suppliers;
        private bool _isLoading;

        private int _currentPage = 1;
        private int _pageSize = 15;
        private int _totalPages;
        private int _totalItems;

        private int _totalPurchaseOrders;
        public int TotalPurchaseOrders
        {
            get => _totalPurchaseOrders;
            set { _totalPurchaseOrders = value; OnPropertyChanged(); }
        }

        private decimal _totalPurchaseAmount;
        public decimal TotalPurchaseAmount
        {
            get => _totalPurchaseAmount;
            set { _totalPurchaseAmount = value; OnPropertyChanged(); }
        }

        private decimal _averagePurchaseAmount;
        public decimal AveragePurchaseAmount
        {
            get => _averagePurchaseAmount;
            set { _averagePurchaseAmount = value; OnPropertyChanged(); }
        }

        private int _currentPeriodPurchaseOrders;
        public int CurrentPeriodPurchaseOrders
        {
            get => _currentPeriodPurchaseOrders;
            set { _currentPeriodPurchaseOrders = value; OnPropertyChanged(); }
        }

        private decimal _currentPeriodTotalAmount;
        public decimal CurrentPeriodTotalAmount
        {
            get => _currentPeriodTotalAmount;
            set { _currentPeriodTotalAmount = value; OnPropertyChanged(); }
        }

        public PurchaseListViewModel(
            IPurchaseServiceClient purchaseOrderService,
            IProductService productService,
            ISupplierServiceClient supplierService,
            IMapper mapper,
            NotificationViewModel notificationViewModel,
            IUnitServiceClient unitService,
            LoadingViewModel loadingViewModel)
        {
            _purchaseOrderService = purchaseOrderService;
            _productService = productService;
            _supplierService = supplierService;
            _mapper = mapper;
            _notificationViewModel = notificationViewModel;
            _unitService = unitService;
            _loadingViewModel = loadingViewModel;

            PurchaseOrders = new ObservableCollection<PurchaseDto>();
            Suppliers = new ObservableCollection<SupplierDto>();

            SearchCommand = new RelayCommand(async () => await ExecuteSearch(1));
            AddNewPurchaseOrderCommand = new RelayCommand(ExecuteAddNewPurchaseOrder);
            ViewPurchaseOrderCommand = new RelayCommand<PurchaseDto>(ExecuteViewPurchaseOrder);
            NextPageCommand = new RelayCommand(async () => await ExecuteSearch(CurrentPage + 1), CanExecuteNextPage);
            PreviousPageCommand = new RelayCommand(async () => await ExecuteSearch(CurrentPage - 1), CanExecutePreviousPage);
            DeletePurchaseOrderCommand = new RelayCommand<PurchaseDto>(async (purchase) => await ExecuteDeletePurchaseOrder(purchase));
            PrintPurchaseOrderCommand = new RelayCommand<PurchaseDto>(ExecutePrintPurchaseOrder);
            ExportToExcelCommand = new RelayCommand(ExportToExcel);

            LoadSuppliers();
            ExecuteSearch(1);
        }

        public string SearchOrderNumber
        {
            get => _searchOrderNumber;
            set => SetProperty(ref _searchOrderNumber, value);
        }

        public int? SelectedSupplierId
        {
            get => _selectedSupplierId;
            set => SetProperty(ref _selectedSupplierId, value);
        }

        public DateTime? StartDate
        {
            get => _startDate;
            set => SetProperty(ref _startDate, value);
        }

        public DateTime? EndDate
        {
            get => _endDate;
            set => SetProperty(ref _endDate, value);
        }

        public ObservableCollection<PurchaseDto> PurchaseOrders
        {
            get => _purchaseOrders;
            set => SetProperty(ref _purchaseOrders, value);
        }

        public ObservableCollection<SupplierDto> Suppliers
        {
            get => _suppliers;
            set => SetProperty(ref _suppliers, value);
        }

        public bool IsLoading
        {
            get => _isLoading;
            set => SetProperty(ref _isLoading, value);
        }

        public int CurrentPage
        {
            get => _currentPage;
            set
            {
                if (SetProperty(ref _currentPage, value))
                {
                    OnPropertyChanged(nameof(PaginationInfo));
                    ((RelayCommand)NextPageCommand).RaiseCanExecuteChanged();
                    ((RelayCommand)PreviousPageCommand).RaiseCanExecuteChanged();
                }
            }
        }

        public int PageSize
        {
            get => _pageSize;
            set => SetProperty(ref _pageSize, value);
        }

        public int TotalPages
        {
            get => _totalPages;
            set
            {
                if (SetProperty(ref _totalPages, value))
                {
                    OnPropertyChanged(nameof(PaginationInfo));
                    ((RelayCommand)NextPageCommand).RaiseCanExecuteChanged();
                    ((RelayCommand)PreviousPageCommand).RaiseCanExecuteChanged();
                }
            }
        }

        public int TotalItems
        {
            get => _totalItems;
            set
            {
                if (SetProperty(ref _totalItems, value))
                {
                    OnPropertyChanged(nameof(PaginationInfo));
                }
            }
        }

        public string PaginationInfo => $"Page {CurrentPage} of {TotalPages} ({TotalItems} items)";

        public ICommand SearchCommand { get; }
        public ICommand AddNewPurchaseOrderCommand { get; }
        public ICommand ViewPurchaseOrderCommand { get; }
        public ICommand NextPageCommand { get; }
        public ICommand PreviousPageCommand { get; }
        public ICommand DeletePurchaseOrderCommand { get; }
        public ICommand PrintPurchaseOrderCommand { get; }
        public ICommand ExportToExcelCommand { get; }

        private async Task LoadSuppliers()
        {
            try
            {
                var suppliers = await _supplierService.GetAllSuppliersAsync(null, null, new PaginationFilter { PageNumber = 1, PageSize = 1000 });
                if (suppliers != null)
                {
                    Suppliers.Clear();
                    foreach (var supplier in suppliers.Data)
                    {
                        Suppliers.Add(supplier);
                    }
                }
                else
                {
                    _notificationViewModel.ShowError("Failed to load suppliers.");
                }
            }
            catch (Exception ex)
            {
                _notificationViewModel.ShowError($"Error loading suppliers: {ex.Message}");
            }
        }

        private async Task ExecuteSearch(int pageNumber)
        {
            if (pageNumber < 1) pageNumber = 1;

            IsLoading = true;
            //_loadingViewModel.IsLoading = true;
            try
            {
                var paginationFilter = new PaginationFilter { PageNumber = pageNumber, PageSize = PageSize };
                var purchaseOrdersResponse = await _purchaseOrderService.GetAllPurchasesAsync(
                    SelectedSupplierId,
                    SearchOrderNumber,
                    StartDate,
                    EndDate?.AddDays(1).AddTicks(-1),
                    pagination: paginationFilter);

                if (purchaseOrdersResponse != null)
                {
                    PurchaseOrders.Clear();
                    foreach (var purchaseOrder in purchaseOrdersResponse.Data)
                    {
                        PurchaseOrders.Add(purchaseOrder);
                    }
                    CurrentPage = purchaseOrdersResponse.PageNumber;
                    TotalPages = purchaseOrdersResponse.TotalPages;
                    TotalItems = purchaseOrdersResponse.TotalItems;

                    // Calculate statistics
                    CalculateStatistics(purchaseOrdersResponse);
                }
                else
                {
                    PurchaseOrders.Clear();
                    CurrentPage = 1;
                    TotalPages = 0;
                    TotalItems = 0;

                    // Reset statistics
                    TotalPurchaseOrders = 0;
                    TotalPurchaseAmount = 0;
                    AveragePurchaseAmount = 0;
                    CurrentPeriodPurchaseOrders = 0;
                    CurrentPeriodTotalAmount = 0;
                }
            }
            catch (Exception ex)
            {
                Helpers.BootstrapMessageBoxHelper.ShowError($"Search error: {ex.Message}", owner: FindParentWindow());
                PurchaseOrders.Clear();
                CurrentPage = 1;
                TotalPages = 0;
                TotalItems = 0;
                // Reset statistics
                TotalPurchaseOrders = 0;
                TotalPurchaseAmount = 0;
                AveragePurchaseAmount = 0;
                CurrentPeriodPurchaseOrders = 0;
                CurrentPeriodTotalAmount = 0;
            }
            finally
            {
                IsLoading = false;
                //_loadingViewModel.IsLoading = false;
            }
        }

        private bool CanExecuteNextPage()
        {
            return CurrentPage < TotalPages;
        }

        private bool CanExecutePreviousPage()
        {
            return CurrentPage > 1;
        }

        private Window FindParentWindow()
        {
            foreach (Window window in System.Windows.Application.Current.Windows)
            {
                if (window.Content != null &&
                    VisualTreeHelper.GetChild(window.Content as DependencyObject, 0) is FrameworkElement content &&
                    window.Name == "PurchaseListScreen")
                {
                    return window;
                }
            }
            return System.Windows.Application.Current.MainWindow;
        }

        private void ExecuteAddNewPurchaseOrder()
        {
            try
            {
                var purchaseOrderView = new PurchaseOrderView(_purchaseOrderService, _productService, _supplierService, _unitService, _mapper, _notificationViewModel);
                purchaseOrderView.Owner = FindParentWindow();
                purchaseOrderView.ShowDialog();
                ExecuteSearch(CurrentPage);
            }
            catch (Exception ex)
            {
                _notificationViewModel.ShowError($"Error opening new purchase order view: {ex.Message}");
            }
        }

        private async Task ExecuteDeletePurchaseOrder(PurchaseDto purchase)
        {
            if (purchase == null) return;
            var result = System.Windows.MessageBox.Show($"هل أنت متأكد من حذف طلب الشراء رقم {purchase.OrderNumber}؟", "تأكيد الحذف", MessageBoxButton.YesNo, MessageBoxImage.Warning);
            if (result != MessageBoxResult.Yes) return;
            try
            {
                await _purchaseOrderService.DeletePurchaseAsync(purchase.Id);
                _notificationViewModel.ShowSuccess("تم حذف طلب الشراء بنجاح.");
                await ExecuteSearch(CurrentPage); // Refresh the list
            }
            catch (Exception ex)
            {
                _notificationViewModel.ShowError($"حدث خطأ أثناء الحذف: {ex.Message}");
            }
        }

        private void ExecutePrintPurchaseOrder(PurchaseDto purchase)
        {
            if (purchase == null) return;
            // TODO: Implement your print logic here.
            _notificationViewModel.ShowInfo("ميزة الطباعة لم تُنفذ بعد.");
        }

        private async void ExecuteViewPurchaseOrder(PurchaseDto purchaseOrder)
        {
            if (purchaseOrder == null) return;
            try
            {
                var purchaseOrderDb = await _purchaseOrderService.GetPurchaseByIdAsync(purchaseOrder.Id);
                var purchaseOrderView = new PurchaseOrderView(_purchaseOrderService, _productService, _supplierService, _unitService, _mapper, _notificationViewModel);
                purchaseOrderView.Owner = FindParentWindow();
                if (purchaseOrderView.DataContext is PurchaseOrderViewModel viewModel)
                {
                    viewModel.IsReadOnly = true;
                    if (viewModel.GetType().GetMethod("LoadPurchaseOrderData") != null)
                    {
                        viewModel.GetType().GetMethod("LoadPurchaseOrderData")?.Invoke(viewModel, new object[] { purchaseOrderDb });
                    }
                }
                purchaseOrderView.ShowDialog();
            }
            catch (Exception ex)
            {
                _notificationViewModel.ShowError($"Error opening purchase order view: {ex.Message}");
            }
        }

        private void CalculateStatistics(PagedResponse<PurchaseDto> pagedResponse)
        {
            var allOrders = PurchaseOrders ?? new ObservableCollection<PurchaseDto>();
            TotalPurchaseOrders = allOrders.Count;
            TotalPurchaseAmount = allOrders.Sum(po => po.TotalAmount);
            AveragePurchaseAmount = TotalPurchaseOrders > 0 ? TotalPurchaseAmount / TotalPurchaseOrders : 0;
            var thirtyDaysAgo = DateTime.Now.AddDays(-30);
            var currentPeriodOrders = allOrders.Where(po => po.CreationDate >= thirtyDaysAgo).ToList();
            CurrentPeriodPurchaseOrders = pagedResponse.Data.Count;
            CurrentPeriodTotalAmount = pagedResponse.Data.Sum(po => po.TotalAmount);
        }

        private void ExportToExcel()
        {
            var saveFileDialog = new Microsoft.Win32.SaveFileDialog
            {
                Filter = "Excel Files (*.xlsx)|*.xlsx|CSV Files (*.csv)|*.csv",
                FileName = $"طلبات_الشراء_{DateTime.Now:yyyyMMdd_HHmmss}.csv"
            };

            if (saveFileDialog.ShowDialog() == true)
            {
                try
                {
                    using (var writer = new StreamWriter(saveFileDialog.FileName, false, Encoding.UTF8))
                    {
                        // Write header (adjust columns as per your DataGrid)
                        writer.WriteLine("رقم الطلب,المورد,تاريخ الطلب,المبلغ الإجمالي");
                        foreach (var purchase in PurchaseOrders)
                        {
                            writer.WriteLine($"{purchase.OrderNumber},{purchase.SupplierName},{purchase.CreationDate:yyyy-MM-dd},{purchase.TotalAmount}");
                        }
                    }
                    Helpers.BootstrapMessageBoxHelper.Show(owner: FindParentWindow(), message: "تم تصدير البيانات بنجاح!");
                }   
                catch (Exception ex)
                {
                    Helpers.BootstrapMessageBoxHelper.Show(owner: FindParentWindow(), message: $"حدث خطأ أثناء التصدير: {ex.Message}");
                }
            }
        }
    }
}