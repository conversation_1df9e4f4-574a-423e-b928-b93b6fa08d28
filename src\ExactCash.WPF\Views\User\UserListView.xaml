<Window x:Class="ExactCash.WPF.Views.User.UserListView"
        x:Name="UserListScreen"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="إدارة المستخدمين"
        WindowState="Maximized"
        WindowStartupLocation="CenterScreen"
        ResizeMode="CanResize"
        FlowDirection="RightToLeft"
        FontFamily="Segoe UI">
    <Window.Resources>
        <Style x:Key="CloseButtonStyle"
               TargetType="Button">
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="Transparent"
                                BorderThickness="0">
                            <ContentPresenter HorizontalAlignment="Center"
                                              VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver"
                                     Value="True">
                                <Setter Property="Foreground"
                                        Value="#e74c3c"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
        <Style x:Key="ModernButton"
               TargetType="Button">
            <Setter Property="Background"
                    Value="#0078D4"/>
            <Setter Property="Foreground"
                    Value="White"/>
            <Setter Property="BorderThickness"
                    Value="0"/>
            <Setter Property="Padding"
                    Value="15,8"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="4">
                            <ContentPresenter HorizontalAlignment="Center"
                                              VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver"
                                     Value="True">
                                <Setter Property="Background"
                                        Value="#106EBE"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
        <Style x:Key="SuccessButton"
               TargetType="Button">
            <Setter Property="Background"
                    Value="#28a745"/>
            <Setter Property="Foreground"
                    Value="White"/>
            <Setter Property="BorderThickness"
                    Value="0"/>
            <Setter Property="Padding"
                    Value="15,8"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="4">
                            <ContentPresenter HorizontalAlignment="Center"
                                              VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver"
                                     Value="True">
                                <Setter Property="Background"
                                        Value="#218838"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
        <Style x:Key="ModernTextBox"
               TargetType="TextBox">
            <Setter Property="Height"
                    Value="35"/>
            <Setter Property="Padding"
                    Value="8,5"/>
            <Setter Property="Background"
                    Value="White"/>
            <Setter Property="BorderBrush"
                    Value="#ced4da"/>
            <Setter Property="BorderThickness"
                    Value="1"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="TextBox">
                        <Border Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="4">
                            <ScrollViewer x:Name="PART_ContentHost"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver"
                                     Value="True">
                                <Setter Property="BorderBrush"
                                        Value="#80bdff"/>
                            </Trigger>
                            <Trigger Property="IsFocused"
                                     Value="True">
                                <Setter Property="BorderBrush"
                                        Value="#80bdff"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </Window.Resources>
    <Border BorderThickness="1"
            BorderBrush="#dee2e6"
            Background="White"
            CornerRadius="6">
        <DockPanel>
            <!-- Header -->
            <Border DockPanel.Dock="Top"
                    Background="#0078D4"
                    Height="60"
                    BorderThickness="0,0,0,1"
                    BorderBrush="White">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>
                    <TextBlock Text="إدارة المستخدمين"
                               Foreground="White"
                               FontSize="20"
                               FontWeight="Bold"
                               VerticalAlignment="Center"
                               Margin="20,0,0,0"/>
                    <Button Grid.Column="1"
                            Content="✕"
                            Style="{StaticResource CloseButtonStyle}"
                            FontSize="20"
                            Foreground="White"
                            Width="60"
                            Height="60"
                            Click="CloseButton_Click"/>
                    <Button Grid.Column="2"
                            Content="−"
                            Style="{StaticResource CloseButtonStyle}"
                            FontSize="20"
                            Foreground="White"
                            Width="60"
                            Height="60"
                            Click="MinimizeButton_Click"/>
                </Grid>
            </Border>
            <!-- Main Content -->
            <Grid Margin="20">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>
                <!-- Search Panel -->
                <GroupBox Header="بحث"
                          Margin="0,0,0,20"
                          BorderBrush="#dee2e6">
                    <Grid Margin="10">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>
                        <!-- Username Search -->
                        <StackPanel Grid.Column="0"
                                    Margin="0,0,10,0">
                            <TextBlock Text="اسم المستخدم"/>
                            <TextBox Text="{Binding UsernameSearch, UpdateSourceTrigger=PropertyChanged}"
                                     Style="{StaticResource ModernTextBox}"/>
                        </StackPanel>
                        <!-- Email Search -->
                        <StackPanel Grid.Column="1"
                                    Margin="0,0,10,0">
                            <TextBlock Text="البريد الإلكتروني"/>
                            <TextBox Text="{Binding EmailSearch, UpdateSourceTrigger=PropertyChanged}"
                                     Style="{StaticResource ModernTextBox}"/>
                        </StackPanel>
                        <!-- Phone Number Search -->
                        <StackPanel Grid.Column="2"
                                Margin="0,0,10,0">
                            <TextBlock Text="رقم الجوال"/>
                            <TextBox Text="{Binding PhoneNumberSearch, UpdateSourceTrigger=PropertyChanged}"
                                    Style="{StaticResource ModernTextBox}"/>
                        </StackPanel>
                        <!-- Search and Reset Buttons -->
                        <StackPanel Grid.Column="3"
                                    Orientation="Horizontal"
                                    HorizontalAlignment="Right"
                                    VerticalAlignment="Bottom">
                            <Button Content="بحث"
                                    Command="{Binding SearchCommand}"
                                    Style="{StaticResource ModernButton}"
                                    Width="100"
                                    Height="35"
                                    Margin="0,0,10,0"/>
                            <Button Content="إعادة تعيين"
                                    Command="{Binding ResetSearchCommand}"
                                    Style="{StaticResource ModernButton}"
                                    Width="100"
                                    Height="35"/>
                        </StackPanel>
                    </Grid>
                </GroupBox>
                <!-- Action Buttons -->
                <StackPanel Grid.Row="1"
                            Orientation="Horizontal"
                            HorizontalAlignment="Right"
                            Margin="0,0,0,20">
                    <Button Content="إضافة مستخدم جديد"
                            Command="{Binding AddUserCommand}"
                            Style="{StaticResource SuccessButton}"
                            Height="35"
                            Width="140"
                            Margin="0,0,10,0"/>
                    <Button Content="تحديث"
                            Command="{Binding RefreshCommand}"
                            Style="{StaticResource ModernButton}"
                            Height="35"
                            Width="80"/>
                </StackPanel>
                <!-- Users Grid -->
                <DataGrid Grid.Row="2"
                          ItemsSource="{Binding Users}"
                          AutoGenerateColumns="False"
                          IsReadOnly="True"
                          GridLinesVisibility="None"
                          BorderThickness="1"
                          BorderBrush="#dee2e6"
                          Background="White"
                          RowBackground="White"
                          AlternatingRowBackground="#f8f9fa"
                          RowHeight="40"
                          CanUserAddRows="False"
                          CanUserDeleteRows="False"
                          CanUserReorderColumns="False"
                          CanUserSortColumns="True"
                          SelectionMode="Single"
                          SelectionUnit="FullRow">
                    <DataGrid.Resources>
                        <Style TargetType="DataGridRow">
                            <Setter Property="BorderThickness"
                                    Value="0"/>
                            <Setter Property="BorderBrush"
                                    Value="Transparent"/>
                        </Style>
                    </DataGrid.Resources>
                    <DataGrid.Columns>
                        <DataGridTextColumn Header="اسم المستخدم"
                                            Binding="{Binding UserName}"
                                            Width="150"/>
                        <DataGridTextColumn Header="البريد الإلكتروني"
                                            Binding="{Binding Email}"
                                            Width="200"/>
                        <DataGridTextColumn Header="رقم الجوال"
                                            Binding="{Binding PhoneNumber}"
                                            Width="120"/>
                        <DataGridTextColumn Header="الدور"
                                            Binding="{Binding Roles[0], TargetNullValue='-'}"
                                            Width="100"/>
                        <DataGridCheckBoxColumn Header="مفعل البريد"
                                                Binding="{Binding EmailConfirmed}"
                                                Width="80"/>
                        <DataGridCheckBoxColumn Header="مغلق"
                                                Binding="{Binding LockoutEnabled}"
                                                Width="80"/>
                        <!-- Actions Column -->
                        <DataGridTemplateColumn Header="الإجراءات"
                                                Width="220">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <StackPanel Orientation="Horizontal"
                                                HorizontalAlignment="Center">
                                        <Button Style="{StaticResource ModernButton}"
                                                Command="{Binding DataContext.EditUserCommand, RelativeSource={RelativeSource AncestorType=DataGrid}}"
                                                CommandParameter="{Binding}"
                                                ToolTip="تعديل"
                                                Width="35"
                                                Height="30"
                                                Margin="0,0,5,0">
                                            <TextBlock Text="✏️"
                                                       FontSize="16"/>
                                        </Button>
                                        <Button Style="{StaticResource ModernButton}"
                                                Background="#dc3545"
                                                Command="{Binding DataContext.DeleteUserCommand, RelativeSource={RelativeSource AncestorType=DataGrid}}"
                                                CommandParameter="{Binding}"
                                                ToolTip="حذف"
                                                Width="35"
                                                Height="30"
                                                Margin="0,0,5,0">
                                            <TextBlock Text="🗑️"
                                                       FontSize="16"/>
                                        </Button>
                                        <Button Style="{StaticResource ModernButton}"
                                                Command="{Binding DataContext.LockUserCommand, RelativeSource={RelativeSource AncestorType=DataGrid}}"
                                                CommandParameter="{Binding}"
                                                ToolTip="قفل"
                                                Width="35"
                                                Height="30"
                                                Margin="0,0,5,0">
                                            <TextBlock Text="🔒"
                                                       FontSize="16"/>
                                        </Button>
                                        <Button Style="{StaticResource ModernButton}"
                                                Command="{Binding DataContext.UnlockUserCommand, RelativeSource={RelativeSource AncestorType=DataGrid}}"
                                                CommandParameter="{Binding}"
                                                ToolTip="فتح"
                                                Width="35"
                                                Height="30">
                                            <TextBlock Text="🔓"
                                                       FontSize="16"/>
                                        </Button>
                                    </StackPanel>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>
                    </DataGrid.Columns>
                </DataGrid>
                <!-- Pagination Controls -->
                <StackPanel Grid.Row="3"
                            Orientation="Horizontal"
                            HorizontalAlignment="Center"
                            Margin="0,10,0,0">
                    <Button Content="▶"
                            Command="{Binding PreviousPageCommand}"
                            Style="{StaticResource ModernButton}"
                            Width="40"
                            Height="35"
                            Margin="0,0,10,0"/>
                    <TextBlock Text="{Binding PaginationInfo}"
                               VerticalAlignment="Center"
                               Margin="10,0"/>
                    <Button Content="◀"
                            Command="{Binding NextPageCommand}"
                            Style="{StaticResource ModernButton}"
                            Width="40"
                            Height="35"
                            Margin="10,0,0,0"/>
                </StackPanel>
            </Grid>
        </DockPanel>
    </Border>
</Window> 