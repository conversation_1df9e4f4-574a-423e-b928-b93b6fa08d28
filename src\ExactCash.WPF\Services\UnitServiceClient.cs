﻿using ExactCash.Application.DTOs;

namespace ExactCash.WPF.Services
{
    public class UnitServiceClient : IUnitServiceClient
    {
        private readonly HttpService _httpService;

        public UnitServiceClient(string baseUrl)
        {
            _httpService = new HttpService(baseUrl);
        }

        public async Task<UnitDto> CreateUnitAsync(UnitDto unitDto)
        {
            return await _httpService.PostAsync<UnitDto>("api/Units", unitDto);
        }

        public async Task DeleteUnitAsync(int id)
        {
            await _httpService.DeleteAsync($"api/Units/{id}");
        }

        public async Task<IEnumerable<UnitDto>> GetAllUnitsAsync()
        {
            return await _httpService.GetAsync<IEnumerable<UnitDto>>("api/Units");
        }

        public async Task<UnitDto> GetUnitByIdAsync(int id)
        {
            return await _httpService.GetAsync<UnitDto>($"api/Units/{id}");
        }

        public async Task UpdateUnitAsync(int id, UnitDto unitDto)
        {
            await _httpService.PutAsync<UnitDto>($"api/Units/{id}", unitDto);
        }
    }
}
