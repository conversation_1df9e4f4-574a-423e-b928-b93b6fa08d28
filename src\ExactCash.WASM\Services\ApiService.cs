using ExactCash.WASM.Services.Interfaces;
using System.Net.Http.Json;
using System.Text.Json;
using System.Text;

namespace ExactCash.WASM.Services;

public class ApiService : IApiService
{
    private readonly HttpClient _httpClient;
    private readonly JsonSerializerOptions _jsonOptions;

    public ApiService(IHttpClientFactory httpClientFactory)
    {
        _httpClient = httpClientFactory.CreateClient("ExactCashAPI");
        _jsonOptions = new JsonSerializerOptions
        {
            PropertyNameCaseInsensitive = true,
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase
        };
    }

    public async Task<T?> GetAsync<T>(string endpoint)
    {
        try
        {
            var response = await _httpClient.GetAsync(endpoint);
            
            if (response.IsSuccessStatusCode)
            {
                var content = await response.Content.ReadAsStringAsync();
                return JsonSerializer.Deserialize<T>(content, _jsonOptions);
            }

            await HandleErrorResponse(response);
            return default;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"API GET Error: {ex.Message}");
            throw;
        }
    }

    public async Task<T?> PostAsync<T>(string endpoint, object data)
    {
        try
        {
            var response = await _httpClient.PostAsJsonAsync(endpoint, data, _jsonOptions);
            
            if (response.IsSuccessStatusCode)
            {
                var content = await response.Content.ReadAsStringAsync();
                return JsonSerializer.Deserialize<T>(content, _jsonOptions);
            }

            await HandleErrorResponse(response);
            return default;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"API POST Error: {ex.Message}");
            throw;
        }
    }

    public async Task<T?> PutAsync<T>(string endpoint, object data)
    {
        try
        {
            var response = await _httpClient.PutAsJsonAsync(endpoint, data, _jsonOptions);
            
            if (response.IsSuccessStatusCode)
            {
                var content = await response.Content.ReadAsStringAsync();
                return JsonSerializer.Deserialize<T>(content, _jsonOptions);
            }

            await HandleErrorResponse(response);
            return default;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"API PUT Error: {ex.Message}");
            throw;
        }
    }

    public async Task<bool> DeleteAsync(string endpoint)
    {
        try
        {
            var response = await _httpClient.DeleteAsync(endpoint);
            
            if (response.IsSuccessStatusCode)
            {
                return true;
            }

            await HandleErrorResponse(response);
            return false;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"API DELETE Error: {ex.Message}");
            throw;
        }
    }

    public async Task<HttpResponseMessage> GetResponseAsync(string endpoint)
    {
        return await _httpClient.GetAsync(endpoint);
    }

    public async Task<HttpResponseMessage> PostResponseAsync(string endpoint, object data)
    {
        return await _httpClient.PostAsJsonAsync(endpoint, data, _jsonOptions);
    }

    public async Task<HttpResponseMessage> PutResponseAsync(string endpoint, object data)
    {
        return await _httpClient.PutAsJsonAsync(endpoint, data, _jsonOptions);
    }

    public async Task<HttpResponseMessage> DeleteResponseAsync(string endpoint)
    {
        return await _httpClient.DeleteAsync(endpoint);
    }

    private async Task HandleErrorResponse(HttpResponseMessage response)
    {
        var content = await response.Content.ReadAsStringAsync();
        
        try
        {
            var errorResponse = JsonSerializer.Deserialize<ErrorResponse>(content, _jsonOptions);
            throw new ApiException(errorResponse?.Message ?? "حدث خطأ في الخادم", response.StatusCode);
        }
        catch (JsonException)
        {
            throw new ApiException($"خطأ في الخادم: {response.StatusCode}", response.StatusCode);
        }
    }

    private class ErrorResponse
    {
        public string Message { get; set; } = string.Empty;
        public List<string> Errors { get; set; } = new();
    }
}

public class ApiException : Exception
{
    public System.Net.HttpStatusCode StatusCode { get; }

    public ApiException(string message, System.Net.HttpStatusCode statusCode) : base(message)
    {
        StatusCode = statusCode;
    }
}
