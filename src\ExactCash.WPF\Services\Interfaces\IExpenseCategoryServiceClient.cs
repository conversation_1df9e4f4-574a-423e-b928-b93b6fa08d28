﻿using ExactCash.Application.DTOs;

namespace ExactCash.WPF.Services.Interfaces
{
    public interface IExpenseCategoryServiceClient
    {
        Task<IEnumerable<ExpenseCategoryDto>> GetAllAsync();
        Task<ExpenseCategoryDto> GetByIdAsync(int id);
        Task<ExpenseCategoryDto> CreateAsync(ExpenseCategoryDto dto);
        Task<bool> UpdateAsync(ExpenseCategoryDto dto);
        Task<bool> DeleteAsync(int id);
    }
}
