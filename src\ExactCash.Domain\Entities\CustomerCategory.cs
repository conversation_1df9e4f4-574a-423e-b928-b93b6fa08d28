using ExactCash.Domain.Common;
using System.Text.Json.Serialization;
#nullable disable

namespace ExactCash.Domain.Entities
{
    /// <summary>
    /// Represents a customer category (e.g., VIP, Regular, Wholesale).
    /// </summary>
    public class CustomerCategory : BaseEntity
    {
        /// <summary>
        /// The name of the customer category.
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// The description of the customer category.
        /// </summary>
        public string Description { get; set; }

        /// <summary>
        /// IsActive
        /// </summary>
        public bool IsActive { get; set; }

        /// <summary>
        /// Navigation property for the customers that belong to this category.
        /// </summary>
        [JsonIgnore]
        public ICollection<Customer> Customers { get; set; } = new List<Customer>();
    }
}
