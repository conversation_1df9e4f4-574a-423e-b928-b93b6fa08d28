﻿using ExactCash.Application.DTOs;
using ExactCash.Domain.Entities;

namespace ExactCash.WPF.Services
{
    public class BrandsServiceClient : IBrandsServiceClient
    {
        private readonly HttpService _httpService;

        public BrandsServiceClient(string baseUrl)
        {
            _httpService = new HttpService(baseUrl);
        }

        public async Task<BrandDto> CreateBrandAsync(BrandDto brandDto)
        {
           return await _httpService.PostAsync<BrandDto>("api/Brands", brandDto);
        }

        public async Task DeleteBrandAsync(int id)
        {
            await _httpService.DeleteAsync($"api/Brands/{id}");
        }

        public async Task<IEnumerable<BrandDto>> GetAllBrandsAsync()
        {
            return await _httpService.GetAsync<IEnumerable<BrandDto>>("api/Brands");
        }

        public async Task<BrandDto> GetBrandByIdAsync(int id)
        {
           return await _httpService.GetAsync<BrandDto>($"api/Brands/{id}");
        }

        public async Task UpdateBrandAsync(BrandDto brandDto)
        {
            await _httpService.PutAsync<BrandDto>($"api/Brands/{brandDto.Id}", brandDto);
        }
    }
}
