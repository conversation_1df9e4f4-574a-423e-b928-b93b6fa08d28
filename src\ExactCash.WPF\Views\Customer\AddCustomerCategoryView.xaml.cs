using System.Windows;
using ExactCash.Application.DTOs;
using ExactCash.WPF.Services;

namespace ExactCash.WPF.Views.Customer
{
    public partial class AddCustomerCategoryView : Window
    {
        private readonly ICustomerCategoryServiceClient _service;
        private readonly CustomerCategoryDto _categoryToEdit;
        private readonly bool _isEditMode;

        public string CategoryName => NameTextBox.Text;
        public string CategoryDescription => DescriptionTextBox.Text;

        // Constructor for adding new category
        public AddCustomerCategoryView(ICustomerCategoryServiceClient service)
        {
            InitializeComponent();
            _service = service;
            _isEditMode = false;
            TitleTextBlock.Text = "إضافة تصنيف عميل";
            SaveButton.Content = "إضافة";
        }

        // Constructor for editing existing category
        public AddCustomerCategoryView(ICustomerCategoryServiceClient service, CustomerCategoryDto categoryToEdit)
        {
            InitializeComponent();
            _service = service;
            _categoryToEdit = categoryToEdit;
            _isEditMode = true;
            
            TitleTextBlock.Text = "تعديل تصنيف عميل";
            SaveButton.Content = "تحديث";
            
            // Populate fields with existing data
            NameTextBox.Text = categoryToEdit.Name;
            DescriptionTextBox.Text = categoryToEdit.Description;
        }

        private async void SaveButton_Click(object sender, RoutedEventArgs e)
        {
            if (string.IsNullOrWhiteSpace(NameTextBox.Text))
            {
                Helpers.BootstrapMessageBoxHelper.ShowError("يرجى إدخال اسم التصنيف.", "خطأ", owner: this);
                return;
            }

            try
            {
                if (_isEditMode)
                {
                    // Update existing category
                    var updatedCategory = new CustomerCategoryDto
                    {
                        Id = _categoryToEdit.Id,
                        Name = NameTextBox.Text.Trim(),
                        Description = DescriptionTextBox.Text?.Trim(),
                        IsActive = _categoryToEdit.IsActive,
                        CreationDate = _categoryToEdit.CreationDate,
                        CreatedBy = _categoryToEdit.CreatedBy
                    };

                    var result = await _service.UpdateAsync(updatedCategory);
                    
                    if (result.Success)
                    {
                        Helpers.BootstrapMessageBoxHelper.ShowSuccess(result.Message, owner: this);
                        DialogResult = true;
                        Close();
                    }
                    else
                    {
                        Helpers.BootstrapMessageBoxHelper.ShowError(result.Message, "خطأ", owner: this);
                    }
                }
                else
                {
                    // Create new category
                    var newCategory = new CustomerCategoryDto
                    {
                        Name = NameTextBox.Text.Trim(),
                        Description = DescriptionTextBox.Text?.Trim(),
                        IsActive = true
                    };

                    var result = await _service.CreateAsync(newCategory);
                    
                    if (result.Success)
                    {
                        Helpers.BootstrapMessageBoxHelper.ShowSuccess(result.Message, owner: this);
                        DialogResult = true;
                        Close();
                    }
                    else
                    {
                        Helpers.BootstrapMessageBoxHelper.ShowError(result.Message, "خطأ", owner: this);
                    }
                }
            }
            catch (Exception ex)
            {
                Helpers.BootstrapMessageBoxHelper.ShowError($"حدث خطأ: {ex.Message}", "خطأ", owner: this);
            }
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }
    }
}
