using ExactCash.Application.DTOs;

namespace ExactCash.WPF.Services
{
    public interface IPrinterService
    {
        /// <summary>
        /// Prints a sale invoice to the Xprinter
        /// </summary>
        /// <param name="sale">The sale to print</param>
        /// <returns>True if printing was successful, false otherwise</returns>
        Task<bool> PrintInvoiceAsync(SaleDto sale);
    }
}