using ExactCash.Application.DTOs;
using ExactCash.Application.DTOs.Common;
using ExactCash.Application.Interfaces;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
#nullable disable

namespace ExactCash.API.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize(Roles = "Admin")]
    public class RolesController : ControllerBase
    {
        private readonly IRoleService _roleService;

        public RolesController(IRoleService roleService)
        {
            _roleService = roleService;
        }

        [HttpGet]
        public async Task<IActionResult> GetAllRoles([FromQuery] PaginationFilter pagination)
        {
            var result = await _roleService.GetAllRolesAsync(pagination);
            return Ok(result);
        }
        
        [HttpGet("get-all-roles")]
        public async Task<IActionResult> GetAllRoles()
        {
            var result = await _roleService.GetAllRolesAsync();
            return Ok(result);
        }

        [HttpGet("{id}")]
        public async Task<IActionResult> GetRoleById(string id)
        {
            var result = await _roleService.GetRoleByIdAsync(id);
            if (result == null)
                return NotFound();

            return Ok(result);
        }

        [HttpPost]
        public async Task<IActionResult> CreateRole([FromBody] CreateRoleDto roleDto)
        {
            var result = await _roleService.CreateRoleAsync(roleDto);
            if (result == null)
                return BadRequest(new { message = "Failed to create role" });

            return CreatedAtAction(nameof(GetRoleById), new { id = result.Id }, result);
        }

        [HttpPut("{id}")]
        public async Task<IActionResult> UpdateRole(string id, [FromBody] UpdateRoleDto roleDto)
        {
            var result = await _roleService.UpdateRoleAsync(id, roleDto);
            if (result == null)
                return NotFound();

            return Ok(result);
        }

        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteRole(string id)
        {
            var result = await _roleService.DeleteRoleAsync(id);
            if (!result)
                return BadRequest(new { message = "Failed to delete role" });

            return Ok(new { message = "Role deleted successfully" });
        }

        [HttpPost("assign-users")]
        public async Task<IActionResult> AssignUsersToRole([FromBody] RoleUsersDto roleUsersDto)
        {
            var result = await _roleService.AssignUsersToRoleAsync(roleUsersDto);
            if (!result)
                return BadRequest(new { message = "Failed to assign users to role" });

            return Ok(new { message = "Users assigned to role successfully" });
        }

        [HttpPost("remove-users")]
        public async Task<IActionResult> RemoveUsersFromRole([FromBody] RoleUsersDto roleUsersDto)
        {
            var result = await _roleService.RemoveUsersFromRoleAsync(roleUsersDto);
            if (!result)
                return BadRequest(new { message = "Failed to remove users from role" });

            return Ok(new { message = "Users removed from role successfully" });
        }

        [HttpGet("{roleId}/users")]
        public async Task<IActionResult> GetUsersInRole(string roleId)
        {
            var result = await _roleService.GetUsersInRoleAsync(roleId);
            return Ok(result);
        }

        [HttpGet("user/{userId}")]
        public async Task<IActionResult> GetRolesForUser(string userId)
        {
            var result = await _roleService.GetRolesForUserAsync(userId);
            return Ok(result);
        }

        [HttpGet("user/{userId}/check/{roleName}")]
        public async Task<IActionResult> IsUserInRole(string userId, string roleName)
        {
            var result = await _roleService.IsUserInRoleAsync(userId, roleName);
            return Ok(result);
        }
    }
}