<Window x:Class="ExactCash.WPF.Views.Supplier.AddSupplierView"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:ExactCash.WPF.Views.Supplier"
        mc:Ignorable="d"
        Title="{Binding WindowTitle}"
        Height="600"
        Width="800"
        WindowStartupLocation="CenterOwner"
        FlowDirection="RightToLeft"
        Background="#f8f9fa"
        WindowStyle="None"
        AllowsTransparency="True"
        ResizeMode="NoResize">

        <Window.Resources>
                <Style x:Key="CloseButtonStyle"
                       TargetType="Button">
                        <Setter Property="Template">
                                <Setter.Value>
                                        <ControlTemplate TargetType="Button">
                                                <Border x:Name="border"
                                                        Background="Transparent"
                                                        BorderThickness="0">
                                                        <ContentPresenter HorizontalAlignment="Center"
                                                                          VerticalAlignment="Center"/>
                                                </Border>
                                                <ControlTemplate.Triggers>
                                                        <Trigger Property="IsMouseOver"
                                                                 Value="True">
                                                                <Setter Property="Foreground"
                                                                        Value="#e74c3c"/>
                                                        </Trigger>
                                                </ControlTemplate.Triggers>
                                        </ControlTemplate>
                                </Setter.Value>
                        </Setter>
                </Style>

                <Style x:Key="ModernTextBox"
                       TargetType="TextBox">
                        <Setter Property="Height"
                                Value="35"/>
                        <Setter Property="Padding"
                                Value="10,5"/>
                        <Setter Property="BorderThickness"
                                Value="1"/>
                        <Setter Property="BorderBrush"
                                Value="#CCCCCC"/>
                        <Setter Property="Background"
                                Value="White"/>
                        <Setter Property="FontSize"
                                Value="14"/>
                </Style>

                <Style x:Key="ModernButton"
                       TargetType="Button">
                        <Setter Property="Background"
                                Value="#0078D4"/>
                        <Setter Property="Foreground"
                                Value="White"/>
                        <Setter Property="BorderThickness"
                                Value="0"/>
                        <Setter Property="Padding"
                                Value="15,8"/>
                        <Setter Property="FontSize"
                                Value="14"/>
                        <Setter Property="Template">
                                <Setter.Value>
                                        <ControlTemplate TargetType="Button">
                                                <Border Background="{TemplateBinding Background}"
                                                        BorderThickness="{TemplateBinding BorderThickness}"
                                                        BorderBrush="{TemplateBinding BorderBrush}"
                                                        CornerRadius="4">
                                                        <ContentPresenter HorizontalAlignment="Center"
                                                                          VerticalAlignment="Center"/>
                                                </Border>
                                        </ControlTemplate>
                                </Setter.Value>
                        </Setter>
                        <Style.Triggers>
                                <Trigger Property="IsMouseOver"
                                         Value="True">
                                        <Setter Property="Background"
                                                Value="#106EBE"/>
                                </Trigger>
                        </Style.Triggers>
                </Style>
        </Window.Resources>

        <Border BorderThickness="1"
                BorderBrush="#dee2e6"
                Background="White"
                CornerRadius="6">
                <DockPanel>
                        <!-- Header -->
                        <Border DockPanel.Dock="Top"
                                Background="#0078D4"
                                Height="60"
                                BorderThickness="0,0,0,1"
                                BorderBrush="White">
                                <Grid>
                                        <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="*"/>
                                                <ColumnDefinition Width="Auto"/>
                                        </Grid.ColumnDefinitions>

                                        <!-- Title -->
                                        <TextBlock Text="{Binding WindowTitle}"
                                                   Foreground="White"
                                                   FontSize="20"
                                                   FontWeight="Bold"
                                                   VerticalAlignment="Center"
                                                   Margin="20,0,0,0"/>

                                        <!-- Close Button -->
                                        <Button Grid.Column="1"
                                                Content="✕"
                                                Click="CloseButton_Click"
                                                Style="{StaticResource CloseButtonStyle}"
                                                FontSize="20"
                                                Foreground="White"
                                                Width="60"
                                                Height="60"/>
                                </Grid>
                        </Border>

                        <!-- Content -->
                        <Grid Margin="20">
                                <Grid.RowDefinitions>
                                        <RowDefinition Height="*"/>
                                        <RowDefinition Height="Auto"/>
                                </Grid.RowDefinitions>

                                <!-- Form Fields -->
                                <ScrollViewer VerticalScrollBarVisibility="Auto">
                                        <StackPanel>
                                                <!-- Name -->
                                                <StackPanel Margin="0,0,0,15">
                                                        <TextBlock Text="اسم المورد *"
                                                                   Margin="0,0,0,5"/>
                                                        <TextBox Text="{Binding Name, UpdateSourceTrigger=PropertyChanged}"
                                                                 Style="{StaticResource ModernTextBox}"/>
                                                </StackPanel>

                                                <!-- Phone -->
                                                <StackPanel Margin="0,0,0,15">
                                                        <TextBlock Text="رقم الهاتف *"
                                                                   Margin="0,0,0,5"/>
                                                        <TextBox Text="{Binding Phone, UpdateSourceTrigger=PropertyChanged}"
                                                                 Style="{StaticResource ModernTextBox}"/>
                                                </StackPanel>

                                                <!-- Email -->
                                                <StackPanel Margin="0,0,0,15">
                                                        <TextBlock Text="البريد الإلكتروني"
                                                                   Margin="0,0,0,5"/>
                                                        <TextBox Text="{Binding Email, UpdateSourceTrigger=PropertyChanged}"
                                                                 Style="{StaticResource ModernTextBox}"/>
                                                </StackPanel>

                                                <!-- Contact Person -->
                                                <StackPanel Margin="0,0,0,15">
                                                        <TextBlock Text="الشخص المسؤول"
                                                                   Margin="0,0,0,5"/>
                                                        <TextBox Text="{Binding ContactPerson, UpdateSourceTrigger=PropertyChanged}"
                                                                 Style="{StaticResource ModernTextBox}"/>
                                                </StackPanel>

                                                <!-- Category -->
                                                <StackPanel Margin="0,0,0,15">
                                                        <TextBlock Text="التصنيف"
                                                                   Margin="0,0,0,5"/>
                                                        <ComboBox ItemsSource="{Binding Categories}"
                                                                  SelectedItem="{Binding SelectedCategory}"
                                                                  DisplayMemberPath="Name"
                                                                  Height="35"
                                                                  Padding="10,5"
                                                                  BorderThickness="1"
                                                                  BorderBrush="#CCCCCC"
                                                                  Background="White"
                                                                  FontSize="14"/>
                                                </StackPanel>

                                                <!-- Address -->
                                                <StackPanel Margin="0,0,0,15">
                                                        <TextBlock Text="العنوان"
                                                                   Margin="0,0,0,5"/>
                                                        <TextBox Text="{Binding Address, UpdateSourceTrigger=PropertyChanged}"
                                                                 Style="{StaticResource ModernTextBox}"/>
                                                </StackPanel>

                                                <!-- Tax ID -->
                                                <StackPanel Margin="0,0,0,15">
                                                        <TextBlock Text="الرقم الضريبي"
                                                                   Margin="0,0,0,5"/>
                                                        <TextBox Text="{Binding TaxId, UpdateSourceTrigger=PropertyChanged}"
                                                                 Style="{StaticResource ModernTextBox}"/>
                                                </StackPanel>

                                                <!-- Bank Account Number -->
                                                <StackPanel Margin="0,0,0,15">
                                                        <TextBlock Text="رقم الحساب البنكي"
                                                                   Margin="0,0,0,5"/>
                                                        <TextBox Text="{Binding BankAccountNumber, UpdateSourceTrigger=PropertyChanged}"
                                                                 Style="{StaticResource ModernTextBox}"/>
                                                </StackPanel>

                                                <!-- Bank Name -->
                                                <StackPanel Margin="0,0,0,15">
                                                        <TextBlock Text="اسم البنك"
                                                                   Margin="0,0,0,5"/>
                                                        <TextBox Text="{Binding BankName, UpdateSourceTrigger=PropertyChanged}"
                                                                 Style="{StaticResource ModernTextBox}"/>
                                                </StackPanel>

                                                <!-- Notes -->
                                                <StackPanel Margin="0,0,0,15">
                                                        <TextBlock Text="ملاحظات"
                                                                   Margin="0,0,0,5"/>
                                                        <TextBox Text="{Binding Notes, UpdateSourceTrigger=PropertyChanged}"
                                                                 Style="{StaticResource ModernTextBox}"
                                                                 Height="70"
                                                                 TextWrapping="Wrap"
                                                                 AcceptsReturn="True"
                                                                 VerticalScrollBarVisibility="Auto"/>
                                                </StackPanel>
                                        </StackPanel>
                                </ScrollViewer>

                                <!-- Action Buttons -->
                                <StackPanel Grid.Row="1"
                                            Orientation="Horizontal"
                                            HorizontalAlignment="Center"
                                            Margin="0,20,0,0">
                                        <Button Content="{Binding SaveButtonText}"
                                                Command="{Binding SaveCommand}"
                                                Style="{StaticResource ModernButton}"
                                                Width="120"
                                                Height="35"
                                                Margin="0,0,10,0"/>
                                        <Button Content="إلغاء"
                                                Click="CloseButton_Click"
                                                Style="{StaticResource ModernButton}"
                                                Background="#6c757d"
                                                Width="120"
                                                Height="35"/>
                                </StackPanel>
                        </Grid>
                </DockPanel>
        </Border>
</Window>