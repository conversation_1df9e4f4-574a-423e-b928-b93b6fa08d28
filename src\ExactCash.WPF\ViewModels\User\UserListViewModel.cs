using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Windows;
using System.Windows.Input;
using System.Windows.Media;
using ExactCash.Application.DTOs;
using ExactCash.WPF.Commands;
using ExactCash.WPF.Services;
using ExactCash.WPF.Services.Interfaces;
using ExactCash.WPF.Views;
using ExactCash.WPF.Views.Sale;
using static System.Windows.Forms.VisualStyles.VisualStyleElement.StartPanel;

namespace ExactCash.WPF.ViewModels.User
{
    public class UserListViewModel : INotifyPropertyChanged
    {
        private readonly IUserServiceClient _userService;
        private readonly IRoleServiceClient _roleService;
        public ObservableCollection<UserDto> Users { get; set; } = new();

        // Search fields
        private string _usernameSearch;
        public string UsernameSearch
        {
            get => _usernameSearch;
            set { _usernameSearch = value; OnPropertyChanged(nameof(UsernameSearch)); }
        }
        private string _emailSearch;
        public string EmailSearch
        {
            get => _emailSearch;
            set { _emailSearch = value; OnPropertyChanged(nameof(EmailSearch)); }
        }
        private string _phoneNumberSearch;
        public string PhoneNumberSearch
        {
            get => _phoneNumberSearch;
            set { _phoneNumberSearch = value; OnPropertyChanged(nameof(PhoneNumberSearch)); }
        }

        // Paging
        private int _currentPage = 1;
        public int CurrentPage
        {
            get => _currentPage;
            set { _currentPage = value; OnPropertyChanged(nameof(CurrentPage)); }
        }
        private int _totalPages = 1;
        public int TotalPages
        {
            get => _totalPages;
            set { _totalPages = value; OnPropertyChanged(nameof(TotalPages)); }
        }
        public int PageSize { get; set; } = 20;
        public string PaginationInfo => $"صفحة {CurrentPage} من {TotalPages}";

        // Commands
        public ICommand SearchCommand { get; }
        public ICommand ResetSearchCommand { get; }
        public ICommand AddUserCommand { get; }
        public ICommand EditUserCommand { get; }
        public ICommand DeleteUserCommand { get; }
        public ICommand LockUserCommand { get; }
        public ICommand UnlockUserCommand { get; }
        public ICommand NextPageCommand { get; }
        public ICommand PreviousPageCommand { get; }
        public ICommand RefreshCommand { get; }

        public UserListViewModel(IUserServiceClient userService, IRoleServiceClient roleService)
        {
            _userService = userService;
            _roleService = roleService;
            SearchCommand = new RelayCommand(async () => await ExecuteSearch());
            ResetSearchCommand = new RelayCommand(async () => await ExecuteResetSearch());
            AddUserCommand = new RelayCommand(ExecuteAddUser);
            EditUserCommand = new RelayCommand<UserDto>(ExecuteEditUser);
            DeleteUserCommand = new RelayCommand<UserDto>(async user => await ExecuteDeleteUser(user));
            LockUserCommand = new RelayCommand<UserDto>(async user => await ExecuteLockUser(user));
            UnlockUserCommand = new RelayCommand<UserDto>(async user => await ExecuteUnlockUser(user));
            NextPageCommand = new RelayCommand(async () => await ExecuteNextPage(), () => CurrentPage < TotalPages);
            PreviousPageCommand = new RelayCommand(async () => await ExecutePreviousPage(), () => CurrentPage > 1);
            RefreshCommand = new RelayCommand(async () => await LoadUsers());
            LoadUsers().ConfigureAwait(false);
        }

        public event PropertyChangedEventHandler PropertyChanged;
        protected void OnPropertyChanged(string propertyName) => PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));

        private async Task LoadUsers()
        {
            var pagination = new ExactCash.Application.DTOs.Common.PaginationFilter
            {
                PageNumber = CurrentPage,
                PageSize = PageSize
            };
            var result = await _userService.GetAllUsersAsync(UsernameSearch, EmailSearch, PhoneNumberSearch, pagination);
            Users.Clear();
            if (result?.Data != null)
            {
                foreach (var user in result.Data)
                    Users.Add(user);
                TotalPages = result.TotalPages;
            }
            else
            {
                TotalPages = 1;
            }
            OnPropertyChanged(nameof(PaginationInfo));
        }

        private async Task ExecuteSearch()
        {
            CurrentPage = 1;
            await LoadUsers();
        }

        private async Task ExecuteResetSearch()
        {
            UsernameSearch = string.Empty;
            EmailSearch = string.Empty;
            PhoneNumberSearch = string.Empty;
            CurrentPage = 1;
            await LoadUsers();
        }
        private Window FindParentWindow()
        {
            foreach (Window window in System.Windows.Application.Current.Windows)
            {
                if (window.Content != null &&
                    VisualTreeHelper.GetChild(window.Content as DependencyObject, 0) is FrameworkElement content &&
                    window.Name == "UserListScreen")
                {
                    return window;
                }
            }
            return System.Windows.Application.Current.MainWindow;
        }

        private async void ExecuteAddUser()
        {
            var addUserView = new Views.User.AddUserView(_roleService);
            addUserView.Owner = FindParentWindow();
            if (addUserView.ShowDialog() == true)
            {
                var userDto = new CreateUserDto
                {
                    UserName = addUserView.UserName,
                    Email = addUserView.Email,
                    PhoneNumber = addUserView.Phone,
                    Password = addUserView.Password,
                    Roles = addUserView.SelectedRoles
                };
                var result = await _userService.CreateUserAsync(userDto);
                if (result != null)
                {
                    await LoadUsers();
                }
            }
        }

        private void ExecuteEditUser(UserDto user)
        {
            if (user == null) return;
            // TODO: Show edit user dialog and refresh list on success
        }

        private async Task ExecuteDeleteUser(UserDto user)
        {
            if (user == null) return;

            try
            {
                var result = System.Windows.MessageBox.Show(
                $"هل أنت متأكد من حذف المستخدم {user.UserName}؟",
                "تأكيد الحذف",
                MessageBoxButton.YesNo,
                MessageBoxImage.Warning);

                if (result == MessageBoxResult.Yes)
                {
                    await _userService.DeleteUserAsync(user.Id.ToString());
                    Users.Remove(user);
                    Helpers.BootstrapMessageBoxHelper.Show(owner: FindParentWindow(), message: "تم حذف المستخدم بنجاح", title: "نجاح");
                }
            }
            catch (Exception ex)
            {
                Helpers.BootstrapMessageBoxHelper.Show(owner: FindParentWindow(), message: $"فشل حذف المستخدم:\n{ex.Message}", title: "خطأ");
            }
        }

        private async Task ExecuteLockUser(UserDto user)
        {
            if (user == null) return;
            // TODO: Call API to lock user
            user.LockoutEnabled = true;
            OnPropertyChanged(nameof(Users));
        }

        private async Task ExecuteUnlockUser(UserDto user)
        {
            if (user == null) return;
            // TODO: Call API to unlock user
            user.LockoutEnabled = false;
            OnPropertyChanged(nameof(Users));
        }

        private async Task ExecuteNextPage()
        {
            if (CurrentPage < TotalPages)
            {
                CurrentPage++;
                await LoadUsers();
            }
        }

        private async Task ExecutePreviousPage()
        {
            if (CurrentPage > 1)
            {
                CurrentPage--;
                await LoadUsers();
            }
        }
    }
}