using System.Collections.ObjectModel;
using System.Windows;
using ExactCash.Application.DTOs;
using ExactCash.WPF.Services.Interfaces;

namespace ExactCash.WPF.Views.Expense
{
    public partial class AddExpenseView : Window
    {
        private readonly IExpenseServiceClient _service;
        private readonly IExpenseCategoryServiceClient _categoryService;
        private readonly ExpenseDto _editingExpense;
        public string WindowTitle { get; set; }
        public string HeaderText { get; set; }
        public string SaveButtonText { get; set; }
        public ObservableCollection<ExpenseCategoryDto> Categories { get; set; } = new ObservableCollection<ExpenseCategoryDto>();

        public AddExpenseView(IExpenseServiceClient service, IExpenseCategoryServiceClient categoryService, ExpenseDto editingExpense = null)
        {
            InitializeComponent();
            _service = service;
            _categoryService = categoryService;
            _editingExpense = editingExpense;

            if (_editingExpense != null)
            {
                // Edit mode
                WindowTitle = "تعديل مصروف";
                HeaderText = "تعديل مصروف";
                SaveButtonText = "تحديث";
                DatePicker.SelectedDate = _editingExpense.ExpenseDate;
                AmountTextBox.Text = _editingExpense.Amount.ToString("N2");
                DescriptionTextBox.Text = _editingExpense.Description;
                ReferenceNumberTextBox.Text = _editingExpense.ReferenceNumber;
                // TODO: Set CategoryComboBox and PaymentMethodComboBox selected items
            }
            else
            {
                // Add mode
                WindowTitle = "إضافة مصروف";
                HeaderText = "إضافة مصروف";
                SaveButtonText = "حفظ";
                DatePicker.SelectedDate = System.DateTime.Now;
            }
            DataContext = this;
            Loaded += AddExpenseView_Loaded;
        }

        private async void AddExpenseView_Loaded(object sender, RoutedEventArgs e)
        {
            Categories.Clear();
            var categories = await _categoryService.GetAllAsync();
            foreach (var cat in categories)
                Categories.Add(cat);
            CategoryComboBox.ItemsSource = Categories;
            if (_editingExpense != null && _editingExpense.CategoryId.HasValue)
            {
                CategoryComboBox.SelectedItem = Categories.FirstOrDefault(c => c.Id == _editingExpense.CategoryId);
            }
        }

        private async void SaveButton_Click(object sender, RoutedEventArgs e)
        {
            if (string.IsNullOrWhiteSpace(AmountTextBox.Text) ||
                string.IsNullOrWhiteSpace(DescriptionTextBox.Text) ||
                DatePicker.SelectedDate == null)
            {
                System.Windows.MessageBox.Show("يرجى إدخال جميع الحقول المطلوبة.", "خطأ في التحقق", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            if (!decimal.TryParse(AmountTextBox.Text, out decimal amount))
            {
                System.Windows.MessageBox.Show("يرجى إدخال مبلغ صحيح.", "خطأ في التحقق", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            try
            {
                if (_editingExpense != null)
                {
                    // Update existing expense
                    _editingExpense.ExpenseDate = DatePicker.SelectedDate?.ToUniversalTime() ?? System.DateTime.UtcNow;
                    _editingExpense.Amount = amount;
                    _editingExpense.Description = DescriptionTextBox.Text.Trim();
                    _editingExpense.CategoryId = (CategoryComboBox.SelectedItem as ExpenseCategoryDto)?.Id;
                    _editingExpense.PaymentMethodId = (PaymentMethodComboBox.SelectedItem as PaymentMethodDto)?.Id;
                    _editingExpense.ReferenceNumber = ReferenceNumberTextBox.Text.Trim();

                    await _service.UpdateAsync(_editingExpense);
                }
                else
                {
                    // Add new expense
                    var expense = new ExpenseDto
                    {
                        ExpenseDate = DatePicker.SelectedDate?.ToUniversalTime() ?? System.DateTime.UtcNow,
                        Amount = amount,
                        Description = DescriptionTextBox.Text.Trim(),
                        CategoryId = (CategoryComboBox.SelectedItem as ExpenseCategoryDto)?.Id,
                        PaymentMethodId = (PaymentMethodComboBox.SelectedItem as PaymentMethodDto)?.Id,
                        ReferenceNumber = ReferenceNumberTextBox.Text.Trim()
                    };
                    await _service.CreateAsync(expense);
                }
                DialogResult = true;
                Close();
            }
            catch (System.Exception ex)
            {
                System.Windows.MessageBox.Show($"خطأ في حفظ المصروف: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }
    }
}

// Minimal PaymentMethodDto if missing
namespace ExactCash.Application.DTOs
{
    public class PaymentMethodDto
    {
        public int Id { get; set; }
        public string Name { get; set; }
    }
}