<Window x:Name="PurchaseOrderScreen"
        x:Class="ExactCash.WPF.Views.PurchaseOrder.PurchaseOrderView"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:ExactCash.WPF.Views.PurchaseOrder"
        mc:Ignorable="d"
        Title="طلب شراء"
        Height="800"
        Width="1200"
        WindowStartupLocation="CenterOwner"
        ResizeMode="NoResize"
        Background="#F8F9FA"
        FlowDirection="RightToLeft"
        FontFamily="Segoe UI">

        <Window.Resources>
                <Style x:Key="ModernBorder"
                       TargetType="Border">
                        <Setter Property="Background"
                                Value="White"/>
                        <Setter Property="BorderBrush"
                                Value="#DEE2E6"/>
                        <Setter Property="BorderThickness"
                                Value="1"/>
                        <Setter Property="CornerRadius"
                                Value="6"/>
                        <Setter Property="Padding"
                                Value="15"/>
                </Style>

                <ControlTemplate x:Key="ComboBoxToggleButton"
                                 TargetType="ToggleButton">
                        <Grid>
                                <Grid.ColumnDefinitions>
                                        <ColumnDefinition/>
                                        <ColumnDefinition Width="0"/>
                                </Grid.ColumnDefinitions>
                                <Border x:Name="Border"
                                        Grid.ColumnSpan="2"
                                        CornerRadius="2"
                                        Background="Transparent"
                                        BorderThickness="1"
                                        BorderBrush="Transparent"/>
                                <Border Grid.Column="0"
                                        CornerRadius="2,0,0,2"
                                        Margin="1"
                                        Background="Transparent"
                                        BorderThickness="0,0,1,0"
                                        BorderBrush="Transparent"/>
                                <Path x:Name="Arrow"
                                      Grid.Column="1"
                                      HorizontalAlignment="Center"
                                      VerticalAlignment="Center"
                                      Data="M 0 0 L 4 4 L 8 0 Z"
                                      Fill="Gray"/>
                        </Grid>
                        <ControlTemplate.Triggers>
                                <Trigger Property="ToggleButton.IsMouseOver"
                                         Value="true">
                                        <Setter Property="Background"
                                                TargetName="Border"
                                                Value="#E5E5E5"/>
                                </Trigger>
                                <Trigger Property="ToggleButton.IsChecked"
                                         Value="true">
                                        <Setter Property="Background"
                                                TargetName="Border"
                                                Value="#E5E5E5"/>
                                </Trigger>
                                <Trigger Property="IsEnabled"
                                         Value="False">
                                        <Setter Property="Fill"
                                                TargetName="Arrow"
                                                Value="Gray"/>
                                </Trigger>
                        </ControlTemplate.Triggers>
                </ControlTemplate>

                <Style x:Key="ModernButton"
                       TargetType="Button">
                        <Setter Property="Height"
                                Value="35"/>
                        <Setter Property="Padding"
                                Value="15,5"/>
                        <Setter Property="Background"
                                Value="#0078D4"/>
                        <Setter Property="Foreground"
                                Value="White"/>
                        <Setter Property="BorderThickness"
                                Value="0"/>
                        <Setter Property="Cursor"
                                Value="Hand"/>
                        <Setter Property="FontSize"
                                Value="14"/>
                </Style>

                <Style x:Key="ModernTextBox"
                       TargetType="TextBox">
                        <Setter Property="Height"
                                Value="35"/>
                        <Setter Property="Padding"
                                Value="10,5"/>
                        <Setter Property="BorderThickness"
                                Value="1"/>
                        <Setter Property="BorderBrush"
                                Value="#CCCCCC"/>
                        <Setter Property="Background"
                                Value="White"/>
                        <Setter Property="FontSize"
                                Value="14"/>
                        <Setter Property="TextAlignment"
                                Value="Right"/>
                </Style>

                <Style x:Key="ModernComboBox"
                       TargetType="ComboBox">
                        <Setter Property="Height"
                                Value="35"/>
                        <Setter Property="Padding"
                                Value="10,5"/>
                        <Setter Property="BorderThickness"
                                Value="1"/>
                        <Setter Property="BorderBrush"
                                Value="#CCCCCC"/>
                        <Setter Property="Background"
                                Value="White"/>
                        <Setter Property="FontSize"
                                Value="14"/>
                        <Setter Property="HorizontalContentAlignment"
                                Value="Right"/>
                </Style>

                <Style x:Key="ModernDatePicker"
                       TargetType="DatePicker">
                        <Setter Property="Height"
                                Value="35"/>
                        <Setter Property="Padding"
                                Value="10,5"/>
                        <Setter Property="BorderThickness"
                                Value="1"/>
                        <Setter Property="BorderBrush"
                                Value="#CCCCCC"/>
                        <Setter Property="Background"
                                Value="White"/>
                        <Setter Property="FontSize"
                                Value="14"/>
                </Style>

                <Style x:Key="ModernDataGridRowStyle"
                       TargetType="DataGridRow">
                        <Setter Property="Background"
                                Value="Transparent"/>
                        <Setter Property="BorderThickness"
                                Value="0"/>
                        <Setter Property="BorderBrush"
                                Value="Transparent"/>
                        <Setter Property="MinHeight"
                                Value="30"/>
                        <Style.Triggers>
                                <Trigger Property="ItemsControl.AlternationIndex"
                                         Value="1">
                                        <Setter Property="Background"
                                                Value="#F5F5F5"/>
                                </Trigger>
                                <Trigger Property="IsMouseOver"
                                         Value="True">
                                        <Setter Property="Background"
                                                Value="#E8E8E8"/>
                                </Trigger>
                        </Style.Triggers>
                </Style>

                <Style x:Key="ModernDataGridCellStyle"
                       TargetType="DataGridCell">
                        <Setter Property="BorderThickness"
                                Value="0"/>
                        <Setter Property="BorderBrush"
                                Value="Transparent"/>
                        <Setter Property="Padding"
                                Value="10,5"/>
                        <Setter Property="VerticalAlignment"
                                Value="Center"/>
                        <Setter Property="HorizontalAlignment"
                                Value="Stretch"/>
                        <Setter Property="FocusVisualStyle"
                                Value="{x:Null}"/>
                        <Setter Property="Template">
                                <Setter.Value>
                                        <ControlTemplate TargetType="DataGridCell">
                                                <Border Background="{TemplateBinding Background}"
                                                        BorderBrush="{TemplateBinding BorderBrush}"
                                                        BorderThickness="{TemplateBinding BorderThickness}"
                                                        Padding="{TemplateBinding Padding}"
                                                        SnapsToDevicePixels="True">
                                                        <ContentPresenter VerticalAlignment="{TemplateBinding VerticalAlignment}"
                                                                          HorizontalAlignment="Right"
                                                                          SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}"/>
                                                </Border>
                                        </ControlTemplate>
                                </Setter.Value>
                        </Setter>
                        <Style.Triggers>
                                <Trigger Property="IsSelected"
                                         Value="True">
                                        <Setter Property="Background"
                                                Value="#D1E7FD"/>
                                        <Setter Property="Foreground"
                                                Value="#000"/>
                                </Trigger>
                        </Style.Triggers>
                </Style>

                <Style x:Key="ModernDataGridColumnHeaderStyle"
                       TargetType="DataGridColumnHeader">
                        <Setter Property="Background"
                                Value="#F5F5F5"/>
                        <Setter Property="BorderThickness"
                                Value="0,0,0,1"/>
                        <Setter Property="BorderBrush"
                                Value="#DEE2E6"/>
                        <Setter Property="Padding"
                                Value="10,8"/>
                        <Setter Property="HorizontalContentAlignment"
                                Value="Right"/>
                        <Setter Property="VerticalContentAlignment"
                                Value="Center"/>
                        <Setter Property="FontWeight"
                                Value="SemiBold"/>
                        <Setter Property="MinHeight"
                                Value="35"/>
                </Style>
        </Window.Resources>

        <Border BorderThickness="1"
                BorderBrush="#dee2e6"
                Background="White"
                CornerRadius="6">
                <DockPanel>
                        <!-- Header -->
                        <Border DockPanel.Dock="Top"
                                Background="#0078D4"
                                Height="60"
                                BorderThickness="0,0,0,1"
                                BorderBrush="White">
                                <Grid>
                                        <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="Auto"/>
                                                <ColumnDefinition Width="Auto"/>
                                                <ColumnDefinition Width="*"/>
                                        </Grid.ColumnDefinitions>

                                        <StackPanel Grid.Column="0"
                                                    Orientation="Horizontal"
                                                    Margin="20,0,0,0">
                                                <Button x:Name="MinimizeButton"
                                                        Click="MinimizeButton_Click"
                                                        Style="{StaticResource ModernButton}"
                                                        Background="Transparent"
                                                        BorderThickness="0"
                                                        Width="30"
                                                        Height="30"
                                                        Margin="0,0,5,0">
                                                        <TextBlock Text="─"
                                                                   FontSize="16"/>
                                                </Button>
                                                <Button x:Name="CloseButton"
                                                        Click="CloseButton_Click"
                                                        Style="{StaticResource ModernButton}"
                                                        Background="Transparent"
                                                        BorderThickness="0"
                                                        Width="30"
                                                        Height="30">
                                                        <TextBlock Text="✕"
                                                                   FontSize="16"/>
                                                </Button>
                                        </StackPanel>

                                        <TextBlock Grid.Column="2"
                                                   Text="طلب شراء"
                                                   Foreground="White"
                                                   FontSize="20"
                                                   FontWeight="Bold"
                                                   VerticalAlignment="Center"
                                                   HorizontalAlignment="Right"
                                                   Margin="0,0,20,0"/>
                                </Grid>
                        </Border>

                        <!-- Main Content -->
                        <Grid Margin="20">
                                <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="*"/>
                                        <RowDefinition Height="Auto"/>
                                </Grid.RowDefinitions>

                                <!-- Supplier Information Section -->
                                <Border Grid.Row="0"
                                        Style="{StaticResource ModernBorder}"
                                        Margin="0,0,0,10">
                                        <Grid>
                                                <Grid.ColumnDefinitions>
                                                        <ColumnDefinition Width="Auto"/>
                                                        <ColumnDefinition Width="*"/>
                                                        <ColumnDefinition Width="Auto"/>
                                                        <ColumnDefinition Width="*"/>
                                                        <ColumnDefinition Width="Auto"/>
                                                        <ColumnDefinition Width="*"/>
                                                </Grid.ColumnDefinitions>

                                                <TextBlock Text="المورد:"
                                                           VerticalAlignment="Center"
                                                           Margin="0,0,10,0"/>

                                                <ComboBox x:Name="SupplierSearchBox"
                                                          Grid.Column="1"
                                                          ItemsSource="{Binding SupplierSearchResults}"
                                                          SelectedItem="{Binding SelectedSupplier}"
                                                          Style="{StaticResource ModernComboBox}"
                                                          Margin="0,0,10,10"
                                                          IsDropDownOpen="{Binding HasSupplierSearchResults, Mode=OneWay}"
                                                          SelectionChanged="SupplierSearchBox_SelectionChanged"
                                                          DropDownOpened="SupplierSearchBox_DropDownOpened">
                                                        <ComboBox.Template>
                                                                <ControlTemplate TargetType="ComboBox">
                                                                        <Grid>
                                                                                <Grid.ColumnDefinitions>
                                                                                        <ColumnDefinition Width="*"/>
                                                                                        <ColumnDefinition Width="20"/>
                                                                                </Grid.ColumnDefinitions>
                                                                                <TextBox x:Name="PART_EditableTextBox"
                                                                                         Text="{Binding DataContext.SupplierSearchTerm, UpdateSourceTrigger=PropertyChanged, RelativeSource={RelativeSource AncestorType=Window}}"
                                                                                         Style="{StaticResource ModernTextBox}"
                                                                                         Margin="0"
                                                                                         GotFocus="SupplierTextBox_GotFocus"/>
                                                                                <ToggleButton x:Name="ToggleButton"
                                                                                              Grid.Column="1"
                                                                                              Background="{TemplateBinding Background}"
                                                                                              BorderBrush="{TemplateBinding BorderBrush}"
                                                                                              BorderThickness="{TemplateBinding BorderThickness}"
                                                                                              IsChecked="{Binding IsDropDownOpen, Mode=TwoWay, RelativeSource={RelativeSource TemplatedParent}}">
                                                                                        <Path Fill="Gray"
                                                                                              HorizontalAlignment="Center"
                                                                                              VerticalAlignment="Center"
                                                                                              Data="M 0 0 L 4 4 L 8 0 Z"/>
                                                                                </ToggleButton>
                                                                                <Popup x:Name="Popup"
                                                                                       Placement="Bottom"
                                                                                       IsOpen="{TemplateBinding IsDropDownOpen}"
                                                                                       AllowsTransparency="True"
                                                                                       Focusable="False"
                                                                                       PopupAnimation="Slide">
                                                                                        <Grid x:Name="DropDown"
                                                                                              SnapsToDevicePixels="True"
                                                                                              MinWidth="{TemplateBinding ActualWidth}"
                                                                                              MaxHeight="{TemplateBinding MaxDropDownHeight}">
                                                                                                <Border x:Name="DropDownBorder"
                                                                                                        Background="White"
                                                                                                        BorderThickness="1"
                                                                                                        BorderBrush="#DDDDDD">
                                                                                                        <ScrollViewer>
                                                                                                                <ItemsPresenter SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}"/>
                                                                                                        </ScrollViewer>
                                                                                                </Border>
                                                                                        </Grid>
                                                                                </Popup>
                                                                        </Grid>
                                                                </ControlTemplate>
                                                        </ComboBox.Template>
                                                        <ComboBox.ItemTemplate>
                                                                <DataTemplate>
                                                                        <StackPanel Orientation="Horizontal">
                                                                                <TextBlock Text="{Binding Name}"
                                                                                           VerticalAlignment="Center"/>
                                                                        </StackPanel>
                                                                </DataTemplate>
                                                        </ComboBox.ItemTemplate>
                                                </ComboBox>

                                                <TextBlock Grid.Column="2"
                                                           Text="تاريخ الطلب:"
                                                           VerticalAlignment="Center"
                                                           Margin="20,0,10,0"/>
                                                <DatePicker Grid.Column="3"
                                                            SelectedDate="{Binding OrderDate}"
                                                            Style="{StaticResource ModernDatePicker}"/>

                                                <TextBlock Grid.Column="4"
                                                           Text="رقم الطلب:"
                                                           VerticalAlignment="Center"
                                                           Margin="20,0,10,0"/>
                                                <TextBox Grid.Column="5"
                                                         Text="{Binding OrderNumber}"
                                                         IsReadOnly="True"
                                                         Style="{StaticResource ModernTextBox}"/>
                                        </Grid>
                                </Border>

                                <!-- Products Section -->
                                <Border Grid.Row="1"
                                        Style="{StaticResource ModernBorder}"
                                        Margin="0,0,0,10">
                                        <Grid>
                                                <Grid.RowDefinitions>
                                                        <RowDefinition Height="Auto"/>
                                                        <RowDefinition Height="*"/>
                                                </Grid.RowDefinitions>

                                                <!-- Product Search -->
                                                <Grid Grid.Row="0"
                                                      Margin="0,0,0,20">
                                                        <Grid.ColumnDefinitions>
                                                                <ColumnDefinition Width="Auto"/>
                                                                <ColumnDefinition Width="*"/>
                                                                <ColumnDefinition Width="Auto"/>
                                                                <ColumnDefinition Width="*"/>
                                                                <ColumnDefinition Width="Auto"/>
                                                        </Grid.ColumnDefinitions>

                                                        <TextBlock Text="البحث عن منتج:"
                                                                   VerticalAlignment="Center"
                                                                   Margin="0,0,10,0"/>
                                                        <ComboBox x:Name="ProductSearchBox"
                                                                  Grid.Column="1"
                                                                  ItemsSource="{Binding ProductSearchResults}"
                                                                  SelectedItem="{Binding SelectedProduct}"
                                                                  Style="{StaticResource ModernComboBox}"
                                                                  Margin="0,0,10,0"
                                                                  IsDropDownOpen="{Binding HasSearchResults, Mode=OneWay}"
                                                                  SelectionChanged="ProductSearchBox_SelectionChanged"
                                                                  DropDownOpened="ProductSearchBox_DropDownOpened">
                                                                <ComboBox.Template>
                                                                        <ControlTemplate TargetType="ComboBox">
                                                                                <Grid>
                                                                                        <Grid.ColumnDefinitions>
                                                                                                <ColumnDefinition Width="*"/>
                                                                                                <ColumnDefinition Width="20"/>
                                                                                        </Grid.ColumnDefinitions>
                                                                                        <TextBox x:Name="PART_EditableTextBox"
                                                                                                 Text="{Binding DataContext.ProductSearchTerm, UpdateSourceTrigger=PropertyChanged, RelativeSource={RelativeSource AncestorType=Window}}"
                                                                                                 Style="{StaticResource ModernTextBox}"
                                                                                                 Margin="0"
                                                                                                 GotFocus="ProductTextBox_GotFocus"/>
                                                                                        <ToggleButton x:Name="ToggleButton"
                                                                                                      Grid.Column="1"
                                                                                                      Background="{TemplateBinding Background}"
                                                                                                      BorderBrush="{TemplateBinding BorderBrush}"
                                                                                                      BorderThickness="{TemplateBinding BorderThickness}"
                                                                                                      IsChecked="{Binding IsDropDownOpen, Mode=TwoWay, RelativeSource={RelativeSource TemplatedParent}}">
                                                                                                <Path Fill="Gray"
                                                                                                      HorizontalAlignment="Center"
                                                                                                      VerticalAlignment="Center"
                                                                                                      Data="M 0 0 L 4 4 L 8 0 Z"/>
                                                                                        </ToggleButton>
                                                                                        <Popup x:Name="Popup"
                                                                                               Placement="Bottom"
                                                                                               IsOpen="{TemplateBinding IsDropDownOpen}"
                                                                                               AllowsTransparency="True"
                                                                                               Focusable="False"
                                                                                               PopupAnimation="Slide">
                                                                                                <Grid x:Name="DropDown"
                                                                                                      SnapsToDevicePixels="True"
                                                                                                      MinWidth="{TemplateBinding ActualWidth}"
                                                                                                      MaxHeight="{TemplateBinding MaxDropDownHeight}">
                                                                                                        <Border x:Name="DropDownBorder"
                                                                                                                Background="White"
                                                                                                                BorderThickness="1"
                                                                                                                BorderBrush="#DDDDDD">
                                                                                                                <ScrollViewer>
                                                                                                                        <ItemsPresenter SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}"/>
                                                                                                                </ScrollViewer>
                                                                                                        </Border>
                                                                                                </Grid>
                                                                                        </Popup>
                                                                                </Grid>
                                                                        </ControlTemplate>
                                                                </ComboBox.Template>
                                                                <ComboBox.ItemTemplate>
                                                                        <DataTemplate>
                                                                                <StackPanel Orientation="Horizontal">
                                                                                        <Image Source="{Binding ImagePath}"
                                                                                               Width="30"
                                                                                               Height="30"
                                                                                               Margin="0,0,5,0"/>
                                                                                        <TextBlock Text="{Binding Name}"
                                                                                                   VerticalAlignment="Center"/>
                                                                                </StackPanel>
                                                                        </DataTemplate>
                                                                </ComboBox.ItemTemplate>
                                                        </ComboBox>

                                                        <TextBlock Grid.Column="2"
                                                                   Text="الكمية:"
                                                                   VerticalAlignment="Center"
                                                                   Margin="20,0,10,0"/>
                                                        <TextBox Grid.Column="3"
                                                                 Text="{Binding ProductQuantity}"
                                                                 Style="{StaticResource ModernTextBox}"/>

                                                        <Button Grid.Column="4"
                                                                Command="{Binding AddProductCommand}"
                                                                Style="{StaticResource ModernButton}"
                                                                Margin="10,0,0,0">
                                                                إضافة
                                                        </Button>
                                                </Grid>

                                                <!-- Selected Products Grid -->
                                                <DataGrid x:Name="ProductsGrid"
                                                          Grid.Row="1"
                                                          ItemsSource="{Binding SelectedProducts}"
                                                          AutoGenerateColumns="False"
                                                          CanUserAddRows="False"
                                                          CanUserDeleteRows="False"
                                                          IsReadOnly="False"
                                                          GridLinesVisibility="None"
                                                          AlternationCount="2"
                                                          RowStyle="{StaticResource ModernDataGridRowStyle}"
                                                          CellStyle="{StaticResource ModernDataGridCellStyle}"
                                                          ColumnHeaderStyle="{StaticResource ModernDataGridColumnHeaderStyle}"
                                                          Background="Transparent"
                                                          BorderThickness="0">
                                                        <DataGrid.Resources>
                                                                <Style TargetType="TextBlock">
                                                                        <Setter Property="TextAlignment"
                                                                                Value="Right"/>
                                                                </Style>
                                                        </DataGrid.Resources>
                                                        <DataGrid.Columns>
                                                                <DataGridTextColumn Header="المنتج"
                                                                                    Binding="{Binding ProductName}"
                                                                                    Width="200"
                                                                                    IsReadOnly="True"/>
                                                                <DataGridTextColumn Header="الكمية"
                                                                                    Binding="{Binding Quantity, UpdateSourceTrigger=PropertyChanged, StringFormat=N2}"
                                                                                    Width="100"/>
                                                                <DataGridTemplateColumn Header="الوحدة"
                                                                                        Width="150">
                                                                        <DataGridTemplateColumn.CellTemplate>
                                                                                <DataTemplate>
                                                                                        <ComboBox ItemsSource="{Binding AvailableUnits}"
                                                                                                  SelectedValue="{Binding SelectedUnitId, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}"
                                                                                                  SelectedValuePath="Id"
                                                                                                  DisplayMemberPath="Name"
                                                                                                  Style="{StaticResource ModernComboBox}"
                                                                                                  HorizontalAlignment="Stretch"
                                                                                                  VerticalAlignment="Center"/>
                                                                                </DataTemplate>
                                                                        </DataGridTemplateColumn.CellTemplate>
                                                                </DataGridTemplateColumn>
                                                                <DataGridTextColumn Header="السعر"
                                                                                    Binding="{Binding Price, UpdateSourceTrigger=PropertyChanged, StringFormat=N2}"
                                                                                    Width="100"/>
                                                                <DataGridTextColumn Header="الإجمالي"
                                                                                    Binding="{Binding TotalPrice, Mode=OneWay, StringFormat=N2}"
                                                                                    Width="100"
                                                                                    IsReadOnly="True"/>
                                                                <DataGridTemplateColumn Header=""
                                                                                        Width="100">
                                                                        <DataGridTemplateColumn.CellTemplate>
                                                                                <DataTemplate>
                                                                                        <Button Style="{StaticResource ModernButton}"
                                                                                                Background="#dc3545"
                                                                                                Foreground="White"
                                                                                                Command="{Binding DataContext.RemoveProductCommand, RelativeSource={RelativeSource AncestorType=DataGrid}}"
                                                                                                CommandParameter="{Binding}"
                                                                                                ToolTip="حذف"
                                                                                                Width="30"
                                                                                                Height="30"
                                                                                                Padding="0"
                                                                                                VerticalAlignment="Center"
                                                                                                HorizontalAlignment="Center">
                                                                                                <TextBlock Text="🗑️"
                                                                                                           FontSize="14"
                                                                                                           VerticalAlignment="Center"
                                                                                                           HorizontalAlignment="Center"/>
                                                                                        </Button>
                                                                                </DataTemplate>
                                                                        </DataGridTemplateColumn.CellTemplate>
                                                                </DataGridTemplateColumn>
                                                        </DataGrid.Columns>
                                                </DataGrid>
                                        </Grid>
                                </Border>

                                <!-- Footer Buttons -->
                                <StackPanel Grid.Row="2"
                                            Orientation="Horizontal"
                                            HorizontalAlignment="Center"
                                            Margin="0,10,0,0">
                                        <Button Command="{Binding SaveOrderCommand}"
                                                Style="{StaticResource ModernButton}"
                                                Margin="0,0,10,0">
                                                حفظ
                                        </Button>
                                        <Button Command="{Binding CancelCommand}"
                                                Style="{StaticResource ModernButton}"
                                                Background="#6C757D">
                                                إلغاء
                                        </Button>
                                </StackPanel>
                        </Grid>
                </DockPanel>
        </Border>
</Window>