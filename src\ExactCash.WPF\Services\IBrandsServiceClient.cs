﻿using ExactCash.Application.DTOs;
using ExactCash.Domain.Entities;

namespace ExactCash.WPF.Services
{
    public interface IBrandsServiceClient
    {
        /// <summary>
        /// Gets a brand by its ID.
        /// </summary>
        Task<BrandDto> GetBrandByIdAsync(int id);

        /// <summary>
        /// Gets all brands with optional filtering.
        /// </summary>
        Task<IEnumerable<BrandDto>> GetAllBrandsAsync();

        /// <summary>
        /// Creates a new brand.
        /// </summary>
        Task<BrandDto> CreateBrandAsync(BrandDto brandDto);

        /// <summary>
        /// Updates an existing brand.
        /// </summary>
        Task UpdateBrandAsync(BrandDto brandDto);

        /// <summary>
        /// Deletes a brand.
        /// </summary>
        Task DeleteBrandAsync(int id);
    }
}
