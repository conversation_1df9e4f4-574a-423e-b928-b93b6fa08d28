﻿using ExactCash.Domain.Common;
#nullable disable

namespace ExactCash.Domain.Entities
{
    /// <summary>
    /// Represents a payment made for a sale transaction.
    /// </summary>
    public class Payment : BaseEntity
    {
        /// <summary>
        /// Foreign key to the sale the payment is for.
        /// </summary>
        public int SaleId { get; set; }

        /// <summary>
        /// Navigation property to the sale that this payment is linked to.
        /// </summary>
        public Sale Sale { get; set; }

        /// <summary>
        /// The payment method (e.g., Cash, Credit Card, Mobile Payment).
        /// </summary>
        public string PaymentMethod { get; set; }

        /// <summary>
        /// The amount paid by the customer.
        /// </summary>
        public decimal Amount { get; set; }

        /// <summary>
        /// Date and time when the payment was made.
        /// </summary>
        public DateTime PaymentDate { get; set; }
    }
}
