using System.ComponentModel.DataAnnotations;
#nullable disable

namespace ExactCash.WASM.Application.DTOs
{
    public class RoleClaimDto
    {
        public int Id { get; set; }
        public string RoleId { get; set; }
        public string ClaimType { get; set; }
        public string ClaimValue { get; set; }
    }

    public class CreateRoleClaimDto
    {
        [Required]
        public string RoleId { get; set; }

        [Required]
        public string ClaimType { get; set; }

        [Required]
        public string ClaimValue { get; set; }
    }

    public class UpdateRoleClaimDto
    {
        [Required]
        public string ClaimType { get; set; }

        [Required]
        public string ClaimValue { get; set; }
    }

    public class RoleClaimsDto
    {
        public string RoleId { get; set; }
        public List<CreateRoleClaimDto> Claims { get; set; }
    }
}