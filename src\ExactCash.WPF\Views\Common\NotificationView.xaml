<UserControl x:Class="ExactCash.WPF.Views.Common.NotificationView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:local="clr-namespace:ExactCash.WPF.Views.Common"
             mc:Ignorable="d"
             d:DesignHeight="60"
        d:DesignWidth="300">
    <Grid>
        <Border Background="{Binding BackgroundColor}"
                CornerRadius="5"
                Margin="10">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- Icon -->
                <TextBlock Grid.Column="0"
                           Text="{Binding Icon}"
                           FontFamily="Segoe MDL2 Assets"
                           FontSize="24"
                           Foreground="White"
                           Margin="15,0"
                           VerticalAlignment="Center"/>

                <!-- Message -->
                <TextBlock Grid.Column="1"
                           Text="{Binding Message}"
                           FontFamily="Droid Arabic Kufi"
                           FontSize="14"
                           Foreground="White"
                           TextWrapping="Wrap"
                           VerticalAlignment="Center"/>

                <!-- Close Button -->
                <Button Grid.Column="2"
                        Content="✕"
                        Command="{Binding CloseCommand}"
                        Style="{StaticResource NotificationCloseButtonStyle}"
                        Margin="10,0"/>
            </Grid>
        </Border>
    </Grid>
</UserControl> 