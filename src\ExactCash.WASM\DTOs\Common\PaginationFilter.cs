﻿using ExactCash.WASM.Enums;

namespace ExactCash.WASM.Application.DTOs.Common
{
    public class PaginationFilter
    {
        #region Props.

        /// <summary>
        /// PageNumber
        /// </summary>
        public int PageNumber { get; set; }

        /// <summary>
        /// PageSize
        /// </summary>
        public int PageSize { get; set; }

        /// <summary>
        /// SortOrder
        /// </summary>
        public SortOrder SortOrder { get; set; }

        /// <summary>
        /// SortField
        /// </summary>
        public string SortField { get; set; }

        #endregion Props.

        #region Ctor.

        /// <summary>
        /// constructor
        /// </summary>
        public PaginationFilter()
        {
            PageNumber = 1;
            PageSize = 10;
            SortOrder = SortOrder.Asc;
            SortField = "";
        }

        /// <summary>
        /// parameterized constructor
        /// </summary>
        /// <param name="pageNumber"></param>
        /// <param name="pageSize"></param>
        /// <param name="sortOrder"></param>
        /// <param name="sortField"></param>
        public PaginationFilter(int pageNumber, int pageSize, SortOrder sortOrder, string sortField)
        {
            PageNumber = pageNumber < 1 ? 1 : pageNumber;
            PageSize = pageSize > 10 ? 10 : pageSize;
            SortOrder = sortOrder;
            SortField = sortField;
        }

        #endregion Ctor.
    }
}
