using ExactCash.Domain.Common;
using System.Text.Json.Serialization;

namespace ExactCash.Domain.Entities
{
    /// <summary>
    /// Represents a payment made against a credit sale transaction.
    /// </summary>
    public class CreditPayment : BaseEntity
    {
        /// <summary>
        /// Foreign key to the credit sale transaction.
        /// </summary>
        public int CreditSaleTransactionId { get; set; }

        /// <summary>
        /// Navigation property to the credit sale transaction.
        /// </summary>
        [JsonIgnore]
        public CreditSaleTransaction CreditSaleTransaction { get; set; }

        /// <summary>
        /// The amount paid.
        /// </summary>
        public decimal Amount { get; set; }

        /// <summary>
        /// The payment method used (Cash, Card, Bank Transfer, etc.).
        /// </summary>
        public string PaymentMethod { get; set; }

        /// <summary>
        /// Date and time when the payment was made.
        /// </summary>
        public DateTime PaymentDate { get; set; }

        /// <summary>
        /// Reference number for the payment (check number, transaction ID, etc.).
        /// </summary>
        public string? ReferenceNumber { get; set; }

        /// <summary>
        /// Notes or comments about this payment.
        /// </summary>
        public string? Notes { get; set; }

        /// <summary>
        /// The user who collected this payment.
        /// </summary>
        public string? CollectedBy { get; set; }

        #region Constructors
        public CreditPayment()
        {
            PaymentDate = DateTime.UtcNow;
        }

        public CreditPayment(int creditSaleTransactionId, decimal amount, string paymentMethod, string notes = null)
        {
            CreditSaleTransactionId = creditSaleTransactionId;
            Amount = amount;
            PaymentMethod = paymentMethod;
            Notes = notes;
            PaymentDate = DateTime.UtcNow;
            CreationDate = DateTime.UtcNow;
        }

        public CreditPayment(int creditSaleTransactionId, decimal amount, string paymentMethod,
            string referenceNumber, string collectedBy, string notes = null)
        {
            CreditSaleTransactionId = creditSaleTransactionId;
            Amount = amount;
            PaymentMethod = paymentMethod;
            ReferenceNumber = referenceNumber;
            CollectedBy = collectedBy;
            Notes = notes;
            PaymentDate = DateTime.UtcNow;
            CreationDate = DateTime.UtcNow;
        }
        #endregion

        #region Methods
        /// <summary>
        /// Updates the payment details.
        /// </summary>
        /// <param name="amount">New payment amount</param>
        /// <param name="paymentMethod">New payment method</param>
        /// <param name="referenceNumber">New reference number</param>
        /// <param name="notes">New notes</param>
        public void UpdatePayment(decimal amount, string paymentMethod, string referenceNumber = null, string notes = null)
        {
            Amount = amount;
            PaymentMethod = paymentMethod;
            ReferenceNumber = referenceNumber;
            Notes = notes;
            LastUpdatedDate = DateTime.UtcNow;
        }

        /// <summary>
        /// Validates the payment data.
        /// </summary>
        /// <returns>True if valid, false otherwise</returns>
        public bool IsValid()
        {
            return Amount > 0 && !string.IsNullOrWhiteSpace(PaymentMethod);
        }
        #endregion
    }
}
