@page "/"
@inject IApiService ApiService
@inject ILoadingService LoadingService
@inject AppState AppState
@attribute [Authorize]

<PageTitle>لوحة التحكم - ExactCash</PageTitle>

<div class="dashboard-container">
    <div class="row">
        <!-- Statistics Cards -->
        <div class="col-md-3 mb-4">
            <div class="card stat-card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">مبيعات اليوم</h6>
                            <h3 class="mb-0">@todayStats.TotalSales.ToString("N2") ج.م</h3>
                        </div>
                        <div class="stat-icon">
                            <i class="bi bi-cart-fill"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-3 mb-4">
            <div class="card stat-card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">عدد الفواتير</h6>
                            <h3 class="mb-0">@todayStats.InvoiceCount</h3>
                        </div>
                        <div class="stat-icon">
                            <i class="bi bi-receipt"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-3 mb-4">
            <div class="card stat-card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">المنتجات</h6>
                            <h3 class="mb-0">@inventoryStats.TotalProducts</h3>
                        </div>
                        <div class="stat-icon">
                            <i class="bi bi-box-seam"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-3 mb-4">
            <div class="card stat-card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">العملاء</h6>
                            <h3 class="mb-0">@customerStats.TotalCustomers</h3>
                        </div>
                        <div class="stat-icon">
                            <i class="bi bi-people-fill"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Recent Sales -->
        <div class="col-md-8 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">آخر المبيعات</h5>
                </div>
                <div class="card-body">
                    @if (recentSales?.Any() == true)
                    {
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>رقم الفاتورة</th>
                                        <th>العميل</th>
                                        <th>المبلغ</th>
                                        <th>التاريخ</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var sale in recentSales.Take(5))
                                    {
                                        <tr>
                                            <td>@sale.InvoiceNumber</td>
                                            <td>@sale.CustomerName</td>
                                            <td>@sale.TotalAmount.ToString("N2") ج.م</td>
                                            <td>@sale.SaleDate.ToString("yyyy/MM/dd HH:mm")</td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                    }
                    else
                    {
                        <p class="text-muted">لا توجد مبيعات حديثة</p>
                    }
                </div>
            </div>
        </div>

        <!-- Low Stock Alert -->
        <div class="col-md-4 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">تنبيه المخزون المنخفض</h5>
                </div>
                <div class="card-body">
                    @if (lowStockProducts?.Any() == true)
                    {
                        @foreach (var product in lowStockProducts.Take(5))
                        {
                            <div class="alert alert-warning py-2 mb-2">
                                <small>
                                    <strong>@product.Name</strong><br />
                                    الكمية: @product.StockQuantity
                                </small>
                            </div>
                        }
                    }
                    else
                    {
                        <p class="text-success">جميع المنتجات متوفرة بكميات كافية</p>
                    }
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">إجراءات سريعة</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-2 col-sm-4 col-6 mb-3">
                            <a href="/sales" class="btn btn-outline-primary w-100 h-100 d-flex flex-column align-items-center justify-content-center">
                                <i class="bi bi-cart-plus fs-1 mb-2"></i>
                                <span>بيع جديد</span>
                            </a>
                        </div>
                        <div class="col-md-2 col-sm-4 col-6 mb-3">
                            <a href="/products/add" class="btn btn-outline-success w-100 h-100 d-flex flex-column align-items-center justify-content-center">
                                <i class="bi bi-plus-circle fs-1 mb-2"></i>
                                <span>منتج جديد</span>
                            </a>
                        </div>
                        <div class="col-md-2 col-sm-4 col-6 mb-3">
                            <a href="/customers/add" class="btn btn-outline-info w-100 h-100 d-flex flex-column align-items-center justify-content-center">
                                <i class="bi bi-person-plus fs-1 mb-2"></i>
                                <span>عميل جديد</span>
                            </a>
                        </div>
                        <div class="col-md-2 col-sm-4 col-6 mb-3">
                            <a href="/purchase-orders/add" class="btn btn-outline-warning w-100 h-100 d-flex flex-column align-items-center justify-content-center">
                                <i class="bi bi-clipboard-plus fs-1 mb-2"></i>
                                <span>أمر شراء</span>
                            </a>
                        </div>
                        <div class="col-md-2 col-sm-4 col-6 mb-3">
                            <a href="/reports" class="btn btn-outline-secondary w-100 h-100 d-flex flex-column align-items-center justify-content-center">
                                <i class="bi bi-graph-up fs-1 mb-2"></i>
                                <span>التقارير</span>
                            </a>
                        </div>
                        <div class="col-md-2 col-sm-4 col-6 mb-3">
                            <a href="/settings" class="btn btn-outline-dark w-100 h-100 d-flex flex-column align-items-center justify-content-center">
                                <i class="bi bi-gear fs-1 mb-2"></i>
                                <span>الإعدادات</span>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@code {
    private DashboardStats todayStats = new();
    private InventoryStats inventoryStats = new();
    private CustomerStats customerStats = new();
    private List<RecentSale>? recentSales;
    private List<LowStockProduct>? lowStockProducts;

    protected override async Task OnInitializedAsync()
    {
        await LoadDashboardData();
    }

    private async Task LoadDashboardData()
    {
        try
        {
            LoadingService.Show("جاري تحميل بيانات لوحة التحكم...");

            // Load all dashboard data in parallel
            var tasks = new[]
            {
                LoadTodayStats(),
                LoadInventoryStats(),
                LoadCustomerStats(),
                LoadRecentSales(),
                LoadLowStockProducts()
            };

            await Task.WhenAll(tasks);
        }
        catch (Exception ex)
        {
            // Handle error
            Console.WriteLine($"Error loading dashboard data: {ex.Message}");
        }
        finally
        {
            LoadingService.Hide();
        }
    }

    private async Task LoadTodayStats()
    {
        // Implementation for loading today's sales statistics
        todayStats = new DashboardStats
        {
            TotalSales = 15750.50m,
            InvoiceCount = 23
        };
    }

    private async Task LoadInventoryStats()
    {
        inventoryStats = new InventoryStats
        {
            TotalProducts = 1250
        };
    }

    private async Task LoadCustomerStats()
    {
        customerStats = new CustomerStats
        {
            TotalCustomers = 456
        };
    }

    private async Task LoadRecentSales()
    {
        // Mock data - replace with actual API call
        recentSales = new List<RecentSale>();
    }

    private async Task LoadLowStockProducts()
    {
        // Mock data - replace with actual API call
        lowStockProducts = new List<LowStockProduct>();
    }

    public class DashboardStats
    {
        public decimal TotalSales { get; set; }
        public int InvoiceCount { get; set; }
    }

    public class InventoryStats
    {
        public int TotalProducts { get; set; }
    }

    public class CustomerStats
    {
        public int TotalCustomers { get; set; }
    }

    public class RecentSale
    {
        public string InvoiceNumber { get; set; } = string.Empty;
        public string CustomerName { get; set; } = string.Empty;
        public decimal TotalAmount { get; set; }
        public DateTime SaleDate { get; set; }
    }

    public class LowStockProduct
    {
        public string Name { get; set; } = string.Empty;
        public int StockQuantity { get; set; }
    }
}
