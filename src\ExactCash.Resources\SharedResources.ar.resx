<?xml version="1.0" encoding="utf-8"?>
<root>
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  
  <!-- Authentication & Login -->
  <data name="Login" xml:space="preserve">
    <value>تسجيل الدخول</value>
  </data>
  <data name="Username" xml:space="preserve">
    <value>اسم المستخدم</value>
  </data>
  <data name="Password" xml:space="preserve">
    <value>كلمة المرور</value>
  </data>
  <data name="RememberMe" xml:space="preserve">
    <value>تذكرني</value>
  </data>
  <data name="LoginButton" xml:space="preserve">
    <value>دخول</value>
  </data>
  <data name="LoginSuccessful" xml:space="preserve">
    <value>تم تسجيل الدخول بنجاح</value>
  </data>
  <data name="LoginError" xml:space="preserve">
    <value>حدث خطأ أثناء تسجيل الدخول</value>
  </data>
  <data name="InvalidCredentials" xml:space="preserve">
    <value>اسم المستخدم أو كلمة المرور غير صحيحة</value>
  </data>
  
  <!-- Common UI -->
  <data name="Loading" xml:space="preserve">
    <value>جاري التحميل...</value>
  </data>
  <data name="Save" xml:space="preserve">
    <value>حفظ</value>
  </data>
  <data name="Cancel" xml:space="preserve">
    <value>إلغاء</value>
  </data>
  <data name="Delete" xml:space="preserve">
    <value>حذف</value>
  </data>
  <data name="Edit" xml:space="preserve">
    <value>تعديل</value>
  </data>
  <data name="Add" xml:space="preserve">
    <value>إضافة</value>
  </data>
  <data name="Search" xml:space="preserve">
    <value>بحث</value>
  </data>
  <data name="Clear" xml:space="preserve">
    <value>مسح</value>
  </data>
  <data name="Close" xml:space="preserve">
    <value>إغلاق</value>
  </data>
  <data name="Yes" xml:space="preserve">
    <value>نعم</value>
  </data>
  <data name="No" xml:space="preserve">
    <value>لا</value>
  </data>
  <data name="OK" xml:space="preserve">
    <value>موافق</value>
  </data>
  
  <!-- Navigation -->
  <data name="Dashboard" xml:space="preserve">
    <value>لوحة التحكم</value>
  </data>
  <data name="Sales" xml:space="preserve">
    <value>المبيعات</value>
  </data>
  <data name="Products" xml:space="preserve">
    <value>المنتجات</value>
  </data>
  <data name="Customers" xml:space="preserve">
    <value>العملاء</value>
  </data>
  <data name="Suppliers" xml:space="preserve">
    <value>الموردين</value>
  </data>
  <data name="Reports" xml:space="preserve">
    <value>التقارير</value>
  </data>
  <data name="Settings" xml:space="preserve">
    <value>الإعدادات</value>
  </data>
  <data name="Logout" xml:space="preserve">
    <value>تسجيل الخروج</value>
  </data>
  
  <!-- Messages -->
  <data name="Success" xml:space="preserve">
    <value>نجح</value>
  </data>
  <data name="Error" xml:space="preserve">
    <value>خطأ</value>
  </data>
  <data name="Warning" xml:space="preserve">
    <value>تحذير</value>
  </data>
  <data name="Information" xml:space="preserve">
    <value>معلومات</value>
  </data>
  <data name="OperationSuccessful" xml:space="preserve">
    <value>تمت العملية بنجاح</value>
  </data>
  <data name="OperationFailed" xml:space="preserve">
    <value>فشلت العملية</value>
  </data>
  
</root>
