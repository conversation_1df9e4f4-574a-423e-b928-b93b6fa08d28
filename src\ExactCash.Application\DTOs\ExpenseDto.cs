﻿using ExactCash.Application.DTOs.Common;
using ExactCash.Domain.Entities;
#nullable disable

namespace ExactCash.Application.DTOs
{
    /// <summary>
    /// Expense
    /// </summary>
    public class ExpenseDto : BaseEntityDto
    {
        /// <summary>
        /// ExpenseDate
        /// </summary>
        public DateTime ExpenseDate { get; set; }
        /// <summary>
        /// Amount
        /// </summary>
        public decimal Amount { get; set; }
        /// <summary>
        /// Description
        /// </summary>
        public string Description { get; set; }
        /// <summary>
        /// CategoryId
        /// </summary>
        public int? CategoryId { get; set; }
        /// <summary>
        /// Category
        /// </summary>
        public ExpenseCategoryDto Category { get; set; }

        /// <summary>
        /// CategoryName
        /// </summary>
        public string CategoryName { get; set; }

        /// <summary>
        /// PaymentMethodId
        /// </summary>
        public int? PaymentMethodId { get; set; }
        /// <summary>
        /// PaymentMethod
        /// </summary>
        public PaymentMethod PaymentMethod { get; set; }
        /// <summary>
        /// ReferenceNumber
        /// </summary>
        public string ReferenceNumber { get; set; }
        /// <summary>
        /// IsDeleted
        /// </summary>
        public bool IsDeleted { get; set; }
    }
}
