@model FDIN.Web.Data.Sale

@{
    ViewData["Title"] = $"Invoice {Model.InvoiceNumber}";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h4 class="mb-0">Invoice Details</h4>
                    <div class="btn-group">
                        <a href="@Url.Action("Download", new { id = Model.Id })" class="btn btn-outline-primary">
                            <i class="fas fa-download"></i> Download
                        </a>
                        <button onclick="window.print()" class="btn btn-outline-secondary">
                            <i class="fas fa-print"></i> Print
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row mb-4">
                        <div class="col-sm-6">
                            <h6 class="mb-3">From:</h6>
                            <div>
                                <strong>@Model.StoreName</strong><br>
                                Invoice #: @Model.InvoiceNumber<br>
                                Date: @Model.SaleDate.ToString("MMM dd, yyyy")<br>
                                Status: <span class="badge bg-@(Model.PaidAmount >= Model.TotalAmount ? "success" : Model.PaidAmount > 0 ? "warning" : "danger")">
                                    @(Model.PaidAmount >= Model.TotalAmount ? "Paid" : Model.PaidAmount > 0 ? "Partial" : "Pending")
                                </span>
                            </div>
                        </div>
                        <div class="col-sm-6">
                            <h6 class="mb-3">To:</h6>
                            <div>
                                <strong>@Model.User.FullName</strong><br>
                                Phone: @Model.User.Phone<br>
                                Email: @Model.User.Email
                            </div>
                        </div>
                    </div>

                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Item</th>
                                    <th>Unit</th>
                                    <th class="text-end">Quantity</th>
                                    <th class="text-end">Price</th>
                                    <th class="text-end">Discount</th>
                                    <th class="text-end">Total</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach (var item in Model.SaleItems)
                                {
                                        <tr>
                                            <td>@item.ProductName</td>
                                            <td>@item.UnitName</td>
                                            <td class="text-end">@item.Quantity</td>
                                            <td class="text-end">@item.Price.ToString("C")</td>
                                            <td class="text-end">@item.Discount.ToString("C")</td>
                                            <td class="text-end">@((item.Price * item.Quantity - item.Discount).ToString("C"))</td>
                                        </tr>
                                }
                            </tbody>
                            <tfoot>
                                <tr>
                                    <td colspan="5" class="text-end"><strong>Subtotal:</strong></td>
                                    <td class="text-end">@Model.TotalAmount.ToString("C")</td>
                                </tr>
                                <tr>
                                    <td colspan="5" class="text-end"><strong>Discount:</strong></td>
                                    <td class="text-end">@Model.DiscountAmount.ToString("C")</td>
                                </tr>
                                <tr>
                                    <td colspan="5" class="text-end"><strong>Tax:</strong></td>
                                    <td class="text-end">@Model.TaxAmount.ToString("C")</td>
                                </tr>
                                <tr>
                                    <td colspan="5" class="text-end"><strong>Total:</strong></td>
                                    <td class="text-end"><strong>@Model.NetAmount.ToString("C")</strong></td>
                                </tr>
                                <tr>
                                    <td colspan="5" class="text-end"><strong>Paid Amount:</strong></td>
                                    <td class="text-end">@Model.PaidAmount.ToString("C")</td>
                                </tr>
                                <tr>
                                    <td colspan="5" class="text-end"><strong>Remaining Amount:</strong></td>
                                    <td class="text-end">@Model.RemainingAmount.ToString("C")</td>
                                </tr>
                            </tfoot>
                        </table>
                    </div>

                    @if (!string.IsNullOrEmpty(Model.Notes))
                    {
                            <div class="mt-4">
                                <h6>Notes:</h6>
                                <p class="text-muted">@Model.Notes</p>
                            </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
        <script>
            // Add any JavaScript functionality here
        </script>
}