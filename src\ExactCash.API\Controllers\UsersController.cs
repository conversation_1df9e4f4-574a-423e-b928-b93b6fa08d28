using ExactCash.Application.DTOs;
using ExactCash.Application.DTOs.Common;
using ExactCash.Application.Interfaces;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
#nullable disable

namespace ExactCash.API.Controllers
{
    [Authorize]
    [ApiController]
    [Route("api/[controller]")]
    public class UsersController : ControllerBase
    {
        private readonly IUserService _userService;

        public UsersController(IUserService userService)
        {
            _userService = userService;
        }

        [HttpGet]
        public async Task<IActionResult> GetAllUsers([FromQuery]string email,[FromQuery] string username,[FromQuery] string phoneNumber, [FromQuery] PaginationFilter pagination)
        {
            var result = await _userService.GetAllUsersAsync(email,username,phoneNumber, pagination);
            return Ok(result);
        }

        [HttpGet("{id}")]
        public async Task<IActionResult> GetUser(string id)
        {
            var user = await _userService.GetUserByIdAsync(id);
            if (user == null)
                return NotFound();

            return Ok(user);
        }

        [HttpPost]
        public async Task<IActionResult> CreateUser([FromBody] CreateUserDto userDto)
        {
            var result = await _userService.CreateUserAsync(userDto);
            if (!result.Succeeded)
                return BadRequest(result.Errors);

            return Ok(result);
        }

        [HttpPut("{id}")]
        public async Task<IActionResult> UpdateUser(string id, [FromBody] UpdateUserDto userDto)
        {
            var result = await _userService.UpdateUserAsync(id, userDto);
            if (!result.Succeeded)
                return BadRequest(result.Errors);

            return NoContent();
        }

        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteUser(string id)
        {
            var result = await _userService.DeleteUserAsync(id);
            if (!result.Succeeded)
                return BadRequest(result.Errors);

            return NoContent();
        }

        [HttpPost("{id}/change-password")]
        public async Task<IActionResult> ChangePassword(string id, [FromBody] ChangePasswordDto passwordDto)
        {
            var result = await _userService.ChangePasswordAsync(id, passwordDto);
            if (!result.Succeeded)
                return BadRequest(result.Errors);

            return NoContent();
        }

        [HttpPost("reset-password")]
        [AllowAnonymous]
        public async Task<IActionResult> ResetPassword([FromBody] ResetPasswordDto resetPasswordDto)
        {
            var result = await _userService.ResetPasswordAsync(resetPasswordDto);
            if (!result.Succeeded)
                return BadRequest(result.Errors);

            return NoContent();
        }

        [HttpPost("forgot-password")]
        [AllowAnonymous]
        public async Task<IActionResult> ForgotPassword([FromQuery] string email)
        {
            var token = await _userService.GeneratePasswordResetTokenAsync(email);
            if (token == null)
                return NotFound();

            // TODO: Send email with token
            return Ok(new { token });
        }

        [HttpPost("{id}/lock")]
        public async Task<IActionResult> LockUser(string id)
        {
            var result = await _userService.LockUserAsync(id);
            if (!result.Succeeded)
                return BadRequest(result.Errors);

            return NoContent();
        }

        [HttpPost("{id}/unlock")]
        public async Task<IActionResult> UnlockUser(string id)
        {
            var result = await _userService.UnlockUserAsync(id);
            if (!result.Succeeded)
                return BadRequest(result.Errors);

            return NoContent();
        }

        [HttpPost("{id}/confirm-email")]
        [AllowAnonymous]
        public async Task<IActionResult> ConfirmEmail(string id, [FromQuery] string token)
        {
            var result = await _userService.ConfirmEmailAsync(id, token);
            if (!result.Succeeded)
                return BadRequest(result.Errors);

            return NoContent();
        }

        [HttpGet("{id}/email-confirmation-token")]
        public async Task<IActionResult> GetEmailConfirmationToken(string id)
        {
            var token = await _userService.GenerateEmailConfirmationTokenAsync(id);
            if (token == null)
                return NotFound();

            return Ok(new { token });
        }
    }
}