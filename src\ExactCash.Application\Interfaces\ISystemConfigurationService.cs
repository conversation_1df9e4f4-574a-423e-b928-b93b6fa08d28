using ExactCash.Application.DTOs;
using ExactCash.Application.DTOs.Common;
using ExactCash.Application.Responses;
using System.Threading.Tasks;

namespace ExactCash.Application.Interfaces
{
    public interface ISystemConfigurationService
    {
        Task<SystemConfigurationDto> GetSystemConfigurationByIdAsync(int id);
        Task<PagedResponse<SystemConfigurationDto>> GetAllSystemConfigurationsAsync(string key, string category, PaginationFilter pagination);

        Task<List<SystemConfigurationDto>> GetAllSystemConfigurationsAsync();

        Task<SystemConfigurationDto> CreateSystemConfigurationAsync(SystemConfigurationDto configurationDto);
        Task UpdateSystemConfigurationAsync(SystemConfigurationDto configurationDto);
        Task DeleteSystemConfigurationAsync(int id);

        /// <summary>
        /// Loads currency configuration and applies it to CurrencyHelper
        /// </summary>
        Task LoadCurrencyConfigurationAsync();

        /// <summary>
        /// Gets currency configuration settings
        /// </summary>
        Task<Dictionary<string, string>> GetCurrencySettingsAsync();
    }
}