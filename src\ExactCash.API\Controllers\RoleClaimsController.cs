using ExactCash.Application.DTOs;
using ExactCash.Application.Interfaces;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
#nullable disable

namespace ExactCash.API.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize(Roles = "Admin")]
    public class RoleClaimsController : ControllerBase
    {
        private readonly IRoleClaimService _roleClaimService;

        public RoleClaimsController(IRoleClaimService roleClaimService)
        {
            _roleClaimService = roleClaimService;
        }

        [HttpGet("{id}")]
        public async Task<IActionResult> GetRoleClaimById(int id)
        {
            var result = await _roleClaimService.GetRoleClaimByIdAsync(id);
            if (result == null)
                return NotFound();

            return Ok(result);
        }

        [HttpGet("role/{roleId}")]
        public async Task<IActionResult> GetRoleClaimsByRoleId(string roleId)
        {
            var result = await _roleClaimService.GetRoleClaimsByRoleIdAsync(roleId);
            return Ok(result);
        }

        [HttpPost]
        public async Task<IActionResult> CreateRoleClaim([FromBody] CreateRoleClaimDto roleClaimDto)
        {
            var result = await _roleClaimService.CreateRoleClaimAsync(roleClaimDto);
            if (result == null)
                return BadRequest(new { message = "Failed to create role claim" });

            return CreatedAtAction(nameof(GetRoleClaimById), new { id = result.Id }, result);
        }

        [HttpPut("{id}")]
        public async Task<IActionResult> UpdateRoleClaim(int id, [FromBody] UpdateRoleClaimDto roleClaimDto)
        {
            var result = await _roleClaimService.UpdateRoleClaimAsync(id, roleClaimDto);
            if (result == null)
                return NotFound();

            return Ok(result);
        }

        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteRoleClaim(int id)
        {
            var result = await _roleClaimService.DeleteRoleClaimAsync(id);
            if (!result)
                return BadRequest(new { message = "Failed to delete role claim" });

            return Ok(new { message = "Role claim deleted successfully" });
        }

        [HttpPost("add-claims")]
        public async Task<IActionResult> AddClaimsToRole([FromBody] RoleClaimsDto roleClaimsDto)
        {
            var result = await _roleClaimService.AddClaimsToRoleAsync(roleClaimsDto);
            if (!result)
                return BadRequest(new { message = "Failed to add claims to role" });

            return Ok(new { message = "Claims added to role successfully" });
        }

        [HttpPost("remove-claims")]
        public async Task<IActionResult> RemoveClaimsFromRole([FromBody] RoleClaimsDto roleClaimsDto)
        {
            var result = await _roleClaimService.RemoveClaimsFromRoleAsync(roleClaimsDto);
            if (!result)
                return BadRequest(new { message = "Failed to remove claims from role" });

            return Ok(new { message = "Claims removed from role successfully" });
        }

        [HttpGet("claim-types")]
        public async Task<IActionResult> GetRoleClaimTypes()
        {
            var result = await _roleClaimService.GetRoleClaimTypesAsync();
            return Ok(result);
        }

        [HttpGet("claim-values/{claimType}")]
        public async Task<IActionResult> GetRoleClaimValues(string claimType)
        {
            var result = await _roleClaimService.GetRoleClaimValuesAsync(claimType);
            return Ok(result);
        }

        [HttpGet("check-claim/{roleId}/{claimType}/{claimValue}")]
        public async Task<IActionResult> HasClaim(string roleId, string claimType, string claimValue)
        {
            var result = await _roleClaimService.HasClaimAsync(roleId, claimType, claimValue);
            return Ok(result);
        }
    }
}