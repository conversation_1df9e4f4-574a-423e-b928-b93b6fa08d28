﻿using ExactCash.Domain.Common;
using System.Text.Json.Serialization;
#nullable disable

namespace ExactCash.Domain.Entities
{
    /// <summary>
    /// Represents a product category (e.g., Electronics, Beverages).
    /// </summary>
    public class Category : BaseEntity
    {
        /// <summary>
        /// The name of the category.
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// Navigation property for the products that belong to this category.
        /// </summary>
        [JsonIgnore]
        public ICollection<Product> Products { get; set; } = new List<Product>();
    }

}
