using System;
using System.Threading.Tasks;
using System.Windows.Documents;
using ExactCash.Application.DTOs;
using ExactCash.Application.Responses;
using ExactCash.WPF.Services;
using System.Collections.Generic;
using ExactCash.WPF.Models;
using ExactCash.Domain.Entities;
using ExactCash.Application.DTOs.Common;
using ExactCash.Infrastructure.Migrations;
using ZXing.QrCode.Internal;
#nullable disable

namespace ExactCash.WPF.Services
{
    public class CustomerService : ICustomerService
    {
        private readonly HttpService _httpService;
        private const string BaseUrl = "api/customers";

        public CustomerService(string baseUrl)
        {
            _httpService = new HttpService(baseUrl);
        }

        public async Task<Customer> GetCustomerByIdAsync(int id)
        {
            try
            {
                return await _httpService.GetAsync<Customer>($"{BaseUrl}/{id}");
            }
            catch (Exception ex)
            {
                throw new Exception($"Error getting customer: {ex.Message}");
            }
        }

        public async Task<Customer> SearchCustomersAsync(string searchTerm)
        {
            if (string.IsNullOrWhiteSpace(searchTerm))
                return null;

            try
            {
                var url = $"{BaseUrl}/search?term={searchTerm}";
                var customer = await _httpService.GetAsync<Customer>(url);
                return customer;
            }
            catch (Exception ex)
            {
                throw new Exception($"Error searching customers: {ex.Message}");
            }
        }

        public async Task<CustomerDto> CreateCustomerAsync(CustomerDto customer)
        {
            try
            {
                return await _httpService.PostAsync<CustomerDto>($"{BaseUrl}", customer);
            }
            catch (Exception ex)
            {
                throw new Exception($"Error creating customer: {ex.Message}");
            }
        }

        public async Task<BaseResponse<bool>> UpdateCustomerAsync(Customer customer)
        {
            try
            {
                return await _httpService.PutAsync<BaseResponse<bool>>($"{BaseUrl}/{customer.Id}", customer);
            }
            catch (Exception ex)
            {
                throw new Exception($"Error updating customer: {ex.Message}");
            }
        }

        public async Task<bool> DeleteCustomerAsync(int id)
        {
            try
            {
                await _httpService.DeleteAsync($"{BaseUrl}/{id}");
                return true;
            }
            catch (Exception ex)
            {
                throw new Exception($"Error deleting customer: {ex.Message}");
            }
        }

        public async Task<PagedResponse<CustomerDto>> GetAllCustomersAsync(string name, string phone, int? categoryId, PaginationFilter pagination)
        {
            var queryParams = new List<string>();

            if (!string.IsNullOrEmpty(name)) queryParams.Add($"name={Uri.EscapeDataString(name)}");
            if (!string.IsNullOrEmpty(phone)) queryParams.Add($"phone={Uri.EscapeDataString(phone)}");
            if (categoryId.HasValue && categoryId.Value > 0) queryParams.Add($"categoryId={categoryId.Value}");

            if (pagination != null)
            {
                queryParams.Add($"pageNumber={pagination.PageNumber}");
                queryParams.Add($"pageSize={pagination.PageSize}");
            }

            var queryString = string.Join("&", queryParams);
            var url = $"{BaseUrl}?{queryString}";

            return await _httpService.GetAsync<PagedResponse<CustomerDto>>(url);
        }

        public async Task<List<CustomerDto>> GetAllCustomersAsync()
        {
            try
            {
                return await _httpService.GetAsync<List<CustomerDto>>($"{BaseUrl}/get-all-customers");
            }
            catch (Exception ex)
            {
                throw new Exception($"Error getting customer: {ex.Message}");
            }
        }

    }
}