@inject NavigationManager Navigation
@inject IAuthenticationService AuthService

<div class="top-row ps-3 navbar navbar-dark">
    <div class="container-fluid">
        <a class="navbar-brand" href="">
            <img src="images/logo.png" alt="ExactCash" height="30" />
            ExactCash
        </a>
        <button title="تبديل التنقل" class="navbar-toggler" @onclick="ToggleNavMenu">
            <span class="navbar-toggler-icon"></span>
        </button>
    </div>
</div>

<div class="@NavMenuCssClass nav-scrollable" @onclick="CollapseNavMenu">
    <nav class="flex-column">
        <div class="nav-item px-3">
            <NavLink class="nav-link" href="" Match="NavLinkMatch.All">
                <span class="bi bi-house-door-fill-nav-menu" aria-hidden="true"></span> الرئيسية
            </NavLink>
        </div>
        
        <div class="nav-item px-3">
            <NavLink class="nav-link" href="sales">
                <span class="bi bi-cart-fill" aria-hidden="true"></span> المبيعات
            </NavLink>
        </div>

        <div class="nav-item px-3">
            <NavLink class="nav-link" href="products">
                <span class="bi bi-box-seam" aria-hidden="true"></span> المنتجات
            </NavLink>
        </div>

        <div class="nav-item px-3">
            <NavLink class="nav-link" href="customers">
                <span class="bi bi-people-fill" aria-hidden="true"></span> العملاء
            </NavLink>
        </div>

        <div class="nav-item px-3">
            <NavLink class="nav-link" href="suppliers">
                <span class="bi bi-truck" aria-hidden="true"></span> الموردين
            </NavLink>
        </div>

        <div class="nav-item px-3">
            <NavLink class="nav-link" href="purchase-orders">
                <span class="bi bi-clipboard-check" aria-hidden="true"></span> أوامر الشراء
            </NavLink>
        </div>

        <div class="nav-item px-3">
            <NavLink class="nav-link" href="inventory">
                <span class="bi bi-boxes" aria-hidden="true"></span> المخزون
            </NavLink>
        </div>

        <div class="nav-item px-3">
            <NavLink class="nav-link" href="expenses">
                <span class="bi bi-receipt" aria-hidden="true"></span> المصروفات
            </NavLink>
        </div>

        <div class="nav-item px-3">
            <NavLink class="nav-link" href="credit-sales">
                <span class="bi bi-credit-card" aria-hidden="true"></span> البيع بالآجل
            </NavLink>
        </div>

        <div class="nav-item px-3">
            <NavLink class="nav-link" href="reports">
                <span class="bi bi-graph-up" aria-hidden="true"></span> التقارير
            </NavLink>
        </div>

        <div class="nav-item px-3">
            <NavLink class="nav-link" href="users">
                <span class="bi bi-person-gear" aria-hidden="true"></span> المستخدمين
            </NavLink>
        </div>

        <div class="nav-item px-3">
            <NavLink class="nav-link" href="settings">
                <span class="bi bi-gear-fill" aria-hidden="true"></span> الإعدادات
            </NavLink>
        </div>

        <div class="nav-item px-3 mt-auto">
            <button class="nav-link btn btn-link" @onclick="Logout">
                <span class="bi bi-box-arrow-right" aria-hidden="true"></span> تسجيل الخروج
            </button>
        </div>
    </nav>
</div>

@code {
    private bool collapseNavMenu = true;

    private string? NavMenuCssClass => collapseNavMenu ? "collapse" : null;

    private void ToggleNavMenu()
    {
        collapseNavMenu = !collapseNavMenu;
    }

    private void CollapseNavMenu()
    {
        collapseNavMenu = true;
    }

    private async Task Logout()
    {
        await AuthService.LogoutAsync();
        Navigation.NavigateTo("/login");
    }
}
