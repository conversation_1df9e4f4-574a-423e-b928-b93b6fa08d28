using ExactCash.Application.DTOs;
using ExactCash.Application.Interfaces;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Security.Claims;

namespace ExactCash.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class CustomerCategoriesController : ControllerBase
    {
        private readonly ICustomerCategoryService _customerCategoryService;

        public CustomerCategoriesController(ICustomerCategoryService customerCategoryService)
        {
            _customerCategoryService = customerCategoryService;
        }

        [HttpGet("get-all")]
        public async Task<IActionResult> GetAll()
        {
            var categories = await _customerCategoryService.GetAllAsync();
            return Ok(categories);
        }

        [HttpGet("get-active")]
        public async Task<IActionResult> GetActive()
        {
            var categories = await _customerCategoryService.GetActiveAsync();
            return Ok(categories);
        }

        [HttpGet("{id}")]
        public async Task<IActionResult> GetById(int id)
        {
            var category = await _customerCategoryService.GetByIdAsync(id);
            if (category == null)
                return NotFound();

            return Ok(category);
        }

        [HttpPost("create")]
        public async Task<IActionResult> Create([FromBody] CustomerCategoryDto categoryDto, [FromQuery] string createdBy = "System")
        {
            if (!ModelState.IsValid)
                return BadRequest(ModelState);

            var userEmail = User.FindFirst(ClaimTypes.Email)?.Value;
            var result = await _customerCategoryService.CreateAsync(categoryDto, userEmail);

            if (result.Success)
                return Ok(result);

            return BadRequest(result);
        }

        [HttpPut("update")]
        public async Task<IActionResult> Update([FromBody] CustomerCategoryDto categoryDto, [FromQuery] string updatedBy = "System")
        {
            if (!ModelState.IsValid)
                return BadRequest(ModelState);

            var userEmail = User.FindFirst(ClaimTypes.Email)?.Value;
            var result = await _customerCategoryService.UpdateAsync(categoryDto, userEmail);

            if (result.Success)
                return Ok(result);

            return BadRequest(result);
        }

        [HttpDelete("delete/{id}")]
        public async Task<IActionResult> Delete(int id)
        {
            var result = await _customerCategoryService.DeleteAsync(id);

            if (result.Success)
                return Ok(result);

            return BadRequest(result);
        }
    }
}
