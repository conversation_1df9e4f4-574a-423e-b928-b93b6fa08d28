﻿#nullable disable

namespace ExactCash.Application.DTOs
{
    public class ProfitLossReportDto
    {
        public DateTime ReportDate { get; set; }
        public decimal TotalRevenue { get; set; }
        public decimal TotalCostOfGoodsSold { get; set; }
        public decimal GrossProfit { get; set; }
        public decimal TotalExpenses { get; set; }
        public decimal NetProfit { get; set; }
        public decimal GrossProfitMargin { get; set; }
        public decimal NetProfitMargin { get; set; }
        public decimal TotalTax { get; set; }
        public decimal TotalDiscounts { get; set; }
        public int TotalTransactions { get; set; }
        public decimal AverageTransactionValue { get; set; }

        // Formatted properties for display
        public string FormattedTotalRevenue => $"{TotalRevenue:N2} ج.م";
        public string FormattedTotalCostOfGoodsSold => $"{TotalCostOfGoodsSold:N2} ج.م";
        public string FormattedGrossProfit => $"{GrossProfit:N2} ج.م";
        public string FormattedTotalExpenses => $"{TotalExpenses:N2} ج.م";
        public string FormattedNetProfit => $"{NetProfit:N2} ج.م";
        public string FormattedGrossProfitMargin => $"{GrossProfitMargin:N1}%";
        public string FormattedNetProfitMargin => $"{NetProfitMargin:N1}%";
        public string FormattedTotalTax => $"{TotalTax:N2} ج.م";
        public string FormattedTotalDiscounts => $"{TotalDiscounts:N2} ج.م";
        public string FormattedAverageTransactionValue => $"{AverageTransactionValue:N2} ج.م";

        public string ProfitColor => NetProfit >= 0 ? "#28A745" : "#DC3545";

        public ProfitLossReportDto()
        {

        }
    }

    public class CashFlowReportDto
    {
        public DateTime ReportDate { get; set; }
        public decimal OpeningBalance { get; set; }
        public decimal CashSales { get; set; }
        public decimal CashReceipts { get; set; }
        public decimal CashExpenses { get; set; }
        public decimal CashWithdrawals { get; set; }
        public decimal ClosingBalance { get; set; }
        public decimal NetCashFlow { get; set; }
        public int CashTransactions { get; set; }
        public int CreditTransactions { get; set; }
        public decimal CreditSales { get; set; }
        public decimal AccountsReceivable { get; set; }

        // Formatted properties for display
        public string FormattedOpeningBalance => $"{OpeningBalance:N2} ج.م";
        public string FormattedCashSales => $"{CashSales:N2} ج.م";
        public string FormattedCashReceipts => $"{CashReceipts:N2} ج.م";
        public string FormattedCashExpenses => $"{CashExpenses:N2} ج.م";
        public string FormattedCashWithdrawals => $"{CashWithdrawals:N2} ج.م";
        public string FormattedClosingBalance => $"{ClosingBalance:N2} ج.م";
        public string FormattedNetCashFlow => $"{NetCashFlow:N2} ج.م";
        public string FormattedCreditSales => $"{CreditSales:N2} ج.م";
        public string FormattedAccountsReceivable => $"{AccountsReceivable:N2} ج.م";

        public string CashFlowColor => NetCashFlow >= 0 ? "#28A745" : "#DC3545";

        public CashFlowReportDto()
        {

        }
    }

    public class PaymentMethodReportDto
    {
        public string PaymentMethod { get; set; }
        public int TransactionCount { get; set; }
        public decimal TotalAmount { get; set; }
        public decimal Percentage { get; set; }
        public decimal AverageTransactionValue { get; set; }
        public DateTime ReportStartDate { get; set; }
        public DateTime ReportEndDate { get; set; }

        // Formatted properties for display
        public string FormattedTotalAmount => $"{TotalAmount:N2} ج.م";
        public string FormattedPercentage => $"{Percentage:N1}%";
        public string FormattedAverageTransactionValue => $"{AverageTransactionValue:N2} ج.م";

        public PaymentMethodReportDto()
        {

        }
    }

    public class TaxReportDto
    {
        public DateTime ReportDate { get; set; }
        public decimal TaxableAmount { get; set; }
        public decimal TaxAmount { get; set; }
        public decimal TaxRate { get; set; }
        public int TaxableTransactions { get; set; }
        public decimal NonTaxableAmount { get; set; }
        public int NonTaxableTransactions { get; set; }
        public decimal TotalSales { get; set; }
        public decimal TaxPercentageOfSales { get; set; }

        // Formatted properties for display
        public string FormattedTaxableAmount => $"{TaxableAmount:N2} ج.م";
        public string FormattedTaxAmount => $"{TaxAmount:N2} ج.م";
        public string FormattedTaxRate => $"{TaxRate:N1}%";
        public string FormattedNonTaxableAmount => $"{NonTaxableAmount:N2} ج.م";
        public string FormattedTotalSales => $"{TotalSales:N2} ج.م";
        public string FormattedTaxPercentageOfSales => $"{TaxPercentageOfSales:N1}%";

        public TaxReportDto()
        {

        }
    }

    public class ExpenseReportDto
    {
        public int ExpenseId { get; set; }
        public DateTime ExpenseDate { get; set; }
        public string CategoryName { get; set; }
        public string Description { get; set; }
        public decimal Amount { get; set; }
        public string PaymentMethod { get; set; }
        public string CreatedBy { get; set; }
        public string Reference { get; set; }

        // Formatted properties for display
        public string FormattedAmount => $"{Amount:N2} ج.م";

        public ExpenseReportDto()
        {

        }
    }

    public class ExpenseSummaryDto
    {
        public string CategoryName { get; set; }
        public decimal TotalAmount { get; set; }
        public int TransactionCount { get; set; }
        public decimal Percentage { get; set; }
        public decimal AverageExpense { get; set; }
        public DateTime ReportStartDate { get; set; }
        public DateTime ReportEndDate { get; set; }

        // Formatted properties for display
        public string FormattedTotalAmount => $"{TotalAmount:N2} ج.م";
        public string FormattedPercentage => $"{Percentage:N1}%";
        public string FormattedAverageExpense => $"{AverageExpense:N2} ج.م";

        public ExpenseSummaryDto()
        {

        }
    }
}
