<Window x:Class="ExactCash.WPF.Views.Reports.GenericPlaceholderWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="تقرير" Height="600" Width="1000"
        WindowStartupLocation="CenterScreen"
        FlowDirection="RightToLeft">
    <Grid>
        <Border x:Name="MainBorder" Background="#2196F3" Padding="20">
            <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                <TextBlock x:Name="IconText" Text="📊" FontSize="48" Foreground="White" HorizontalAlignment="Center"/>
                <TextBlock x:Name="TitleText" Text="تقرير" 
                          FontSize="24" 
                          FontWeight="Bold" 
                          Foreground="White" 
                          HorizontalAlignment="Center"
                          Margin="0,10,0,0"/>
                <TextBlock Text="سيتم تطوير هذا التقرير قريباً" 
                          FontSize="16" 
                          Foreground="White" 
                          HorizontalAlignment="Center"
                          Margin="0,20,0,0"/>
                <Button Content="إغلاق" 
                        Background="White" 
                        Foreground="#2196F3" 
                        Padding="20,10" 
                        Margin="0,30,0,0"
                        Click="Close_Click"/>
            </StackPanel>
        </Border>
    </Grid>
</Window>
